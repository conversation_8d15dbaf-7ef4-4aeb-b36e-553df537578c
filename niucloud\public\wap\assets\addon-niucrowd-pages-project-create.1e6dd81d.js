import{N as e,r as a,l as t,s as l,Q as o,bE as n,b8 as s,X as c,bL as r,y as i,o as u,c as d,w as m,k as _,g as f,b as g,A as p,R as y,T as v,B as h,S as b,a3 as k,C as x,e as j,b9 as w,I as V,ag as C,ay as T,H as A}from"./index-dd56d0cc.js";import{a as I}from"./category.367da76d.js";import{k as U}from"./project.e6204607.js";import{g as q}from"./member.ebe67bf1.js";import{c as P}from"./auth.d5d24670.js";import{I as F}from"./image-upload.3ccee1ca.js";import{_ as L}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-upload.44346d61.js";import"./u-loading-icon.f15d7447.js";import"./common.eabc72c7.js";const M=L({__name:"create",setup(L){const M=e({project_name:"",category_id:"",cover_image:"",description:"",target_amount:"",duration_days:30,agreeAgreement:!1,rewards:[{amount:"",limit_count:"",content:""}]}),$=a([]),z=a(null),H=a(0),B=a(!1),G=a(!1),N=a(!0),Q=a(!1),E=a(!1),J=a({title:"",content:""}),K=t((()=>[{category_id:"",category_name:"请选择项目分类"},...$.value]));l((async()=>{await O()})),o((()=>{R()}));const O=async()=>{try{if(N.value=!0,!P())return void n({title:"登录提示",content:"发布项目需要先登录，是否立即前往登录？",confirmText:"去登录",cancelText:"返回",success:e=>{e.confirm?s({url:"/pages/member/login"}):c()}});const e=await q();if(console.log("认证状态检查结果:",e),1!==e.code)return void n({title:"检查失败",content:"无法获取认证状态，请检查网络连接后重试",confirmText:"重试",cancelText:"返回",success:e=>{e.confirm?O():c()}});{const a=e.data,t=a.status;if(-1===t){if(!a.can_publish)return void n({title:"身份认证提示",content:"发布项目需要先完成身份认证，是否立即前往认证？",confirmText:"去认证",cancelText:"返回",success:e=>{e.confirm?r({url:"/addon/niucrowd/pages/member/auth-submit"}):c()}});console.log("系统配置不需要认证，允许发布项目")}else{if(0===t)return void n({title:"认证审核中",content:"您的身份认证正在审核中，请耐心等待审核结果。审核通过后即可发布项目。",confirmText:"查看状态",cancelText:"返回",success:e=>{e.confirm?r({url:"/addon/niucrowd/pages/member/auth-status"}):c()}});if(2===t)return void n({title:"认证未通过",content:"您的身份认证未通过审核，请重新提交认证资料后再发布项目。",confirmText:"重新认证",cancelText:"返回",success:e=>{e.confirm?r({url:"/addon/niucrowd/pages/member/auth-submit"}):c()}});if(1!==t)return void n({title:"状态异常",content:`认证状态异常(${t})，请重新登录或联系客服`,confirmText:"确定",success:()=>{c()}});if(!a.can_publish)return void n({title:"发布权限不足",content:"当前系统配置不允许发布项目，请联系管理员",confirmText:"确定",success:()=>{c()}});console.log("身份认证检查通过，允许发布项目")}}}catch(e){return console.error("检查认证状态失败:",e),void n({title:"检查失败",content:"检查认证状态时发生错误，请重试",confirmText:"重试",cancelText:"返回",success:e=>{e.confirm?O():c()}})}finally{N.value=!1}},R=async()=>{try{Q.value=!0,console.log("开始加载分类列表...");const e=await I();if(console.log("分类API响应:",e),e&&e.data&&1===e.code){const a=Array.isArray(e.data)?e.data:e.data.data||[];a.length>0?($.value=a.map((e=>({category_id:e.category_id,category_name:e.category_name}))),console.log("成功加载分类:",$.value)):(console.warn("API返回空分类列表，使用默认分类"),S())}else console.warn("API响应格式错误，使用默认分类"),S()}catch(e){console.error("加载分类失败:",e),S(),i({title:"加载分类失败，使用默认分类",icon:"none"})}finally{Q.value=!1,console.log("分类加载完成，当前分类数量:",$.value.length)}},S=()=>{$.value=[{category_id:1,category_name:"科技产品"},{category_id:2,category_name:"创意设计"},{category_id:3,category_name:"生活用品"},{category_id:4,category_name:"文化艺术"},{category_id:5,category_name:"公益慈善"}],console.log("使用默认分类:",$.value)},X=e=>{let a=e.detail.value;a=a.replace(/[^\d.]/g,"");const t=a.split(".");t[0].length>8&&(t[0]=t[0].substring(0,8)),t.length>1&&(t[1]=t[1].substring(0,2),a=t.join("."));parseFloat(a)>99999999&&(a="99999999",i({title:"目标金额不能超过99,999,999元",icon:"none"})),M.target_amount=a},D=()=>{Q.value?i({title:"分类加载中，请稍候...",icon:"none"}):0!==$.value.length?B.value=!0:i({title:"暂无可选分类",icon:"none"})},W=()=>{B.value=!1},Y=()=>{try{const e=H.value,a=K.value[e];if(console.log("确认分类选择:",{index:e,selectedItem:a}),0!==e&&a.category_id){const a=$.value[e-1];if(!a)return console.error("分类选择错误：无法找到对应的分类数据"),void i({title:"分类选择失败：数据错误",icon:"none"});z.value=a,M.category_id=a.category_id,console.log("成功选择分类:",a),i({title:`已选择：${a.category_name}`,icon:"success"})}else console.log("用户选择了占位项，清空选择"),z.value=null,M.category_id="",H.value=0;W()}catch(e){console.error("确认分类选择失败:",e),i({title:"分类选择失败",icon:"none"})}},Z=()=>{M.agreeAgreement=!M.agreeAgreement,console.log("协议同意状态:",M.agreeAgreement)},ee=async()=>{try{E.value=!0,J.value={title:"项目发布协议",content:'\n                <div style="padding: 20px; line-height: 1.6;">\n                    <h3>项目发布协议</h3>\n                    <p>欢迎使用本平台发布共享创业项目。在发布项目前，请仔细阅读以下协议条款：</p>\n\n                    <h4>1. 项目真实性</h4>\n                    <p>您承诺所发布的项目信息真实、准确、完整，不存在虚假宣传或误导性信息。</p>\n\n                    <h4>2. 知识产权</h4>\n                    <p>您确认拥有项目相关的知识产权，或已获得相应授权，不侵犯他人合法权益。</p>\n\n                    <h4>3. 资金使用</h4>\n                    <p>筹集的资金将专门用于项目开发和实施，您承诺合理使用并按承诺提供回报。</p>\n\n                    <h4>4. 平台责任</h4>\n                    <p>平台仅提供信息发布服务，不对项目成功与否承担责任。</p>\n\n                    <h4>5. 违约责任</h4>\n                    <p>如违反本协议，平台有权暂停或终止服务，并保留追究法律责任的权利。</p>\n\n                    <p>点击"同意协议"即表示您已阅读并同意遵守以上条款。</p>\n                </div>\n            '}}catch(e){console.error("加载协议内容失败:",e),i({title:"加载协议失败",icon:"none"})}},ae=()=>{M.agreeAgreement=!0,E.value=!1,i({title:"已同意协议",icon:"success"})},te=()=>{M.rewards.push({amount:"",limit_count:"",content:""})},le=async()=>{if(M.project_name)if(M.category_id)if(M.agreeAgreement)if(M.cover_image)if(M.description)if(!M.target_amount||parseFloat(M.target_amount)<=0)i({title:"请输入有效的目标金额",icon:"none"});else if(parseFloat(M.target_amount)>99999999)i({title:"目标金额不能超过99,999,999元",icon:"none"});else if(!M.duration_days||parseInt(M.duration_days)<=0)i({title:"请输入有效的筹款天数",icon:"none"});else{for(let e=0;e<M.rewards.length;e++){const a=M.rewards[e];if(!a.amount||!a.content)return void i({title:`请完善回报${e+1}的信息`,icon:"none"})}G.value=!0;try{const e={project_name:M.project_name,category_id:M.category_id,project_desc:M.description,project_content:M.description,cover_image:M.cover_image,target_amount:parseFloat(M.target_amount),duration_days:parseInt(M.duration_days),agreeAgreement:M.agreeAgreement?"yes":"no",rewards:M.rewards.map((e=>({reward_name:`回报档位 ¥${e.amount}`,reward_desc:e.content,content:e.content,amount:parseFloat(e.amount),stock:e.limit_count?parseInt(e.limit_count):0,delivery_time:"项目成功后30天内发货"})))};await U(e);n({title:"项目提交成功",content:"您的项目已提交，正在等待审核。请选择下一步操作：",confirmText:"查看我的项目",cancelText:"返回首页",success:e=>{e.confirm?s({url:"/addon/niucrowd/pages/member/projects"}):w({url:"/pages/index"})}})}catch(e){console.error("提交项目失败:",e),i({title:e.message||"提交失败，请重试",icon:"none"})}finally{G.value=!1}}else i({title:"请输入项目描述",icon:"none"});else i({title:"请上传项目封面",icon:"none"});else i({title:"请先同意项目发布协议",icon:"none"});else i({title:"请选择项目分类",icon:"none"});else i({title:"请输入项目名称",icon:"none"})};return(e,a)=>{const t=_,l=V,o=C,n=T,s=A;return u(),d(t,{class:"create-page"},{default:m((()=>[f(" 认证检查加载状态 "),N.value?(u(),d(t,{key:0,class:"auth-checking"},{default:m((()=>[g(t,{class:"loading-container"},{default:m((()=>[g(t,{class:"loading-icon"},{default:m((()=>[p("🔄")])),_:1}),g(l,{class:"loading-text"},{default:m((()=>[p("正在验证身份认证状态...")])),_:1})])),_:1})])),_:1})):(u(),y(b,{key:1},[f(" 项目创建表单 "),g(t,{class:"form-container"},{default:m((()=>[g(t,{class:"form-section"},{default:m((()=>[g(t,{class:"section-title"},{default:m((()=>[p("基本信息")])),_:1}),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("项目名称 "),g(l,{class:"required"},{default:m((()=>[p("*")])),_:1})])),_:1}),g(o,{modelValue:M.project_name,"onUpdate:modelValue":a[0]||(a[0]=e=>M.project_name=e),placeholder:"请输入项目名称",class:"input",maxlength:"50"},null,8,["modelValue"])])),_:1}),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("项目分类 "),g(l,{class:"required"},{default:m((()=>[p("*")])),_:1})])),_:1}),g(t,{class:"select-box",onClick:D},{default:m((()=>[g(l,{class:v(["select-text",{placeholder:!z.value}])},{default:m((()=>[p(h(z.value?z.value.category_name:"请选择项目分类"),1)])),_:1},8,["class"]),g(l,{class:"iconfont iconxiangyoujiantou"}),Q.value?(u(),d(l,{key:0,class:"loading-text"},{default:m((()=>[p("加载中...")])),_:1})):f("v-if",!0)])),_:1})])),_:1}),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("项目封面 "),g(l,{class:"required"},{default:m((()=>[p("*")])),_:1})])),_:1}),g(t,{class:"upload-container"},{default:m((()=>[g(F,{modelValue:M.cover_image,"onUpdate:modelValue":a[1]||(a[1]=e=>M.cover_image=e),"max-count":1,"size-limit":5,"show-tips":!0},null,8,["modelValue"]),g(t,{class:"upload-tip"},{default:m((()=>[p("请上传清晰的项目封面图片，支持JPG、PNG格式，大小不超过5MB")])),_:1})])),_:1})])),_:1}),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("项目描述 "),g(l,{class:"required"},{default:m((()=>[p("*")])),_:1})])),_:1}),g(n,{modelValue:M.description,"onUpdate:modelValue":a[2]||(a[2]=e=>M.description=e),placeholder:"请详细描述您的项目...",class:"textarea",maxlength:"500"},null,8,["modelValue"])])),_:1})])),_:1}),g(t,{class:"form-section"},{default:m((()=>[g(t,{class:"section-title"},{default:m((()=>[p("筹款信息")])),_:1}),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("目标金额 "),g(l,{class:"required"},{default:m((()=>[p("*")])),_:1})])),_:1}),g(t,{class:"input-group"},{default:m((()=>[g(l,{class:"currency"},{default:m((()=>[p("¥")])),_:1}),g(o,{modelValue:M.target_amount,"onUpdate:modelValue":a[3]||(a[3]=e=>M.target_amount=e),placeholder:"0.00",class:"input amount-input",type:"digit",onInput:X},null,8,["modelValue"])])),_:1}),g(t,{class:"form-tip"},{default:m((()=>[p("最高支持99,999,999元")])),_:1})])),_:1}),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("筹款天数 "),g(l,{class:"required"},{default:m((()=>[p("*")])),_:1})])),_:1}),g(t,{class:"input-group"},{default:m((()=>[g(o,{modelValue:M.duration_days,"onUpdate:modelValue":a[4]||(a[4]=e=>M.duration_days=e),placeholder:"30",class:"input",type:"number"},null,8,["modelValue"]),g(l,{class:"unit"},{default:m((()=>[p("天")])),_:1})])),_:1})])),_:1})])),_:1}),g(t,{class:"form-section"},{default:m((()=>[g(t,{class:"section-title"},{default:m((()=>[p("回报设置")])),_:1}),g(t,{class:"section-desc"},{default:m((()=>[p("为支持者设置不同金额的回报")])),_:1}),g(t,{class:"reward-list"},{default:m((()=>[(u(!0),y(b,null,k(M.rewards,((e,a)=>(u(),d(t,{key:a,class:"reward-item"},{default:m((()=>[g(t,{class:"reward-header"},{default:m((()=>[g(l,{class:"reward-title"},{default:m((()=>[p("回报 "+h(a+1),1)])),_:2},1024),M.rewards.length>1?(u(),d(l,{key:0,class:"remove-btn",onClick:e=>(e=>{M.rewards.splice(e,1)})(a)},{default:m((()=>[p("删除")])),_:2},1032,["onClick"])):f("v-if",!0)])),_:2},1024),g(t,{class:"reward-form"},{default:m((()=>[g(t,{class:"form-row"},{default:m((()=>[g(t,{class:"form-col"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("支持金额")])),_:1}),g(t,{class:"input-group"},{default:m((()=>[g(l,{class:"currency"},{default:m((()=>[p("¥")])),_:1}),g(o,{modelValue:e.amount,"onUpdate:modelValue":a=>e.amount=a,placeholder:"0.00",class:"input",type:"digit"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024),g(t,{class:"form-col"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("限制数量")])),_:1}),g(o,{modelValue:e.limit_count,"onUpdate:modelValue":a=>e.limit_count=a,placeholder:"不限制",class:"input",type:"number"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024),g(t,{class:"form-item"},{default:m((()=>[g(t,{class:"label"},{default:m((()=>[p("回报内容")])),_:1}),g(n,{modelValue:e.content,"onUpdate:modelValue":a=>e.content=a,placeholder:"请描述支持者将获得的回报...",class:"textarea small",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),g(t,{class:"add-reward-btn",onClick:te},{default:m((()=>[g(l,{class:"iconfont icontianjia"}),g(l,null,{default:m((()=>[p("添加回报")])),_:1})])),_:1})])),_:1})])),_:1})],2112)),f(" 协议确认 "),g(t,{class:"agreement-section"},{default:m((()=>[g(t,{class:"agreement-checkbox",onClick:Z},{default:m((()=>[g(t,{class:v(["checkbox",{checked:M.agreeAgreement}])},{default:m((()=>[M.agreeAgreement?(u(),d(l,{key:0,class:"check-icon"},{default:m((()=>[p("✓")])),_:1})):f("v-if",!0)])),_:1},8,["class"]),g(l,{class:"agreement-text"},{default:m((()=>[p(" 我已阅读并同意 "),g(l,{class:"agreement-link",onClick:x(ee,["stop"])},{default:m((()=>[p("《项目发布协议》")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),g(t,{class:"bottom-bar"},{default:m((()=>[g(s,{class:"submit-btn",onClick:le,disabled:G.value||!M.agreeAgreement},{default:m((()=>[p(h(G.value?"提交中...":"提交项目"),1)])),_:1},8,["disabled"])])),_:1}),f(" 分类选择弹窗 "),B.value?(u(),d(t,{key:2,class:"category-popup-mask",onClick:W},{default:m((()=>[g(t,{class:"category-popup",onClick:a[5]||(a[5]=x((()=>{}),["stop"]))},{default:m((()=>[g(t,{class:"popup-header"},{default:m((()=>[g(l,{class:"cancel-btn",onClick:W},{default:m((()=>[p("取消")])),_:1}),g(l,{class:"title"},{default:m((()=>[p("选择项目分类")])),_:1}),g(l,{class:"confirm-btn",onClick:Y},{default:m((()=>[p("确定")])),_:1})])),_:1}),g(t,{class:"category-list"},{default:m((()=>[(u(!0),y(b,null,k(j(K),((e,a)=>(u(),d(t,{key:a,class:v(["category-item",{active:H.value===a}]),onClick:e=>(e=>{try{if(console.log("选择分类项索引:",e,"显示列表长度:",K.value.length),e<0||e>=K.value.length)return void console.error("分类选择错误：索引超出范围",{index:e,listLength:K.value.length});H.value=e,console.log("临时选择分类索引:",e)}catch(a){console.error("选择分类项失败:",a)}})(a)},{default:m((()=>[g(l,{class:"category-name"},{default:m((()=>[p(h(e.category_name),1)])),_:2},1024),H.value===a?(u(),d(l,{key:0,class:"check-icon"},{default:m((()=>[p("✓")])),_:1})):f("v-if",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 协议弹窗 "),E.value?(u(),d(t,{key:3,class:"agreement-modal-mask",onClick:a[9]||(a[9]=e=>E.value=!1)},{default:m((()=>[g(t,{class:"agreement-modal",onClick:a[8]||(a[8]=x((()=>{}),["stop"]))},{default:m((()=>[g(t,{class:"modal-header"},{default:m((()=>[g(l,{class:"modal-title"},{default:m((()=>[p(h(J.value.title||"项目发布协议"),1)])),_:1}),g(l,{class:"close-btn",onClick:a[6]||(a[6]=e=>E.value=!1)},{default:m((()=>[p("×")])),_:1})])),_:1}),g(t,{class:"modal-content"},{default:m((()=>[g(t,{class:"agreement-content",innerHTML:J.value.content||"正在加载协议内容..."},null,8,["innerHTML"])])),_:1}),g(t,{class:"modal-footer"},{default:m((()=>[g(s,{class:"cancel-btn",onClick:a[7]||(a[7]=e=>E.value=!1)},{default:m((()=>[p("取消")])),_:1}),g(s,{class:"agree-btn",onClick:ae},{default:m((()=>[p("同意协议")])),_:1})])),_:1})])),_:1})])),_:1})):f("v-if",!0)])),_:1})}}},[["__scopeId","data-v-cd7ebe4c"]]);export{M as default};

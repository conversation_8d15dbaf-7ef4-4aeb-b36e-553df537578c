import{ao as a,d as e,r as s,o as p,c as t,w as r,b as o,A as n,B as l,R as u,a3 as x,S as c,g as d,C as i,k as f,ap as _,H as m,i as v,j as g}from"./index-4b8dc7db.js";import{_ as y}from"./u-popup.e2790691.js";function b(e,s){return a.get(`pay/friendspay/info/${e}/${s}`,{},{showErrorMessage:!0})}const h=e({__name:"message",setup(a,{expose:e}){const b=s(!1),h=s({}),w=()=>{b.value=!1};return e({open:a=>{h.value=a,b.value=!0}}),(a,e)=>{const s=f,k=_,j=m,C=v(g("u-popup"),y);return p(),t(s,{onTouchmove:e[0]||(e[0]=i((()=>{}),["prevent","stop"]))},{default:r((()=>[o(C,{show:b.value,onClose:w,mode:"center",round:"var(--rounded-big)"},{default:r((()=>[Object.keys(h.value).length?(p(),t(s,{key:0,class:"w-[570rpx] px-[32rpx] popup-common center"},{default:r((()=>[o(s,{class:"title"},{default:r((()=>[n(l(h.value.pay_explain_title),1)])),_:1}),h.value.pay_explain_content?(p(),t(k,{key:0,"scroll-y":!0,class:"px-[30rpx] box-border max-h-[260rpx]"},{default:r((()=>[(p(!0),u(c,null,x(h.value.pay_explain_content.split("\n"),(a=>(p(),t(s,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:r((()=>[n(l(a),1)])),_:2},1024)))),256))])),_:1})):d("v-if",!0),o(s,{class:"btn-wrap !pt-[40rpx]"},{default:r((()=>[o(j,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:w},{default:r((()=>[n("我知道了")])),_:1})])),_:1})])),_:1})):d("v-if",!0)])),_:1},8,["show"])])),_:1})}}});export{h as _,b as g};

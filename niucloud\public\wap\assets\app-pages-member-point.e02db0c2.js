import{d as e,l as t,r as a,s as r,c5 as l,c6 as s,o as p,c as x,w as c,R as i,b as n,n as o,e as d,A as u,B as f,S as g,a3 as m,g as _,E as b,D as h,a as v,k as y,G as k,I as w,H as j,i as F,j as C}from"./index-4b8dc7db.js";import{_ as E}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{t as I}from"./topTabbar.1aa95d14.js";import{_ as R}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.11ef83b8.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */const S=R(e({__name:"point",setup(e){I().setTopTabbarParam({title:"我的积分"});let R={};const S=t((()=>({backgroundImage:"url("+b("static/resource/images/member/point/point_bg.png")+") ",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundPosition:"bottom"}))),z=t((()=>Object.keys(R).length?h(Number(R.height))+h(R.top)+50+"rpx;":"50rpx")),B=a({}),T=a([]),P=a(!0);r((async()=>{let e=await l(),t=await s();B.value=e.data,T.value=t.data,P.value=!1}));const A=(e="",t={})=>{v({url:e,param:t})};return(e,t)=>{const a=y,r=k,l=w,s=j,h=F(C("loading-page"),E);return p(),x(a,{class:"bg-[var(--page-bg-color)] min-h-[100vh]",style:o(e.themeColor())},{default:c((()=>[P.value?_("v-if",!0):(p(),i(g,{key:0},[n(a,{class:"w-full bg-[var(--page-bg-color)]"},{default:c((()=>[n(a,{class:"pb-[210rpx] relative",style:o(d(S))},{default:c((()=>[n(a,{class:"text-[70rpx] leading-[90rpx] text-[#fff] pl-[60rpx] font-500 pt-[77rpx] price-font"},{default:c((()=>[u(f(B.value.point||0),1)])),_:1}),n(a,{class:"flex items-center pl-[60rpx]"},{default:c((()=>[n(r,{class:"h-[36rpx] w-[36rpx] -mb-[4rpx]",src:d(b)("static/resource/images/member/point/icon.png"),mode:"heightFix"},null,8,["src"]),n(a,{class:"text-[26rpx] leading-[36rpx] text-[#fff] ml-[10rpx]"},{default:c((()=>[u("我的积分")])),_:1})])),_:1}),n(a,{class:"side-tab",style:o({top:d(z)}),onClick:t[0]||(t[0]=e=>A("/app/pages/member/point_detail"))},{default:c((()=>[n(l,{class:"nc-iconfont nc-icon-jifenduihuanV6xx1 icon"}),n(l,{class:"desc"},{default:c((()=>[u("积分明细")])),_:1})])),_:1},8,["style"])])),_:1},8,["style"]),n(a,{class:"sidebar-margin flex flex-col mt-[-178rpx] relative"},{default:c((()=>[n(a,{class:"w-[322rpx] h-[80rpx] text-[30rpx] text-[#333] font-500 box-border pl-[30rpx] pt-[var(--pad-top-m)] rounded-tl-[var(--rounded-big)] overflow-hidden mb-[-2rpx]",style:o({backgroundImage:"url("+d(b)("static/resource/images/member/point/top_bg.png")+") ",backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},{default:c((()=>[n(l,{class:"leading-[42rpx]"},{default:c((()=>[u("积分详情")])),_:1})])),_:1},8,["style"]),n(a,{class:"flex items-center px-[30rpx] rounded-[var(--rounded-big)] !rounded-tl-none bg-[#fff] h-[173rpx] box-border"},{default:c((()=>[n(a,{class:"w-[196rpx] flex-shrink-0 text-center"},{default:c((()=>[n(a,{class:"text-[#333] text-[42rpx] leading-[54rpx] price-font"},{default:c((()=>[u(f(B.value.point_get||0),1)])),_:1}),n(a,{class:"mt-[8rpx] text-[var(--text-color-light6)] text-[26rpx] leading-[36rpx] font-400"},{default:c((()=>[u("累计积分")])),_:1})])),_:1}),n(a,{class:"w-[1rpx] h-[50rpx] flex-shrink-0 bg-[var(--temp-bg)] mx-[10rpx]"}),n(a,{class:"w-[196rpx] flex-shrink-0 text-center"},{default:c((()=>[n(a,{class:"text-[#333] text-[42rpx] leading-[54rpx] price-font"},{default:c((()=>[u(f(B.value.use||0),1)])),_:1}),n(a,{class:"mt-[8rpx] text-[var(--text-color-light6)] text-[26rpx] leading-[36rpx] font-400"},{default:c((()=>[u("累计消费")])),_:1})])),_:1}),n(a,{class:"w-[1rpx] h-[50rpx] flex-shrink-0 bg-[var(--temp-bg)] mx-[10rpx]"}),n(a,{class:"w-[196rpx] min-w-[209.33rpx] flex-shrink-0 text-center"},{default:c((()=>[n(a,{class:"text-[#333] text-[42rpx] leading-[54rpx] price-font"},{default:c((()=>[u(f(B.value.point||0),1)])),_:1}),n(a,{class:"mt-[8rpx] text-[var(--text-color-light6)] text-[26rpx] leading-[36rpx] font-400"},{default:c((()=>[u("可用积分")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(a,{class:"mt-[var(--top-m)] sidebar-margin card-template"},{default:c((()=>[n(a,{class:"title"},{default:c((()=>[u("热门活动")])),_:1}),n(a,{class:"mt-[60rpx] flex justify-between"},{default:c((()=>[n(a,{class:"w-[200rpx] h-[253rpx] box-border pt-[69rpx] relative text-center",style:o({backgroundImage:"url("+d(b)("static/resource/images/member/point/activity_1.png")+") ",backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},{default:c((()=>[n(r,{class:"h-[78rpx] w-[78rpx] absolute left-[65rpx] top-[-21rpx]",src:d(b)("static/resource/images/member/point/activity_icon_1.png"),mode:"heightFix"},null,8,["src"]),n(a,{class:"text-[28rpx] leading-[39rpx] text-[#333]"},{default:c((()=>[u("每日赚积分")])),_:1}),n(a,{class:"mt-[10rpx] text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:c((()=>[u("每日签到")])),_:1}),n(a,{class:"w-full flex justify-center mt-[20rpx]"},{default:c((()=>[n(s,{class:"h-[54rpx] !m-0 rounded-[100rpx] text-[24rpx] flex-center !text-[#fff]",shape:"circle",style:o({background:"linear-gradient( 94deg, #FB7939 0%, #FE120E 99%), #EF000C"}),onClick:t[1]||(t[1]=e=>A("/app/pages/member/sign_in"))},{default:c((()=>[u("去签到")])),_:1},8,["style"])])),_:1})])),_:1},8,["style"]),n(a,{class:"w-[200rpx] h-[253rpx] box-border pt-[69rpx] relative text-center",style:o({backgroundImage:"url("+d(b)("static/resource/images/member/point/activity_2.png")+") ",backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},{default:c((()=>[n(r,{class:"h-[78rpx] w-[78rpx] absolute left-[65rpx] top-[-21rpx]",src:d(b)("static/resource/images/member/point/activity_icon_2.png"),mode:"heightFix"},null,8,["src"]),n(a,{class:"text-[28rpx] leading-[39rpx] text-[#333]"},{default:c((()=>[u("积分当钱花")])),_:1}),n(a,{class:"mt-[10rpx] text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:c((()=>[u("抵扣部分费用")])),_:1}),n(a,{class:"w-full flex justify-center mt-[20rpx]"},{default:c((()=>[n(s,{class:"h-[54rpx] !m-0 rounded-[100rpx] text-[24rpx] flex-center !text-[#fff]",shape:"circle",style:o({background:"linear-gradient( 94deg, #FB7939 0%, #FE120E 99%), #EF000C"}),onClick:t[2]||(t[2]=e=>A("/addon/shop/pages/point/index"))},{default:c((()=>[u("去兑换")])),_:1},8,["style"])])),_:1})])),_:1},8,["style"]),n(a,{class:"w-[200rpx] h-[253rpx] box-border pt-[69rpx] relative text-center",style:o({backgroundImage:"url("+d(b)("static/resource/images/member/point/activity_3.png")+") ",backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},{default:c((()=>[n(r,{class:"h-[78rpx] w-[78rpx] absolute left-[65rpx] top-[-21rpx]",src:d(b)("static/resource/images/member/point/icon.png"),mode:"heightFix"},null,8,["src"]),n(a,{class:"text-[28rpx] leading-[39rpx] text-[#333]"},{default:c((()=>[u("购物返积分")])),_:1}),n(a,{class:"mt-[10rpx] text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:c((()=>[u("下单得积分")])),_:1}),n(a,{class:"w-full flex justify-center mt-[20rpx]"},{default:c((()=>[n(s,{class:"h-[54rpx] !m-0 rounded-[100rpx] text-[24rpx] flex-center !text-[#fff]",shape:"circle",style:o({background:"linear-gradient( 94deg, #FB7939 0%, #FE120E 99%), #EF000C"}),onClick:t[3]||(t[3]=e=>A("/addon/shop/pages/goods/list"))},{default:c((()=>[u("去逛逛")])),_:1},8,["style"])])),_:1})])),_:1},8,["style"])])),_:1})])),_:1}),T.value.length?(p(),x(a,{key:0,class:"mt-[var(--top-m)] sidebar-margin card-template"},{default:c((()=>[n(a,{class:"title"},{default:c((()=>[u("做任务领积分")])),_:1}),(p(!0),i(g,null,m(T.value,((e,t)=>(p(),x(a,{class:"flex items-center justify-between mt-[30rpx]"},{default:c((()=>[n(a,{class:"flex items-center flex-1"},{default:c((()=>[n(r,{class:"h-[80rpx] w-[80rpx]",src:d(b)(e.icon||""),mode:"heightFix"},null,8,["src"]),n(a,{class:"flex flex-col ml-[20rpx]"},{default:c((()=>[n(l,{class:"text-[28rpx]"},{default:c((()=>[u(f(e.title),1)])),_:2},1024),n(a,{class:"mt-[14rpx] text-[var(--text-color-light6)] text-[24rpx] font-400"},{default:c((()=>[u(f(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),e.button?(p(),x(s,{key:0,class:"h-[54rpx] !m-0 rounded-[40rpx] text-[24rpx] flex-center !text-[#fff] primary-btn-bg",shape:"circle",onClick:t=>A(e.button.wap_redirect)},{default:c((()=>[u(f(e.button.text),1)])),_:2},1032,["onClick"])):_("v-if",!0)])),_:2},1024)))),256))])),_:1})):_("v-if",!0)],64)),n(h,{loading:P.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-9fc0063c"]]);export{S as default};

import{d as e,r as a,o as t,R as s,g as l,b as o,w as p,C as n,n as r,c as u,e as c,A as i,S as d,aj as f,bC as v,E as m,G as _,k as h,I as y,H as g,i as b,j as w}from"./index-4b8dc7db.js";import{_ as j}from"./u-popup.e2790691.js";import{_ as x}from"./_plugin-vue_export-helper.1b428a4d.js";const C=x(e({__name:"share-poster",props:{posterId:{type:String||Number,default:0},posterType:{type:String,default:""},posterParam:{type:Object,default:{}},copyUrl:{type:String,default:""},copyUrlParam:{type:String,default:""}},emits:["close"],setup(e,{expose:x,emit:C}){const S=e,k=a(!1),P=()=>{let e="";if(S.copyUrl){let a=location.pathname,t=["/app/","/addon/"];for(let e=0;e<t.length;e++)-1!=a.indexOf(t[e])&&(a=a.substr(0,a.indexOf(t[e])));e=location.origin+a+S.copyUrl+S.copyUrlParam}else e=location.origin+location.pathname+S.copyUrlParam;f(e,(()=>{k.value=!1}))},U=a(!1),O=a(!1),T=a(""),D=()=>{if(T.value)U.value=!1,O.value=!0;else{U.value=!0,O.value=!1;let e={id:S.posterId,type:S.posterType,param:S.posterParam},a=Date.parse(new Date);v(e).then((e=>{T.value=e.data&&m(e.data)||"";let t=Date.parse(new Date)-a;t<2200?setTimeout((()=>{U.value=!1,O.value=!0}),2200-t):(U.value=!1,O.value=!0)})).catch((()=>{A()}))}},I=a(!1),z=()=>{I.value=!1},F=a(0),A=()=>{k.value=!1,U.value=!1,O.value=!1,C("close")};return x({openShare:()=>{k.value=!0,D()},loadPoster:D}),(e,a)=>{const f=_,v=h,x=y,C=g,S=b(w("u-popup"),j);return t(),s(d,null,[l(" 分享弹窗 "),o(v,{onTouchmove:a[2]||(a[2]=n((()=>{}),["prevent","stop"])),class:"share-popup"},{default:p((()=>[o(S,{show:k.value,type:"bottom",onClose:A,overlayOpacity:"0.8"},{default:p((()=>[o(v,{onTouchmove:a[0]||(a[0]=n((()=>{}),["prevent","stop"]))},{default:p((()=>[o(v,{class:"poster-img-wrap",style:r({top:F.value})},{default:p((()=>[U.value?(t(),u(f,{key:0,class:"poster-animation",src:c(m)("addon/shop/poster_animation.gif"),mode:"aspectFit"},null,8,["src"])):l("v-if",!0),O.value?(t(),u(f,{key:1,class:"poster-img",src:c(m)(T.value),mode:"aspectFit","show-menu-by-longpress":!0},null,8,["src"])):l("v-if",!0)])),_:1},8,["style"]),o(v,{class:"share-content"},{default:p((()=>[o(v,{class:"share-box",onClick:P},{default:p((()=>[o(C,{class:"share-btn",plain:!0},{default:p((()=>[o(v,{class:"text-[#07c160] iconfont iconfuzhilianjie"}),o(x,null,{default:p((()=>[i("复制链接")])),_:1})])),_:1})])),_:1})])),_:1}),o(v,{class:"share-footer",onClick:A},{default:p((()=>[o(x,null,{default:p((()=>[i("取消分享")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),o(S,{show:I.value,mode:"center",round:10,closeable:!0,onClose:a[1]||(a[1]=e=>I.value=!1),"safe-area-inset-bottom":!1},{default:p((()=>[o(v,{class:"dialog-popup"},{default:p((()=>[o(v,{class:"title"},{default:p((()=>[i("提示")])),_:1}),o(v,{class:"message"},{default:p((()=>[i("您拒绝了保存图片到相册的授权请求，无法保存图片到相册，如需正常使用，请授权之后再进行操作。")])),_:1}),o(v,{class:"action-wrap"},{default:p((()=>[o(v,{onClick:z},{default:p((()=>[i("取消")])),_:1}),o(v,null,{default:p((()=>[o(C,{type:"default",class:"authorization-btn","open-type":"openSetting",onOpensetting:z,"hover-class":"none"},{default:p((()=>[i("立即授权")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})],2112)}}}),[["__scopeId","data-v-8d54e49c"]]);export{C as s};

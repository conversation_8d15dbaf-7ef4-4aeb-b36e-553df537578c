import{d as e,r as a,s as t,z as l,al as s,am as r,L as i,M as o,o as n,c as u,w as p,b as x,e as d,A as f,B as c,g as _,n as m,R as v,S as g,a3 as y,x as b,J as h,i as j,j as k,I as F,k as w,H as T,E as B,F as I,a as C,T as P,G as L}from"./index-4b8dc7db.js";import{_ as M}from"./u-avatar.db5bcfa1.js";import{_ as O}from"./u--image.892273b2.js";import{_ as R}from"./pay.2b11dfa6.js";import{_ as z}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{t as E}from"./topTabbar.1aa95d14.js";import{g as G,_ as J}from"./message.vue_vue_type_script_setup_true_lang.31c86b7e.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-text.40d19739.js";import"./u-image.18977c35.js";import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.e2790691.js";import"./u-safe-bottom.987908cd.js";import"./pay.eee54be7.js";import"./u-loading-icon.11ef83b8.js";const V=S(e({__name:"money",setup(e){const S=E();S.setTopTabbarParam({title:""});const V=a(!0),q=a(0),A=a(""),H=a({}),N=a(!1),Q=a(!0),D=a(null);t((e=>{q.value=e.id||0,A.value=e.type||""})),l((()=>{q.value&&A.value&&K(A.value,q.value)})),s((()=>{D.value&&(clearTimeout(D.value),D.value=null)})),r((()=>{D.value&&(clearTimeout(D.value),D.value=null)}));const K=(e,a)=>{Q.value&&(V.value=!0,Q.value=!1),G(e,a).then((t=>{H.value=t.data,i({title:H.value.config.pay_page_name}),S.setTopTabbarParam({title:H.value.config.pay_page_name}),V.value=!1,2!=H.value.status&&1!=H.value.status&&-1!=H.value.status?D.value=setTimeout((()=>{K(e,a)}),3e3):(clearTimeout(D.value),D.value=null)})).catch((()=>{D.value&&(clearTimeout(D.value),D.value=null),V.value=!1;o({title:"未找到帮付订单信息",url:"/app/pages/index/index",mode:"reLaunch"})}))},U=a(null),W=()=>{U.value.open(H.value.config)},X=a(),Y=()=>{var e;b()?null==(e=X.value)||e.open(H.value.trade_type,H.value.trade_id,"/app/pages/index/index","friendspay"):h().setLoginBack({url:"/app/pages/friendspay/money",param:{id:H.value.trade_id,type:H.value.trade_type}})};return(e,a)=>{const t=j(k("u-avatar"),M),l=F,s=w,r=T,i=L,o=j(k("u--image"),O),b=j(k("pay"),R),h=j(k("loading-page"),z);return n(),u(s,{style:m(e.themeColor())},{default:p((()=>[Object.keys(H.value).length&&!V.value?(n(),u(s,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:p((()=>[x(s,{style:m({background:"url("+d(B)("static/resource/images/app/friendpay_money.png")+") left bottom / cover no-repeat"}),class:"pb-[194rpx] overflow-hidden"},{default:p((()=>[x(s,{class:"mt-[20rpx] flex flex-col items-center"},{default:p((()=>[x(t,{src:d(B)(H.value.member.headimg),size:"50",leftIcon:"none","default-url":d(B)("static/resource/images/default_headimg.png")},null,8,["src","default-url"]),x(s,{class:"flex items-center mt-[20rpx] text-[#fff] text-[26rpx] leading-[36rpx]"},{default:p((()=>[x(l,{class:"font-bold mr-[10rpx] max-w-[250rpx] truncate"},{default:p((()=>[f(c(H.value.member.nickname),1)])),_:1}),x(l,null,{default:p((()=>[f("请您帮忙付款~")])),_:1})])),_:1}),H.value.config.pay_leave_message?(n(),u(s,{key:0,class:"message bg-[#fe0708] relative max-w-[520rpx] px-[20rpx] py-[12rpx] rounded-[12rpx] border-solid border-[1rpx] border-color text-[24rpx] text-[#fff] leading-[30rpx] box-border text-center mt-[20rpx] mx-[114rpx]"},{default:p((()=>[f(c(H.value.config.pay_leave_message),1)])),_:1})):_("v-if",!0)])),_:1})])),_:1},8,["style"]),x(s,{class:"-mt-[154rpx] card-template sidebar-margin mb-[var(--top-m)]"},{default:p((()=>[x(s,{class:"text-[24rpx] text-center mb-[10rpx]"},{default:p((()=>[f(c(d(I)("payMoney")),1)])),_:1}),x(s,{class:"text-center mb-[50rpx]"},{default:p((()=>[x(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:p((()=>[f("￥")])),_:1}),x(l,{class:"text-[56rpx] font-bold price-font text-[#FF4142]"},{default:p((()=>[f(c(parseFloat(H.value.money).toFixed(2).split(".")[0]),1)])),_:1}),x(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:p((()=>[f("."+c(parseFloat(H.value.money).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),x(s,{class:"px-[20rpx] box-border"},{default:p((()=>[2==H.value.status?(n(),u(r,{key:0,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:p((()=>[f(c(d(I)("finish")),1)])),_:1})):-1==H.value.status?(n(),u(r,{key:1,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:p((()=>[f(c(d(I)("close")),1)])),_:1})):(n(),u(r,{key:2,class:"botton-color !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none",loading:N.value,onClick:Y},{default:p((()=>[f(c(H.value.config.pay_button_name?H.value.config.pay_button_name:d(I)("payGenerously")),1)])),_:1},8,["loading"]))])),_:1}),x(s,{class:"px-[10px] flex justify-between items-center text-[var(--text-color-light9)] mt-[20rpx]"},{default:p((()=>[x(s,{class:"flex items-baseline justify-between text-[var(--text-color-light9)]",onClick:a[0]||(a[0]=e=>d(C)({url:"/app/pages/index/index"}))},{default:p((()=>[x(l,{class:"text-[24rpx] mr-[6rpx]"},{default:p((()=>[f("返回首页")])),_:1})])),_:1}),H.value.config.pay_explain_switch?(n(),u(s,{key:0,class:"flex-shrink-0",onClick:W},{default:p((()=>[x(l,{class:"mr-[8rpx] text-[24rpx]"},{default:p((()=>[f(c(H.value.config.pay_explain_title),1)])),_:1}),x(l,{class:"nc-iconfont nc-icon-jichuxinxiV6xx text-[26rpx]"})])),_:1})):_("v-if",!0)])),_:1})])),_:1}),H.value.config.pay_info_switch?(n(),u(s,{key:0,class:"card-template sidebar-margin mb-[var(--top-m)]"},{default:p((()=>["[]"!==JSON.stringify(H.value.trade_info)&&H.value.trade_info.item_list.length?(n(),v(g,{key:0},[x(s,{class:"flex justify-between items-center mb-[30rpx]"},{default:p((()=>[x(s,{class:"text-[30rpx] text-[#333] font-500"},{default:p((()=>[f(c(d(I)("helpPayInfo")),1)])),_:1}),_(' <view class="flex-shrink-0" @click="handleMessage" v-if="friendsInfo.config.pay_explain_switch">\n                            <text class="mr-[8rpx] text-[24rpx]">{{ friendsInfo.config.pay_explain_title }}</text>\n                            <text class="nc-iconfont nc-icon-jichuxinxiV6xx text-[26rpx]"></text>\n                        </view> ')])),_:1}),x(s,{class:"border-0 border-solid border-b-[1rpx] border-[#f6f6f6] mb-[20rpx]"},{default:p((()=>[(n(!0),v(g,null,y(H.value.trade_info.item_list,((e,a)=>(n(),u(s,{class:P(["flex justify-between",{" mb-[34rpx]":a+1!=H.value.trade_info.length}])},{default:p((()=>[x(s,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden flex-shrink-0"},{default:p((()=>[x(o,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"170rpx",height:"170rpx",src:d(B)(e.item_image?e.item_image:""),model:"aspectFill"},{error:p((()=>[x(i,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:d(B)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),x(s,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:p((()=>[x(s,null,{default:p((()=>[x(s,{class:"text-[28rpx] using-hidden leading-[40rpx] text-[#333]"},{default:p((()=>[f(c(e.item_name),1)])),_:2},1024),e.item_sub_name?(n(),u(s,{key:0,class:"text-[24rpx] mt-[14rpx] text-[var(--text-color-light9)] using-hidden leading-[28rpx]"},{default:p((()=>[f(c(e.item_sub_name),1)])),_:2},1024)):_("v-if",!0)])),_:2},1024),x(s,{class:"flex justify-between items-baseline"},{default:p((()=>[x(s,{class:"price-font text-[#FF4142]"},{default:p((()=>[x(l,{class:"text-[24rpx]"},{default:p((()=>[f("￥")])),_:1}),x(l,{class:"text-[40rpx] font-500"},{default:p((()=>[f(c(parseFloat(e.item_price).toFixed(2).split(".")[0]),1)])),_:2},1024),x(l,{class:"text-[24rpx] font-500"},{default:p((()=>[f("."+c(parseFloat(e.item_price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),x(l,{class:"text-right text-[26rpx]"},{default:p((()=>[f("x"+c(e.item_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),256))])),_:1}),x(s,{class:"text-[26rpx] text-right"},{default:p((()=>[f(c(H.value.trade_info.item_total),1)])),_:1})],64)):(n(),u(s,{key:1,class:"text-[28rpx] leading-[40rpx] text-[#333]"},{default:p((()=>[f(c(H.value.body),1)])),_:1}))])),_:1})):_("v-if",!0)])),_:1})):_("v-if",!0),_(" 帮付说明 "),x(J,{ref_key:"messageRef",ref:U},null,512),_(" 支付 "),x(b,{ref_key:"payRef",ref:X},null,512),x(h,{loading:V.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-75f0d586"]]);export{V as default};

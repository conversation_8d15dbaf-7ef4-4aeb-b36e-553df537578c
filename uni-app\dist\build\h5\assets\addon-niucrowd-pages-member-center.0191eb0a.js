import{r as e,s as t,z as a,o as s,c,w as l,k as u,g as o,b as n,A as r,B as i,R as m,a3 as d,S as _,bE as f,b8 as p,y as g,i as v,j as h,I as j}from"./index-4b8dc7db.js";import{_ as x}from"./u-avatar.db5bcfa1.js";import{_ as k}from"./u-icon.33002907.js";import{_ as b}from"./u-image.18977c35.js";import{_ as w}from"./u-tag.aca70620.js";import{g as y}from"./member.90cbfaed.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-text.40d19739.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */const T=z({__name:"center",setup(z){const T=e({nickname:"",headimg:""}),C=e({status:-1,status_text:"未认证",is_verified:!1}),S=e({project_count:0,support_count:0,total_amount:0}),E=e([]);t((()=>{F()})),a((()=>{F()}));const F=async()=>{try{if(!uni.getStorageSync("token"))return;await I(),await $(),await q()}catch(e){console.error("加载用户数据失败:",e)}},I=async()=>{try{const e=await y();C.value=e.data}catch(e){console.error("加载认证状态失败:",e)}},$=async()=>{S.value={project_count:4,support_count:3,total_amount:127e3}},q=async()=>{E.value=[{project_id:5,project_name:"智能手表众筹项目",cover_image:"",current_amount:45e3,target_amount:1e5,status_name:"进行中"},{project_id:6,project_name:"智能家居控制系统",cover_image:"",current_amount:12e3,target_amount:5e4,status_name:"进行中"}]},A=async()=>{try{const e=uni.getStorageSync("wapToken"),t=uni.getStorageSync("wap_member_info");if(!e||!t)return void f({title:"提示",content:"请先登录后再发布项目",confirmText:"去登录",success:e=>{e.confirm&&p({url:"/pages/member/login"})}});await I();const a=C.value.status;if(-1===a){if(!C.value.can_publish)return void f({title:"身份认证提示",content:"发布项目需要先完成身份认证，是否立即前往认证？",confirmText:"去认证",cancelText:"取消",success:e=>{e.confirm&&Q()}});console.log("系统配置不需要认证，允许发布项目")}else{if(0===a)return void f({title:"认证审核中",content:"您的身份认证正在审核中，请耐心等待审核结果。审核通过后即可发布项目。",confirmText:"查看状态",cancelText:"知道了",success:e=>{e.confirm&&Q()}});if(2===a)return void f({title:"认证未通过",content:"您的身份认证未通过审核，请重新提交认证资料。",confirmText:"重新认证",cancelText:"取消",success:e=>{e.confirm&&Q()}});if(1!==a)return void f({title:"状态异常",content:`认证状态异常(${a})，请重新登录或联系客服`,confirmText:"确定"});if(!C.value.can_publish)return void f({title:"提示",content:"当前系统配置不允许发布项目，请联系管理员",confirmText:"确定"})}p({url:"/addon/niucrowd/pages/project/create"})}catch(e){console.error("检查认证状态失败:",e),g({title:"检查认证状态失败，请重试",icon:"none"})}},B=()=>{p({url:"/addon/niucrowd/pages/member/projects"})},P=()=>{p({url:"/addon/niucrowd/pages/member/support-list"})},Q=async()=>{try{const e=uni.getStorageSync("wapToken"),t=uni.getStorageSync("wap_member_info");if(!e||!t)return void f({title:"提示",content:"请先登录后再进行身份认证",confirmText:"去登录",success:e=>{e.confirm&&p({url:"/pages/member/login"})}});await I();switch(C.value.status){case-1:p({url:"/addon/niucrowd/pages/member/auth-router"});break;case 0:case 1:p({url:"/addon/niucrowd/pages/member/auth-status"});break;case 2:f({title:"认证未通过",content:"您的身份认证未通过审核，是否重新提交认证资料？",confirmText:"重新认证",cancelText:"查看详情",success:e=>{e.confirm?p({url:"/addon/niucrowd/pages/member/auth-router"}):p({url:"/addon/niucrowd/pages/member/auth-status"})}});break;default:p({url:"/addon/niucrowd/pages/member/auth-router"})}}catch(e){console.error("获取认证状态失败:",e),p({url:"/addon/niucrowd/pages/member/auth-router"})}},R=()=>{p({url:"/pages/member/profile"})};return(e,t)=>{const a=v(h("u-avatar"),x),f=u,g=v(h("u-icon"),k),y=j,z=v(h("u-image"),b),F=v(h("u-tag"),w);return s(),c(f,{class:"member-center"},{default:l((()=>[o(" 用户信息卡片 "),n(f,{class:"user-card"},{default:l((()=>[n(f,{class:"user-info"},{default:l((()=>[n(f,{class:"avatar-section"},{default:l((()=>[n(a,{src:T.value.headimg||"/static/images/default-avatar.png",size:"80",shape:"circle"},null,8,["src"])])),_:1}),n(f,{class:"info-section"},{default:l((()=>[n(f,{class:"user-name"},{default:l((()=>[r(i(T.value.nickname||"未设置昵称"),1)])),_:1}),n(f,{class:"auth-status"},{default:l((()=>[C.value.is_verified?(s(),c(f,{key:0,class:"verified"},{default:l((()=>[n(g,{name:"checkmark-circle-fill",color:"#52c41a",size:"16"}),n(y,null,{default:l((()=>[r("已认证")])),_:1})])),_:1})):(s(),c(f,{key:1,class:"unverified",onClick:Q},{default:l((()=>[n(g,{name:"info-circle",color:"#faad14",size:"16"}),n(y,null,{default:l((()=>[r(i(C.value.status_text||"未认证"),1)])),_:1})])),_:1}))])),_:1})])),_:1})])),_:1}),n(f,{class:"user-stats"},{default:l((()=>[n(f,{class:"stat-item"},{default:l((()=>[n(f,{class:"stat-value"},{default:l((()=>[r(i(S.value.project_count||0),1)])),_:1}),n(f,{class:"stat-label"},{default:l((()=>[r("发起项目")])),_:1})])),_:1}),n(f,{class:"stat-item"},{default:l((()=>[n(f,{class:"stat-value"},{default:l((()=>[r(i(S.value.support_count||0),1)])),_:1}),n(f,{class:"stat-label"},{default:l((()=>[r("支持项目")])),_:1})])),_:1}),n(f,{class:"stat-item"},{default:l((()=>[n(f,{class:"stat-value"},{default:l((()=>[r("¥"+i(S.value.total_amount||0),1)])),_:1}),n(f,{class:"stat-label"},{default:l((()=>[r("筹集金额")])),_:1})])),_:1})])),_:1})])),_:1}),o(" 快捷操作 "),n(f,{class:"quick-actions"},{default:l((()=>[n(f,{class:"action-item",onClick:A},{default:l((()=>[n(f,{class:"action-icon"},{default:l((()=>[n(g,{name:"plus-circle",color:"#007aff",size:"24"})])),_:1}),n(y,{class:"action-text"},{default:l((()=>[r("发起项目")])),_:1})])),_:1}),n(f,{class:"action-item",onClick:B},{default:l((()=>[n(f,{class:"action-icon"},{default:l((()=>[n(g,{name:"folder",color:"#52c41a",size:"24"})])),_:1}),n(y,{class:"action-text"},{default:l((()=>[r("我的项目")])),_:1})])),_:1}),n(f,{class:"action-item",onClick:P},{default:l((()=>[n(f,{class:"action-icon"},{default:l((()=>[n(g,{name:"heart",color:"#ff6b35",size:"24"})])),_:1}),n(y,{class:"action-text"},{default:l((()=>[r("我的支持")])),_:1})])),_:1}),n(f,{class:"action-item",onClick:Q},{default:l((()=>[n(f,{class:"action-icon"},{default:l((()=>[n(g,{name:"account",color:"#faad14",size:"24"})])),_:1}),n(y,{class:"action-text"},{default:l((()=>[r("身份认证")])),_:1})])),_:1})])),_:1}),o(" 功能菜单 "),n(f,{class:"menu-section"},{default:l((()=>[n(f,{class:"menu-title"},{default:l((()=>[r("我的众筹")])),_:1}),n(f,{class:"menu-list"},{default:l((()=>[n(f,{class:"menu-item",onClick:B},{default:l((()=>[n(f,{class:"menu-left"},{default:l((()=>[n(g,{name:"folder",color:"#007aff",size:"20"}),n(y,{class:"menu-text"},{default:l((()=>[r("我的项目")])),_:1})])),_:1}),n(f,{class:"menu-right"},{default:l((()=>[S.value.project_count?(s(),c(y,{key:0,class:"menu-count"},{default:l((()=>[r(i(S.value.project_count),1)])),_:1})):o("v-if",!0),n(g,{name:"arrow-right",color:"#c8c9cc",size:"16"})])),_:1})])),_:1}),n(f,{class:"menu-item",onClick:P},{default:l((()=>[n(f,{class:"menu-left"},{default:l((()=>[n(g,{name:"heart",color:"#ff6b35",size:"20"}),n(y,{class:"menu-text"},{default:l((()=>[r("我的支持")])),_:1})])),_:1}),n(f,{class:"menu-right"},{default:l((()=>[S.value.support_count?(s(),c(y,{key:0,class:"menu-count"},{default:l((()=>[r(i(S.value.support_count),1)])),_:1})):o("v-if",!0),n(g,{name:"arrow-right",color:"#c8c9cc",size:"16"})])),_:1})])),_:1}),n(f,{class:"menu-item",onClick:A},{default:l((()=>[n(f,{class:"menu-left"},{default:l((()=>[n(g,{name:"plus-circle",color:"#52c41a",size:"20"}),n(y,{class:"menu-text"},{default:l((()=>[r("发起项目")])),_:1})])),_:1}),n(f,{class:"menu-right"},{default:l((()=>[n(g,{name:"arrow-right",color:"#c8c9cc",size:"16"})])),_:1})])),_:1})])),_:1})])),_:1}),o(" 账户管理 "),n(f,{class:"menu-section"},{default:l((()=>[n(f,{class:"menu-title"},{default:l((()=>[r("账户管理")])),_:1}),n(f,{class:"menu-list"},{default:l((()=>[n(f,{class:"menu-item",onClick:Q},{default:l((()=>[n(f,{class:"menu-left"},{default:l((()=>[n(g,{name:"account",color:"#faad14",size:"20"}),n(y,{class:"menu-text"},{default:l((()=>[r("身份认证")])),_:1})])),_:1}),n(f,{class:"menu-right"},{default:l((()=>[n(y,{class:"auth-status-text"},{default:l((()=>[r(i(C.value.status_text||"未认证"),1)])),_:1}),n(g,{name:"arrow-right",color:"#c8c9cc",size:"16"})])),_:1})])),_:1}),n(f,{class:"menu-item",onClick:R},{default:l((()=>[n(f,{class:"menu-left"},{default:l((()=>[n(g,{name:"setting",color:"#909399",size:"20"}),n(y,{class:"menu-text"},{default:l((()=>[r("个人资料")])),_:1})])),_:1}),n(f,{class:"menu-right"},{default:l((()=>[n(g,{name:"arrow-right",color:"#c8c9cc",size:"16"})])),_:1})])),_:1})])),_:1})])),_:1}),o(" 最近项目 "),E.value.length>0?(s(),c(f,{key:0,class:"recent-section"},{default:l((()=>[n(f,{class:"section-header"},{default:l((()=>[n(y,{class:"section-title"},{default:l((()=>[r("最近项目")])),_:1}),n(y,{class:"view-all",onClick:B},{default:l((()=>[r("查看全部")])),_:1})])),_:1}),n(f,{class:"project-list"},{default:l((()=>[(s(!0),m(_,null,d(E.value,(e=>(s(),c(f,{key:e.project_id,class:"project-item",onClick:t=>{return a=e.project_id,void p({url:`/addon/niucrowd/pages/project/detail?id=${a}`});var a}},{default:l((()=>[n(f,{class:"project-image"},{default:l((()=>[n(z,{src:e.cover_image||"/static/images/default-project.png",width:"80rpx",height:"80rpx",mode:"aspectFill","border-radius":"8"},null,8,["src"])])),_:2},1024),n(f,{class:"project-info"},{default:l((()=>[n(f,{class:"project-name"},{default:l((()=>[r(i(e.project_name),1)])),_:2},1024),n(f,{class:"project-progress"},{default:l((()=>[n(y,{class:"amount"},{default:l((()=>[r("¥"+i(e.current_amount),1)])),_:2},1024),n(y,{class:"target"},{default:l((()=>[r("/¥"+i(e.target_amount),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(f,{class:"project-status"},{default:l((()=>[n(F,{text:e.status_name,size:"mini"},null,8,["text"])])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):o("v-if",!0)])),_:1})}}},[["__scopeId","data-v-72e3cee1"]]);export{T as default};

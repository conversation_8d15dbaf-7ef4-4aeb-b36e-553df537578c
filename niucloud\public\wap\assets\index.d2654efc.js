import{d as e,r as a,l as o,N as t,Q as r,a9 as n,o as i,c as l,w as s,g as m,f as d,v as f,e as u,b as p,n as c,y as _,k as g,aV as y,aW as v}from"./index-4b8dc7db.js";import{u as S}from"./useDiyForm.a361a03b.js";import{d as b}from"./index.106c0c40.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";const T=h(e({__name:"index",props:["form_id","relate_id","storage_name","form_border"],setup(e,{expose:h}){const T=e,F=S({form_id:T.form_id,needLogin:!1}),x=a(null),N=o((()=>F.requestData)),j=t({});r((()=>{F.getData((()=>{var e;if(j.status=F.data.status,j.status){j.title=F.data.title,j.global=F.data.global,j.global&&(j.global.topStatusBar.isShow=!1,j.global.bottomTabBarSwitch=!1);let a=[];"none"==T.form_border&&(j.global.borderControl=!1),F.data.value.forEach((e=>{"diy_form"==e.componentType&&"FormSubmit"!=e.componentName&&a.push(e)})),j.value=a,j.componentRefs=null,null==(e=x.value)||e.refresh(),k()}}))}));const k=()=>{n((()=>j.value),((e,a)=>{if(e){let a={validTime:y(5),components:[]};e.forEach((e=>{if("diy_form"==e.componentType&&"FormSubmit"!=e.componentName&&e.field.cache){let o=v(e.field);delete o.remark,delete o.detailComponent,delete o.default,a.components.push({id:e.id,componentName:e.componentName,componentType:e.componentType,componentTitle:e.componentTitle,isHidden:e.isHidden,field:o})}})),a.components.length&&uni.setStorageSync("diyFormStorage_"+T.form_id,a)}}),{deep:!0})};return F.onHide(),F.onUnload(),h({verify:()=>{if(!j.status)return!0;if(!j.value)return!0;let e=!0,a=x.value.getFormRef().componentRefs;for(let t=0;t<j.value.length;t++){let o=j.value[t];if(o.field.required||o.field.value){let t=`diy${o.componentName}Ref`,r=!1;if(a[t]){for(let e=0;e<a[t].length;e++){let o=a[t][e].verify();if(o&&!o.code){r=!0,_({title:o.message,icon:"none"});break}}if(r){e=!1;break}}}}if(!e)return!1;const o={form_id:T.form_id,value:uni.getStorageSync("diyFormStorage_"+T.form_id),relate_id:T.relate_id||0};return T.storage_name&&uni.setStorageSync(T.storage_name,o),e},getData:()=>({form_id:T.form_id,value:j.value,relate_id:T.relate_id||0}),clearStorage:(e=[])=>{uni.removeStorageSync("diyFormStorage_"+T.form_id),T.storage_name&&uni.removeStorageSync(T.storage_name),e&&e.forEach((e=>{uni.removeStorageSync(e)}))}}),(e,a)=>{const o=g;return i(),l(o,{style:c(e.themeColor())},{default:s((()=>[m(" 自定义组件渲染 "),d(p(o,{class:"diy-template-wrap"},{default:s((()=>[p(b,{ref_key:"diyGroupRef",ref:x,data:j},null,8,["data"])])),_:1},512),[[f,1==u(N).status&&!u(F).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-cfbaf382"]]);export{T as d};

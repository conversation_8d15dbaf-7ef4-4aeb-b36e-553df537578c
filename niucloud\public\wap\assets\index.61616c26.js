import{_ as e}from"./u-icon.33002907.js";import{a4 as t,a5 as a,a6 as l,a7 as r,a8 as s,i as o,j as i,o as n,c as d,w as c,b as u,T as m,C as p,n as f,g as x,A as h,B as v,k as _,I as b,d as g,r as y,N as k,R as w,S as D,a3 as $,e as S,E as A,bH as C,y as T,ap as V,H as j,bI as z,F as M,a as N,ao as O,l as E,f as H,v as U,an as I,b2 as B}from"./index-4b8dc7db.js";import{_ as F}from"./u-transition.5763ee65.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as R}from"./u-loading-icon.11ef83b8.js";import{_ as Z}from"./u-empty.a1ed4d4b.js";import{_ as q}from"./u-popup.e2790691.js";import{k as P}from"./order.b56aabaa.js";import{_ as Y,a as L}from"./u-form.a144799c.js";import{_ as W}from"./u-input.7a7ec88f.js";const G=Q({name:"u-alert",mixins:[a,l,{props:{title:{type:String,default:()=>t.alert.title},type:{type:String,default:()=>t.alert.type},description:{type:String,default:()=>t.alert.description},closable:{type:Boolean,default:()=>t.alert.closable},showIcon:{type:Boolean,default:()=>t.alert.showIcon},effect:{type:String,default:()=>t.alert.effect},center:{type:Boolean,default:()=>t.alert.center},fontSize:{type:[String,Number],default:()=>t.alert.fontSize}}}],data:()=>({show:!0}),computed:{iconColor(){return"light"===this.effect?this.type:"#fff"},iconName(){switch(this.type){case"success":return"checkmark-circle-fill";case"error":return"close-circle-fill";case"warning":default:return"error-circle-fill";case"info":return"info-circle-fill";case"primary":return"more-circle-fill"}}},emits:["click"],methods:{addUnit:r,addStyle:s,clickHandler(){this.$emit("click")},closeHandler(){this.show=!1}}},[["render",function(t,a,l,r,s,g){const y=o(i("u-icon"),e),k=_,w=b,D=o(i("u-transition"),F);return n(),d(D,{mode:"fade",show:s.show},{default:c((()=>[u(k,{class:m(["u-alert",[`u-alert--${t.type}--${t.effect}`]]),onClick:p(g.clickHandler,["stop"]),style:f([g.addStyle(t.customStyle)])},{default:c((()=>[t.showIcon?(n(),d(k,{key:0,class:"u-alert__icon"},{default:c((()=>[u(y,{name:g.iconName,size:"18",color:g.iconColor},null,8,["name","color"])])),_:1})):x("v-if",!0),u(k,{class:"u-alert__content",style:f([{paddingRight:t.closable?"20px":0}])},{default:c((()=>[t.title?(n(),d(w,{key:0,class:m(["u-alert__content__title",["dark"===t.effect?"u-alert__text--dark":`u-alert__text--${t.type}--light`]]),style:f([{fontSize:g.addUnit(t.fontSize),textAlign:t.center?"center":"left"}])},{default:c((()=>[h(v(t.title),1)])),_:1},8,["style","class"])):x("v-if",!0),t.description?(n(),d(w,{key:1,class:m(["u-alert__content__desc",["dark"===t.effect?"u-alert__text--dark":`u-alert__text--${t.type}--light`]]),style:f([{fontSize:g.addUnit(t.fontSize),textAlign:t.center?"center":"left"}])},{default:c((()=>[h(v(t.description),1)])),_:1},8,["style","class"])):x("v-if",!0)])),_:1},8,["style"]),t.closable?(n(),d(k,{key:1,class:"u-alert__close",onClick:p(g.closeHandler,["stop"])},{default:c((()=>[u(y,{name:"close",color:g.iconColor,size:"15"},null,8,["color"])])),_:1},8,["onClick"])):x("v-if",!0)])),_:1},8,["class","onClick","style"])])),_:1},8,["show"])}],["__scopeId","data-v-0e484b05"]]),J=g({__name:"select-store",emits:["confirm"],setup(e,{expose:t,emit:a}){const l=y(!1),r=y(!1),s=y(!0),p=y([]),f=y(null),g=k({lat:0,lng:0}),z=e=>{f.value?f.value=f.value.store_id!=e.store_id?e:null:f.value=e},M=()=>{a("confirm",f.value),l.value=!1},N=e=>{if((e=parseFloat(e))<1e3)return`${e}m`;return`${(e/1e3).toFixed(2)}km`};return t({open:()=>{l.value=!0},getData:e=>{if(!r.value){if(r.value=!0,uni.getStorageSync("location_address")){let e=uni.getStorageSync("location_address");g.lat=e.latitude,g.lng=e.longitude}else C({type:"gcj02",success:e=>{g.lat=e.latitude,g.lng=e.longitude},fail:e=>{if(e.errno)if(104==e.errno){T({title:"用户未授权隐私权限，获取位置失败",icon:"none"})}else if(112==e.errno){T({title:"隐私协议中未声明，获取位置失败",icon:"none"})}if(e.errMsg)if(-1!=e.errMsg.indexOf("getLocation:fail")||-1!=e.errMsg.indexOf("deny")||-1!=e.errMsg.indexOf("denied")){T({title:"用户未授权获取位置权限，将无法提供距离最近的门店",icon:"none"})}else T({title:e.errMsg,icon:"none"})}});setTimeout((()=>{P({latlng:g}).then((({data:t})=>{p.value=t,t.length&&z(t[0]),"function"==typeof e&&e(t),s.value=!1})).catch((()=>{s.value=!1}))}),1500)}}}),(e,t)=>{const a=_,r=b,g=o(i("u-loading-icon"),R),y=o(i("u-empty"),Z),k=V,C=j,T=o(i("u-popup"),q);return n(),d(T,{show:l.value,onClose:t[0]||(t[0]=e=>l.value=!1),mode:"bottom",round:10},{default:c((()=>[u(a,{class:"popup-common"},{default:c((()=>[u(a,{class:"title"},{default:c((()=>[h("请选择自提点")])),_:1}),u(k,{"scroll-y":"true",class:"h-[50vh]"},{default:c((()=>[u(a,{class:"p-[var(--popup-sidebar-m)] pt-0 text-sm"},{default:c((()=>[(n(!0),w(D,null,$(p.value,(e=>(n(),d(a,{class:m(["mt-[var(--top-m)] border-1 border-[#eee] border-solid rounded-[var(--rounded-mid)] px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] mb-[var(--top-m)]",{"!border-primary bg-[var(--primary-color-light2)]":f.value&&f.value.store_id==e.store_id}]),onClick:t=>z(e)},{default:c((()=>[u(a,{class:"flex"},{default:c((()=>[u(a,{class:"flex-1 w-0"},{default:c((()=>[u(r,{class:"text-[30rpx] text-[#333]"},{default:c((()=>[h(v(e.store_name),1)])),_:2},1024),u(r,{class:"text-[26rpx] ml-[12rpx] text-[var(--text-color-light6)]"},{default:c((()=>[h(v(e.store_mobile),1)])),_:2},1024)])),_:2},1024),e.distance?(n(),d(a,{key:0},{default:c((()=>[u(r,{class:"text-red text-[26rpx] font-normal"},{default:c((()=>[h(v(N(e.distance)),1)])),_:2},1024)])),_:2},1024)):x("v-if",!0)])),_:2},1024),u(a,{class:"mt-[20rpx] text-[26rpx] leading-[1.4] flex"},{default:c((()=>[u(r,{class:"flex-shrink-0"},{default:c((()=>[h("门店地址：")])),_:1}),u(r,null,{default:c((()=>[h(v(e.full_address),1)])),_:2},1024)])),_:2},1024),u(a,{class:"mt-[16rpx] text-[26rpx]"},{default:c((()=>[h("营业时间："+v(e.trade_time),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),256))])),_:1}),s.value?(n(),d(a,{key:0,class:"h-[50vh] flex items-center flex-col justify-center"},{default:c((()=>[u(g,{vertical:!0})])),_:1})):x("v-if",!0),s.value||p.value.length?x("v-if",!0):(n(),d(a,{key:1,class:"h-[95%] flex items-center flex-col justify-center"},{default:c((()=>[u(y,{text:"没有可选择的自提点",width:"214",icon:S(A)("static/resource/images/empty.png")},null,8,["icon"])])),_:1}))])),_:1}),u(a,{class:"btn-wrap"},{default:c((()=>[u(C,{class:"primary-btn-bg btn",onClick:M},{default:c((()=>[h("确认")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}}),K=g({__name:"address-list",props:["back"],emits:["confirm"],setup(e,{expose:t,emit:a}){const l=e,r=y(!1),s=y(!1),f=y(""),g=y({}),k=y([]);z({}).then((({data:e})=>{k.value=e,s.value=!1}));const A=(e,t=2)=>{uni.setStorage({key:"selectAddressCallback",data:{back:l.back,delivery:g.value.delivery},success(){N({url:"/app/pages/member/address_edit",param:{id:e.id,source:"shop_order_payment",isSelectMap:t}})}})},C=()=>{uni.setStorage({key:"selectAddressCallback",data:{back:l.back,delivery:g.value.delivery},success(){N({url:"/app/pages/member/address_edit",param:{source:"shop_order_payment"}})}})};return t({open:e=>{r.value=!0,g.value=e,f.value=e.id}}),(e,t)=>{const l=_,s=b,y=V,T=j,z=o(i("u-popup"),q);return n(),d(z,{show:r.value,onClose:t[1]||(t[1]=e=>r.value=!1),mode:"bottom",round:10},{default:c((()=>[u(l,{onTouchmove:t[0]||(t[0]=p((()=>{}),["prevent","stop"])),class:"popup-common"},{default:c((()=>[u(l,{class:"title"},{default:c((()=>[h(v(S(M)("selectAddress")),1)])),_:1}),u(y,{"scroll-y":"true",class:"h-[50vh]"},{default:c((()=>[(n(!0),w(D,null,$(k.value,((e,t)=>(n(),d(l,{key:e.id,class:m(["flex items-center mx-[var(--popup-sidebar-m)] border-1 border-[#eee] border-solid rounded-[var(--rounded-mid)] px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)]",{"mb-[var(--top-m)]":k.value.length-1!=t,"text-[var(--primary-color)] !border-[var(--primary-color)]":e.id==f.value}]),onClick:e=>(e=>{let t=k.value[e];if("local_delivery"!=g.value.delivery||t.lat||t.lng){let t={};t.address_id=k.value[e].id,t.delivery=g.value.delivery,a("confirm",t)}else A(t,1);r.value=!1})(t)},{default:c((()=>[u(s,{class:"nc-iconfont nc-icon-dingweiV6xx-1 text-[36rpx]"}),u(l,{class:"flex flex-col mx-[20rpx] w-[480rpx]"},{default:c((()=>[u(l,{class:"flex items-center truncate leading-[1.5]"},{default:c((()=>[u(s,{class:"mr-[8rpx] text-[30rpx] truncate max-w-[300rpx]"},{default:c((()=>[h(v(e.name),1)])),_:2},1024),u(s,{class:"text-[30rpx]"},{default:c((()=>[h(v(e.mobile),1)])),_:2},1024)])),_:2},1024),u(l,{class:"truncate text-[26rpx] leading-[1.5] mt-[12rpx]"},{default:c((()=>[h(v(e.full_address),1)])),_:2},1024)])),_:2},1024),u(s,{class:"nc-iconfont nc-icon-xiugaiV6xx text-[32rpx] ml-auto",onClick:t=>A(e)},null,8,["onClick"])])),_:2},1032,["class","onClick"])))),128)),!k.value||k.value&&!k.value.length?(n(),d(l,{key:0,class:"text-[var(--text-color-light6)] text-[28rpx] text-center"},{default:c((()=>[h(v(S(M)("emptyAddress")),1)])),_:1})):x("v-if",!0)])),_:1}),u(l,{class:"btn-wrap"},{default:c((()=>[u(T,{class:"primary-btn-bg btn",onClick:C},{default:c((()=>[h(v(S(M)("addAddress")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}});const X=g({__name:"invoice",emits:["confirm"],setup(e,{expose:t,emit:a}){const l=y(!1),r=y({is_invoice:2,invoice_content:[]}),s=y(!1);y(!1);const p=y({header_type:1,header_name:"",type:"",name:"",tax_number:"",telephone:"",address:"",bank_name:"",bank_card_number:""}),f=E((()=>1==r.value.is_invoice));O.get("shop/config/invoice").then((({data:e})=>{r.value=e,e.invoice_content.length&&(p.value.name=e.invoice_content[0])})).catch();const b=e=>{I((()=>{p.value.tax_number=e.replace(/[^0-9a-zA-Z]/g,"")}))},g=y(null),k=E((()=>({header_name:{type:"string",required:s.value,message:"请输入发票抬头",trigger:["blur","change"]},tax_number:[{type:"string",required:s.value&&2==p.value.header_type,message:"请输入纳税人识别号",trigger:["blur","change"]},{validator(e,t,a){/^[0-9a-zA-Z]+$/.test(t)||2!=p.header_type?a():a(new Error("请输入正确的纳税人识别号"))}}]}))),A=()=>{g.value.validate().then((()=>{const e=s.value?p.value:{};a("confirm",e),l.value=!1}))},C=()=>{l.value=!1};return t({open:()=>{l.value=!0},invoiceOpen:f}),(e,t)=>{const a=_,f=o(i("u-form-item"),Y),y=o(i("u-input"),W),T=o(i("u-form"),L),z=V,M=j,N=o(i("u-popup"),q);return n(),d(N,{show:l.value,onClose:C,mode:"bottom"},{default:c((()=>[x(' class="bg-[#fff] rounded-[10rpx] popup-common" @touchmove.prevent.stop '),u(a,{class:"popup-common"},{default:c((()=>[u(a,{class:"title"},{default:c((()=>[h("请填写发票信息")])),_:1}),u(z,{"scroll-y":!0,class:"h-[50vh]"},{default:c((()=>[u(a,{class:"px-[var(--popup-sidebar-m)] pb-[60rpx] pt-0 text-sm"},{default:c((()=>[u(T,{labelPosition:"left",model:p.value,labelWidth:"200rpx",labelStyle:{"font-size":"28rpx"},errorType:"toast",rules:S(k),ref_key:"formRef",ref:g},{default:c((()=>[u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"需要发票",leftIconStyle:"text-[28rpx]"},{default:c((()=>[u(a,{class:"flex"},{default:c((()=>[u(a,{class:m(["h-[60rpx] box-border rounded px-[30rpx] leading-[56rpx] mr-[20rpx] border-[2rpx] border-[var(--temp-bg)] bg-[var(--temp-bg)] border-solid text-[24rpx]",{"!bg-[var(--primary-color-light)] !text-[var(--primary-color)] !border-primary":!s.value}]),onClick:t[0]||(t[0]=e=>s.value=!1)},{default:c((()=>[h("不需要")])),_:1},8,["class"]),u(a,{class:m(["h-[60rpx] box-border rounded px-[30rpx] leading-[56rpx] border-[2rpx] border-[var(--temp-bg)] border-solid text-[24rpx] bg-[var(--temp-bg)]",{"!bg-[var(--primary-color-light)] !text-[var(--primary-color)] !border-primary":s.value}]),onClick:t[1]||(t[1]=e=>s.value=!0)},{default:c((()=>[h("需要")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1}),H(u(a,null,{default:c((()=>[u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"抬头类型"},{default:c((()=>[u(a,{class:"flex"},{default:c((()=>[u(a,{class:m(["h-[60rpx] box-border rounded px-[30rpx] mr-[20rpx] leading-[56rpx] border-[2rpx] border-[var(--temp-bg)] border-solid text-[24rpx] bg-[var(--temp-bg)]",{"!bg-[var(--primary-color-light)] !text-[var(--primary-color)] !border-primary":1==p.value.header_type}]),onClick:t[2]||(t[2]=e=>p.value.header_type=1)},{default:c((()=>[h("个人 ")])),_:1},8,["class"]),u(a,{class:m(["h-[60rpx] box-border rounded px-[30rpx] leading-[56rpx] border-[2rpx] border-[var(--temp-bg)] border-solid text-[24rpx] bg-[var(--temp-bg)]",{"!bg-[var(--primary-color-light)] !text-[var(--primary-color)] !border-primary":2==p.value.header_type}]),onClick:t[3]||(t[3]=e=>p.value.header_type=2)},{default:c((()=>[h("企业 ")])),_:1},8,["class"])])),_:1})])),_:1})])),_:1}),u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"发票内容",prop:"header_name"},{default:c((()=>[u(a,{class:"flex flex-wrap"},{default:c((()=>[(n(!0),w(D,null,$(r.value.invoice_content,((e,t)=>(n(),d(a,{class:m(["box-border rounded px-[20rpx] py-[12rpx] leading-[1.4] border-[2rpx] border-[var(--temp-bg)] border-solid text-[24rpx] bg-[var(--temp-bg)] my-[10rpx]",{"!bg-[var(--primary-color-light)] !text-[var(--primary-color)] !border-primary":p.value.name==e,"mr-[20rpx]":r.value.invoice_content.length-1!=t}]),onClick:t=>p.value.name=e},{default:c((()=>[h(v(e),1)])),_:2},1032,["class","onClick"])))),256))])),_:1})])),_:1})])),_:1}),u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"发票抬头",prop:"header_name"},{default:c((()=>[u(y,{fontSize:"28rpx",modelValue:p.value.header_name,"onUpdate:modelValue":t[4]||(t[4]=e=>p.value.header_name=e),modelModifiers:{trim:!0},border:"none",maxlength:"50","placeholder-class":"!text-[var(--text-color-light9)] text-[28rpx]",clearable:"",placeholder:"请输入发票抬头"},null,8,["modelValue"])])),_:1})])),_:1}),2==p.value.header_type?(n(),d(a,{key:0},{default:c((()=>[u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"纳税人识别号",prop:"tax_number"},{default:c((()=>[u(y,{fontSize:"28rpx",modelValue:p.value.tax_number,"onUpdate:modelValue":t[5]||(t[5]=e=>p.value.tax_number=e),modelModifiers:{trim:!0},border:"none",clearable:"",placeholder:"请输入纳税人识别号",maxlength:"20","placeholder-class":"!text-[var(--text-color-light9)] text-[28rpx]",onChange:b},null,8,["modelValue"])])),_:1})])),_:1}),x(' <view class="py-[20rpx] h-[48rpx] flex items-center">\r\n                                  <text class="text-[30rpx]">更多选填内容</text>\r\n                                  <text class="text-xs text-gray-subtitle ml-[10rpx]">注册地址、电话、开户银行及账号</text>\r\n                                  <view class="text-xs text-right flex-1" @click="optionalShow = !optionalShow">\r\n                                    <text>{{ optionalShow ? \'收起\' : \'展开\' }}</text>\r\n                                    <text class="text-[30rpx] nc-iconfont text-gray-subtitle ml-[5rpx]" :class="optionalShow ? \'nc-icon-shangV6xx-1\' : \'nc-icon-xiaV6xx\'"></text>\r\n                                  </view>\r\n                                </view> '),u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"注册地址"},{default:c((()=>[u(y,{fontSize:"28rpx",modelValue:p.value.address,"onUpdate:modelValue":t[6]||(t[6]=e=>p.value.address=e),"placeholder-class":"!text-[var(--text-color-light9)] text-[28rpx]",border:"none",clearable:"",maxlength:"120",placeholder:"(选填)请输入企业注册地址"},null,8,["modelValue"])])),_:1})])),_:1}),u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"注册电话"},{default:c((()=>[u(y,{fontSize:"28rpx",modelValue:p.value.telephone,"onUpdate:modelValue":t[7]||(t[7]=e=>p.value.telephone=e),"placeholder-class":"!text-[var(--text-color-light9)] text-[28rpx]",border:"none",clearable:"",maxlength:"12",placeholder:"(选填)请输入企业注册电话"},null,8,["modelValue"])])),_:1})])),_:1}),u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"开户银行"},{default:c((()=>[u(y,{fontSize:"28rpx",modelValue:p.value.bank_name,"onUpdate:modelValue":t[8]||(t[8]=e=>p.value.bank_name=e),"placeholder-class":"!text-[var(--text-color-light9)] text-[28rpx]",border:"none",clearable:"",maxlength:"50",placeholder:"(选填)请输入企业开户银行"},null,8,["modelValue"])])),_:1})])),_:1}),u(a,{class:"mt-[10rpx]"},{default:c((()=>[u(f,{label:"银行账号"},{default:c((()=>[u(y,{fontSize:"28rpx",modelValue:p.value.bank_card_number,"onUpdate:modelValue":t[9]||(t[9]=e=>p.value.bank_card_number=e),"placeholder-class":"!text-[var(--text-color-light9)] text-[28rpx]",border:"none",clearable:"",maxlength:"25",placeholder:"(选填)请输入企业开户银行账号"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})):x("v-if",!0)])),_:1},512),[[U,s.value]])])),_:1},8,["model","rules"])])),_:1})])),_:1}),u(a,{class:"btn-wrap"},{default:c((()=>[u(M,{class:"primary-btn-bg btn",onClick:A},{default:c((()=>[h("确认")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}});function ee(e){return e<10?`0${e}`:e}function te(){const e=new Date,t=e.getFullYear(),a=e.getMonth()+1,l=e.getDate(),r=t+"-"+ee(a)+"-"+ee(l),s=ee(a)+"-"+ee(l),o=ee(a)+"月"+ee(l)+"日",i=e.getHours(),n=e.getMinutes(),d=e.getSeconds(),c=Math.floor(Date.now()/1e3);return{y:t,md:o,mdTime:s,date:r,time:ee(i)+":"+ee(n)+":"+ee(d),seconTime:c}}function ae(e,t){const a=new Date(e),l=a.getFullYear(),r=a.getMonth()+1,s=a.getDate(),o=a.getDay(),i=a.getHours(),n=a.getMinutes();return{allDate:`${l}/${ee(r)}/${ee(s)}`,date:`${ee(l)}-${ee(r)}-${ee(s)}`,md:`${ee(r)}月${ee(s)}日`,mdTime:`${ee(r)}-${ee(s)}`,day:`周${["日","一","二","三","四","五","六"][o]}`,dayNum:`${[0,1,2,3,4,5,6][o]}`,hour:ee(i)+":"+ee(n)+(t?"":":00")}}function le(e){let t=Math.floor(e/3600),a=Math.floor(e/60)-60*t;return t=t<10?"0"+t:t,a=a<10?"0"+a:a,t+":"+a}function re(e){const t=e.split(":");return 60*Number(t[0])*60+60*Number(t[1])}const se=Q({name:"times",model:{prop:"showPop",event:"change"},props:{rules:{type:Object,default:()=>({})},isQuantum:{type:Boolean,default:!1},isOpen:{type:Boolean,default:!0}},data:()=>({orderDateTime:"",orderDateStamp:"",dateArr:[],timeArr:[],nowDate:"",dateActive:0,timeActive:0,selectDate:"",show:!1}),created(){this.nowDate=te().md,this.initOnload()},watch:{rules:{handler:function(e){Object.keys(e).length&&this.initOnload()},deep:!0,immediate:!0}},methods:{img:A,initOnload(){if(!this.rules.trade_time_json||!this.rules.time_week)return;const e="subscribe"==this.rules.type&&"advance_day"in this.rules&&"most_day"in this.rules&&null!==this.rules.advance_day&&null!==this.rules.most_day;let t=[];t=this.isOpen||"subscribe"!==this.rules.type?e?function(e,t){const a=[];let l=864e5;const r=(new Date).getTime();for(let s=e;s<=t;s++)a.push({date:ae(r+l*s).date,mdTime:ae(r+l*s).md,md:0===s?"今天":1===s?"明天":2===s?"后天":ae(r+l*s).md,timeStamp:r+l*s,week:ae(r+l*s).day,dayNum:ae(r+l*s).dayNum,disable:!1});return a}(this.rules.advance_day,this.rules.most_day):function(){const e=[],t=(new Date).getTime();let a=864e5,l={0:"今天",1:"明天",2:"后天"};for(let r=0;r<7;r++)e.push({date:ae(t+a*r).date,mdTime:ae(t+a*r).md,md:l[r]?l[r]:ae(t+a*r).md,timeStamp:t+a*r,week:ae(t+a*r).day,dayNum:ae(t+a*r).dayNum,disable:!1});return e}():function(){const e=[],t=(new Date).getTime();return e.push({date:ae(t).date,mdTime:ae(t).md,md:"今天",timeStamp:t,week:ae(t).day,dayNum:ae(t).dayNum,disable:!1}),e}(),this.timeArr=function(e,t=.5,a=!0){const l=[],r=3600*t;return e.forEach((e=>{let t=e.start_time,s=e.end_time;for(let o=t;o<s;o+=r){let e=o+r>s?s:o+r;a?l.push({begin:le(o),end:le(e),disable:!1}):l.push({time:le(o),disable:!1})}})),l}(this.rules.trade_time_json,Number(this.rules.time_interval)/60,this.isQuantum);const a=Math.floor(re(te().time)),l=new Date;let r=null,s=null;e&&(r=new Date(l),r.setDate(l.getDate()+this.rules.advance_day),r.setHours(0,0,0,0),s=new Date(l),s.setDate(l.getDate()+this.rules.most_day),s.setHours(23,59,59,999)),this.dateArr=[],t.forEach((t=>{const l=new Date(t.date),o=!e||l>=r&&l<=s,i=t.mdTime===this.nowDate,n=this.rules.time_week.includes(t.dayNum);if(n&&o||e&&i&&n||!this.isOpen&&i){const l=i&&!this.isOpen;t.children=l?[]:B(this.timeArr);const r=a,s=this.rules.trade_time_json.some((e=>r>=e.start_time&&r<e.end_time));i&&e&&s&&t.children.unshift({begin:"立即",end:"配送",value:"immediate",disable:!1}),t.children=t.children.filter((e=>{if("immediate"===e.value)return!0;if(!e.end||!e.begin)return!1;const l=re(e.begin),r=re(e.end),s=a;return!(t.children.some((e=>"immediate"===e.value))&&t.mdTime===this.nowDate&&s>=l&&s<r)&&!(t.mdTime===this.nowDate&&r<s)})),t.children.length>0&&this.dateArr.push(t)}}));let o=!0;this.dateArr.forEach(((e,t)=>{e.children.forEach(((a,l)=>{!a.disable&&o&&(o=!1,this.timeActive=l,this.dateActive=t,this.selectDate=e.mdTime,this.orderDateStamp=e.date,"immediate"===a.value?(this.orderDate=`${this.selectDate}(立即配送)`,this.orderDateTime=`${e.date} 立即配送`):(this.orderDate=`${this.selectDate}(${a.begin}~${a.end})`,this.orderDateTime=`${e.date} ${a.begin}~${a.end}`),this.$emit("change",this.orderDateTime),this.$emit("getDate",this.orderDate),this.$emit("getStamp",this.orderDateStamp))}))}))},selectDateEvent(e,t){this.dateActive=e,this.selectDate=t.mdTime,this.orderDateStamp=t.date,this.timeActive=0;const a=t.children[this.timeActive];"immediate"===a.value?(this.orderDate=`${this.selectDate}(立即配送)`,this.orderDateTime=`${t.date} 立即配送`):(this.orderDate=`${this.selectDate}(${a.begin}~${a.end})`,this.orderDateTime=`${t.date} ${a.begin}~${a.end}`),this.$emit("change",this.orderDateTime),this.$emit("getDate",this.orderDate),this.$emit("getStamp",this.orderDateStamp)},selectTimeEvent(e,t){this.handleSelectQuantum(e,t),this.show=!1,this.$emit("change",this.orderDateTime),this.$emit("getDate",this.orderDate)},handleSelectQuantum(e,t){this.timeActive=e,"immediate"===t.value?(this.orderDate=`${this.selectDate}(立即配送)`,this.orderDateTime=`${this.orderDateStamp} 立即配送`):(this.orderDate=`${this.selectDate}(${t.begin}~${t.end})`,this.orderDateTime=`${this.orderDateStamp} ${t.begin}~${t.end}`)}}},[["render",function(e,t,a,l,r,s){const p=b,f=_,g=V,y=o(i("u-empty"),Z),k=o(i("u-popup"),q);return n(),d(k,{show:r.show,mode:"bottom",round:10,closeable:"",onClose:t[0]||(t[0]=e=>r.show=!1)},{default:c((()=>[u(f,{class:"box h-[728rpx]"},{default:c((()=>[u(f,{class:"title px-[30rpx] box-border text-center text-[28rpx] font-bold h-[90rpx] leading-[90rpx] border-0 border-solid border-[#f7f4f4] border-b-[2rpx]"},{default:c((()=>[u(p,null,{default:c((()=>[h(v("subscribe"==a.rules.type?"请选择配送时间":"请选择自提时间"),1)])),_:1})])),_:1}),r.dateArr.length?(n(),d(f,{key:0,class:"body flex h-[calc(100%-90rpx)] box-border"},{default:c((()=>[x(" 左侧日期选择 "),u(g,{"scroll-y":!0,class:"left bg-[#f8f8f8] shrink-0 w-[230rpx]","scroll-with-animation":"","scroll-into-view":"id"+(r.dateActive?r.dateActive-1:0)},{default:c((()=>[(n(!0),w(D,null,$(r.dateArr,((e,t)=>(n(),d(f,{key:t,class:m(["date-box flex px-[30rpx] py-[16rpx] box-border text-[24rpx] items-center",{"bg-[#fff]":t==r.dateActive}]),id:"id"+t,onClick:a=>s.selectDateEvent(t,e)},{default:c((()=>[u(f,{class:"text-[24rpx] leading-[58rpx]"},{default:c((()=>[h(v(e.md),1)])),_:2},1024),u(f,{class:"text-[24rpx] leading-[58rpx]"},{default:c((()=>[h("("+v(e.week)+")",1)])),_:2},1024)])),_:2},1032,["id","onClick","class"])))),128))])),_:1},8,["scroll-into-view"]),x(" 右侧时间选择 "),u(g,{"scroll-y":!0,class:"right w-[calc(100%-230rpx)] px-[30rpx] box-border","scroll-with-animation":"","scroll-into-view":"id"+(r.timeActive?r.timeActive-1:0)},{default:c((()=>[x(" 时间选项 "),(n(!0),w(D,null,$(r.dateArr[r.dateActive].children,((e,t)=>(n(),w(D,{key:t},[e.disable?x("v-if",!0):(n(),d(f,{key:0,class:m(["h-[72rpx] flex border-0 border-solid border-b-[2rpx] border-[#eee] justify-between items-center",{"text-[var(--primary-color)]":t==r.timeActive}]),onClick:a=>s.selectTimeEvent(t,e),id:"id"+t},{default:c((()=>[u(f,{class:m(["text-[24rpx]",{"text-[var(--primary-color)]":t==r.timeActive}])},{default:c((()=>["immediate"==e.value?(n(),d(p,{key:0},{default:c((()=>[h(v(e.begin)+v(e.end),1)])),_:2},1024)):(n(),d(p,{key:1},{default:c((()=>[h(v(e.begin)+"-"+v(e.end),1)])),_:2},1024))])),_:2},1032,["class"]),t==r.timeActive?(n(),d(p,{key:0,class:"nc-iconfont nc-icon-duihaoV6mm mr-[30rpx] text-[38rpx]"})):x("v-if",!0)])),_:2},1032,["onClick","class","id"]))],64)))),128))])),_:1},8,["scroll-into-view"])])),_:1})):(n(),d(f,{key:1,class:"h-[80%] flex items-center flex-col justify-center"},{default:c((()=>[u(y,{text:"subscribe"==a.rules.type?"没有可选择的配送时间":"没有可选择的自提时间",width:"214",icon:s.img("static/resource/images/empty.png")},null,8,["text","icon"])])),_:1}))])),_:1})])),_:1},8,["show"])}]]);export{J as _,X as a,K as b,G as c,se as n};

import{d as e,r as a,z as t,x as r,o as l,c as s,w as x,b as p,e as c,A as n,f as o,v as i,T as u,g as f,n as d,t as m,af as v,a as _,y as g,I as h,k as y,G as b,ag as w,i as j,j as C,E as k}from"./index-4b8dc7db.js";import{_ as V}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{g as I,a as z}from"./verify.2951bd10.js";import{_ as F}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.11ef83b8.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */const S=F(e({__name:"index",setup(e){const F=a("manualInput");F.value="manualInput";const S=a(!1),E=a(""),Z=a(!0),A=a(!1);t((()=>{r()&&G()}));const G=()=>{I().then((e=>{e.data?(A.value=!0,Z.value=!1):(A.value=!1,Z.value=!1)}))},H=()=>{m()&&(v.init(),v.scanQRCode((e=>{if(e.resultStr){let a=e.resultStr;_({url:"/app/pages/verify/verify",param:{code:a}})}})))};let Q=!1;const R=()=>/[\S]+/.test(E.value)?!Q&&(Q=!0,void z({code:E.value}).then((e=>{Q=!1,_({url:"/app/pages/verify/verify",param:{code:E.value}})})).catch((()=>{Q=!1}))):(g({title:"请输入核销码",icon:"none"}),!1),T=()=>{S.value=!S.value},U=e=>{"sweepCode"!=e||m()?F.value=e:g({title:"H5端不支持扫码核销",icon:"none"})};return(e,a)=>{const t=h,r=y,m=b,v=w,g=j(C("loading-page"),V);return l(),s(r,{style:d(e.themeColor())},{default:x((()=>[!Z.value&&A.value?(l(),s(r,{key:0,class:"w-[100vw] min-h-[100vh] bg-[#f8f8f8]"},{default:x((()=>[p(r,{class:"w-full bg-[#fff] verify-box h-[760rpx]"},{default:x((()=>[p(r,{class:"text-[var(--primary-color)] fixed top-[40rpx] right-[30rpx] flex items-center",onClick:a[0]||(a[0]=e=>c(_)({url:"/app/pages/verify/record"}))},{default:x((()=>[p(t,{class:"nc-iconfont nc-icon-lishijiluV6xx !text-[28rpx] -mb-[2rpx]"}),p(t,{class:"text-[26rpx] ml-[8rpx]"},{default:x((()=>[n("核销记录")])),_:1})])),_:1}),o(p(r,{class:"flex flex-col items-center justify-center"},{default:x((()=>[p(r,{class:"sweep-code flex items-center justify-center",onClick:H},{default:x((()=>[p(m,{class:"w-[354rpx] h-[354rpx]",src:c(k)("static/resource/images/verify/saoma.png")},null,8,["src"])])),_:1}),p(r,{class:"mt-[40rpx] text-[30rpx]"},{default:x((()=>[n("点击扫描二维码")])),_:1}),p(r,{class:"mt-[20rpx] text-[var(--text-color-light9)] text-[26rpx] font-400 pb-[142rpx]"},{default:x((()=>[n("扫描二维码进行核销")])),_:1})])),_:1},512),[[i,"sweepCode"==F.value]]),o(p(r,null,{default:x((()=>[p(r,{class:"flex pt-[126rpx] items-center justify-center"},{default:x((()=>[p(r,{class:"flex justify-center items-center flex-col pr-[30rpx] min-w-[130rpx]"},{default:x((()=>[p(m,{class:"w-[100rpx] h-[100rpx]",src:c(k)("static/resource/images/verify/shuruhexiaoma.png")},null,8,["src"]),p(r,{class:"text-[26rpx] h-[36rpx] leading-[36rpx] mt-[14rpx]"},{default:x((()=>[n("验证核销码")])),_:1})])),_:1}),p(m,{class:"w-[74rpx] h-[12rpx] mb-[50rpx]",src:c(k)("static/resource/images/verify/youjiantou.png")},null,8,["src"]),p(r,{class:"flex justify-center items-center flex-col pl-[30rpx] min-w-[130rpx]"},{default:x((()=>[p(m,{class:"w-[100rpx] h-[100rpx]",src:c(k)("static/resource/images/verify/hexiao1.png")},null,8,["src"]),p(r,{class:"text-[26rpx] h-[36rpx] leading-[36rpx] mt-[14rpx]"},{default:x((()=>[n("核销")])),_:1})])),_:1})])),_:1}),p(r,{class:"mt-[50rpx]"},{default:x((()=>[p(r,{class:"h-[90rpx] border-[2rpx] border-solid border-[#eee] rounded-[16rpx] box-border p-[20rpx] mx-[60rpx] flex items-center"},{default:x((()=>[p(t,{class:"nc-iconfont nc-icon-saotiaoxingmaV6xx text-[44rpx] text-[#EF000C]"}),p(v,{type:"text",placeholder:"请输入核销码",class:"h-[90rpx] border-none text-start ml-[30rpx] text-[28rpx] flex-1","placeholder-class":"_placeholder",modelValue:E.value,"onUpdate:modelValue":a[1]||(a[1]=e=>E.value=e),focus:S.value,ref:"input"},null,8,["modelValue","focus"])])),_:1}),p(r,{class:"h-[80rpx] primary-btn-bg min-w-[630rpx] text-[#fff] flex-center !text-[26rpx] save-btn rounded-[100rpx] h-[80rpx] font-500 mx-[60rpx] mt-[146rpx] relative z-1",onClick:R},{default:x((()=>[n("核销")])),_:1})])),_:1})])),_:1},512),[[i,"manualInput"==F.value]])])),_:1}),p(r,{class:"w-[630rpx] h-[100rpx] bg-[#fff] mx-[auto] mt-[220rpx] rounded-[90rpx] flex relative action-type-wrap"},{default:x((()=>[p(r,{class:u(["relative w-[51%] pr-[50rpx] box-border rounded-[50rpx] z-0 flex flex-col items-center justify-center",{xuanZhong1:"sweepCode"==F.value}]),onClick:a[2]||(a[2]=e=>U("sweepCode"))},{default:x((()=>[p(t,{class:"nc-iconfont nc-icon-saoyisaoV6xx !text-[40rpx]"}),p(r,{class:"text-[24rpx] leading-[1] mt-[10rpx]"},{default:x((()=>[n("扫码核销")])),_:1})])),_:1},8,["class"]),p(r,{class:"flex flex-col items-center flex-col w-[120rpx] h-[120rpx] bg-[#FF7354] rounded-[50%] absolute top-[-10rpx] left-[255rpx] heXiao text-white z-10 shrink-0"},{default:x((()=>[p(r,{class:"nc-iconfont nc-icon-saotiaoxingmaV6xx ns-gradient-otherpages-member-balance-balance-rechange !text-[44rpx] mt-[19rpx]"}),p(r,{class:"text-[24rpx] mt-[8rpx] leading-[34rpx] h-[34rpx]"},{default:x((()=>[n("核销台")])),_:1})])),_:1}),p(r,{class:u(["relative w-[51%] pl-[50rpx] box-border rounded-[50rpx] z-0 flex flex-col items-center justify-center",{xuanZhong:"manualInput"==F.value}]),onClick:a[3]||(a[3]=e=>U("manualInput"))},{default:x((()=>[p(t,{class:"iconfont iconVector-77 !text-[40rpx]"}),p(r,{class:"ml-[20rpx] text-[24rpx] leading-[1] mt-[10rpx]",onClick:T},{default:x((()=>[n("手动输入")])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})):f("v-if",!0),Z.value||A.value?f("v-if",!0):(l(),s(r,{key:1,class:"w-[100vw] min-h-[100vh] bg-[#f8f8f8] overflow-hidden"},{default:x((()=>[p(r,{class:"empty-page"},{default:x((()=>[p(m,{class:"img",src:c(k)("static/resource/images/system/empty.png"),mode:"aspectFit"},null,8,["src"]),p(r,{class:"desc"},{default:x((()=>[n("非核销员无此权限")])),_:1})])),_:1})])),_:1})),p(g,{loading:Z.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-745e2fdc"]]);export{S as default};

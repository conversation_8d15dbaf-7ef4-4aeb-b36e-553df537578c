<template>
    <view class="create-page">
        <!-- 认证检查加载状态 -->
        <view v-if="authChecking" class="auth-checking">
            <view class="loading-container">
                <view class="loading-icon">🔄</view>
                <text class="loading-text">正在验证身份认证状态...</text>
            </view>
        </view>

        <!-- 项目创建表单 -->
        <view v-else class="form-container">
            <view class="form-section">
                <view class="section-title">基本信息</view>
                
                <view class="form-item">
                    <view class="label">项目名称 <text class="required">*</text></view>
                    <input 
                        v-model="formData.project_name" 
                        placeholder="请输入项目名称" 
                        class="input"
                        maxlength="50"
                    />
                </view>
                
                <view class="form-item">
                    <view class="label">项目分类 <text class="required">*</text></view>
                    <view class="select-box" @click="openCategoryPicker">
                        <text class="select-text" :class="{ placeholder: !selectedCategory }">
                            {{ selectedCategory ? selectedCategory.category_name : '请选择项目分类' }}
                        </text>
                        <text class="iconfont iconxiangyoujiantou"></text>
                        <text v-if="categoryLoading" class="loading-text">加载中...</text>
                    </view>
                </view>
                
                <view class="form-item">
                    <view class="label">项目封面 <text class="required">*</text></view>
                    <view class="upload-container">
                        <image-upload
                            v-model="formData.cover_image"
                            :max-count="1"
                            :size-limit="5"
                            :show-tips="true"
                        />
                        <view class="upload-tip">请上传清晰的项目封面图片，支持JPG、PNG格式，大小不超过5MB</view>
                    </view>
                </view>
                
                <view class="form-item">
                    <view class="label">项目描述 <text class="required">*</text></view>
                    <textarea 
                        v-model="formData.description" 
                        placeholder="请详细描述您的项目..." 
                        class="textarea"
                        maxlength="500"
                    />
                </view>
            </view>
            
            <view class="form-section">
                <view class="section-title">筹款信息</view>
                
                <view class="form-item">
                    <view class="label">目标金额 <text class="required">*</text></view>
                    <view class="input-group">
                        <text class="currency">¥</text>
                        <input
                            v-model="formData.target_amount"
                            placeholder="0.00"
                            class="input amount-input"
                            type="digit"
                            @input="onAmountInput"
                        />
                    </view>
                    <view class="form-tip">最高支持99,999,999元</view>
                </view>
                
                <view class="form-item">
                    <view class="label">筹款天数 <text class="required">*</text></view>
                    <view class="input-group">
                        <input 
                            v-model="formData.duration_days" 
                            placeholder="30" 
                            class="input"
                            type="number"
                        />
                        <text class="unit">天</text>
                    </view>
                </view>
            </view>
            
            <view class="form-section">
                <view class="section-title">回报设置</view>
                <view class="section-desc">为支持者设置不同金额的回报</view>
                
                <view class="reward-list">
                    <view v-for="(reward, index) in formData.rewards" :key="index" class="reward-item">
                        <view class="reward-header">
                            <text class="reward-title">回报 {{ index + 1 }}</text>
                            <text class="remove-btn" @click="removeReward(index)" v-if="formData.rewards.length > 1">删除</text>
                        </view>
                        
                        <view class="reward-form">
                            <view class="form-row">
                                <view class="form-col">
                                    <view class="label">支持金额</view>
                                    <view class="input-group">
                                        <text class="currency">¥</text>
                                        <input 
                                            v-model="reward.amount" 
                                            placeholder="0.00" 
                                            class="input"
                                            type="digit"
                                        />
                                    </view>
                                </view>
                                <view class="form-col">
                                    <view class="label">限制数量</view>
                                    <input 
                                        v-model="reward.limit_count" 
                                        placeholder="不限制" 
                                        class="input"
                                        type="number"
                                    />
                                </view>
                            </view>
                            
                            <view class="form-item">
                                <view class="label">回报内容</view>
                                <textarea 
                                    v-model="reward.content" 
                                    placeholder="请描述支持者将获得的回报..." 
                                    class="textarea small"
                                    maxlength="200"
                                />
                            </view>
                        </view>
                    </view>
                </view>
                
                <view class="add-reward-btn" @click="addReward">
                    <text class="iconfont icontianjia"></text>
                    <text>添加回报</text>
                </view>
            </view>
        </view>

        <!-- 协议确认 -->
        <view class="agreement-section">
            <view class="agreement-checkbox" @click="toggleAgreement">
                <view class="checkbox" :class="{ checked: formData.agreeAgreement }">
                    <text v-if="formData.agreeAgreement" class="check-icon">✓</text>
                </view>
                <text class="agreement-text">
                    我已阅读并同意
                    <text class="agreement-link" @click.stop="showAgreementModalFunc">《项目发布协议》</text>
                </text>
            </view>
        </view>

        <view class="bottom-bar">
            <button class="submit-btn" @click="submitProject" :disabled="submitting || !formData.agreeAgreement">
                {{ submitting ? '提交中...' : '提交项目' }}
            </button>
        </view>
        
        <!-- 分类选择弹窗 -->
        <view v-if="showCategoryPicker" class="category-popup-mask" @click="closeCategoryPicker">
            <view class="category-popup" @click.stop>
                <view class="popup-header">
                    <text class="cancel-btn" @click="closeCategoryPicker">取消</text>
                    <text class="title">选择项目分类</text>
                    <text class="confirm-btn" @click="confirmCategorySelection">确定</text>
                </view>
                <view class="category-list">
                    <view
                        v-for="(category, index) in categoryDisplayList"
                        :key="index"
                        class="category-item"
                        :class="{ active: selectedCategoryIndex === index }"
                        @click="selectCategoryItem(index)"
                    >
                        <text class="category-name">{{ category.category_name }}</text>
                        <text v-if="selectedCategoryIndex === index" class="check-icon">✓</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 协议弹窗 -->
        <view v-if="showAgreementModal" class="agreement-modal-mask" @click="showAgreementModal = false">
            <view class="agreement-modal" @click.stop>
                <view class="modal-header">
                    <text class="modal-title">{{ agreementData.title || '项目发布协议' }}</text>
                    <text class="close-btn" @click="showAgreementModal = false">×</text>
                </view>
                <view class="modal-content">
                    <view class="agreement-content" v-html="agreementData.content || '正在加载协议内容...'"></view>
                </view>
                <view class="modal-footer">
                    <button class="cancel-btn" @click="showAgreementModal = false">取消</button>
                    <button class="agree-btn" @click="agreeAndClose">同意协议</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getCategoryOptions } from '../../api/category'
import { createProject } from '../../api/project'
import { getAuthStatus } from '../../api/member'
import { checkLoginStatus, requireLogin } from '../../utils/auth'
import ImageUpload from '../../components/image-upload/image-upload.vue'

const formData = reactive({
    project_name: '',
    category_id: '',
    cover_image: '',
    description: '',
    target_amount: '',
    duration_days: 30,
    agreeAgreement: false, // 协议同意状态
    rewards: [
        {
            amount: '',
            limit_count: '',
            content: ''
        }
    ]
})

const categoryList = ref([])
const selectedCategory = ref(null)
const selectedCategoryIndex = ref(0) // 添加索引跟踪
const showCategoryPicker = ref(false)
const submitting = ref(false)
const authChecking = ref(true)
const categoryLoading = ref(false) // 添加分类加载状态

// 协议相关状态
const showAgreementModal = ref(false)
const agreementData = ref({
    title: '',
    content: ''
})

// 计算属性：包含占位项的分类显示列表
const categoryDisplayList = computed(() => {
    const placeholder = { category_id: '', category_name: '请选择项目分类' }
    return [placeholder, ...categoryList.value]
})

// 页面加载时检查身份认证状态
onLoad(async () => {
    await checkAuthStatus()
})

onMounted(() => {
    loadCategoryList()
})

// 检查身份认证状态
const checkAuthStatus = async () => {
    try {
        authChecking.value = true

        // 检查登录状态
        if (!checkLoginStatus()) {
            uni.showModal({
                title: '登录提示',
                content: '发布项目需要先登录，是否立即前往登录？',
                confirmText: '去登录',
                cancelText: '返回',
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '/pages/member/login'
                        })
                    } else {
                        uni.navigateBack()
                    }
                }
            })
            return
        }

        // 获取认证状态
        const res = await getAuthStatus()
        console.log('认证状态检查结果:', res)

        if (res.code === 1) {
            const authInfo = res.data
            const status = authInfo.status

            if (status === -1) {
                // 未提交认证，检查是否允许发布
                if (authInfo.can_publish) {
                    // 系统配置不需要认证，允许直接发布
                    console.log('系统配置不需要认证，允许发布项目')
                } else {
                    // 系统配置需要认证，提示用户先认证
                    uni.showModal({
                        title: '身份认证提示',
                        content: '发布项目需要先完成身份认证，是否立即前往认证？',
                        confirmText: '去认证',
                        cancelText: '返回',
                        success: (res) => {
                            if (res.confirm) {
                                uni.redirectTo({
                                    url: '/addon/niucrowd/pages/member/auth-submit'
                                })
                            } else {
                                uni.navigateBack()
                            }
                        }
                    })
                    return
                }
            } else if (status === 0) {
                // 认证审核中
                uni.showModal({
                    title: '认证审核中',
                    content: '您的身份认证正在审核中，请耐心等待审核结果。审核通过后即可发布项目。',
                    confirmText: '查看状态',
                    cancelText: '返回',
                    success: (res) => {
                        if (res.confirm) {
                            uni.redirectTo({
                                url: '/addon/niucrowd/pages/member/auth-status'
                            })
                        } else {
                            uni.navigateBack()
                        }
                    }
                })
                return
            } else if (status === 2) {
                // 认证被拒绝
                uni.showModal({
                    title: '认证未通过',
                    content: '您的身份认证未通过审核，请重新提交认证资料后再发布项目。',
                    confirmText: '重新认证',
                    cancelText: '返回',
                    success: (res) => {
                        if (res.confirm) {
                            uni.redirectTo({
                                url: '/addon/niucrowd/pages/member/auth-submit'
                            })
                        } else {
                            uni.navigateBack()
                        }
                    }
                })
                return
            } else if (status === 1) {
                // 认证通过，检查发布权限
                if (!authInfo.can_publish) {
                    uni.showModal({
                        title: '发布权限不足',
                        content: '当前系统配置不允许发布项目，请联系管理员',
                        confirmText: '确定',
                        success: () => {
                            uni.navigateBack()
                        }
                    })
                    return
                }
                // 认证通过且有发布权限，允许继续
                console.log('身份认证检查通过，允许发布项目')
            } else {
                // 未知状态
                uni.showModal({
                    title: '状态异常',
                    content: `认证状态异常(${status})，请重新登录或联系客服`,
                    confirmText: '确定',
                    success: () => {
                        uni.navigateBack()
                    }
                })
                return
            }
        } else {
            // API调用失败
            uni.showModal({
                title: '检查失败',
                content: '无法获取认证状态，请检查网络连接后重试',
                confirmText: '重试',
                cancelText: '返回',
                success: (res) => {
                    if (res.confirm) {
                        checkAuthStatus()
                    } else {
                        uni.navigateBack()
                    }
                }
            })
            return
        }

    } catch (error) {
        console.error('检查认证状态失败:', error)
        uni.showModal({
            title: '检查失败',
            content: '检查认证状态时发生错误，请重试',
            confirmText: '重试',
            cancelText: '返回',
            success: (res) => {
                if (res.confirm) {
                    checkAuthStatus()
                } else {
                    uni.navigateBack()
                }
            }
        })
        return
    } finally {
        authChecking.value = false
    }
}

// 加载分类列表
const loadCategoryList = async () => {
    try {
        categoryLoading.value = true
        console.log('开始加载分类列表...')

        const res = await getCategoryOptions()
        console.log('分类API响应:', res)

        if (res && res.data && res.code === 1) {
            // 处理API响应数据
            const list = Array.isArray(res.data) ? res.data : (res.data.data || [])

            if (list.length > 0) {
                categoryList.value = list.map(item => ({
                    category_id: item.category_id,
                    category_name: item.category_name
                }))
                console.log('成功加载分类:', categoryList.value)
            } else {
                console.warn('API返回空分类列表，使用默认分类')
                useDefaultCategories()
            }
        } else {
            console.warn('API响应格式错误，使用默认分类')
            useDefaultCategories()
        }
    } catch (error) {
        console.error('加载分类失败:', error)
        useDefaultCategories()
        uni.showToast({
            title: '加载分类失败，使用默认分类',
            icon: 'none'
        })
    } finally {
        categoryLoading.value = false
        console.log('分类加载完成，当前分类数量:', categoryList.value.length)
    }
}

// 使用默认分类
const useDefaultCategories = () => {
    categoryList.value = [
        { category_id: 1, category_name: '科技产品' },
        { category_id: 2, category_name: '创意设计' },
        { category_id: 3, category_name: '生活用品' },
        { category_id: 4, category_name: '文化艺术' },
        { category_id: 5, category_name: '公益慈善' }
    ]
    console.log('使用默认分类:', categoryList.value)
}

// 处理金额输入
const onAmountInput = (e) => {
    let value = e.detail.value
    // 移除非数字和小数点的字符
    value = value.replace(/[^\d.]/g, '')

    // 限制最多8位整数
    const parts = value.split('.')
    if (parts[0].length > 8) {
        parts[0] = parts[0].substring(0, 8)
    }

    // 限制最多2位小数
    if (parts.length > 1) {
        parts[1] = parts[1].substring(0, 2)
        value = parts.join('.')
    }

    // 检查是否超过最大值
    const numValue = parseFloat(value)
    if (numValue > 99999999) {
        value = '99999999'
        uni.showToast({
            title: '目标金额不能超过99,999,999元',
            icon: 'none'
        })
    }

    formData.target_amount = value
}



// 打开分类选择器
const openCategoryPicker = () => {
    if (categoryLoading.value) {
        uni.showToast({
            title: '分类加载中，请稍候...',
            icon: 'none'
        })
        return
    }

    if (categoryList.value.length === 0) {
        uni.showToast({
            title: '暂无可选分类',
            icon: 'none'
        })
        return
    }

    showCategoryPicker.value = true
}

// 关闭分类选择器
const closeCategoryPicker = () => {
    showCategoryPicker.value = false
}

// 选择分类项
const selectCategoryItem = (index) => {
    try {
        console.log('选择分类项索引:', index, '显示列表长度:', categoryDisplayList.value.length)

        // 检查索引有效性
        if (index < 0 || index >= categoryDisplayList.value.length) {
            console.error('分类选择错误：索引超出范围', { index, listLength: categoryDisplayList.value.length })
            return
        }

        selectedCategoryIndex.value = index
        console.log('临时选择分类索引:', index)

    } catch (error) {
        console.error('选择分类项失败:', error)
    }
}

// 确认分类选择
const confirmCategorySelection = () => {
    try {
        const index = selectedCategoryIndex.value
        const selectedItem = categoryDisplayList.value[index]

        console.log('确认分类选择:', { index, selectedItem })

        // 检查是否选择了占位项
        if (index === 0 || !selectedItem.category_id) {
            console.log('用户选择了占位项，清空选择')
            selectedCategory.value = null
            formData.category_id = ''
            selectedCategoryIndex.value = 0
        } else {
            // 选择了有效分类
            const actualCategory = categoryList.value[index - 1] // 减1因为有占位项
            if (actualCategory) {
                selectedCategory.value = actualCategory
                formData.category_id = actualCategory.category_id
                console.log('成功选择分类:', actualCategory)

                uni.showToast({
                    title: `已选择：${actualCategory.category_name}`,
                    icon: 'success'
                })
            } else {
                console.error('分类选择错误：无法找到对应的分类数据')
                uni.showToast({
                    title: '分类选择失败：数据错误',
                    icon: 'none'
                })
                return
            }
        }

        closeCategoryPicker()

    } catch (error) {
        console.error('确认分类选择失败:', error)
        uni.showToast({
            title: '分类选择失败',
            icon: 'none'
        })
    }
}

// 协议相关函数
// 切换协议同意状态
const toggleAgreement = () => {
    formData.agreeAgreement = !formData.agreeAgreement
    console.log('协议同意状态:', formData.agreeAgreement)
}

// 显示协议弹窗
const showAgreementModalFunc = async () => {
    try {
        showAgreementModal.value = true
        // 这里可以添加加载协议内容的API调用
        // const res = await getAgreementContent('project_publish')
        // agreementData.value = res.data

        // 临时使用默认协议内容
        agreementData.value = {
            title: '项目发布协议',
            content: `
                <div style="padding: 20px; line-height: 1.6;">
                    <h3>项目发布协议</h3>
                    <p>欢迎使用本平台发布共享创业项目。在发布项目前，请仔细阅读以下协议条款：</p>

                    <h4>1. 项目真实性</h4>
                    <p>您承诺所发布的项目信息真实、准确、完整，不存在虚假宣传或误导性信息。</p>

                    <h4>2. 知识产权</h4>
                    <p>您确认拥有项目相关的知识产权，或已获得相应授权，不侵犯他人合法权益。</p>

                    <h4>3. 资金使用</h4>
                    <p>筹集的资金将专门用于项目开发和实施，您承诺合理使用并按承诺提供回报。</p>

                    <h4>4. 平台责任</h4>
                    <p>平台仅提供信息发布服务，不对项目成功与否承担责任。</p>

                    <h4>5. 违约责任</h4>
                    <p>如违反本协议，平台有权暂停或终止服务，并保留追究法律责任的权利。</p>

                    <p>点击"同意协议"即表示您已阅读并同意遵守以上条款。</p>
                </div>
            `
        }
    } catch (error) {
        console.error('加载协议内容失败:', error)
        uni.showToast({
            title: '加载协议失败',
            icon: 'none'
        })
    }
}

// 同意协议并关闭弹窗
const agreeAndClose = () => {
    formData.agreeAgreement = true
    showAgreementModal.value = false
    uni.showToast({
        title: '已同意协议',
        icon: 'success'
    })
}

// 添加回报
const addReward = () => {
    formData.rewards.push({
        amount: '',
        limit_count: '',
        content: ''
    })
}

// 删除回报
const removeReward = (index) => {
    formData.rewards.splice(index, 1)
}

// 提交项目
const submitProject = async () => {
    // 表单验证
    if (!formData.project_name) {
        uni.showToast({ title: '请输入项目名称', icon: 'none' })
        return
    }
    
    if (!formData.category_id) {
        uni.showToast({ title: '请选择项目分类', icon: 'none' })
        return
    }

    if (!formData.agreeAgreement) {
        uni.showToast({ title: '请先同意项目发布协议', icon: 'none' })
        return
    }
    
    if (!formData.cover_image) {
        uni.showToast({ title: '请上传项目封面', icon: 'none' })
        return
    }
    
    if (!formData.description) {
        uni.showToast({ title: '请输入项目描述', icon: 'none' })
        return
    }
    
    if (!formData.target_amount || parseFloat(formData.target_amount) <= 0) {
        uni.showToast({ title: '请输入有效的目标金额', icon: 'none' })
        return
    }

    // 检查目标金额是否超过限制
    if (parseFloat(formData.target_amount) > 99999999) {
        uni.showToast({ title: '目标金额不能超过99,999,999元', icon: 'none' })
        return
    }
    
    if (!formData.duration_days || parseInt(formData.duration_days) <= 0) {
        uni.showToast({ title: '请输入有效的筹款天数', icon: 'none' })
        return
    }
    
    // 验证回报设置
    for (let i = 0; i < formData.rewards.length; i++) {
        const reward = formData.rewards[i]
        if (!reward.amount || !reward.content) {
            uni.showToast({ title: `请完善回报${i + 1}的信息`, icon: 'none' })
            return
        }
    }
    
    submitting.value = true

    try {
        // 准备提交数据
        const submitData = {
            project_name: formData.project_name,
            category_id: formData.category_id,
            project_desc: formData.description, // 项目简介
            project_content: formData.description, // 项目详情（暂时使用相同内容）
            cover_image: formData.cover_image,
            target_amount: parseFloat(formData.target_amount),
            duration_days: parseInt(formData.duration_days),
            agreeAgreement: formData.agreeAgreement ? 'yes' : 'no', // 协议同意状态
            rewards: formData.rewards.map(reward => ({
                reward_name: `回报档位 ¥${reward.amount}`,
                reward_desc: reward.content,
                content: reward.content, // 兼容字段
                amount: parseFloat(reward.amount),
                stock: reward.limit_count ? parseInt(reward.limit_count) : 0,
                delivery_time: '项目成功后30天内发货'
            }))
        }

        const res = await createProject(submitData)

        // 显示成功弹窗并让用户选择下一步操作
        uni.showModal({
            title: '项目提交成功',
            content: '您的项目已提交，正在等待审核。请选择下一步操作：',
            confirmText: '查看我的项目',
            cancelText: '返回首页',
            success: (modalRes) => {
                if (modalRes.confirm) {
                    // 跳转到我的项目页面
                    uni.navigateTo({
                        url: '/addon/niucrowd/pages/member/projects'
                    })
                } else {
                    // 返回首页
                    uni.switchTab({
                        url: '/pages/index'
                    })
                }
            }
        })

    } catch (error) {
        console.error('提交项目失败:', error)
        uni.showToast({
            title: error.message || '提交失败，请重试',
            icon: 'none'
        })
    } finally {
        submitting.value = false
    }
}
</script>

<style lang="scss" scoped>
.create-page {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding-bottom: 120rpx;
}

.auth-checking {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
}

.loading-container {
    text-align: center;
    background: white;
    border-radius: 16rpx;
    padding: 60rpx 40rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.loading-icon {
    font-size: 60rpx;
    margin-bottom: 20rpx;
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 28rpx;
    color: #666;
}

.form-container {
    padding: 20rpx;
}

.form-section {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.section-desc {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 20rpx;
}

.form-item {
    margin-bottom: 30rpx;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 12rpx;

    .required {
        color: #ff4757;
    }
}

.form-tip {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
}

.input {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 8rpx;
    font-size: 28rpx;
    background-color: #fff;
}

.textarea {
    width: 100%;
    min-height: 120rpx;
    padding: 20rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 8rpx;
    font-size: 28rpx;
    background-color: #fff;
    
    &.small {
        min-height: 80rpx;
    }
}

.input-group {
    display: flex;
    align-items: center;
    border: 2rpx solid #e9ecef;
    border-radius: 8rpx;
    background-color: #fff;
    
    .currency, .unit {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #666;
    }
    
    .amount-input {
        border: none;
        flex: 1;
    }
}

.select-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 20rpx;
    border: 2rpx solid #e9ecef;
    border-radius: 8rpx;
    background-color: #fff;

    .select-text {
        font-size: 28rpx;
        color: #333;
        flex: 1;

        &.placeholder {
            color: #999;
        }
    }

    .loading-text {
        font-size: 24rpx;
        color: #007aff;
        margin-left: 10rpx;
    }
}

.upload-container {
    .upload-tip {
        font-size: 24rpx;
        color: #999;
        margin-top: 12rpx;
        line-height: 1.4;
    }
}

.reward-list {
    margin-bottom: 20rpx;
}

.reward-item {
    border: 2rpx solid #e9ecef;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.reward-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .reward-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
    }
    
    .remove-btn {
        font-size: 26rpx;
        color: #ff4757;
    }
}

.form-row {
    display: flex;
    gap: 20rpx;
    margin-bottom: 20rpx;
}

.form-col {
    flex: 1;
}

.add-reward-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    border: 2rpx dashed #007aff;
    border-radius: 8rpx;
    color: #007aff;
    font-size: 28rpx;

    .iconfont {
        margin-right: 8rpx;
    }
}

// 协议确认样式
.agreement-section {
    padding: 20rpx;
    background-color: #fff;
    margin-bottom: 20rpx;
}

.agreement-checkbox {
    display: flex;
    align-items: center;
    padding: 20rpx;

    .checkbox {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #ddd;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16rpx;

        &.checked {
            background-color: #007aff;
            border-color: #007aff;

            .check-icon {
                color: #fff;
                font-size: 20rpx;
                font-weight: bold;
            }
        }
    }

    .agreement-text {
        font-size: 26rpx;
        color: #666;
        flex: 1;

        .agreement-link {
            color: #007aff;
            text-decoration: underline;
        }
    }
}

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background-color: #fff;
    border-top: 1rpx solid #e9ecef;
}

.submit-btn {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
    color: #fff;
    border: none;
    border-radius: 40rpx;
    font-size: 32rpx;
    font-weight: bold;
    
    &:disabled {
        background: #ccc;
    }
}

// 分类选择弹窗样式
.category-popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: flex-end;
}

.category-popup {
    width: 100%;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    max-height: 80vh;
    overflow: hidden;
}

.popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #e9ecef;

    .cancel-btn, .confirm-btn {
        font-size: 28rpx;
        color: #007aff;
    }

    .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }
}

.category-list {
    max-height: 60vh;
    overflow-y: auto;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
        border-bottom: none;
    }

    &.active {
        background-color: #f0f9ff;

        .category-name {
            color: #007aff;
        }
    }

    .category-name {
        font-size: 28rpx;
        color: #333;
    }

    .check-icon {
        font-size: 32rpx;
        color: #007aff;
        font-weight: bold;
    }
}

// 协议弹窗样式
.agreement-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
}

.agreement-modal {
    width: 100%;
    max-width: 600rpx;
    max-height: 80vh;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #e9ecef;

    .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }

    .close-btn {
        font-size: 40rpx;
        color: #999;
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.modal-content {
    flex: 1;
    overflow-y: auto;
    max-height: 60vh;

    .agreement-content {
        padding: 30rpx;
        font-size: 26rpx;
        line-height: 1.6;
        color: #333;
    }
}

.modal-footer {
    display: flex;
    padding: 30rpx;
    gap: 20rpx;
    border-top: 1rpx solid #e9ecef;

    .cancel-btn, .agree-btn {
        flex: 1;
        height: 80rpx;
        border: none;
        border-radius: 8rpx;
        font-size: 28rpx;
    }

    .cancel-btn {
        background-color: #f5f5f5;
        color: #666;
    }

    .agree-btn {
        background-color: #007aff;
        color: #fff;
    }
}
</style>

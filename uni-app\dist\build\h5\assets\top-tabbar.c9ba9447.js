import{q as t,s as e,t as l,af as a,b3 as o,d as s,a1 as n,az as i,l as r,W as c,r as d,Q as u,e as p,o as y,c as f,w as m,b as g,n as x,T as _,g as k,A as v,B as h,C as S,a as b,X as C,an as w,aq as B,k as I,G as A,I as T,E as V,ar as z}from"./index-dd56d0cc.js";import{m as j}from"./manifest.ed582bbb.js";import{_ as q}from"./_plugin-vue_export-helper.1b428a4d.js";function F(s){const n=t();let i=s;1!=n.mapConfig.is_open&&(i=!1);const r=()=>{if(!i)return!1;uni.getStorageSync("manually_select_location_from_map")||l()&&n.mapConfig.is_open&&!uni.getStorageSync("location_address")&&a.init((()=>{a.getLocation((t=>{let e=t.latitude+","+t.longitude;c(e)}))}))},c=(t="")=>{if(!i)return!1;let e={latlng:""};e.latlng=t||n.diyAddressInfo.latitude+","+n.diyAddressInfo.longitude,o(e).then((t=>{if(t.data&&Object.keys(t.data).length){let l={},a=e.latlng.split(",");l.latitude=a[0],l.longitude=a[1],l.full_address=null!=t.data.province?t.data.province:"",l.full_address+=null!=t.data.city?t.data.city:"",l.full_address+=null!=t.data.district?t.data.district:"",l.full_address+=null!=t.data.community?t.data.community:"",l.province_id=t.data.province_id,l.province=t.data.province,l.city_id=t.data.city_id,l.city=t.data.city,l.district_id=t.data.district_id,l.district=t.data.district,l.community=t.data.community,l.formatted_addresses=t.data.formatted_addresses,n.setAddressInfo(l)}else n.setAddressInfo();setTimeout((()=>{uni.removeStorageSync("manually_select_location_from_map")}),500)}))},d=()=>{let t=uni.getStorageSync("location_address");if(t){var e=new Date;n.mapConfig.valid_time>0?t.is_expired=e.getTime()/1e3>t.valid_time:t.is_expired=!1}else t={is_expired:!1};return t};return{init:r,onLoad:(t="")=>{e((e=>{e&&e.latng&&c(e.latng),uni.removeStorageSync("manually_select_location_from_map"),"function"==typeof t&&t(e)}))},refresh:()=>{if(!i)return!1;!uni.getStorageSync("manually_select_location_from_map")&&uni.getStorageSync("location_address")&&(d()&&!d().is_expired?n.setAddressInfo(uni.getStorageSync("location_address")):uni.removeStorageSync("location_address")),!uni.getStorageSync("manually_select_location_from_map")&&d()&&d().is_expired&&r()},reposition:()=>{if(!i)return!1;n.diyAddressInfo&&n.diyAddressInfo.latitude,n.diyAddressInfo&&n.diyAddressInfo.longitude,uni.setStorageSync("manually_select_location_from_map",!0);let t=location.origin+location.pathname;window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(t)+"&key="+j.h5.sdkConfigs.maps.qqmap.key+"&referer=myapp"}}}const R=q(s({__name:"top-tabbar",props:{data:{type:Object,default:{}},titleColor:{type:String,default:"#606266"},customBack:{type:Function,default:null},scrollBool:{type:[String,Number],default:-1},isBack:{type:Boolean,default:!0},isFill:{type:Boolean,default:!0}},setup(e,{expose:l}){const a=e;n().platform;const o=t(),s=i(),j=r((()=>{let t=!0;return t=!X.value&&a.isFill,t})),q=r((()=>a.data)),R=r((()=>{if(a.data&&a.data.topStatusBar)return a.data.topStatusBar})),$=r((()=>{let t="";return a.isBack?(t+="padding-left: 30rpx;","style-1"==R.value.style&&(t+="padding-right:80rpx;")):("style-1"==R.value.style&&(t+="padding-right: 30rpx;"),t+="padding-left: 30rpx;"),t})),G=r((()=>{let t="";return t+="font-size: 28rpx;",t+=`color: ${L.value};`,"style-1"==R.value.style&&(t+=`text-align: ${R.value.textAlign};`),t})),L=r((()=>{let t="";return t=1==a.scrollBool?R.value.rollTextColor:R.value.textColor,t})),U=r((()=>{let t="";return t=1==a.scrollBool?R.value.rollBgColor:R.value.bgColor,t}));let H=uni.getStorageSync("componentsScrollValGroup");if(H)H.TopTabbar=0,uni.setStorageSync("componentsScrollValGroup",H);else{let t={TopTabbar:0};uni.setStorageSync("componentsScrollValGroup",t)}let O=c();const P=()=>{1===O.length?"app/pages/auth/index"===O[0].route?uni.getStorage({key:"loginBack",success:t=>{b(t?{...t.data,mode:"redirectTo"}:{url:"/app/pages/index/index",mode:"switchTab"})},fail:t=>{b({url:"/app/pages/index/index",mode:"switchTab"})}}):"function"==typeof a.customBack?a.customBack():b({url:"/app/pages/index/index",mode:"switchTab"}):"function"==typeof a.customBack?a.customBack():C()},D=r((()=>`calc(100vw - ${o.menuButtonInfo.right}px + ${o.menuButtonInfo.width}px + 10px)`)),E=d(0),N=z();let Q=!1;R.value&&"style-4"==R.value.style&&(Q=!0);const W=F(Q);W.onLoad(),W.init();let X=d(!1);u((()=>{w((()=>{B().in(N).select(".ns-navbar-wrap .u-navbar .content-wrap").boundingClientRect((t=>{E.value=t?t.height:0,s.$patch((t=>{t.topTabarHeight=E.value}))})).exec()})),W.refresh(),X.value=uni.getStorageSync("imageAdsSameScreen")||!1}));return l({refresh:()=>{W.refresh()}}),(t,l)=>{const n=I,i=A,r=T;return"decorate"!=p(s).mode&&p(R)?(y(),f(n,{key:0,class:_(["ns-navbar-wrap",p(R).style])},{default:m((()=>[g(n,{class:_(["u-navbar",{fixed:-1!=a.scrollBool,absolute:-1==a.scrollBool}]),style:x({backgroundColor:p(U)})},{default:m((()=>[g(n,{class:"navbar-inner",style:x({width:"100%",height:E.value+"px"})},{default:m((()=>["style-1"==p(R).style?(y(),f(n,{key:0,class:_(["content-wrap",[p(R).textAlign]]),style:x(p($))},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(n,{class:"title-wrap",style:x(p(G))},{default:m((()=>[v(h(p(q).title),1)])),_:1},8,["style"])])),_:1},8,["class","style"])):k("v-if",!0),"style-2"==p(R).style?(y(),f(n,{key:1,class:"content-wrap",style:x(p($)),onClick:l[0]||(l[0]=t=>p(s).toRedirect(p(R).link))},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(n,{class:"title-wrap",style:x({color:p(R).textColor})},{default:m((()=>[g(n,null,{default:m((()=>[g(i,{src:p(V)(p(R).imgUrl),mode:"heightFix"},null,8,["src"])])),_:1}),g(n,{style:x({color:p(R).textColor})},{default:m((()=>[v(h(p(q).title),1)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1},8,["style"])):k("v-if",!0),"style-3"==p(R).style?(y(),f(n,{key:2,style:x(p($)),class:"content-wrap"},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(n,{class:"title-wrap",onClick:l[1]||(l[1]=t=>p(s).toRedirect(p(R).link))},{default:m((()=>[g(i,{src:p(V)(p(R).imgUrl),mode:"heightFix"},null,8,["src"])])),_:1}),g(n,{class:"search",onClick:l[2]||(l[2]=t=>p(s).toRedirect(p(R).link)),style:x({height:p(o).menuButtonInfo.height-2+"px",lineHeight:p(o).menuButtonInfo.height-2+"px"})},{default:m((()=>[g(r,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[24rpx] absolute left-[20rpx]"}),g(r,{class:"text-[24rpx]"},{default:m((()=>[v(h(p(R).inputPlaceholder),1)])),_:1})])),_:1},8,["style"]),g(n,{style:x({width:p(D)})},null,8,["style"])])),_:1},8,["style"])):k("v-if",!0),"style-4"==p(R).style?(y(),f(n,{key:3,style:x(p($)),class:"content-wrap"},{default:m((()=>[e.isBack?(y(),f(n,{key:0,class:"back-wrap -ml-[16rpx] text-[26px] nc-iconfont nc-icon-zuoV6xx",style:x({color:p(L)}),onClick:P},null,8,["style"])):k("v-if",!0),g(r,{class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[28rpx]",style:x({color:p(R).textColor})},null,8,["style"]),p(o).diyAddressInfo?(y(),f(n,{key:1,class:"title-wrap",onClick:l[3]||(l[3]=S((t=>p(W).reposition()),["stop"])),style:x({color:p(R).textColor})},{default:m((()=>[v(h(p(o).diyAddressInfo.community),1)])),_:1},8,["style"])):(y(),f(n,{key:2,class:"title-wrap",onClick:l[4]||(l[4]=S((t=>p(W).reposition()),["stop"])),style:x({color:p(R).textColor})},{default:m((()=>[v(h(p(o).defaultPositionAddress),1)])),_:1},8,["style"])),g(r,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx]",onClick:l[5]||(l[5]=S((t=>p(W).reposition()),["stop"])),style:x({color:p(R).textColor})},null,8,["style"])])),_:1},8,["style"])):k("v-if",!0)])),_:1},8,["style"])])),_:1},8,["class","style"]),k(" 解决fixed定位后导航栏塌陷的问题 "),p(j)?(y(),f(n,{key:0,class:"u-navbar-placeholder",style:x({width:"100%",paddingTop:E.value+"px"})},null,8,["style"])):k("v-if",!0)])),_:1},8,["class"])):k("v-if",!0)}}}),[["__scopeId","data-v-fb5eb5f5"]]);export{R as _,F as u};

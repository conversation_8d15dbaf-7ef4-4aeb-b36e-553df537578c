import{d as e,N as t,r as a,s as r,bp as l,ch as s,ci as n,p as c,l as u,o,c as x,w as p,T as i,b as f,A as d,g,n as m,e as h,B as _,R as v,a3 as b,S as y,cj as w,D as k,I as j,k as M,G as D,H as C,i as F,j as Y,ap as I,E as $,ck as z,cl as L}from"./index-dd56d0cc.js";import{_ as O}from"./u-empty.354f2b69.js";import{_ as S}from"./u-popup.457e1f1f.js";import{_ as N}from"./loading-page.vue_vue_type_script_setup_true_lang.c88f563e.js";import{t as V}from"./topTabbar.986d54c4.js";import{_ as T}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-safe-bottom.22d4d63b.js";import"./u-loading-icon.f15d7447.js";const W=T(e({__name:"sign_in",setup(e){const T=t({dataCount:[],weekCount:[],curYear:0,curMonth:0,curDate:0,curWeek:0,signInList:[],packList:[]}),W=t({weekDay:0,week:0}),A=a(!1),E=a(!1),q=a({}),B=a(!1),R=a({}),P=a(!1),G=a(!1),H=a({}),J=a(!1);let K=null,Q=null;r((()=>{let e=new Date;T.curYear=e.getFullYear(),T.curMonth=e.getMonth(),T.curDate=e.getDate(),T.curWeek=e.getDay(),0==T.curWeek&&(T.curWeek=7),K=l(T.curYear),Q=l(T.curMonth),Z(),ee(),X({year:T.curYear,month:T.curMonth+1}),U()}));const U=()=>{A.value=!0,s().then((e=>{q.value=e.data,q.value.is_use||(fe=ie.setTopTabbarParam({title:"我的签到",topStatusBar:{textColor:"#303133",bgColor:"#fff"}})),A.value=!1}))},X=e=>{n(e).then((e=>{T.signInList=[],T.packList=[],T.packList=e.data.period,e.data.length&&(T.signInList=e.data.days.map((e=>Number(e)))),J.value=!0}))},Z=()=>{let e=new Date(T.curYear,T.curMonth+1,0).getDate(),t=new Date(T.curYear,T.curMonth,1).getDay();T.dataCount=[];for(let a=1;a<e+t;a++){let e=a-t+1;T.dataCount.push(e)}},ee=()=>{let e=`${T.curYear}-${T.curMonth+1>9?T.curMonth+1:"0"+(T.curMonth+1)}-${T.curDate>9?T.curDate:"0"+T.curDate}`;for(let t=T.curWeek-1;t>=0;t--){const a=new Date(e).getDate()-t;T.weekCount.push(a)}for(let t=1;t<=7-T.curWeek;t++){const a=new Date(e).getDate()+t;T.weekCount.push(a)}},te=()=>{let e=(new Date).getMonth();T.curMonth==e||(T.curMonth=(new Date).getMonth(),T.curYear=(new Date).getFullYear(),X({year:T.curYear,month:T.curMonth+1})),E.value=!E.value},ae=e=>{T.dataCount=[],"prev"==e?(T.curMonth--,T.curMonth<0&&(T.curMonth=11,T.curYear--),W.weekDay=1,J.value=!1):(T.curMonth++,T.curMonth>11&&(T.curMonth=0,T.curYear++),W.weekDay=1,J.value=!1);let t={year:T.curYear,month:T.curMonth+1};X(t),Z()},re=c(),le=()=>{w().then((e=>{if(Object.values(e.data).length){R.value=e.data;let t=0;Object.values(R.value.awards).forEach(((e,a)=>{e.content||t++})),t==Object.values(R.value.awards).length&&(R.value.info=""),X({year:T.curYear,month:T.curMonth+1}),U(),re.getMemberInfo(),P.value=!0}}))},se=a(null),ne=e=>{let{curYear:t,curMonth:a}=z(T),r=`${t.value}-${a.value+1<10?"0"+(a.value+1):a.value+1}-${e<10?"0"+e:e}`;if(!T.packList.some((e=>e.day==r)))return;se.value=e;let l={year:T.curYear,month:T.curMonth+1,day:e};L(l).then((e=>{"[]"!=JSON.stringify(e.data)&&(H.value=e.data,G.value=!0)}))},ce=e=>T.signInList.includes(e),ue=e=>e>0&&e<=T.dataCount.length&&(e==T.curDate&&K==T.curYear&&Q==T.curMonth||void 0),oe=e=>{let{curYear:t,curMonth:a}=z(T),r=`${t.value}-${a.value+1<10?"0"+(a.value+1):a.value+1}-${e<10?"0"+e:e}`;return T.packList.some((e=>e.day==r&&e.award))},xe=e=>e>0?e:"";let pe={};const ie=V();let fe=ie.setTopTabbarParam({title:"我的签到"});return u((()=>k(Number(pe.height)+pe.top+8)+382+"rpx;")),u((()=>k(Number(pe.height)+pe.top+8)+50+"rpx;")),(e,t)=>{const a=j,r=M,l=D,s=C,n=F(Y("u-empty"),O),c=I,u=F(Y("u-popup"),S),w=F(Y("loading-page"),N);return o(),x(r,{style:m(e.themeColor())},{default:p((()=>[Object.values(q.value).length?(o(),x(r,{key:0,class:i(["min-h-screen overflow-hidden",{"bg-[#F6F6F6]":q.value&&q.value.is_use}])},{default:p((()=>[q.value.is_use?(o(),x(r,{key:0},{default:p((()=>[f(r,{class:"sigin-header"},{default:p((()=>[q.value.rule_explain?(o(),x(r,{key:0,class:"side-tab",onClick:t[0]||(t[0]=e=>B.value=!0)},{default:p((()=>[f(a,{class:"nc-iconfont nc-icon-a-meiriqiandaoV6xx-36 icon"}),f(a,{class:"desc"},{default:p((()=>[d("签到规则")])),_:1})])),_:1})):g("v-if",!0),f(r,{class:"h-[382rpx]",style:m({backgroundImage:"url("+h($)("static/resource/images/app/sigin_h5.png")+")",backgroundSize:"100%",backgroundRepeat:"no-repeat"})},null,8,["style"])])),_:1}),f(r,null,{default:p((()=>[f(r,{class:"sidebar-margin bg-[#fff] rounded-[16rpx] -mt-[85rpx]"},{default:p((()=>[f(r,{class:"card-template"},{default:p((()=>[E.value?(o(),x(r,{key:0,class:"mb-[30rpx] flex justify-between items-center"},{default:p((()=>[f(r,{class:"flex items-center"},{default:p((()=>[f(a,{class:"iconfont iconshangyibu text-[#303133] text-[20rpx]",onClick:t[1]||(t[1]=e=>ae("prev"))}),f(r,{class:"mx-[24rpx] text-[30rpx] font-500 text-[#303133] leading-[45rpx]"},{default:p((()=>[d(_(T.curYear)+"年"+_(T.curMonth+1)+"月",1)])),_:1}),f(a,{class:"iconfont iconxiayibu1 text-[#303133] text-[20rpx]",onClick:t[2]||(t[2]=e=>ae("next"))})])),_:1}),f(r,{class:"flex items-center"},{default:p((()=>[f(a,{class:"nc-iconfont nc-icon-shangV6xx-1 text-[var(--text-color-light9)] pt-[4rpx] px-[6rpx] text-[24rpx]",onClick:te})])),_:1})])),_:1})):(o(),x(r,{key:1,class:"mb-[30rpx] flex justify-between items-center"},{default:p((()=>[f(r,{class:"font-500 text-[30rpx]"},{default:p((()=>[d("已连续签到"),f(a,{class:"text-[#EF000C] mx-[4rpx]"},{default:p((()=>[d(_(q.value.days),1)])),_:1}),d("天")])),_:1}),E.value?g("v-if",!0):(o(),x(a,{key:0,class:"nc-iconfont nc-icon-xiaV6xx pt-[4rpx] px-[6rpx] text-[var(--text-color-light9)] text-[24rpx]",onClick:t[3]||(t[3]=e=>E.value=!E.value)}))])),_:1})),f(r,{class:"relative z-9 bg-[#fff] rounded-[18rpx]"},{default:p((()=>[f(r,null,{default:p((()=>[f(r,{class:"flex items-center justify-between text-[var(--text-color-light9)] text-[24rpx] mb-[16rpx]"},{default:p((()=>[f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周一")])),_:1}),f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周二")])),_:1}),f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周三")])),_:1}),f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周四")])),_:1}),f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周五")])),_:1}),f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周六")])),_:1}),f(a,{class:"w-[14.28%] leading-[36rpx] text-center"},{default:p((()=>[d("周日")])),_:1})])),_:1}),E.value?(o(),x(r,{key:1,class:"flex flex-wrap items-center justify-start"},{default:p((()=>[(o(!0),v(y,null,b(T.dataCount,((e,t)=>(o(),x(r,{class:"w-[14.28%] flex flex-col justify-center items-center mb-[30rpx]"},{default:p((()=>[xe(e)?(o(),x(r,{key:0,class:i(["w-[74rpx] h-[92rpx] bg-[#F6FAFF] text-[var(--text-color-light6)] box-border py-[10rpx] rounded-[8rpx] flex flex-col items-center",{"sign-bg !text-[#fff]":ce(e)&&J.value,"!bg-[#FDFDFD] border-[1rpx] border-[#F0F4FA] border-solid":!ce(e)&&e<T.curDate&&T.curMonth+1==(new Date).getMonth()+1&&T.curYear==(new Date).getFullYear(),"mb-[20rpx]":ue(e),"mb-[30rpx]":!ue(e)}]),onClick:t=>ne(e)},{default:p((()=>[f(a,{class:"text-[24rpx] leading-[28rpx] mb-[6rpx]"},{default:p((()=>[d(_(xe(e)),1)])),_:2},1024),xe(e)?(o(),x(r,{key:0,class:"flex items-center justufy-center"},{default:p((()=>[oe(e)?(o(),x(l,{key:0,src:h($)("static/resource/images/app/package.png"),class:"w-[40rpx] h-[40rpx]"},null,8,["src"])):ce(e)&&J.value?(o(),x(l,{key:1,src:h($)("static/resource/images/app/hassigin.png"),class:"w-[34rpx] h-[34rpx]"},null,8,["src"])):(o(),v(y,{key:2},[!ce(e)&&e<T.curDate&&T.curMonth+1==(new Date).getMonth()+1?(o(),x(l,{key:0,src:h($)("static/resource/images/app/nosigin.png"),class:"w-[34rpx] h-[34rpx]"},null,8,["src"])):(o(),x(l,{key:1,src:h($)("static/resource/images/app/nosigin1.png"),class:"w-[34rpx] h-[34rpx]"},null,8,["src"]))],64))])),_:2},1024)):g("v-if",!0)])),_:2},1032,["class","onClick"])):g("v-if",!0),ue(e)?(o(),x(r,{key:1,class:"w-[10rpx] h-[10rpx] rounded-[50%] bg-[var(--primary-color)]"})):g("v-if",!0)])),_:2},1024)))),256))])),_:1})):(o(),x(r,{key:0,class:"flex flex-wrap items-center justify-start"},{default:p((()=>[(o(!0),v(y,null,b(T.weekCount,((e,t)=>(o(),x(r,{key:t,class:"w-[14.28%] flex flex-col justify-center items-center"},{default:p((()=>[xe(e)?(o(),x(r,{key:0,class:i(["w-[74rpx] h-[92rpx] bg-[#f4f4f4] text-[var(--text-color-light6)] box-border py-[10rpx] rounded-[8rpx] flex flex-col items-center",{"sign-bg !text-[#fff]":ce(e),"!bg-[#f9f9f9] border-[1rpx] !text-[var(--text-color-light9)] border-[#f5f5f5] border-solid":!ce(e)&&e<T.curDate&&T.curMonth+1==(new Date).getMonth()+1,"mb-[20rpx]":ue(e),"mb-[30rpx]":!ue(e)}]),onClick:t=>ne(e)},{default:p((()=>[f(a,{class:"text-[24rpx] leading-[28rpx] mb-[6rpx]"},{default:p((()=>[d(_(xe(e)),1)])),_:2},1024),xe(e)?(o(),x(r,{key:0,class:"flex items-center justufy-center"},{default:p((()=>[oe(e)?(o(),x(l,{key:0,src:h($)("static/resource/images/app/package.png"),class:"w-[40rpx] h-[40rpx]"},null,8,["src"])):ce(e)?(o(),x(l,{key:1,src:h($)("static/resource/images/app/hassigin.png"),class:"w-[34rpx] h-[34rpx]"},null,8,["src"])):(o(),v(y,{key:2},[!ce(e)&&e<T.curDate&&T.curMonth+1==(new Date).getMonth()+1?(o(),x(l,{key:0,src:h($)("static/resource/images/app/nosigin.png"),class:"w-[34rpx] h-[34rpx]"},null,8,["src"])):(o(),x(l,{key:1,src:h($)("static/resource/images/app/nosigin1.png"),class:"w-[34rpx] h-[34rpx]"},null,8,["src"]))],64))])),_:2},1024)):g("v-if",!0)])),_:2},1032,["class","onClick"])):g("v-if",!0),ue(e)?(o(),x(r,{key:1,class:"w-[10rpx] h-[10rpx] rounded-[50%] bg-[var(--primary-color)]"})):g("v-if",!0)])),_:2},1024)))),128))])),_:1})),T.curMonth+1==(new Date).getMonth()+1&&T.curYear==(new Date).getFullYear()?(o(),x(r,{key:2,class:"mt-[40rpx] flex justify-center"},{default:p((()=>[q.value.is_sign?(o(),x(s,{key:1,class:"rounded-[40rpx] flex-center !bg-transparent text-[26rpx] font-500",style:m({width:"490rpx",height:"80rpx",border:"none",color:"#fff",backgroundImage:`url(${h($)("static/resource/images/app/button_bg1.png")})`,backgroundSize:"100%",backgroundRepeat:"no-repeat"}),shape:"circle"},{default:p((()=>[f(a,{class:"nc-iconfont nc-icon-a-meiriqiandaoV6xx-36 text-[26rpx] text-[#fff] mr-[8rpx]"}),f(a,null,{default:p((()=>[d("已签到")])),_:1})])),_:1},8,["style"])):(o(),x(s,{key:0,class:"rounded-[40rpx] flex-center !bg-transparent text-[26rpx] font-500",style:m({width:"490rpx",height:"80rpx",border:"none",color:"#fff",backgroundImage:`url(${h($)("static/resource/images/app/button_bg2.png")})`,backgroundSize:"100%",backgroundRepeat:"no-repeat"}),shape:"circle",onClick:le},{default:p((()=>[f(a,{class:"nc-iconfont nc-icon-a-meiriqiandaoV6xx-36 text-[26rpx] text-[#fff] mr-[8rpx]"}),f(a,null,{default:p((()=>[d("立即签到")])),_:1})])),_:1},8,["style"]))])),_:1})):g("v-if",!0)])),_:1})])),_:1})])),_:1})])),_:1}),q.value&&q.value.continue_award&&Object.keys(q.value.continue_award).length?(o(),x(r,{key:0,class:"mt-[20rpx] mb-[30rpx] sidebar-margin card-template"},{default:p((()=>[f(r,{class:"mb-[30rpx] flex items-center"},{default:p((()=>[f(r,{class:"font-500 text-[30rpx] text-[#303133]"},{default:p((()=>[d("签到奖励")])),_:1}),g(' <view class="text-[var(--text-color-light6)] text-[26rpx] leading-[30rpx]">\n                                <text>签到记录</text>\n                                <image :src="img(\'static/resource/images/app/more.png\')" class="w-[12rpx] h-[18rpx] ml-[8rpx]" />\n                            </view> ')])),_:1}),f(r,null,{default:p((()=>[(o(!0),v(y,null,b(q.value.continue_award,((e,t)=>(o(),x(r,{key:t,class:i(["flex items-center border-box",{"mt-[40rpx]":t}])},{default:p((()=>[(t+1)%4==1?(o(),x(r,{key:0,class:"w-[90rpx] h-[90rpx] rounded-[50%] bg-[#E7F6FF] flex items-center justify-center flex-shrink-0"},{default:p((()=>[f(l,{src:h($)("static/resource/images/app/icon_02.png"),class:"w-[40rpx] h-[40rpx]"},null,8,["src"])])),_:1})):(t+1)%4==2?(o(),x(r,{key:1,class:"w-[90rpx] h-[90rpx] rounded-[50%] bg-[#ffefef] flex items-center justify-center flex-shrink-0"},{default:p((()=>[f(l,{src:h($)("static/resource/images/app/icon_03.png"),class:"w-[40rpx] h-[40rpx]"},null,8,["src"])])),_:1})):(t+1)%4==3?(o(),x(r,{key:2,class:"w-[90rpx] h-[90rpx] rounded-[50%] bg-[#d3feeb] flex items-center justify-center flex-shrink-0"},{default:p((()=>[f(l,{src:h($)("static/resource/images/app/icon_04.png"),class:"w-[40rpx] h-[40rpx]"},null,8,["src"])])),_:1})):(t+1)%4==0?(o(),x(r,{key:3,class:"w-[90rpx] h-[90rpx] rounded-[50%] bg-[#ffeddd] flex items-center justify-center flex-shrink-0"},{default:p((()=>[f(l,{src:h($)("static/resource/images/app/icon_05.png"),class:"w-[40rpx] h-[40rpx]"},null,8,["src"])])),_:1})):g("v-if",!0),f(r,{class:"flex-1 mx-[20rpx]"},{default:p((()=>[f(r,{class:"font-400 text-[28rpx] text-[#303133] leading-[38rpx] mb-[10rpx]"},{default:p((()=>[d("连续签到"+_(e.continue_sign)+"天",1)])),_:2},1024),e.gift&&e.gift.total?(o(),x(r,{key:0,class:"flex flex-wrap"},{default:p((()=>[f(r,{class:"flex"},{default:p((()=>[f(l,{src:h($)(e.gift.total.icon),class:"w-[30rpx] h-[30rpx] flex-shrink-0"},null,8,["src"]),f(r,{class:"text-[24rpx] ml-[8rpx] text-[#FF9000] leading-[34rpx] max-w-[330rpx]"},{default:p((()=>[d(_(e.gift.total.text),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)):g("v-if",!0)])),_:2},1024),f(r,{class:"flex-shrink-0"},{default:p((()=>[Number(q.value.days)<Number(e.continue_sign)?(o(),x(r,{key:0,class:"w-[130rpx] h-[54rpx] text-center bg-primary-light rounded-[28rpx] text-[22rpx] text-[var(--primary-color)] flex-center font-500"},{default:p((()=>[d("待完成")])),_:1})):(o(),x(r,{key:1,class:"primary-btn-bg w-[130rpx] h-[54rpx] text-center rounded-[27rpx] text-[22rpx] text-[#fff] flex-center font-500"},{default:p((()=>[d("已完成")])),_:1}))])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1})):g("v-if",!0)])),_:1})])),_:1})):(o(),x(r,{key:1,class:"h-[100vh] w-[100vw] flex justify-center items-center"},{default:p((()=>[f(n,{text:"签到未开启",width:"347rpx",height:"265rpx",icon:h($)("static/resource/images/system/empty.png")},null,8,["icon"])])),_:1})),g(" 签到规则"),f(u,{show:B.value,round:16,mode:"bottom",onClose:t[5]||(t[5]=e=>B.value=!1)},{default:p((()=>[f(r,{class:"popup-common"},{default:p((()=>[f(r,{class:"title"},{default:p((()=>[d("签到规则")])),_:1}),f(c,{"scroll-y":!0,class:"px-[30rpx] box-border h-[360rpx] overflow-auto"},{default:p((()=>[(o(!0),v(y,null,b(q.value.rule_explain.split("\n"),(e=>(o(),x(r,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:p((()=>[d(_(e),1)])),_:2},1024)))),256))])),_:1}),f(r,{class:"btn-wrap"},{default:p((()=>[f(s,{class:"primary-btn-bg btn",onClick:t[4]||(t[4]=e=>B.value=!1)},{default:p((()=>[d("知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),g(" 签到奖励 "),f(u,{show:P.value,class:"award-popup overflow-hidden",customStyle:{backgroundColor:"transparent"},onClose:t[8]||(t[8]=e=>P.value=!1),mode:"center",round:"var(--rounded-big)",safeAreaInsetBottom:!1},{default:p((()=>[Object.values(R.value).length?(o(),x(r,{key:0,class:"w-[550rpx] -mt-[124rpx]"},{default:p((()=>[f(r,{class:"flex justify-center"},{default:p((()=>[f(l,{src:h($)("static/resource/images/app/award.png"),class:"w-[484rpx] h-[480rpx] z-10",mode:"aspectFill"},null,8,["src"])])),_:1}),f(r,{class:"-mt-[265rpx] bg-award rounded-[40rpx] pt-[100rpx] pb-[50rpx] mb-[50rpx] relative"},{default:p((()=>[f(r,{class:"px-[32rpx]"},{default:p((()=>[f(r,{class:"text-[36rpx] text-[#EF000C] font-500 mb-[10rpx] text-center"},{default:p((()=>[d(_(R.value.title),1)])),_:1}),R.value.info?(o(),x(r,{key:0,class:"text-[24rpx] text-[#333] leading-[34rpx] text-center mb-[60rpx]"},{default:p((()=>[d(_(R.value.info),1)])),_:1})):g("v-if",!0),f(r,{class:"px-[68rpx] mb-[100rpx]"},{default:p((()=>[(o(!0),v(y,null,b(R.value.awards,((e,t)=>(o(),v(y,null,[e.content?(o(!0),v(y,{key:0},b(e.content,((e,t)=>(o(),x(r,{class:"flex items-center mb-[30rpx]"},{default:p((()=>[f(l,{src:h($)(e.icon),class:"w-[42rpx] h-[42rpx]"},null,8,["src"]),f(r,{class:"ml-[20rpx] text-[28rpx] text-[#303133] leading-[38rpx]"},{default:p((()=>[d(_(e.text),1)])),_:2},1024)])),_:2},1024)))),256)):g("v-if",!0)],64)))),256))])),_:1}),f(r,{class:"flex justify-center relative z-30"},{default:p((()=>[f(r,{class:"w-[370rpx] h-[80rpx] primary-btn-bg font-500 rounded-[100rpx] text-[#ffffff] text-center leading-[80rpx] text-[26rpx]",onClick:t[6]||(t[6]=e=>P.value=!1)},{default:p((()=>[d("我知道了")])),_:1})])),_:1})])),_:1})])),_:1}),f(r,{class:"flex justify-center"},{default:p((()=>[f(a,{class:"nc-iconfont nc-icon-cuohaoV6xx1 text-[#fff] text-[50rpx]",onClick:t[7]||(t[7]=e=>P.value=!1)})])),_:1})])),_:1})):g("v-if",!0)])),_:1},8,["show"]),g(" 查看当日或连续签到奖励 "),f(u,{show:G.value,class:"award-popup overflow-hidden",customStyle:{backgroundColor:"transparent"},onClose:t[11]||(t[11]=e=>G.value=!1),mode:"center",round:"var(--rounded-big)",safeAreaInsetBottom:!1},{default:p((()=>[Object.values(H.value).length?(o(),x(r,{key:0,class:"w-[550rpx] -mt-[124rpx]"},{default:p((()=>[f(r,{class:"flex justify-center"},{default:p((()=>[f(l,{src:h($)("static/resource/images/app/award.png"),class:"w-[484rpx] h-[480rpx] z-10",mode:"aspectFill"},null,8,["src"])])),_:1}),f(r,{class:"-mt-[265rpx] bg-award rounded-[40rpx] pt-[100rpx] pb-[50rpx] mb-[50rpx] relative"},{default:p((()=>[f(r,{class:"px-[32rpx]"},{default:p((()=>[f(r,{class:"text-[36rpx] text-[#303133] font-500 mb-[10rpx] text-center relative z-20"},{default:p((()=>[d(_(H.value.title),1)])),_:1}),f(r,{class:"text-[24rpx] text-[#333] leading-[34rpx] text-center mb-[60rpx]"},{default:p((()=>[d(_(H.value.info),1)])),_:1}),f(r,{class:"px-[68rpx] mb-[100rpx]"},{default:p((()=>[(o(!0),v(y,null,b(H.value.awards,((e,t)=>(o(),v(y,null,[e.content?(o(!0),v(y,{key:0},b(e.content,((e,t)=>(o(),x(r,{class:"flex items-center mb-[32rpx]",key:t},{default:p((()=>[f(l,{src:h($)(e.icon),class:"w-[42rpx] h-[42rpx]"},null,8,["src"]),f(r,{class:"ml-[20rpx] text-[28rpx] text-[#303133] leading-[38rpx]"},{default:p((()=>[d(_(e.text),1)])),_:2},1024)])),_:2},1024)))),128)):g("v-if",!0)],64)))),256))])),_:1}),f(r,{class:"flex justify-center relative z-30"},{default:p((()=>[f(r,{class:"w-[370rpx] h-[80rpx] border-[2rpx] text-[var(--primary-color)] border-solid rounded-[40rpx] border-[var(--primary-color)] text-center flex-center text-[26rpx] box-border",onClick:t[9]||(t[9]=e=>G.value=!1)},{default:p((()=>[d("我知道了")])),_:1})])),_:1})])),_:1})])),_:1}),f(r,{class:"flex justify-center"},{default:p((()=>[f(a,{class:"nc-iconfont nc-icon-cuohaoV6xx1 text-[#fff] text-[50rpx]",onClick:t[10]||(t[10]=e=>G.value=!1)})])),_:1})])),_:1})):g("v-if",!0)])),_:1},8,["show"])])),_:1},8,["class"])):g("v-if",!0),f(w,{loading:A.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-153b0db5"]]);export{W as default};

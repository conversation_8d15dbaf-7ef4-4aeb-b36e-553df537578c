import{_ as e}from"./u-icon.33002907.js";import{a4 as t,a5 as a,a6 as i,a7 as l,bc as s,a8 as o,aN as n,bg as c,i as r,j as h,o as d,c as p,w as b,b as u,T as C,C as k,n as D,$ as m,A as S,B as f,k as x,I as z}from"./index-4b8dc7db.js";import{_ as v}from"./_plugin-vue_export-helper.1b428a4d.js";const g=v({name:"u-checkbox",mixins:[a,i,{props:{name:{type:[String,Number,Boolean],default:()=>t.checkbox.name},shape:{type:String,default:()=>t.checkbox.shape},size:{type:[String,Number],default:()=>t.checkbox.size},checked:{type:Boolean,default:()=>t.checkbox.checked},disabled:{type:[String,Boolean],default:()=>t.checkbox.disabled},activeColor:{type:String,default:()=>t.checkbox.activeColor},inactiveColor:{type:String,default:()=>t.checkbox.inactiveColor},iconSize:{type:[String,Number],default:()=>t.checkbox.iconSize},iconColor:{type:String,default:()=>t.checkbox.iconColor},label:{type:[String,Number],default:()=>t.checkbox.label},labelSize:{type:[String,Number],default:()=>t.checkbox.labelSize},labelColor:{type:String,default:()=>t.checkbox.labelColor},labelDisabled:{type:[String,Boolean],default:()=>t.checkbox.labelDisabled},usedAlone:{type:[Boolean],default:()=>!1}}}],data:()=>({isChecked:!1,parentData:{iconSize:12,labelDisabled:null,disabled:null,shape:"square",activeColor:null,inactiveColor:null,size:18,modelValue:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}),computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize(){return l(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor(){const e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.isChecked?this.elInactiveColor:"transparent":this.isChecked?e:"transparent"},iconClasses(){let e=[];return e.push("u-checkbox__icon-wrap--"+this.elShape),this.elDisabled&&e.push("u-checkbox__icon-wrap--disabled"),this.isChecked&&this.elDisabled&&e.push("u-checkbox__icon-wrap--disabled--checked"),e},iconWrapStyle(){const e={};return e.backgroundColor=this.isChecked&&!this.elDisabled?this.elActiveColor:"#ffffff",e.borderColor=this.isChecked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,e.width=l(this.elSize),e.height=l(this.elSize),this.usedAlone||"right"===this.parentData.iconPlacement&&(e.marginRight=0),e},checkboxStyle(){const e={};return this.usedAlone||(this.parentData.borderBottom&&this.parentData.placement,this.parentData.borderBottom&&"column"===this.parentData.placement&&(e.paddingBottom="8px")),s(e,o(this.customStyle))}},mounted(){this.init()},emits:["change"],methods:{init(){this.usedAlone||(this.updateParentData(),this.parent);const e=this.parentData.modelValue;this.checked?this.isChecked=!0:!this.usedAlone&&n.array(e)&&(this.isChecked=e.some((e=>e===this.name)))},updateParentData(){this.getParentData("u-checkbox-group")},wrapperClickHandler(e){(this.usedAlone||"right"===this.parentData.iconPlacement)&&this.iconClickHandler(e)},iconClickHandler(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},labelClickHandler(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.$emit("change",this.isChecked),this.$nextTick((()=>{c(this,"change")}))},setRadioCheckedStatus(){this.isChecked=!this.isChecked,this.emitEvent(),this.usedAlone||"function"==typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}},watch:{checked(){this.isChecked=this.checked}}},[["render",function(t,a,i,l,s,o){const n=r(h("u-icon"),e),c=x,v=z;return d(),p(c,{class:C(["u-checkbox cursor-pointer",[`u-checkbox-label--${s.parentData.iconPlacement}`,s.parentData.borderBottom&&"column"===s.parentData.placement&&"u-border-bottom"]]),style:D([o.checkboxStyle]),onClick:k(o.wrapperClickHandler,["stop"])},{default:b((()=>[u(c,{class:C(["u-checkbox__icon-wrap cursor-pointer",o.iconClasses]),onClick:k(o.iconClickHandler,["stop"]),style:D([o.iconWrapStyle])},{default:b((()=>[m(t.$slots,"icon",{},(()=>[u(n,{class:"u-checkbox__icon-wrap__icon",name:"checkbox-mark",size:o.elIconSize,color:o.elIconColor},null,8,["size","color"])]),!0)])),_:3},8,["onClick","class","style"]),u(v,{onClick:k(o.labelClickHandler,["stop"]),style:D({color:o.elDisabled?o.elInactiveColor:o.elLabelColor,fontSize:o.elLabelSize,lineHeight:o.elLabelSize})},{default:b((()=>[S(f(t.label),1)])),_:1},8,["onClick","style"])])),_:3},8,["style","onClick","class"])}],["__scopeId","data-v-92081527"]]);export{g as _};

import{a4 as e,a5 as t,a6 as a,a8 as s,a7 as o,bJ as l,i as r,j as c,o as i,c as n,w as u,b as d,n as h,$ as p,A as g,B as _,g as f,T as y,C as m,I as b,k,ag as C,y as j,bz as S,b8 as v,R as w,a3 as x,S as I,ap as L}from"./index-4b8dc7db.js";import{_ as B}from"./u-icon.33002907.js";import{_ as $}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as A}from"./u-loading-icon.11ef83b8.js";import{_ as M}from"./u-empty.a1ed4d4b.js";import{_ as P}from"./u-image.18977c35.js";import{_ as T}from"./u-line-progress.a964ad88.js";import{c as z,g as V}from"./project.df03875d.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */const D=$({name:"u-search",mixins:[t,a,{props:{shape:{type:String,default:()=>e.search.shape},bgColor:{type:String,default:()=>e.search.bgColor},placeholder:{type:String,default:()=>e.search.placeholder},clearabled:{type:Boolean,default:()=>e.search.clearabled},focus:{type:Boolean,default:()=>e.search.focus},showAction:{type:Boolean,default:()=>e.search.showAction},actionStyle:{type:Object,default:()=>e.search.actionStyle},actionText:{type:String,default:()=>e.search.actionText},inputAlign:{type:String,default:()=>e.search.inputAlign},inputStyle:{type:Object,default:()=>e.search.inputStyle},disabled:{type:Boolean,default:()=>e.search.disabled},borderColor:{type:String,default:()=>e.search.borderColor},searchIconColor:{type:String,default:()=>e.search.searchIconColor},color:{type:String,default:()=>e.search.color},placeholderColor:{type:String,default:()=>e.search.placeholderColor},searchIcon:{type:String,default:()=>e.search.searchIcon},searchIconSize:{type:[Number,String],default:()=>e.search.searchIconSize},margin:{type:String,default:()=>e.search.margin},animation:{type:Boolean,default:()=>e.search.animation},modelValue:{type:String,default:()=>e.search.value},value:{type:String,default:()=>e.search.value},maxlength:{type:[String,Number],default:()=>e.search.maxlength},height:{type:[String,Number],default:()=>e.search.height},label:{type:[String,Number,null],default:()=>e.search.label},adjustPosition:{type:Boolean,default:()=>!0},autoBlur:{type:Boolean,default:()=>!1}}}],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword(e){this.$emit("update:modelValue",e),this.$emit("change",e)},modelValue:{immediate:!0,handler(e){this.keyword=e}}},computed:{showActionBtn(){return!this.animation&&this.showAction}},emits:["clear","search","custom","focus","blur","click","clickIcon","update:modelValue","change"],methods:{addStyle:s,addUnit:o,inputChange(e){this.keyword=e.detail.value},clear(){this.keyword="",this.$nextTick((()=>{this.$emit("clear")}))},search(e){this.$emit("search",e.detail.value);try{l()}catch(t){}},custom(){this.$emit("custom",this.keyword);try{l()}catch(e){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout((()=>{this.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(e){this.$emit("clickIcon",this.keyword);try{l()}catch(t){}}}},[["render",function(e,t,a,s,o,l){const j=b,S=r(c("u-icon"),B),v=k,w=C;return i(),n(v,{class:"u-search",onClick:l.clickHandler,style:h([{margin:e.margin},l.addStyle(e.customStyle)])},{default:u((()=>[d(v,{class:"u-search__content",style:h({backgroundColor:e.bgColor,borderRadius:"round"==e.shape?"100px":"4px",borderColor:e.borderColor})},{default:u((()=>[e.$slots.label||null!==e.label?p(e.$slots,"label",{key:0},(()=>[d(j,{class:"u-search__content__label"},{default:u((()=>[g(_(e.label),1)])),_:1})]),!0):f("v-if",!0),d(v,{class:"u-search__content__icon"},{default:u((()=>[d(S,{onClick:l.clickIcon,size:e.searchIconSize,name:e.searchIcon,color:e.searchIconColor?e.searchIconColor:e.color},null,8,["onClick","size","name","color"])])),_:1}),d(w,{"confirm-type":"search",onBlur:l.blur,value:o.keyword,onConfirm:l.search,onInput:l.inputChange,disabled:e.disabled,onFocus:l.getFocus,focus:e.focus,maxlength:e.maxlength,"adjust-position":e.adjustPosition,"auto-blur":e.autoBlur,"placeholder-class":"u-search__content__input--placeholder",placeholder:e.placeholder,"placeholder-style":`color: ${e.placeholderColor}`,class:"u-search__content__input",type:"text",style:h([{textAlign:e.inputAlign,color:e.color,backgroundColor:e.bgColor,height:l.addUnit(e.height)},e.inputStyle])},null,8,["onBlur","value","onConfirm","onInput","disabled","onFocus","focus","maxlength","adjust-position","auto-blur","placeholder","placeholder-style","style"]),o.keyword&&e.clearabled&&o.focused?(i(),n(v,{key:1,class:"u-search__content__icon u-search__content__close",onClick:l.clear},{default:u((()=>[d(S,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])),_:1},8,["onClick"])):f("v-if",!0)])),_:3},8,["style"]),d(j,{style:h([e.actionStyle]),class:y(["u-search__action",[(l.showActionBtn||o.show)&&"u-search__action--active"]]),onClick:m(l.custom,["stop","prevent"])},{default:u((()=>[g(_(e.actionText),1)])),_:1},8,["style","class","onClick"])])),_:3},8,["onClick","style"])}],["__scopeId","data-v-54aceb98"]]);const R=$({data:()=>({searchKeyword:"",currentCategory:"",currentSort:"create_time",categoryList:[],projectList:[],loading:!1,hasMore:!0,page:1,limit:10,sortOptions:[{label:"最新",value:"create_time"},{label:"金额",value:"amount"},{label:"支持数",value:"support"},{label:"浏览量",value:"view"}]}),onLoad(){this.loadCategoryList(),this.loadProjectList()},onReachBottom(){this.hasMore&&!this.loading&&this.loadMore()},onPullDownRefresh(){this.refresh()},methods:{async loadCategoryList(){try{const e=await z();this.categoryList=e.data}catch(e){console.error(e)}},async loadProjectList(e=!1){if(!this.loading){this.loading=!0;try{const t={page:e?1:this.page,limit:this.limit,keyword:this.searchKeyword,category_id:this.currentCategory,order:this.currentSort},a=(await V(t)).data.data;e?(this.projectList=a,this.page=1):this.projectList=[...this.projectList,...a],this.hasMore=a.length===this.limit,e||this.page++}catch(t){console.error(t),j({title:"加载失败",icon:"none"})}finally{this.loading=!1,e&&S()}}},handleSearch(){this.refresh()},selectCategory(e){this.currentCategory=e,this.refresh()},selectSort(e){this.currentSort=e,this.refresh()},refresh(){this.page=1,this.hasMore=!0,this.loadProjectList(!0)},loadMore(){this.loadProjectList()},goToDetail(e){v({url:`/addon/niucrowd/pages/project/detail?id=${e}`})},getRemainDays(e){const t=Date.now()/1e3;return Math.max(0,Math.ceil((e-t)/86400))}}},[["render",function(e,t,a,s,o,l){const h=r(c("u-search"),D),p=k,m=L,C=r(c("u-loading-icon"),A),j=b,S=r(c("u-empty"),M),v=r(c("u-image"),P),B=r(c("u-line-progress"),T);return i(),n(p,{class:"project-list"},{default:u((()=>[f(" 搜索栏 "),d(p,{class:"search-bar"},{default:u((()=>[d(h,{modelValue:o.searchKeyword,"onUpdate:modelValue":t[0]||(t[0]=e=>o.searchKeyword=e),placeholder:"搜索项目",onSearch:l.handleSearch,onCustom:l.handleSearch,"bg-color":"#f8f9fa",shape:"round"},null,8,["modelValue","onSearch","onCustom"])])),_:1}),f(" 分类筛选 "),d(p,{class:"category-filter"},{default:u((()=>[d(m,{"scroll-x":"true",class:"category-scroll"},{default:u((()=>[d(p,{class:"category-list"},{default:u((()=>[d(p,{class:y(["category-item",{active:""===o.currentCategory}]),onClick:t[1]||(t[1]=e=>l.selectCategory(""))},{default:u((()=>[g(" 全部 ")])),_:1},8,["class"]),(i(!0),w(I,null,x(o.categoryList,(e=>(i(),n(p,{key:e.category_id,class:y(["category-item",{active:o.currentCategory===e.category_id}]),onClick:t=>l.selectCategory(e.category_id)},{default:u((()=>[g(_(e.category_name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1}),f(" 排序选择 "),d(p,{class:"sort-bar"},{default:u((()=>[d(p,{class:"sort-list"},{default:u((()=>[(i(!0),w(I,null,x(o.sortOptions,(e=>(i(),n(p,{key:e.value,class:y(["sort-item",{active:o.currentSort===e.value}]),onClick:t=>l.selectSort(e.value)},{default:u((()=>[g(_(e.label),1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),f(" 项目列表 "),d(p,{class:"project-content"},{default:u((()=>[o.loading&&0===o.projectList.length?(i(),n(p,{key:0,class:"loading-wrap"},{default:u((()=>[d(C),d(j,{class:"loading-text"},{default:u((()=>[g("加载中...")])),_:1})])),_:1})):0===o.projectList.length?(i(),n(p,{key:1,class:"empty-wrap"},{default:u((()=>[d(S,{text:"暂无项目"})])),_:1})):(i(),n(p,{key:2,class:"project-grid"},{default:u((()=>[(i(!0),w(I,null,x(o.projectList,(e=>(i(),n(p,{key:e.project_id,class:"project-card",onClick:t=>l.goToDetail(e.project_id)},{default:u((()=>[d(p,{class:"project-image"},{default:u((()=>[d(v,{src:e.cover_image,width:"100%",height:"200rpx","border-radius":"12rpx",fade:!0},null,8,["src"]),d(p,{class:"project-category"},{default:u((()=>{var t;return[g(_(null==(t=e.category)?void 0:t.category_name),1)]})),_:2},1024)])),_:2},1024),d(p,{class:"project-info"},{default:u((()=>[d(p,{class:"project-title"},{default:u((()=>[g(_(e.project_name),1)])),_:2},1024),d(p,{class:"project-desc"},{default:u((()=>[g(_(e.project_desc),1)])),_:2},1024),d(p,{class:"project-progress"},{default:u((()=>[d(B,{percentage:e.progress,"show-percent":!1,height:"8","active-color":"#007aff"},null,8,["percentage"]),d(p,{class:"progress-text"},{default:u((()=>[g(_(e.progress)+"%",1)])),_:2},1024)])),_:2},1024),d(p,{class:"project-stats"},{default:u((()=>[d(p,{class:"stat-item"},{default:u((()=>[d(j,{class:"stat-value"},{default:u((()=>[g("¥"+_(e.current_amount),1)])),_:2},1024),d(j,{class:"stat-label"},{default:u((()=>[g("已筹")])),_:1})])),_:2},1024),d(p,{class:"stat-item"},{default:u((()=>[d(j,{class:"stat-value"},{default:u((()=>[g(_(e.support_count),1)])),_:2},1024),d(j,{class:"stat-label"},{default:u((()=>[g("支持者")])),_:1})])),_:2},1024),d(p,{class:"stat-item"},{default:u((()=>[d(j,{class:"stat-value"},{default:u((()=>[g(_(l.getRemainDays(e.end_time)),1)])),_:2},1024),d(j,{class:"stat-label"},{default:u((()=>[g("剩余天数")])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1}),f(" 加载更多 "),o.hasMore?(i(),n(p,{key:0,class:"load-more",onClick:l.loadMore},{default:u((()=>[o.loading?(i(),n(C,{key:0})):f("v-if",!0),d(j,null,{default:u((()=>[g(_(o.loading?"加载中...":"加载更多"),1)])),_:1})])),_:1},8,["onClick"])):f("v-if",!0)])),_:1})}],["__scopeId","data-v-95895d71"]]);export{R as default};

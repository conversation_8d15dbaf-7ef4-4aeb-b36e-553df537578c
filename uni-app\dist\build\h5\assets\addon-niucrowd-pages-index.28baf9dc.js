import{_ as o}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{d as t,r as s,o as e,c as r,w as i,b as p,e as a,f as m,v as u,g as n,n as _,i as j,j as l,k as d}from"./index-4b8dc7db.js";import{u as c}from"./useDiy.22bb3fb6.js";import{d as g}from"./index.106c0c40.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.11ef83b8.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-icon.33002907.js";/* empty css                                                               */import"./u-popup.e2790691.js";import"./u-safe-bottom.987908cd.js";import"./top-tabbar.59f1aa86.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.5d9e7fd9.js";import"./u-checkbox.598bfa18.js";import"./u-checkbox-group.0c3be417.js";import"./u-button.7aa0e948.js";import"./u-input.7a7ec88f.js";import"./u-picker.0f983101.js";import"./u-upload.899e5f6a.js";import"./u-radio-group.b3c6fbb7.js";import"./diy_form.03750fb6.js";import"./u-action-sheet.8e2525cc.js";import"./u-line.b0d89d5b.js";import"./u-avatar.db5bcfa1.js";import"./u-text.40d19739.js";import"./u-parse.1f53da94.js";import"./tabbar.805b8203.js";import"./u-badge.e47151e6.js";import"./u-tabbar.bc2ea30a.js";import"./category.146302da.js";import"./common.eabc72c7.js";import"./project.df03875d.js";import"./index.3da4012a.js";import"./u--image.892273b2.js";import"./u-image.18977c35.js";/* empty css                                                                */import"./goods.f34d2594.js";import"./useGoods.0898f296.js";import"./add-cart-popup.da2aa6c6.js";import"./u-number-box.cd35fabd.js";import"./coupon.d71b475a.js";import"./point.a8a5a12b.js";import"./rank.1f5790d0.js";import"./bind-mobile.259f3837.js";import"./u-form.a144799c.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-modal.775e667c.js";import"./newcomer.2ec60692.js";import"./order.b56aabaa.js";const f=y(t({__name:"index",setup(t){const y=c({name:"DIY_NIUCROWD_INDEX"}),f=s(null);return s(null),y.onLoad(),y.onShow((o=>{var t;null==(t=f.value)||t.refresh()})),y.onHide(),y.onUnload(),y.onPageScroll(),(t,s)=>{const c=j(l("loading-page"),o),v=d;return e(),r(v,{style:_(t.themeColor())},{default:i((()=>[p(c,{loading:a(y).getLoading()},null,8,["loading"]),m(p(v,null,{default:i((()=>[n(" 自定义模板渲染 "),p(v,{class:"diy-template-wrap bg-index",style:_(a(y).pageStyle())},{default:i((()=>[p(g,{ref_key:"diyGroupRef",ref:f,data:a(y).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[u,!a(y).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-12175715"]]);export{f as default};

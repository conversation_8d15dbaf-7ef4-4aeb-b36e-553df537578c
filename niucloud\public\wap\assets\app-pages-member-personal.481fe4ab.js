import{d as e,r as a,N as t,Q as l,o,c as r,w as i,f as s,v as u,b as n,n as p,k as m,p as d,l as c,s as f,e as _,A as v,B as y,g as x,C as b,c2 as j,y as h,F as g,c3 as k,ax as w,i as C,j as S,H as z,ag as D,E as V,c4 as A,a as O}from"./index-dd56d0cc.js";import{_ as q}from"./u-avatar.ea828bd7.js";import{_ as B}from"./u-upload.44346d61.js";import{_ as F,a as I}from"./u-cell-group.2b93d8d5.js";import{_ as L}from"./u-popup.457e1f1f.js";import{_ as N}from"./u-action-sheet.daa5fa92.js";import{d as P,_ as $}from"./index.2c75d097.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-text.1f240d34.js";import"./u-loading-icon.f15d7447.js";import"./u-line.ddd38835.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-safe-bottom.22d4d63b.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./u-button.b6743e99.js";import"./u-input.ef44c0c4.js";import"./u-picker.1af38a2a.js";import"./u-radio-group.3476fb8a.js";import"./diy_form.2c8e4c0e.js";import"./u-parse.ae2d35cb.js";import"./tabbar.0d5e534b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";/* empty css                                                                */import"./goods.dbf7b09d.js";import"./useGoods.392f2eb1.js";import"./add-cart-popup.4746de5d.js";import"./u-number-box.41986fc4.js";import"./coupon.506f719c.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./bind-mobile.9929c841.js";import"./u-form.b5669646.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-modal.0666cf44.js";import"./newcomer.c56b90d6.js";import"./order.22f5d222.js";const G=e({__name:"personal_form_detail",props:["data","completeLayout"],setup(e){const d=e,c=a(!0),f=t({global:{},value:[]});return l((()=>{f.global.completeLayout=d.completeLayout||"style-1",d.data.formField&&d.data.formField.forEach((e=>{let a={id:e.field_key,componentName:e.field_type,pageStyle:"",viewFormDetail:!0,isShowArrow:!0,field:{name:e.field_name,value:e.field_value,required:e.field_required,unique:e.field_unique,privacyProtection:e.privacy_protection},margin:{top:0,bottom:0,both:0}};try{a.field.value=JSON.parse(e.field_value)}catch(t){a.field.value=e.field_value}f.value.push(a)})),c.value=!1})),(e,a)=>{const t=m;return o(),r(t,{style:p(e.themeColor()),class:"my-[var(--top-m)] sidebar-margin overflow-hidden card-template py-[12rpx] px-[26rpx]"},{default:i((()=>[s(n(t,{class:"diy-template-wrap"},{default:i((()=>[n(P,{ref:"diyGroupRef",data:f},null,8,["data"])])),_:1},512),[[u,!c.value]])])),_:1},8,["style"])}}}),R=E(e({__name:"personal",setup(e){const l=d(),s=c((()=>l.info));a(null),f((()=>{}));const u=t({modal:!1,value:s.nickname||""}),P=e=>{u.value=e.detail.value};let E=a(null);j().then((e=>{E.value=e.data}));const R=()=>{uni.$u.test.isEmpty(u.value)?h({title:g("nicknamePlaceholder"),icon:"none"}):k({field:"nickname",value:u.value}).then((e=>{l.info.nickname=u.value,u.modal=!1}))},U=a(!1),H=c((()=>[{name:g("man"),value:1},{name:g("woman"),value:2}])),J=e=>{k({field:"sex",value:e.value}).then((a=>{l.info.sex_name=e.name}))},M=e=>{w({filePath:e.file.url,name:"file"}).then((e=>{k({field:"headimg",value:e.data.url}).then((()=>{l.info.headimg=e.data.url}))})).catch((()=>{}))},Q=a(!1),T=e=>{k({field:"birthday",value:uni.$u.date(e.value,"yyyy-mm-dd")}).then((()=>{l.info.birthday=uni.$u.date(e.value||e.value+1,"yyyy-mm-dd"),Q.value=!1}))};return(e,a)=>{const t=C(S("u-avatar"),q),l=C(S("u-upload"),B),d=C(S("u-cell"),F),c=m,f=z,j=C(S("u-cell-group"),I),h=D,k=C(S("u-popup"),L),w=C(S("u-action-sheet"),N),K=C(S("u-datetime-picker"),$);return _(s)?(o(),r(c,{key:0,class:"w-full min-h-screen bg-page personal-wrap !pb-[20rpx]",style:p(e.themeColor())},{default:i((()=>[n(c,{class:"my-[var(--top-m)] sidebar-margin overflow-hidden card-template py-[20rpx]"},{default:i((()=>[n(j,{border:!1,class:"cell-group"},{default:i((()=>{return[n(d,{title:_(g)("headimg"),titleStyle:{"font-size":"28rpx"},"is-link":!0},{value:i((()=>[n(l,{onAfterRead:M,maxCount:1},{default:i((()=>[n(t,{src:_(V)(_(s).headimg),"default-url":_(V)("static/resource/images/default_headimg.png"),size:"40",leftIcon:"none"},null,8,["src","default-url"])])),_:1})])),_:1},8,["title"]),n(d,{title:_(g)("nickname"),titleStyle:{"font-size":"28rpx"},"is-link":!0,value:_(s).nickname,onClick:a[0]||(a[0]=e=>u.modal=!0)},null,8,["title","value"]),n(d,{title:_(g)("sex"),titleStyle:{"font-size":"28rpx"},"is-link":!0,value:_(s).sex_name||_(g)("unknown"),onClick:a[1]||(a[1]=e=>U.value=!0)},null,8,["title","value"]),n(d,{title:_(g)("mobile"),titleStyle:{"font-size":"28rpx"}},{value:i((()=>[_(s).mobile?(o(),r(c,{key:0,class:"mr-[10rpx]"},{default:i((()=>[v(y(_(A)(_(s).mobile)),1)])),_:1})):(o(),r(c,{key:1},{default:i((()=>[n(f,{onClick:a[2]||(a[2]=e=>_(O)({url:"/app/pages/auth/bind"})),class:"bg-transparent w-[170rpx] p-[0] rounded-[100rpx] text-[var(--primary-color)] !border-[2rpx] !border-solid border-[var(--primary-color)] text-[24rpx] h-[54rpx] flex-center"},{default:i((()=>[v(y(_(g)("bindMobile")),1)])),_:1})])),_:1}))])),_:1},8,["title"]),n(d,{title:_(g)("birthday"),titleStyle:{"font-size":"28rpx"},"is-link":!0,value:(e=_(s).birthday,(e?uni.$u.date(new Date(e),"yyyy-mm-dd"):"")||_(g)("unknown")),onClick:a[3]||(a[3]=e=>Q.value=!0)},null,8,["title","value"])];var e})),_:1})])),_:1}),x(" 商品的万能表单信息 "),n(c,{onClick:a[4]||(a[4]=e=>_(O)({url:"/app/pages/member/personal_form",param:{form_id:_(s).form_id,form_record_id:_(s).form_record_id}}))},{default:i((()=>[_(E)&&Object.keys(_(E)).length?(o(),r(G,{key:0,class:"personal-form",data:_(E),completeLayout:"style-2"},null,8,["data"])):x("v-if",!0)])),_:1}),x(" 修改昵称 "),n(k,{class:"popup-type",safeAreaInsetBottom:!1,round:"var(--rounded-big)",show:u.modal,mode:"center",onClose:a[7]||(a[7]=e=>u.modal=!1)},{default:i((()=>[n(c,{class:"w-[620rpx] popup-common pb-[40rpx]",onTouchmove:a[6]||(a[6]=b((()=>{}),["prevent","stop"]))},{default:i((()=>[n(c,{class:"title !pt-[50rpx] !pb-[60rpx]"},{default:i((()=>[v(y(_(g)("updateNickname")),1)])),_:1}),n(c,{class:"mx-[50rpx] border-0 border-b border-[#eee] border-solid"},{default:i((()=>[n(h,{type:"nickname",class:"h-[88rpx] text-[26rpx]",modelValue:u.value,"onUpdate:modelValue":a[5]||(a[5]=e=>u.value=e),placeholder:_(g)("nicknamePlaceholder"),placeholderClass:"text-[26rpx] h-[88rpx] flex items-center",onBlur:P},null,8,["modelValue","placeholder"])])),_:1}),n(c,{class:"px-[60rpx] pt-[70rpx]"},{default:i((()=>[n(f,{"hover-class":"none",class:"primary-btn-bg text-[#fff] h-[80rpx] font-500 leading-[80rpx] rounded-[100rpx] text-[26rpx]",onClick:R},{default:i((()=>[v(y(_(g)("confirm")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),x(" 修改性别 "),n(w,{actions:_(H),show:U.value,closeOnClickOverlay:!0,safeAreaInsetBottom:!0,onClose:a[8]||(a[8]=e=>U.value=!1),onSelect:J},null,8,["actions","show"]),x(" 修改生日 "),n(K,{modelValue:_(s).birthday,"onUpdate:modelValue":a[9]||(a[9]=e=>_(s).birthday=e),show:Q.value,mode:"date","confirm-text":_(g)("confirm"),maxDate:(new Date).valueOf(),minDate:0,"cancel-text":_(g)("cancel"),onCancel:a[10]||(a[10]=e=>Q.value=!1),onConfirm:T},null,8,["modelValue","show","confirm-text","maxDate","cancel-text"])])),_:1},8,["style"])):x("v-if",!0)}}}),[["__scopeId","data-v-62122a6d"]]);export{R as default};

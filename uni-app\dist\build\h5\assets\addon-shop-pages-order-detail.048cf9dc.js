import{d as e,r as t,s as a,M as l,E as r,l as s,o as d,c as o,w as i,b as u,e as p,A as n,B as x,g as c,R as _,S as f,a3 as m,T as v,n as y,a as g,bF as b,G as h,k,I as j,i as w,j as F,aI as C,aj as T,C as q,F as E,b2 as M,bE as N,m as R,aH as z}from"./index-4b8dc7db.js";import{_ as I}from"./u--image.892273b2.js";import{_ as L}from"./u-avatar.db5bcfa1.js";import{_ as V}from"./pay.2b11dfa6.js";import{_ as B}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{g as G,o as P,b as O}from"./order.b56aabaa.js";import{g as S}from"./shop.61e7c9c0.js";import{d as $}from"./verify.2951bd10.js";import{l as A}from"./logistics-tracking.ad291e15.js";import{n as H}from"./ns-goods-manjian.0dae0dce.js";import{t as U}from"./topTabbar.1aa95d14.js";import{_ as D}from"./index.vue_vue_type_script_setup_true_lang.73703bcc.js";import{_ as J}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.18977c35.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-text.40d19739.js";import"./u-popup.e2790691.js";import"./u-safe-bottom.987908cd.js";import"./pay.eee54be7.js";import"./u-loading-icon.11ef83b8.js";import"./u-steps.63afec2b.js";import"./index.106c0c40.js";import"./top-tabbar.59f1aa86.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.5d9e7fd9.js";import"./u-checkbox.598bfa18.js";import"./u-checkbox-group.0c3be417.js";import"./u-button.7aa0e948.js";import"./u-input.7a7ec88f.js";import"./u-picker.0f983101.js";import"./u-upload.899e5f6a.js";import"./u-radio-group.b3c6fbb7.js";import"./diy_form.03750fb6.js";import"./u-action-sheet.8e2525cc.js";import"./u-line.b0d89d5b.js";import"./u-parse.1f53da94.js";import"./tabbar.805b8203.js";import"./u-badge.e47151e6.js";import"./u-tabbar.bc2ea30a.js";import"./category.146302da.js";import"./common.eabc72c7.js";import"./project.df03875d.js";import"./index.3da4012a.js";import"./goods.f34d2594.js";import"./useGoods.0898f296.js";import"./add-cart-popup.da2aa6c6.js";import"./u-number-box.cd35fabd.js";import"./coupon.d71b475a.js";import"./point.a8a5a12b.js";import"./rank.1f5790d0.js";import"./bind-mobile.259f3837.js";import"./u-form.a144799c.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-modal.775e667c.js";import"./newcomer.2ec60692.js";const K=J(e({__name:"detail",setup(e){U().setTopTabbarParam({title:"订单详情"});const J=t({}),K=t(!0);t("");const Q=t(""),W=t({}),X=t(!0),Y=t(null),Z=t("");t("");const ee=t("");t(null),a((e=>{if(e.order_id)Q.value=e.order_id,te(Q.value);else{l({url:"/addon/shop/pages/order/list",title:"缺少订单id"})}})),S().then((({data:e})=>{W.value=e}));const te=e=>{K.value=!0,G(e).then((e=>{if(J.value=e.data,e.data.order_goods&&e.data.order_goods.length&&ue.value){let t={};t.order_goods_id=e.data.order_goods[0].order_goods_id,ne(t)}if(e.data.order_goods&&e.data.order_goods.length&&xe.value){let t={};t.order_id=e.data.order_id,_e(t)}J.value.goods=[],J.value.gift_goods=[],J.value.order_goods.forEach(((e,t)=>{e.is_gift?J.value.gift_goods.push(e):J.value.goods.push(e)}));let t=0;for(let a=0;a<J.value.order_goods.length;a++)1!=J.value.order_goods[a].status&&t++;t==J.value.order_goods.length&&(X.value=!1),Z.value=J.value.order_goods[0].goods_name,ee.value=r(J.value.order_goods[0].goods_image_thumb_small||""),K.value=!1})).catch((()=>{K.value=!1}))},ae=e=>{"exchange"==J.value.activity_type?g({url:"/addon/shop/pages/point/detail",param:{id:J.value.relate_id}}):g({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})},le=t(null),re=t(null),se=(e="")=>{var t,a;if("pay"==e)null==(t=le.value)||t.open(J.value.order_type,J.value.order_id,`/addon/shop/pages/order/detail?order_id=${J.value.order_id}`);else if("close"==e)a=J.value,N({title:"提示",content:"您确定要关闭该订单吗？",confirmColor:R().themeColor["--primary-color"],success:e=>{e.confirm&&P(a.order_id).then((e=>{te(a.order_id)}))}});else if("finish"==e)(e=>{N({title:"提示",content:"您确定物品已收到吗？",confirmColor:R().themeColor["--primary-color"],success:t=>{t.confirm&&O(e.order_id).then((t=>{te(e.order_id)}))}})})(J.value);else if("index"==e)g({url:"/addon/shop/pages/index",mode:"reLaunch"});else if("logistics"==e){if(J.value.order_delivery.length>0){let e=[];J.value.order_delivery.forEach(((t,a)=>{"express"!=t.delivery_type||"express"!=t.sub_delivery_type&&"none_express"!=t.sub_delivery_type||(t.name=`包裹${a+1}`,e.push(t))}));let t={id:e[0].id,mobile:J.value.taker_mobile};re.value.open(t),re.value.packageList=e}}else"evaluate"==e&&(J.value.is_evaluate?g({url:"/addon/shop/pages/evaluate/order_evaluate_view",param:{order_id:J.value.order_id}}):g({url:"/addon/shop/pages/evaluate/order_evaluate",param:{order_id:J.value.order_id}}))},de=()=>{b({latitude:Number(J.value.taker_latitude),longitude:Number(J.value.taker_longitude),success:function(){}})},oe=e=>{let t=!1;if("express"!=e.delivery_type)return!1;for(let a=0;a<e.order_delivery.length;a++){if("express"===e.order_delivery[a].sub_delivery_type){t=!0;break}t=!1}return t},ie=t({}),ue=s((()=>{let e=!1;if(1==J.value.order_goods.length){ie.value=J.value.order_goods[0];let t=J.value.order_goods[0];e=1==t.is_verify&&"virtual"==t.goods_type&&"delivery_finish"==t.delivery_status&&3==J.value.status}return e})),pe=t([]),ne=e=>{pe.value=[],$("shopVirtualGoods",e).then((e=>{pe.value=e.data}))},xe=s((()=>{let e=!1;return e="store"==J.value.delivery_type&&2==J.value.status,e})),ce=t([]),_e=e=>{ce.value=[],$("shopPickUpOrder",e).then((e=>{ce.value=e.data}))},fe=t([]),me=e=>{fe.value=e},ve=t([]),ye=e=>{ve.value=e};return(e,t)=>{const a=h,l=k,s=j,b=w(F("u--image"),I),N=z,R=C,G=w(F("u-avatar"),L),P=w(F("pay"),V),O=w(F("loading-page"),B);return d(),o(l,{style:y(e.themeColor())},{default:i((()=>[K.value?c("v-if",!0):(d(),o(l,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:i((()=>[K.value?c("v-if",!0):(d(),o(l,{key:0,class:"pb-20rpx"},{default:i((()=>[J.value.status_name?(d(),o(l,{key:0,class:"pl-[40rpx] pr-[35rpx] bg-linear pb-[100rpx]"},{default:i((()=>[u(l,{class:"flex justify-between items-center pt-[40rpx]"},{default:i((()=>[u(l,{class:"flex flex-col"},{default:i((()=>[u(a,{class:"w-[28rpx] h-[20rpx] mb-[10rpx] ml-[4rpx]",src:p(r)("addon/shop/detail/head_001.png"),mode:"aspectFit"},null,8,["src"]),u(l,{class:"text-[#fff] text-[36rpx] font-500 leading-[42rpx]"},{default:i((()=>[n(x(J.value.status_name.name),1)])),_:1})])),_:1}),u(l,{class:"flex items-end relative -bottom-[6rpx]"},{default:i((()=>[1==J.value.status?(d(),o(a,{key:0,class:"w-[160rpx] h-[140rpx]",src:p(r)("addon/shop/detail/payment.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),2==J.value.status?(d(),o(a,{key:1,class:"w-[160rpx] h-[140rpx]",src:p(r)("addon/shop/detail/deliver_goods.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),3==J.value.status?(d(),o(a,{key:2,class:"w-[160rpx] h-[140rpx]",src:p(r)("addon/shop/detail/receive.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),5==J.value.status?(d(),o(a,{key:3,class:"w-[160rpx] h-[140rpx]",src:p(r)("addon/shop/detail/complete.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),-1==J.value.status?(d(),o(a,{key:4,class:"w-[160rpx] h-[140rpx]",src:p(r)("addon/shop/detail/close.png"),mode:"aspectFit"},null,8,["src"])):c("v-if",!0),u(a,{class:"w-[8rpx] h-[30rpx] mb-[20rpx]",src:p(r)("addon/shop/detail/head_002.png"),mode:"aspectFit"},null,8,["src"])])),_:1})])),_:1})])),_:1})):c("v-if",!0),"virtual"!=J.value.delivery_type?(d(),o(l,{key:1,class:"sidebar-margin mt-[-86rpx] card-template"},{default:i((()=>["express"==J.value.delivery_type?(d(),o(l,{key:0},{default:i((()=>[u(l,{class:"text-[#303133] flex"},{default:i((()=>[u(s,{class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[40rpx] pt-[12rpx] mr-[20rpx]"}),u(l,{class:"flex flex-col"},{default:i((()=>[u(l,{class:"text-[30rpx] leading-[38rpx] overflow-hidden"},{default:i((()=>[u(s,null,{default:i((()=>[n(x(J.value.taker_name),1)])),_:1}),u(s,{class:"ml-[15rpx]"},{default:i((()=>[n(x(J.value.taker_mobile),1)])),_:1})])),_:1}),u(l,{class:"mt-[12rpx] text-[24rpx] text-[var(--text-color-light6)] using-hidden leading-[26rpx]"},{default:i((()=>[n(x(J.value.taker_full_address.split(J.value.taker_address)[0])+x(J.value.taker_address),1)])),_:1})])),_:1})])),_:1})])),_:1})):c("v-if",!0),"store"==J.value.delivery_type?(d(),o(l,{key:1},{default:i((()=>[u(l,{class:"flex items-center mb-3"},{default:i((()=>[u(l,null,{default:i((()=>[u(b,{class:"overflow-hidden",radius:"var(--goods-rounded-mid)",width:"100rpx",height:"100rpx",src:p(r)(J.value.store.store_logo?J.value.store.store_logo:""),model:"aspectFill"},{error:i((()=>[u(a,{class:"w-[100rpx] h-[100rpx] rounded-[var(--goods-rounded-mid)] overflow-hidden",src:p(r)("addon/shop/store_default.png"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1}),u(l,{class:"flex flex-col ml-[20rpx]"},{default:i((()=>[u(s,{class:"text-[30rpx] font-500 text-[#303133] mb-[20rpx]"},{default:i((()=>[n(x(J.value.store.store_name),1)])),_:1}),u(s,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[14rpx]"},{default:i((()=>[n(x(J.value.store.trade_time),1)])),_:1}),u(s,{class:"text-[24rpx] text-[var(--text-color-light6)] leading-[1.4]"},{default:i((()=>[n(x(J.value.store.full_address),1)])),_:1})])),_:1})])),_:1}),J.value.taker_name?(d(),o(l,{key:0,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("姓名")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.taker_name),1)])),_:1})])),_:1})):c("v-if",!0),J.value.taker_mobile?(d(),o(l,{key:1,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("预留手机")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.taker_mobile),1)])),_:1})])),_:1})):c("v-if",!0),J.value.buyer_ask_delivery_time?(d(),o(l,{key:2,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("提货时间")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.buyer_ask_delivery_time),1)])),_:1})])),_:1})):c("v-if",!0)])),_:1})):c("v-if",!0),"local_delivery"==J.value.delivery_type?(d(),o(l,{key:2,class:"flex"},{default:i((()=>[u(s,{onClick:de,class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[40rpx] pt-[12rpx] mr-[20rpx]"}),u(l,{class:"flex flex-col"},{default:i((()=>[u(l,{class:"flex leading-[38rpx] overflow-hidden"},{default:i((()=>[u(s,{class:"text-[30rpx]"},{default:i((()=>[n(x(J.value.taker_name),1)])),_:1}),u(s,{class:"text-[30rpx] ml-[15rpx]"},{default:i((()=>[n(x(J.value.taker_mobile),1)])),_:1})])),_:1}),u(s,{class:"text-[24rpx] mt-[12rpx] leading-[26rpx]"},{default:i((()=>[n(x(J.value.taker_full_address),1)])),_:1})])),_:1})])),_:1})):c("v-if",!0)])),_:1})):c("v-if",!0),c(" 自提核销"),p(xe)?(d(),_(f,{key:2},[ce.value&&ce.value.length?(d(),o(l,{key:0,class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[ce.value.length>1?(d(),o(R,{key:0,class:"h-[450rpx]",circular:"","indicator-dots":"true"},{default:i((()=>[(d(!0),_(f,null,m(ce.value,((e,t)=>(d(),o(N,{key:t},{default:i((()=>[u(l,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(a,{src:e.qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:2},1024),u(l,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[n(x(e.code),1)])),_:2},1024),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t=>p(T)(e.code)},{default:i((()=>[n("复制")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(d(),_(f,{key:1},[u(l,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(a,{src:ce.value[0].qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:1}),u(l,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[n(x(ce.value[0].code),1)])),_:1}),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t[0]||(t[0]=e=>p(T)(ce.value[0].code))},{default:i((()=>[n("复制")])),_:1})])),_:1})],64))])),_:1})):c("v-if",!0)],64)):c("v-if",!0),u(l,{class:v(["sidebar-margin card-template p-[0] py-[var(--pad-top-m)] overflow-hidden",{"pb-[var(--pad-top-m)]":J.value.gift_goods.length<=0}]),style:y("virtual"==J.value.delivery_type?"margin-top: -86rpx":"margin-top: 20rpx")},{default:i((()=>[(d(!0),_(f,null,m(J.value.goods,((e,t)=>(d(),o(l,{key:t,class:"px-[var(--pad-sidebar-m)]"},{default:i((()=>[u(l,{class:"order-goods-item flex justify-between flex-wrap mb-[20rpx]"},{default:i((()=>[u(l,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:t=>ae(e.goods_id)},{default:i((()=>[u(b,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"150rpx",height:"150rpx",src:p(r)(e.goods_image_thumb_small?e.goods_image_thumb_small:""),model:"aspectFill"},{error:i((()=>[u(a,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:p(r)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1032,["onClick"]),u(l,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:i((()=>[u(l,null,{default:i((()=>[u(l,{class:"text-[28rpx] max-w-[490rpx] truncate leading-[40rpx] text-[#333]"},{default:i((()=>[n(x(e.goods_name),1)])),_:2},1024),e.sku_name?(d(),o(l,{key:0},{default:i((()=>[u(l,{class:"text-[22rpx] mt-[14rpx] text-[var(--text-color-light9)] truncate max-w-[490rpx] leading-[28rpx]"},{default:i((()=>[n(x(e.sku_name),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)])),_:2},1024),e.manjian_info&&Object.keys(e.manjian_info).length?(d(),o(l,{key:0,class:"flex items-center mt-[10rpx] mb-[auto]",onClick:q((t=>(e=>{let t={};t.condition_type=M(e).condition_type,t.rule_json=[M(e).rule],t.name=M(e).manjian_name,Y.value.open(t)})(e.manjian_info)),["stop"])},{default:i((()=>[u(l,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[20rpx] flex items-center justify-center w-[88rpx] h-[36rpx] mr-[6rpx]"},{default:i((()=>[n("满减送")])),_:1}),u(s,{class:"text-[22rpx] text-[#999]"},{default:i((()=>[n(x(e.manjian_info.manjian_name),1)])),_:2},1024)])),_:2},1032,["onClick"])):c("v-if",!0),u(l,{class:"flex justify-between items-baseline leading-[28rpx] text-[#333]"},{default:i((()=>[u(l,{class:"price-font"},{default:i((()=>[e.extend&&parseFloat(e.extend.point)>0?(d(),o(l,{key:0,class:"text-[40rpx] inline-block"},{default:i((()=>[u(s,{class:"text-[40rpx] font-200"},{default:i((()=>[n(x(e.extend.point),1)])),_:2},1024),u(s,{class:"text-[32rpx] ml-[4rpx]"},{default:i((()=>[n("积分")])),_:1})])),_:2},1024)):c("v-if",!0),parseFloat(e.price)&&e.extend&&parseFloat(e.extend.point)>0?(d(),o(s,{key:1,class:"mx-[4rpx] text-[32rpx]"},{default:i((()=>[n("+")])),_:1})):c("v-if",!0),parseFloat(e.price)&&e.extend&&parseFloat(e.extend.point)>0?(d(),_(f,{key:2},[u(s,{class:"text-[40rpx] font-200"},{default:i((()=>[n(x(parseFloat(e.price).toFixed(2)),1)])),_:2},1024),u(s,{class:"text-[32rpx] ml-[4rpx]"},{default:i((()=>[n("元")])),_:1})],64)):c("v-if",!0),e.extend&&e.extend.is_newcomer?(d(),_(f,{key:3},[u(s,{class:"text-[24rpx]"},{default:i((()=>[n("￥")])),_:1}),u(s,{class:"text-[40rpx] font-500"},{default:i((()=>[n(x(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),u(s,{class:"text-[24rpx] font-500"},{default:i((()=>[n("."+x(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)],64)):c("v-if",!0),e.extend&&e.extend.is_impulse_buy?(d(),_(f,{key:4},[u(s,{class:"text-[24rpx]"},{default:i((()=>[n("￥")])),_:1}),u(s,{class:"text-[40rpx] font-500"},{default:i((()=>[n(x(parseFloat(e.goods_money).toFixed(2).split(".")[0]),1)])),_:2},1024),u(s,{class:"text-[24rpx] font-500"},{default:i((()=>[n("."+x(parseFloat(e.goods_money).toFixed(2).split(".")[1]),1)])),_:2},1024)],64)):c("v-if",!0),e.extend?c("v-if",!0):(d(),_(f,{key:5},[u(s,{class:"text-[24rpx]"},{default:i((()=>[n("￥")])),_:1}),u(s,{class:"text-[40rpx] font-500"},{default:i((()=>[n(x(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),u(s,{class:"text-[24rpx] font-500"},{default:i((()=>[n("."+x(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)],64))])),_:2},1024),u(s,{class:"text-right text-[26rpx]"},{default:i((()=>[n("x"+x(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),e.extend&&e.extend.is_impulse_buy&&e.num>1?(d(),o(l,{key:0,class:"flex items-center box-border mt-[8rpx]"},{default:i((()=>[u(a,{class:"h-[24rpx] w-[56rpx]",src:p(r)("addon/shop/impulse_buy.png"),mode:"heightFix"},null,8,["src"]),u(l,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:i((()=>[n(x(e.impulse_buy_tips),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0),e.extend&&e.extend.is_newcomer&&e.num>1?(d(),o(l,{key:1,class:"flex items-center box-border mt-[8rpx]"},{default:i((()=>[u(a,{class:"h-[24rpx] w-[56rpx]",src:p(r)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"]),u(l,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:i((()=>[n(" 第1"+x(e.unit)+"，￥"+x(parseFloat(e.extend.newcomer_price).toFixed(2))+"/"+x(e.unit)+"；第"+x(e.num>2?"2~"+e.num:"2")+x(e.unit)+"，￥"+x(parseFloat(e.price).toFixed(2))+"/"+x(e.unit),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0),"1"!=e.status||1==e.is_enable_refund?(d(),o(l,{key:2,class:"flex justify-end w-[100%] mt-[30rpx] mb-[20rpx]"},{default:i((()=>["1"!=e.status?(d(),o(l,{key:0,class:"text-[22rpx] text-[#303133] leading-[50rpx] px-[20rpx] border-[2rpx] border-solid border-[#999] rounded-full",onClick:t=>p(g)({url:"/addon/shop/pages/refund/detail",param:{order_refund_no:e.order_refund_no}})},{default:i((()=>[n("查看退款")])),_:2},1032,["onClick"])):1==e.is_enable_refund?(d(),o(l,{key:1,class:"text-[22rpx] text-[#303133] leading-[50rpx] px-[20rpx] border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx]",onClick:t=>{return a=e.order_goods_id,void g({url:"/addon/shop/pages/refund/apply",param:{order_id:J.value.order_id,order_goods_id:a}});var a}},{default:i((()=>[n("申请退款")])),_:2},1032,["onClick"])):c("v-if",!0)])),_:2},1024)):c("v-if",!0),c(" 商品的万能表单信息 "),e.form_record_id?(d(),o(l,{key:3,class:v({"diy-form-wrap":fe.value.length})},{default:i((()=>[u(D,{record_id:e.form_record_id,completeLayout:"style-2",onCallback:me},null,8,["record_id"])])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1024)))),128)),J.value.gift_goods.length?(d(),o(l,{key:0,class:"pt-[20rpx] bg-[#f9f9f9] mt-[20rpx] mx-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)]"},{default:i((()=>[(d(!0),_(f,null,m(J.value.gift_goods,((e,t)=>(d(),o(l,{class:"order-goods-item flex justify-between flex-wrap px-[var(--pad-sidebar-m)] pb-[20rpx]",key:t},{default:i((()=>[u(l,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:t=>ae(e.goods_id)},{default:i((()=>[u(b,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"120rpx",height:"120rpx",src:p(r)(e.goods_image_thumb_small?e.goods_image_thumb_small:""),model:"aspectFill"},{error:i((()=>[u(a,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:p(r)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1032,["onClick"]),u(l,{class:"ml-[16rpx] py-[8rpx] flex flex-1 flex-col justify-between"},{default:i((()=>[u(l,{class:"flex items-center"},{default:i((()=>[u(l,{class:"bg-[var(--primary-color-light)] whitespace-nowrap text-[var(--primary-color)] rounded-[6rpx] text-[22rpx] flex items-center justify-center w-[64rpx] h-[34rpx] mr-[6rpx]"},{default:i((()=>[n(" 赠品 ")])),_:1}),u(l,{class:"text-[26rpx] max-w-[400rpx] truncate leading-[40rpx] text-[#333]"},{default:i((()=>[n(x(e.goods_name),1)])),_:2},1024)])),_:2},1024),u(l,{class:"flex items-center"},{default:i((()=>[e.sku_name?(d(),o(l,{key:0,class:"text-[22rpx] text-[var(--text-color-light9)] truncate max-w-[400rpx] leading-[28rpx]"},{default:i((()=>[n(x(e.sku_name),1)])),_:2},1024)):c("v-if",!0),u(l,{class:"ml-[auto] font-400 text-[26rpx] text-[#303133]"},{default:i((()=>[u(s,null,{default:i((()=>[n("x")])),_:1}),u(s,null,{default:i((()=>[n(x(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):c("v-if",!0)])),_:1},8,["class","style"]),u(l,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[u(l,{class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("orderNo")),1)])),_:1}),u(l,{class:"flex items-center text-[28rpx]"},{default:i((()=>[u(s,null,{default:i((()=>[n(x(J.value.order_no),1)])),_:1}),u(s,{class:"w-[2rpx] h-[20rpx] bg-[#999] mx-[10rpx]"}),u(s,{class:"text-[var(--primary-color)]",onClick:t[1]||(t[1]=e=>p(T)(J.value.order_no))},{default:i((()=>[n("复制")])),_:1})])),_:1})])),_:1}),J.value.out_trade_no?(d(),o(l,{key:0,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("orderTradeNo")),1)])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.out_trade_no),1)])),_:1})])),_:1})):c("v-if",!0),u(l,{class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("createTime")),1)])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.create_time),1)])),_:1})])),_:1}),J.value.member_remark?(d(),o(l,{key:1,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx] w-[180rpx]"},{default:i((()=>[n(x(p(E)("memberRemark")),1)])),_:1}),u(l,{class:"text-[28rpx] flex-1 text-right"},{default:i((()=>[n(x(J.value.member_remark),1)])),_:1})])),_:1})):c("v-if",!0),u(l,{class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("deliveryType")),1)])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>{var e;return[n(x(J.value.delivery_type_name),1),J.value.order_delivery&&(null==(e=J.value.order_delivery[0])?void 0:e.third_delivery_name)&&"local_delivery"==J.value.delivery_type&&2!=J.value.status_name.status&&1!=J.value.status_name.status?(d(),o(s,{key:0},{default:i((()=>[n("-"+x(J.value.order_delivery[0].third_delivery_name),1)])),_:1})):c("v-if",!0)]})),_:1})])),_:1}),"local_delivery"==J.value.delivery_type&&J.value.buyer_ask_delivery_time?(d(),o(l,{key:2,class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("预约配送时间")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.buyer_ask_delivery_time),1)])),_:1})])),_:1})):c("v-if",!0),J.value.pay?(d(),o(l,{key:3,class:v(["card-template-item justify-between",{"!mb-[18rpx]":J.value.member_id!==J.value.pay.main_id&&2==J.value.pay.status}])},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("payTypeName")),1)])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.pay.type_name),1)])),_:1})])),_:1},8,["class"])):c("v-if",!0),J.value.pay&&J.value.member_id!==J.value.pay.main_id&&2==J.value.pay.status?(d(),o(l,{key:4,class:"card-template-item justify-end"},{default:i((()=>[u(l,{class:"friend-pay relative px-[20rpx] py-[12rpx] bg-[#F2F2F2] rounded-[10rpx] flex items-center"},{default:i((()=>[u(G,{src:p(r)(J.value.pay.pay_member_headimg),size:"20",leftIcon:"none","default-url":p(r)("static/resource/images/default_headimg.png")},null,8,["src","default-url"]),u(s,{class:"ml-[14rpx] text-[24rpx] using-hidden"},{default:i((()=>[n(x(J.value.pay.pay_member)+x(p(E)("helpPay")),1)])),_:1})])),_:1})])),_:1})):c("v-if",!0),J.value.pay?(d(),o(l,{key:5,class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("payTime")),1)])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.pay.pay_time),1)])),_:1})])),_:1})):c("v-if",!0)])),_:1}),J.value.order_delivery&&J.value.order_delivery[0]&&J.value.order_delivery[0].third_delivery_info&&"local_delivery"==J.value.delivery_type&&2!=J.value.status_name.status&&1!=J.value.status_name.status?(d(),o(l,{key:3,class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>{var e,t,a;return[(null==(e=J.value.order_delivery[0].third_delivery_info)?void 0:e.transporter_name)?(d(),o(l,{key:0,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("配送员")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>{var e;return[n(x(null==(e=J.value.order_delivery[0].third_delivery_info)?void 0:e.transporter_name),1)]})),_:1})])),_:1})):c("v-if",!0),(null==(t=J.value.order_delivery[0].third_delivery_info)?void 0:t.transporter_phone)?(d(),o(l,{key:1,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("配送员手机号")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>{var e;return[n(x(null==(e=J.value.order_delivery[0].third_delivery_info)?void 0:e.transporter_phone),1)]})),_:1})])),_:1})):c("v-if",!0),(null==(a=J.value.order_delivery[0].third_delivery_info)?void 0:a.status_name)?(d(),o(l,{key:2,class:"justify-between card-template-item"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("配送状态")])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>{var e;return[n(x(null==(e=J.value.order_delivery[0].third_delivery_info)?void 0:e.status_name),1)]})),_:1})])),_:1})):c("v-if",!0),J.value.order_delivery[0].third_delivery_name?(d(),o(l,{key:3,class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("deliveryType")),1)])),_:1}),u(l,{class:"text-[28rpx]"},{default:i((()=>["local_delivery"==J.value.delivery_type&&2!=J.value.status_name.status&&1!=J.value.status_name.status?(d(),o(s,{key:0},{default:i((()=>[n(x(J.value.order_delivery[0].third_delivery_name),1)])),_:1})):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0)]})),_:1})):c("v-if",!0),c(" 核销码 "),p(ue)?(d(),_(f,{key:4},[pe.value&&pe.value.length?(d(),o(l,{key:0,class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[pe.value.length>1?(d(),o(R,{key:0,class:"h-[450rpx]",circular:"","indicator-dots":"true"},{default:i((()=>[(d(!0),_(f,null,m(pe.value,((e,t)=>(d(),o(N,{key:t},{default:i((()=>[u(l,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(a,{src:e.qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:2},1024),u(l,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[n(x(e.code),1)])),_:2},1024),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t=>p(T)(e.code)},{default:i((()=>[n("复制")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(d(),_(f,{key:1},[u(l,{class:"flex flex-col items-center justify-center"},{default:i((()=>[u(a,{src:pe.value[0].qrcode,class:"w-[300rpx] h-[auto]",mode:"widthFix"},null,8,["src"])])),_:1}),u(l,{class:"flex items-center justify-center mt-[30rpx]"},{default:i((()=>[u(s,{class:"text-[28rpx] font-500"},{default:i((()=>[n(x(pe.value[0].code),1)])),_:1}),u(s,{class:"text-[var(--text-color-light6)] text-[24rpx] ml-[10rpx] border-[2rpx] border-solid border-[#666] bg-[#f7f7f7] px-[12rpx] py-[6rpx] rounded",onClick:t[2]||(t[2]=e=>p(T)(pe.value[0].code))},{default:i((()=>[n("复制")])),_:1})])),_:1})],64))])),_:1})):c("v-if",!0),u(l,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[u(l,{class:"title"},{default:i((()=>[n("核销信息")])),_:1}),u(l,{class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("核销次数")])),_:1}),u(l,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[n(x("剩余"+(ie.value.num-ie.value.verify_count)+"次")+"/"+x("共"+ie.value.num+"次"),1)])),_:1})])),_:1}),u(l,{class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("有效期")])),_:1}),u(l,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[n(x(ie.value.verify_expire_time?ie.value.verify_expire_time:"永久"),1)])),_:1})])),_:1})])),_:1})],64)):c("v-if",!0),c(" 待付款订单的万能表单信息 "),J.value.form_record_id?(d(),o(l,{key:5,class:v({"sidebar-margin mt-[var(--top-m)] card-template":ve.value.length})},{default:i((()=>[u(D,{record_id:J.value.form_record_id,completeLayout:"style-2",onCallback:ye},null,8,["record_id"])])),_:1},8,["class"])):c("v-if",!0),u(l,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:i((()=>[u(l,{class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("goodsMoney")),1)])),_:1}),u(l,{class:"price-font font-500"},{default:i((()=>[parseFloat(J.value.point)>0?(d(),o(s,{key:0,class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.point)+"积分",1)])),_:1})):c("v-if",!0),parseFloat(J.value.point)>0&&parseFloat(J.value.goods_money)?(d(),o(s,{key:1,class:"mx-[4rpx] text-[28rpx]"},{default:i((()=>[n("+")])),_:1})):c("v-if",!0),parseFloat(J.value.goods_money)||!parseFloat(J.value.point)?(d(),_(f,{key:2},[u(s,{class:"text-[28rpx]"},{default:i((()=>[n("￥")])),_:1}),u(s,{class:"text-[28rpx]"},{default:i((()=>[n(x(parseFloat(J.value.goods_money).toFixed(2).split(".")[0]),1)])),_:1}),u(s,{class:"text-[28rpx]"},{default:i((()=>[n("."+x(parseFloat(J.value.goods_money).toFixed(2).split(".")[1]),1)])),_:1})],64)):c("v-if",!0)])),_:1})])),_:1}),parseFloat(J.value.delivery_money)?(d(),o(l,{key:0,class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("deliveryMoney")),1)])),_:1}),u(l,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[n("￥"+x(parseFloat(J.value.delivery_money).toFixed(2)),1)])),_:1})])),_:1})):c("v-if",!0),c(' <view class=" card-template-item justify-between">\r\n                      <view class="text-[28rpx]">{{ t(\'discountMoney\') }}</view>\r\n                      <view class="price-font font-500 text-[28rpx]">\r\n                        -￥{{ parseFloat(detail.discount_money).toFixed(2) }}\r\n                      </view>\r\n                    </view> '),parseFloat(J.value.coupon_money)?(d(),o(l,{key:1,class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("优惠券优惠")])),_:1}),u(l,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[n("-￥"+x(parseFloat(J.value.coupon_money).toFixed(2)),1)])),_:1})])),_:1})):c("v-if",!0),parseFloat(J.value.manjian_discount_money)?(d(),o(l,{key:2,class:"card-template-item justify-between"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n("满减优惠")])),_:1}),u(l,{class:"price-font font-500 text-[28rpx]"},{default:i((()=>[n("-￥"+x(parseFloat(J.value.manjian_discount_money).toFixed(2)),1)])),_:1})])),_:1})):c("v-if",!0),u(l,{class:"card-template-item justify-between items-baseline"},{default:i((()=>[u(l,{class:"text-[28rpx]"},{default:i((()=>[n(x(p(E)("orderMoney")),1)])),_:1}),u(l,{class:"text-[var(--price-text-color)] price-font"},{default:i((()=>[parseFloat(J.value.point)>0?(d(),o(s,{key:0,class:"text-[28rpx]"},{default:i((()=>[n(x(J.value.point)+"积分",1)])),_:1})):c("v-if",!0),parseFloat(J.value.point)>0&&parseFloat(J.value.order_money)?(d(),o(s,{key:1,class:"mx-[4rpx] text-[28rpx]"},{default:i((()=>[n("+")])),_:1})):c("v-if",!0),parseFloat(J.value.order_money)||!parseFloat(J.value.point)?(d(),o(s,{key:2,class:"text-[28rpx]"},{default:i((()=>[n("￥"+x(parseFloat(J.value.order_money).toFixed(2)),1)])),_:1})):c("v-if",!0)])),_:1})])),_:1})])),_:1}),u(l,{class:"flex z-2 justify-between items-center bg-[#fff] fixed left-0 right-0 bottom-0 min-h-[100rpx] pl-[30rpx] pr-[20rpx] flex-wrap pb-ios"},{default:i((()=>[u(l,{class:"flex"},{default:i((()=>[u(l,{class:"flex mr-[34rpx] flex-col justify-center items-center",onClick:t[3]||(t[3]=e=>se("index"))},{default:i((()=>[u(l,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[36rpx]"}),u(s,{class:"text-[20rpx] mt-[10rpx]"},{default:i((()=>[n(x(p(E)("index")),1)])),_:1})])),_:1})])),_:1}),u(l,{class:"flex justify-end"},{default:i((()=>[oe(J.value)?(d(),o(l,{key:0,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx] text-[var(--text-color-light6)]",onClick:t[4]||(t[4]=e=>se("logistics"))},{default:i((()=>[n(x(p(E)("logisticsTracking")),1)])),_:1})):c("v-if",!0),1==J.value.status?(d(),o(l,{key:1,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center text-center border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx] text-[var(--text-color-light6)]",onClick:t[5]||(t[5]=e=>se("close"))},{default:i((()=>[n(x(p(E)("orderClose")),1)])),_:1})):c("v-if",!0),1==J.value.status?(d(),o(l,{key:2,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center text-center text-[#fff] primary-btn-bg rounded-full ml-[20rpx]",onClick:t[6]||(t[6]=e=>se("pay"))},{default:i((()=>[n(x(p(E)("topay")),1)])),_:1})):c("v-if",!0),3==J.value.status?(d(),o(l,{key:3,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center text-center text-[#fff] primary-btn-bg rounded-full ml-[20rpx]",onClick:t[7]||(t[7]=e=>se("finish"))},{default:i((()=>[n(x(p(E)("orderFinish")),1)])),_:1})):c("v-if",!0),5==J.value.status&&X.value?(d(),_(f,{key:4},[1==J.value.is_evaluate||1!=J.value.is_evaluate&&1==W.value.is_evaluate?(d(),o(l,{key:0,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#999] rounded-full ml-[20rpx] !text-[var(--text-color-light6)]",onClick:t[8]||(t[8]=e=>se("evaluate"))},{default:i((()=>[n(x(1==J.value.is_evaluate?p(E)("selectedEvaluate"):p(E)("evaluate")),1)])),_:1})):c("v-if",!0)],64)):c("v-if",!0)])),_:1})])),_:1})])),_:1})),u(l,{class:"tab-bar-placeholder"}),u(P,{ref_key:"payRef",ref:le,onClose:e.payClose},null,8,["onClose"]),u(A,{ref_key:"materialRef",ref:re},null,512),c(" 满减 "),u(H,{ref_key:"manjianShowRef",ref:Y},null,512)])),_:1})),u(O,{loading:K.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-5e60733e"]]);export{K as default};

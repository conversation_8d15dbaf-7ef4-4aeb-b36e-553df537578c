import{d as e,r as a,N as t,Q as l,o,c as r,w as i,f as d,v as s,b as c,n as u,k as m}from"./index-4b8dc7db.js";import{d as n}from"./index.106c0c40.js";import{c as p}from"./diy_form.03750fb6.js";const f=e({__name:"index",props:["record_id","completeLayout"],emits:["callback"],setup(e,{emit:f}){const _=e,y=a(!0),v=t({global:{},value:[]});return l((()=>{p({record_id:_.record_id}).then((e=>{v.global.completeLayout=_.completeLayout||"style-1",e.data.recordsFieldList&&e.data.recordsFieldList.forEach((e=>{let a={id:e.field_key,componentName:e.field_type,pageStyle:"",viewFormDetail:!0,field:{name:e.field_name,value:e.handle_field_value,required:e.field_required,unique:e.field_unique,privacyProtection:e.privacy_protection},margin:{top:0,bottom:0,both:0}};v.value.push(a)})),f("callback",e.data.recordsFieldList),y.value=!1})).catch((()=>{y.value=!1,f("callback",[])}))})),(e,a)=>{const t=m;return o(),r(t,{style:u(e.themeColor())},{default:i((()=>[d(c(t,{class:"diy-template-wrap"},{default:i((()=>[c(n,{ref:"diyGroupRef",data:v},null,8,["data"])])),_:1},512),[[s,!y.value]])])),_:1},8,["style"])}}});export{f as _};

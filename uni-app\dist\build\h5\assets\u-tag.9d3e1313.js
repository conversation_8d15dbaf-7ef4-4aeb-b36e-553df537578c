import{a4 as t,a5 as e,a6 as o,aN as l,i as a,j as s,o as i,c as r,w as n,b as c,T as p,C as g,n as u,$ as d,g as f,A as y,B as m,G as h,k as _,I as C}from"./index-dd56d0cc.js";import{_ as b}from"./u-icon.5895f8fc.js";import{_ as x}from"./u-transition.ab3d3894.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";const S=z({name:"u-tag",mixins:[e,o,{props:{type:{type:String,default:()=>t.tag.type},disabled:{type:[Boolean,String],default:()=>t.tag.disabled},size:{type:String,default:()=>t.tag.size},shape:{type:String,default:()=>t.tag.shape},text:{type:[String,Number],default:()=>t.tag.text},bgColor:{type:String,default:()=>t.tag.bgColor},color:{type:String,default:()=>t.tag.color},borderColor:{type:String,default:()=>t.tag.borderColor},closeColor:{type:String,default:()=>t.tag.closeColor},name:{type:[String,Number],default:()=>t.tag.name},plainFill:{type:Boolean,default:()=>t.tag.plainFill},plain:{type:Boolean,default:()=>t.tag.plain},closable:{type:Boolean,default:()=>t.tag.closable},show:{type:Boolean,default:()=>t.tag.show},icon:{type:String,default:()=>t.tag.icon},iconColor:{type:String,default:()=>t.tag.iconColor}}}],data:()=>({}),computed:{style(){const t={};return this.bgColor&&(t.backgroundColor=this.bgColor),this.color&&(t.color=this.color),this.borderColor&&(t.borderColor=this.borderColor),t},textColor(){const t={};return this.color&&(t.color=this.color),t},imgStyle(){const t="large"===this.size?"17px":"medium"===this.size?"15px":"13px";return{width:t,height:t}},closeSize(){return"large"===this.size?15:"medium"===this.size?13:12},iconSize(){return"large"===this.size?21:"medium"===this.size?19:16},elIconColor(){return this.iconColor?this.iconColor:this.plain?this.type:"#ffffff"}},emits:["click","close"],methods:{testImage:l.image,closeHandler(){this.$emit("close",this.name)},clickHandler(){this.$emit("click",this.name)}}},[["render",function(t,e,o,l,z,S){const k=h,$=a(s("u-icon"),b),w=_,j=C,B=a(s("u-transition"),x);return i(),r(B,{mode:"fade",show:t.show,style:{display:"inline-flex"}},{default:n((()=>[c(w,{class:"u-tag-wrapper cursor-pointer"},{default:n((()=>[c(w,{class:p(["u-tag",[`u-tag--${t.shape}`,!t.plain&&`u-tag--${t.type}`,t.plain&&`u-tag--${t.type}--plain`,`u-tag--${t.size}`,t.plain&&t.plainFill&&`u-tag--${t.type}--plain--fill`]]),onClick:g(S.clickHandler,["stop"]),style:u([{marginRight:t.closable?"10px":0,marginTop:t.closable?"10px":0},S.style])},{default:n((()=>[d(t.$slots,"icon",{},(()=>[t.icon?(i(),r(w,{key:0,class:"u-tag__icon"},{default:n((()=>[S.testImage(t.icon)?(i(),r(k,{key:0,src:t.icon,style:u([S.imgStyle])},null,8,["src","style"])):(i(),r($,{key:1,color:S.elIconColor,name:t.icon,size:S.iconSize},null,8,["color","name","size"]))])),_:1})):f("v-if",!0)]),!0),c(j,{class:p(["u-tag__text",[`u-tag__text--${t.type}`,t.plain&&`u-tag__text--${t.type}--plain`,`u-tag__text--${t.size}`]]),style:u([S.textColor])},{default:n((()=>[y(m(t.text),1)])),_:1},8,["style","class"])])),_:3},8,["class","onClick","style"]),t.closable?(i(),r(w,{key:0,class:p(["u-tag__close",[`u-tag__close--${t.size}`]]),onClick:g(S.closeHandler,["stop"]),style:u({backgroundColor:t.closeColor})},{default:n((()=>[c($,{name:"close",size:S.closeSize,color:"#ffffff"},null,8,["size"])])),_:1},8,["class","onClick","style"])):f("v-if",!0)])),_:3})])),_:3},8,["show"])}],["__scopeId","data-v-e74c230c"]]);export{S as _};

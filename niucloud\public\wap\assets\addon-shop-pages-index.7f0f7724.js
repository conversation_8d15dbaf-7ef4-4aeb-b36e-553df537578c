import{_ as o}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{d as t,u as e,r as s,o as r,c as i,w as p,b as a,e as u,f as m,v as n,g as _,n as j,i as l,j as d,k as c}from"./index-4b8dc7db.js";import{u as g}from"./useDiy.22bb3fb6.js";import{d as y}from"./index.106c0c40.js";import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.11ef83b8.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-icon.33002907.js";/* empty css                                                               */import"./u-popup.e2790691.js";import"./u-safe-bottom.987908cd.js";import"./top-tabbar.59f1aa86.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.5d9e7fd9.js";import"./u-checkbox.598bfa18.js";import"./u-checkbox-group.0c3be417.js";import"./u-button.7aa0e948.js";import"./u-input.7a7ec88f.js";import"./u-picker.0f983101.js";import"./u-upload.899e5f6a.js";import"./u-radio-group.b3c6fbb7.js";import"./diy_form.03750fb6.js";import"./u-action-sheet.8e2525cc.js";import"./u-line.b0d89d5b.js";import"./u-avatar.db5bcfa1.js";import"./u-text.40d19739.js";import"./u-parse.1f53da94.js";import"./tabbar.805b8203.js";import"./u-badge.e47151e6.js";import"./u-tabbar.bc2ea30a.js";import"./category.146302da.js";import"./common.eabc72c7.js";import"./project.df03875d.js";import"./index.3da4012a.js";import"./u--image.892273b2.js";import"./u-image.18977c35.js";/* empty css                                                                */import"./goods.f34d2594.js";import"./useGoods.0898f296.js";import"./add-cart-popup.da2aa6c6.js";import"./u-number-box.cd35fabd.js";import"./coupon.d71b475a.js";import"./point.a8a5a12b.js";import"./rank.1f5790d0.js";import"./bind-mobile.259f3837.js";import"./u-form.a144799c.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-modal.775e667c.js";import"./newcomer.2ec60692.js";import"./order.b56aabaa.js";const f=b(t({__name:"index",setup(t){const{setShare:b}=e(),f=g({name:"DIY_SHOP_INDEX"}),v=s(null);return s(null),f.onLoad(),f.onShow((o=>{var t;let e=o.share?JSON.parse(o.share):null;b(e),null==(t=v.value)||t.refresh()})),f.onHide(),f.onUnload(),f.onPageScroll(),(t,e)=>{const s=l(d("loading-page"),o),g=c;return r(),i(g,{style:j(t.themeColor())},{default:p((()=>[a(s,{loading:u(f).getLoading()},null,8,["loading"]),m(a(g,null,{default:p((()=>[_(" 自定义模板渲染 "),a(g,{class:"diy-template-wrap bg-index",style:j(u(f).pageStyle())},{default:p((()=>[a(y,{ref_key:"diyGroupRef",ref:v,data:u(f).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[n,!u(f).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-9ebb8cc5"]]);export{f as default};

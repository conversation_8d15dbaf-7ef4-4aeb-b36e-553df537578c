import{_ as t}from"./u-transition.5763ee65.js";import{a4 as o,a5 as e,a6 as s,bc as a,a8 as l,i,j as r,o as n,c as u,w as p,$ as d,a7 as c,bd as m,n as y,k as f,g as h,b,C as v,T as g}from"./index-4b8dc7db.js";import{_}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as k}from"./u-icon.33002907.js";import{_ as S}from"./u-safe-bottom.987908cd.js";const C=_({name:"u-overlay",mixins:[e,s,{props:{show:{type:Boolean,default:()=>o.overlay.show},zIndex:{type:[String,Number],default:()=>o.overlay.zIndex},duration:{type:[String,Number],default:()=>o.overlay.duration},opacity:{type:[String,Number],default:()=>o.overlay.opacity}}}],computed:{overlayStyle(){const t={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return a(t,l(this.customStyle))}},emits:["click"],methods:{clickHandler(){this.$emit("click")}}},[["render",function(o,e,s,a,l,c){const m=i(r("u-transition"),t);return n(),u(m,{show:o.show,"custom-class":"u-overlay",duration:o.duration,"custom-style":c.overlayStyle,onClick:c.clickHandler},{default:p((()=>[d(o.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick"])}],["__scopeId","data-v-b2ae91aa"]]);const I=_({name:"u-status-bar",mixins:[e,s,{props:{bgColor:{type:String,default:()=>o.statusBar.bgColor}}}],data:()=>({}),computed:{style(){const t={};return t.height=c(m().statusBarHeight,"px"),t.backgroundColor=this.bgColor,a(t,l(this.customStyle))}}},[["render",function(t,o,e,s,a,l){const i=f;return n(),u(i,{style:y([l.style]),class:"u-status-bar"},{default:p((()=>[d(t.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-58963a5e"]]);const x=_({name:"u-popup",mixins:[e,s,{props:{show:{type:Boolean,default:()=>o.popup.show},overlay:{type:Boolean,default:()=>o.popup.overlay},mode:{type:String,default:()=>o.popup.mode},duration:{type:[String,Number],default:()=>o.popup.duration},closeable:{type:Boolean,default:()=>o.popup.closeable},overlayStyle:{type:[Object,String],default:()=>o.popup.overlayStyle},closeOnClickOverlay:{type:Boolean,default:()=>o.popup.closeOnClickOverlay},zIndex:{type:[String,Number],default:()=>o.popup.zIndex},safeAreaInsetBottom:{type:Boolean,default:()=>o.popup.safeAreaInsetBottom},safeAreaInsetTop:{type:Boolean,default:()=>o.popup.safeAreaInsetTop},closeIconPos:{type:String,default:()=>o.popup.closeIconPos},round:{type:[Boolean,String,Number],default:()=>o.popup.round},zoom:{type:Boolean,default:()=>o.popup.zoom},bgColor:{type:String,default:()=>o.popup.bgColor},overlayOpacity:{type:[Number,String],default:()=>o.popup.overlayOpacity}}}],data(){return{overlayDuration:this.duration+50}},watch:{show(t,o){}},computed:{transitionStyle(){const t={zIndex:this.zIndex,position:"fixed",display:"flex"};return t[this.mode]=0,"left"===this.mode||"right"===this.mode?a(t,{bottom:0,top:0}):"top"===this.mode||"bottom"===this.mode?a(t,{left:0,right:0}):"center"===this.mode?a(t,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0}):void 0},contentStyle(){const t={};if(m(),"center"!==this.mode&&(t.flex=1),this.bgColor&&(t.backgroundColor=this.bgColor),this.round){const o=c(this.round);"top"===this.mode?(t.borderBottomLeftRadius=o,t.borderBottomRightRadius=o):"bottom"===this.mode?(t.borderTopLeftRadius=o,t.borderTopRightRadius=o):"center"===this.mode&&(t.borderRadius=o)}return a(t,l(this.customStyle))},position(){return"center"===this.mode?this.zoom?"fade-zoom":"fade":"left"===this.mode?"slide-left":"right"===this.mode?"slide-right":"bottom"===this.mode?"slide-up":"top"===this.mode?"slide-down":void 0}},emits:["open","close","click"],methods:{overlayClick(){this.closeOnClickOverlay&&this.$emit("close")},close(t){this.$emit("close")},afterEnter(){this.$emit("open")},clickHandler(){"center"===this.mode&&this.overlayClick(),this.$emit("click")}}},[["render",function(o,e,s,a,l,c){const m=i(r("u-overlay"),C),_=i(r("u-status-bar"),I),x=i(r("u-icon"),k),w=f,z=i(r("u-safe-bottom"),S),B=i(r("u-transition"),t);return n(),u(w,{class:"u-popup"},{default:p((()=>[o.overlay?(n(),u(m,{key:0,show:o.show,onClick:c.overlayClick,zIndex:o.zIndex,duration:l.overlayDuration,customStyle:o.overlayStyle,opacity:o.overlayOpacity},null,8,["show","onClick","zIndex","duration","customStyle","opacity"])):h("v-if",!0),b(B,{show:o.show,customStyle:c.transitionStyle,mode:c.position,duration:o.duration,onAfterEnter:c.afterEnter,onClick:c.clickHandler},{default:p((()=>[b(w,{class:"u-popup__content",style:y([c.contentStyle]),onClick:v(o.noop,["stop"])},{default:p((()=>[o.safeAreaInsetTop?(n(),u(_,{key:0})):h("v-if",!0),d(o.$slots,"default",{},void 0,!0),o.closeable?(n(),u(w,{key:1,onClick:v(c.close,["stop"]),class:g(["u-popup__content__close",["u-popup__content__close--"+o.closeIconPos]]),"hover-class":"u-popup__content__close--hover","hover-stay-time":"150"},{default:p((()=>[b(x,{name:"close",color:"#909399",size:"18",bold:""})])),_:1},8,["onClick","class"])):h("v-if",!0),o.safeAreaInsetBottom?(n(),u(z,{key:2})):h("v-if",!0)])),_:3},8,["style","onClick"])])),_:3},8,["show","customStyle","mode","duration","onAfterEnter","onClick"])])),_:3})}],["__scopeId","data-v-934b81cb"]]);export{x as _,C as a,I as b};

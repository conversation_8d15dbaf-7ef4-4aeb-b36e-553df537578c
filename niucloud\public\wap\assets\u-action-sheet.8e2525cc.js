import{a4 as e,a5 as t,a6 as o,a7 as a,bc as s,a8 as i,o as n,c as l,n as c,k as r,i as p,j as u,w as d,b as h,A as m,B as g,C as _,g as f,$ as y,R as S,S as k,a3 as v,I as b}from"./index-4b8dc7db.js";import{_ as C}from"./u-icon.33002907.js";import{_ as T}from"./u-line.b0d89d5b.js";import{_ as B}from"./u-loading-icon.11ef83b8.js";import{_ as w}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as $}from"./u-popup.e2790691.js";const x={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}},A={props:{openType:String},methods:{onGetUserInfo(e){this.$emit("getuserinfo",e.detail)},onContact(e){this.$emit("contact",e.detail)},onGetPhoneNumber(e){this.$emit("getphonenumber",e.detail)},onError(e){this.$emit("error",e.detail)},onLaunchApp(e){this.$emit("launchapp",e.detail)},onOpenSetting(e){this.$emit("opensetting",e.detail)}}};const I=w({name:"u-gap",mixins:[t,o,{props:{bgColor:{type:String,default:()=>e.gap.bgColor},height:{type:[String,Number],default:()=>e.gap.height},marginTop:{type:[String,Number],default:()=>e.gap.marginTop},marginBottom:{type:[String,Number],default:()=>e.gap.marginBottom}}}],computed:{gapStyle(){const e={backgroundColor:this.bgColor,height:a(this.height),marginTop:a(this.marginTop),marginBottom:a(this.marginBottom)};return s(e,i(this.customStyle))}}},[["render",function(e,t,o,a,s,i){const p=r;return n(),l(p,{class:"u-gap",style:c([i.gapStyle])},null,8,["style"])}],["__scopeId","data-v-72d2fb6e"]]);const O=w({name:"u-action-sheet",mixins:[A,x,o,{props:{show:{type:Boolean,default:()=>e.actionSheet.show},title:{type:String,default:()=>e.actionSheet.title},description:{type:String,default:()=>e.actionSheet.description},actions:{type:Array,default:()=>e.actionSheet.actions},cancelText:{type:String,default:()=>e.actionSheet.cancelText},closeOnClickAction:{type:Boolean,default:()=>e.actionSheet.closeOnClickAction},safeAreaInsetBottom:{type:Boolean,default:()=>e.actionSheet.safeAreaInsetBottom},openType:{type:String,default:()=>e.actionSheet.openType},closeOnClickOverlay:{type:Boolean,default:()=>e.actionSheet.closeOnClickOverlay},round:{type:[Boolean,String,Number],default:()=>e.actionSheet.round}}}],data:()=>({}),computed:{itemStyle(){return e=>{let t={};return this.actions[e].color&&(t.color=this.actions[e].color),this.actions[e].fontSize&&(t.fontSize=a(this.actions[e].fontSize)),this.actions[e].disabled&&(t.color="#c0c4cc"),t}}},emits:["close","select"],methods:{closeHandler(){this.closeOnClickOverlay&&this.$emit("close")},cancel(){this.$emit("close")},selectHandler(e){const t=this.actions[e];!t||t.disabled||t.loading||(this.$emit("select",t),this.closeOnClickAction&&this.$emit("close"))}}},[["render",function(e,t,o,a,s,i){const w=b,x=p(u("u-icon"),C),A=r,O=p(u("u-line"),T),j=p(u("u-loading-icon"),B),z=p(u("u-gap"),I),N=p(u("u-popup"),$);return n(),l(N,{show:e.show,mode:"bottom",onClose:i.closeHandler,safeAreaInsetBottom:e.safeAreaInsetBottom,round:e.round},{default:d((()=>[h(A,{class:"u-action-sheet"},{default:d((()=>[e.title?(n(),l(A,{key:0,class:"u-action-sheet__header"},{default:d((()=>[h(w,{class:"u-action-sheet__header__title u-line-1"},{default:d((()=>[m(g(e.title),1)])),_:1}),h(A,{class:"u-action-sheet__header__icon-wrap",onClick:_(i.cancel,["stop"])},{default:d((()=>[h(x,{name:"close",size:"17",color:"#c8c9cc",bold:""})])),_:1},8,["onClick"])])),_:1})):f("v-if",!0),e.description?(n(),l(w,{key:1,class:"u-action-sheet__description",style:c([{marginTop:`${e.title&&e.description?0:"18px"}`}])},{default:d((()=>[m(g(e.description),1)])),_:1},8,["style"])):f("v-if",!0),y(e.$slots,"default",{},(()=>[e.description?(n(),l(O,{key:0})):f("v-if",!0),h(A,{class:"u-action-sheet__item-wrap"},{default:d((()=>[(n(!0),S(k,null,v(e.actions,((t,o)=>(n(),l(A,{key:o},{default:d((()=>[h(A,{class:"u-action-sheet__item-wrap__item",onClick:_((e=>i.selectHandler(o)),["stop"]),"hover-class":t.disabled||t.loading?"":"u-action-sheet--hover","hover-stay-time":150},{default:d((()=>[t.loading?(n(),l(j,{key:1,"custom-class":"van-action-sheet__loading",size:"18",mode:"circle"})):(n(),S(k,{key:0},[h(w,{class:"u-action-sheet__item-wrap__item__name",style:c([i.itemStyle(o)])},{default:d((()=>[m(g(t.name),1)])),_:2},1032,["style"]),t.subname?(n(),l(w,{key:0,class:"u-action-sheet__item-wrap__item__subname"},{default:d((()=>[m(g(t.subname),1)])),_:2},1024)):f("v-if",!0)],64))])),_:2},1032,["onClick","hover-class"]),o!==e.actions.length-1?(n(),l(O,{key:0})):f("v-if",!0)])),_:2},1024)))),128))])),_:1})]),!0),e.cancelText?(n(),l(z,{key:2,bgColor:"#eaeaec",height:"6"})):f("v-if",!0),h(A,{class:"u-action-sheet__item-wrap__item u-action-sheet__cancel","hover-class":"u-action-sheet--hover"},{default:d((()=>[e.cancelText?(n(),l(w,{key:0,onTouchmove:t[0]||(t[0]=_((()=>{}),["stop","prevent"])),"hover-stay-time":150,class:"u-action-sheet__cancel-text",onClick:i.cancel},{default:d((()=>[m(g(e.cancelText),1)])),_:1},8,["onClick"])):f("v-if",!0)])),_:1})])),_:3})])),_:3},8,["show","onClose","safeAreaInsetBottom","round"])}],["__scopeId","data-v-ad7e8d01"]]);export{O as _};

import{a4 as e,a5 as a,a6 as l,a7 as t,a8 as s,i,j as r,o as d,c as o,w as u,b as n,n as c,T as p,k as v,d as m,r as f,s as h,cf as _,l as g,b3 as y,y as b,e as x,A as w,B as C,C as S,F as k,ce as V,cg as A,a as j,I as z,H as $}from"./index-4b8dc7db.js";import{_ as P}from"./u-input.7a7ec88f.js";import{_ as I,a as q}from"./u-form.a144799c.js";import{_ as B}from"./u-loading-icon.11ef83b8.js";import{_ as N}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as T}from"./area-select.vue_vue_type_script_setup_true_lang.5d9e7fd9.js";import{m as U}from"./manifest.ed582bbb.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-line.b0d89d5b.js";import"./u-popup.e2790691.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-safe-bottom.987908cd.js";const W=N({name:"u-switch",mixins:[a,l,{props:{loading:{type:Boolean,default:()=>e.switch.loading},disabled:{type:Boolean,default:()=>e.switch.disabled},size:{type:[String,Number],default:()=>e.switch.size},activeColor:{type:String,default:()=>e.switch.activeColor},inactiveColor:{type:String,default:()=>e.switch.inactiveColor},modelValue:{type:[Boolean,String,Number],default:()=>e.switch.value},activeValue:{type:[String,Number,Boolean],default:()=>e.switch.activeValue},inactiveValue:{type:[String,Number,Boolean],default:()=>e.switch.inactiveValue},asyncChange:{type:Boolean,default:()=>e.switch.asyncChange},space:{type:[String,Number],default:()=>e.switch.space}}}],watch:{modelValue:{immediate:!0,handler(e){e!==this.inactiveValue&&this.activeValue}}},data:()=>({bgColor:"#ffffff"}),computed:{isActive(){return this.modelValue===this.activeValue},switchStyle(){let e={};return e.width=t(2*this.size+2),e.height=t(Number(this.size)+2),this.customInactiveColor&&(e.borderColor="rgba(0, 0, 0, 0)"),e.backgroundColor=this.isActive?this.activeColor:this.inactiveColor,e},nodeStyle(){let e={};e.width=t(this.size-this.space),e.height=t(this.size-this.space);const a=this.isActive?t(this.space):t(this.size);return e.transform=`translateX(-${a})`,e},bgStyle(){let e={};return e.width=t(2*Number(this.size)-this.size/2),e.height=t(this.size),e.backgroundColor=this.inactiveColor,e.transform=`scale(${this.isActive?0:1})`,e},customInactiveColor(){return"#fff"!==this.inactiveColor&&"#ffffff"!==this.inactiveColor}},emits:["update:modelValue","change"],methods:{addStyle:s,clickHandler(){if(!this.disabled&&!this.loading){const e=this.isActive?this.inactiveValue:this.activeValue;this.asyncChange||this.$emit("update:modelValue",e),this.$nextTick((()=>{this.$emit("change",e)}))}}}},[["render",function(e,a,l,t,s,m){const f=v,h=i(r("u-loading-icon"),B);return d(),o(f,{class:p(["u-switch cursor-pointer",[e.disabled&&"u-switch--disabled"]]),style:c([m.switchStyle,m.addStyle(e.customStyle)]),onClick:m.clickHandler},{default:u((()=>[n(f,{class:"u-switch__bg",style:c([m.bgStyle])},null,8,["style"]),n(f,{class:p(["u-switch__node",[e.modelValue&&"u-switch__node--on"]]),style:c([m.nodeStyle]),ref:"u-switch__node"},{default:u((()=>[n(h,{show:e.loading,mode:"circle",timingFunction:"linear",color:e.modelValue?e.activeColor:"#AAABAD",size:.6*e.size},null,8,["show","color","size"])])),_:1},8,["class","style"])])),_:1},8,["class","style","onClick"])}],["__scopeId","data-v-09bf48b9"]]),E=N(m({__name:"address_edit",setup(e){const a=f({id:0,name:"",mobile:"",province_id:0,city_id:0,district_id:0,lat:"",lng:"",address:"",address_name:"",full_address:"",is_default:0,area:""}),l=f(),t=f(null),s=f(""),m=f(!1),B=f(!1),N=f("address"),E=f(2);f(null),h((e=>{E.value=e.isSelectMap||"";const l=uni.getStorageSync("selectAddressCallback");if(e.id)_(e.id).then((e=>{e.data&&Object.assign(a.value,e.data),l&&(N.value="express"==l.delivery?"address":"locationAddress")}));else if(e.name){uni.getStorageSync("addressInfo")&&Object.assign(a.value,uni.getStorageSync("addressInfo")),a.value.address=e.name,X(e.latng);var t=Z("latng").split(",");a.value.lat=t[0],a.value.lng=t[1]}s.value=e.source||"",l&&(N.value="express"==l.delivery?"address":"locationAddress")}));const M=g((()=>({name:{type:"string",required:!0,message:k("namePlaceholder"),trigger:["blur","change"]},mobile:[{type:"string",required:!0,message:k("mobilePlaceholder"),trigger:["blur","change"]},{validator(e,a,l){/^1[3-9]\d{9}$/.test(a)?l():l(new Error(k("mobileError")))}}],area:{validator(){let e=!0;return uni.$u.test.isEmpty(a.value.area)&&uni.$u.test.isEmpty(a.value.address_name)&&(e=!1),e},message:k("selectAreaPlaceholder")},address:{type:"string",required:!0,message:k("addressPlaceholder"),trigger:["blur","change"]}}))),H=()=>{B.value=!0,l.value.open()},R=e=>{var l,t,s,i,r,d,o,u,n;!B.value||a.value.province_id!=(null==(l=e.province)?void 0:l.id)&&a.value.city_id==(null==(t=e.city)?void 0:t.id)&&a.value.district_id==(null==(s=e.district)?void 0:s.id)||(a.value.lat="",a.value.lng=""),a.value.province_id=(null==(i=e.province)?void 0:i.id)||0,a.value.city_id=(null==(r=e.city)?void 0:r.id)||0,a.value.district_id=(null==(d=e.district)?void 0:d.id)||0,a.value.area=`${(null==(o=e.province)?void 0:o.name)||""}${(null==(u=e.city)?void 0:u.name)||""}${(null==(n=e.district)?void 0:n.name)||""}`,B.value=!1},D=f(!1),F=()=>{const e=a.value.id?V:A;t.value.validate().then((()=>{if(!D.value){if(D.value=!0,m.value=!0,a.value.full_address=a.value.area+a.value.address,1==E.value&&!a.value.lat&&!a.value.lng)return b({title:"缺少经纬度，请在地图上重新选点",icon:"none"}),D.value=!1,m.value=!1,!1;e(a.value).then((e=>{D.value=!1,setTimeout((()=>{if(m.value=!1,"shop_order_payment"==s.value){const l=uni.getStorageSync("selectAddressCallback");l&&(l.address_id=e.data.id||a.value.id,uni.setStorage({key:"selectAddressCallback",data:l,success(){j({url:l.back,mode:"redirectTo"})}}))}else j({url:"/app/pages/member/address",mode:"redirectTo",param:{source:s.value}})}),1e3)})).catch((()=>{D.value=!1,m.value=!1}))}}))},O=()=>{var e=a.value;uni.setStorageSync("addressInfo",e);let l=location.origin+location.pathname+"?source="+s.value;E.value&&(l=l+"&isSelectMap="+E.value),window.location.href="https://apis.map.qq.com/tools/locpicker?search=1&type=0&backurl="+encodeURIComponent(l)+"&key="+U.h5.sdkConfigs.maps.qqmap.key+"&referer=myapp"},X=e=>{y({latlng:e}).then((e=>{e.data?(a.value.full_address="",a.value.full_address+=null!=e.data.province?e.data.province:"",a.value.full_address+=null!=e.data.city?""+e.data.city:"",a.value.full_address+=null!=e.data.district?""+e.data.district:"",a.value.address_name=a.value.full_address.replace(/-/g,""),a.value.area=e.data.province+e.data.city+e.data.district||e.data.full_address,a.value.province_id=null!=e.data.province_id?e.data.province_id:0,a.value.city_id=null!=e.data.city_id?e.data.city_id:0,a.value.district_id=null!=e.data.district_id?e.data.district_id:0):b({title:e.msg,icon:"none"})}))},Z=e=>{for(var a=window.location.search.substring(1).split("&"),l=0;l<a.length;l++){var t=a[l].split("=");if(t[0]==e)return t[1]}return!1};return(e,s)=>{const f=i(r("u-input"),P),h=i(r("u-form-item"),I),_=v,g=z,y=i(r("u-switch"),W),b=i(r("u-form"),q),V=$,A=i(r("area-select"),T);return d(),o(_,{class:"bg-[var(--page-bg-color)] min-h-[100vh] overflow-hidden address-edit",style:c(e.themeColor())},{default:u((()=>[n(b,{labelPosition:"left",model:a.value,errorType:"toast",rules:x(M),ref_key:"formRef",ref:t},{default:u((()=>[n(_,{class:"sidebar-margin card-template mt-[var(--top-m)] py-[20rpx]"},{default:u((()=>[n(_,null,{default:u((()=>[n(h,{label:x(k)("name"),prop:"name",labelWidth:"200rpx"},{default:u((()=>[n(f,{fontSize:"28rpx",modelValue:a.value.name,"onUpdate:modelValue":s[0]||(s[0]=e=>a.value.name=e),modelModifiers:{trim:!0},border:"none",clearable:"",maxlength:"25",placeholderStyle:"color: #888",placeholder:x(k)("namePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),n(_,{class:"mt-[16rpx]"},{default:u((()=>[n(h,{label:x(k)("mobile"),prop:"mobile",labelWidth:"200rpx"},{default:u((()=>[n(f,{fontSize:"28rpx",modelValue:a.value.mobile,"onUpdate:modelValue":s[1]||(s[1]=e=>a.value.mobile=e),modelModifiers:{trim:!0},maxlength:"11",border:"none",clearable:"",placeholder:x(k)("mobilePlaceholder"),placeholderStyle:"color: #888"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),n(_,{class:"mt-[16rpx]"},{default:u((()=>[n(h,{label:x(k)("selectArea"),prop:"area",labelWidth:"200rpx"},{default:u((()=>["address"==N.value&&1!=E.value?(d(),o(_,{key:0,class:"flex w-full items-center h-[52rpx]",onClick:H},{default:u((()=>[a.value.area?(d(),o(_,{key:1,class:"text-[28rpx] flex-1 leading-[1.4]"},{default:u((()=>[w(C(a.value.area),1)])),_:1})):(d(),o(_,{key:0,class:"text-[#888] text-[28rpx] flex-1"},{default:u((()=>[w(C(x(k)("selectAreaPlaceholder")),1)])),_:1})),n(_,{onClick:S(O,["stop"]),class:"flex items-center"},{default:u((()=>[n(g,{class:"nc-iconfont nc-icon-dizhiguanliV6xx mr-[4rpx] text-[32rpx] text-[var(--primary-color)]"}),n(g,{class:"text-[24rpx] whitespace-nowrap text-[var(--primary-color)]"},{default:u((()=>[w("定位")])),_:1})])),_:1},8,["onClick"])])),_:1})):(d(),o(_,{key:1,class:"flex justify-between items-center flex-1 h-[52rpx]",onClick:O},{default:u((()=>[a.value.area||a.value.address_name?(d(),o(_,{key:0,class:"text-[28rpx] text-[#303133] leading-[1.4]"},{default:u((()=>[w(C(a.value.area||a.value.address_name),1)])),_:1})):(d(),o(_,{key:1,class:"text-[#888] text-[28rpx]"},{default:u((()=>[w(C(x(k)("selectAddressPlaceholder")),1)])),_:1})),n(_,{class:"flex items-center"},{default:u((()=>[n(g,{class:"nc-iconfont nc-icon-dizhiguanliV6xx text-[32rpx] mr-[4rpx] text-[var(--primary-color)]"}),n(g,{class:"text-[24rpx] whitespace-nowrap text-[var(--primary-color)]"},{default:u((()=>[w("定位")])),_:1})])),_:1})])),_:1}))])),_:1},8,["label"])])),_:1}),n(_,{class:"mt-[16rpx]"},{default:u((()=>[n(h,{label:x(k)("address"),prop:"address",labelWidth:"200rpx"},{default:u((()=>[n(f,{fontSize:"28rpx",modelValue:a.value.address,"onUpdate:modelValue":s[2]||(s[2]=e=>a.value.address=e),border:"none",clearable:"",maxlength:"120",placeholder:x(k)("addressPlaceholder"),placeholderStyle:"color: #888"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1}),n(_,{class:"sidebar-margin card-template mt-[var(--top-m)] py-[10rpx]"},{default:u((()=>[n(h,{label:x(k)("defaultAddress"),prop:"name","border-bottom":!1,labelWidth:"200rpx"},{default:u((()=>[n(y,{modelValue:a.value.is_default,"onUpdate:modelValue":s[3]||(s[3]=e=>a.value.is_default=e),size:"20",activeValue:1,inactiveValue:0,activeColor:"var(--primary-color)",inactiveColor:"var(--temp-bg)"},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1},8,["model","rules"]),n(_,{class:"w-full footer"},{default:u((()=>[n(_,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] footer w-full fixed bottom-0 left-0 right-0 box-border"},{default:u((()=>[n(V,{"hover-class":"none",class:p(["primary-btn-bg !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",{"opacity-50":m.value}]),onClick:F,disabled:m.value,loading:D.value},{default:u((()=>[w(C(x(k)("save")),1)])),_:1},8,["disabled","loading","class"])])),_:1})])),_:1}),n(A,{ref_key:"areaRef",ref:l,onComplete:R,"area-id":a.value.district_id||a.value.city_id},null,8,["area-id"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-5e245e74"]]);export{E as default};

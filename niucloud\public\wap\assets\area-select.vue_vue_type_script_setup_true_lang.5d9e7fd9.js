import{d as t,r as e,N as i,be as c,a9 as a,bf as l,i as r,j as s,o,c as n,w as d,b as p,C as u,A as v,T as f,B as y,g as m,f as x,R as _,S as h,a3 as k,v as C,k as g,ap as b}from"./index-4b8dc7db.js";import{_ as j}from"./u-popup.e2790691.js";const w=t({__name:"area-select",props:{areaId:{type:Number,default:0}},emits:["complete"],setup(t,{expose:w,emit:I}){const N=t,T=e(!1),A=i({province:[],city:[],district:[]}),B=e("province"),R=i({province:null,city:null,district:null});c(0).then((({data:t})=>{A.province=t})).catch(),a((()=>N.areaId),((t,e)=>{t&&!e&&l(t).then((({data:t})=>{t.province&&(R.province=t.province),t.city&&(R.city=t.city),t.district&&(R.district=t.district),null==t.city&&t.province&&t.district&&(R.city=t.district,R.district=null)}))}),{immediate:!0}),a((()=>R.province),(()=>{c(R.province.id).then((({data:t})=>{if(A.city=t,B.value="city",R.city){let e=!1;for(let i=0;i<t.length;i++)if(R.city.id==t[i].id){e=!0;break}e||(R.city=null)}})).catch()}),{deep:!0}),a((()=>R.city),(t=>{t?c(R.city.id).then((({data:t})=>{if(A.district=t,B.value="district",R.district){let e=!1;for(let i=0;i<t.length;i++)if(R.district.id==t[i].id){e=!0;break}e||(R.district=null)}t.length||(I("complete",R),T.value=!1)})).catch():(A.district=[],R.district=null)}),{deep:!0}),a((()=>R.district),(t=>{t&&(B.value="district",I("complete",R),T.value=!1)}),{deep:!0});return w({open:()=>{T.value=!0}}),(t,e)=>{const i=g,c=b,a=r(s("u-popup"),j);return o(),n(a,{show:T.value,onClose:e[4]||(e[4]=t=>T.value=!1),mode:"bottom",round:10},{default:d((()=>[p(i,{onTouchmove:e[3]||(e[3]=u((()=>{}),["prevent","stop"])),class:"popup-common"},{default:d((()=>[p(i,{class:"title"},{default:d((()=>[v("请选择地区")])),_:1}),p(i,{class:"flex p-[30rpx] pt-[0] text-sm font-500"},{default:d((()=>[A.province.length?(o(),n(i,{key:0,class:f(["flex-1 pr-[10rpx]",{"text-[var(--primary-color)]":"province"==B.value}]),onClick:e[0]||(e[0]=t=>B.value="province")},{default:d((()=>[R.province?(o(),n(i,{key:0},{default:d((()=>[v(y(R.province.name),1)])),_:1})):(o(),n(i,{key:1},{default:d((()=>[v("请选择")])),_:1}))])),_:1},8,["class"])):m("v-if",!0),A.city.length?(o(),n(i,{key:1,class:f(["flex-1 pr-[10rpx]",{"text-[var(--primary-color)]":"city"==B.value}]),onClick:e[1]||(e[1]=t=>B.value="city")},{default:d((()=>[R.city?(o(),n(i,{key:0},{default:d((()=>[v(y(R.city.name),1)])),_:1})):(o(),n(i,{key:1},{default:d((()=>[v("请选择")])),_:1}))])),_:1},8,["class"])):m("v-if",!0),A.district.length?(o(),n(i,{key:2,class:f(["flex-1 pr-[10rpx]",{"text-[var(--primary-color)]":"district"==B.value}]),onClick:e[2]||(e[2]=t=>B.value="district")},{default:d((()=>[R.district?(o(),n(i,{key:0},{default:d((()=>[v(y(R.district.name),1)])),_:1})):(o(),n(i,{key:1},{default:d((()=>[v("请选择")])),_:1}))])),_:1},8,["class"])):m("v-if",!0)])),_:1}),p(c,{"scroll-y":"true",class:"h-[50vh]"},{default:d((()=>[p(i,{class:"flex p-[30rpx] pt-0 text-sm"},{default:d((()=>[A.province.length?x((o(),n(i,{key:0},{default:d((()=>[(o(!0),_(h,null,k(A.province,(t=>(o(),n(i,{class:f(["h-[80rpx] flex items-center",{"text-[var(--primary-color)]":R.province&&R.province.id==t.id}]),onClick:e=>R.province=t},{default:d((()=>[v(y(t.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1},512)),[[C,"province"==B.value]]):m("v-if",!0),A.city.length?x((o(),n(i,{key:1},{default:d((()=>[(o(!0),_(h,null,k(A.city,(t=>(o(),n(i,{class:f(["h-[80rpx] flex items-center",{"text-[var(--primary-color)]":R.city&&R.city.id==t.id}]),onClick:e=>R.city=t},{default:d((()=>[v(y(t.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1},512)),[[C,"city"==B.value]]):m("v-if",!0),A.district.length?x((o(),n(i,{key:2},{default:d((()=>[(o(!0),_(h,null,k(A.district,(t=>(o(),n(i,{class:f(["h-[80rpx] flex items-center",{"text-[var(--primary-color)]":R.district&&R.district.id==t.id}]),onClick:e=>R.district=t},{default:d((()=>[v(y(t.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1},512)),[[C,"district"==B.value]]):m("v-if",!0)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}});export{w as _};

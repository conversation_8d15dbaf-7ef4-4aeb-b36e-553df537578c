import{d as o,u as t,h as e,r as s,a as r,o as a,c as p,w as i,b as u,e as m,f as n,v as _,g as l,n as j,i as d,j as c,k as g}from"./index-dd56d0cc.js";import{_ as f}from"./loading-page.vue_vue_type_script_setup_true_lang.c88f563e.js";import{u as y}from"./useDiy.7a879ac9.js";import{d as v}from"./index.2c75d097.js";import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.f15d7447.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-popup.457e1f1f.js";import"./u-safe-bottom.22d4d63b.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./u-button.b6743e99.js";import"./u-input.ef44c0c4.js";import"./u-picker.1af38a2a.js";import"./u-upload.44346d61.js";import"./u-radio-group.3476fb8a.js";import"./diy_form.2c8e4c0e.js";import"./u-action-sheet.daa5fa92.js";import"./u-line.ddd38835.js";import"./u-avatar.ea828bd7.js";import"./u-text.1f240d34.js";import"./u-parse.ae2d35cb.js";import"./tabbar.0d5e534b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";/* empty css                                                                */import"./goods.dbf7b09d.js";import"./useGoods.392f2eb1.js";import"./add-cart-popup.4746de5d.js";import"./u-number-box.41986fc4.js";import"./coupon.506f719c.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./bind-mobile.9929c841.js";import"./u-form.b5669646.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-modal.0666cf44.js";import"./newcomer.c56b90d6.js";import"./order.22f5d222.js";const x=b(o({__name:"index",setup(o){const{setShare:b}=t();e();const x=y({name:"DIY_INDEX"}),h=s(null);return s(null),x.onLoad(),x.onShow((o=>{var t;o.value||o.page&&r({url:o.page,mode:"reLaunch"});let e=o.share?JSON.parse(o.share):null;b(e),null==(t=h.value)||t.refresh()})),x.onHide(),x.onUnload(),x.onPageScroll(),(o,t)=>{const e=d(c("loading-page"),f),s=g;return a(),p(s,{style:j(o.themeColor())},{default:i((()=>[u(e,{loading:m(x).getLoading()},null,8,["loading"]),n(u(s,null,{default:i((()=>[l(" 自定义模板渲染 "),u(s,{class:"diy-template-wrap bg-index",style:j(m(x).pageStyle())},{default:i((()=>[u(v,{ref_key:"diyGroupRef",ref:h,data:m(x).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[_,!m(x).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-fd5f78ed"]]);export{x as default};

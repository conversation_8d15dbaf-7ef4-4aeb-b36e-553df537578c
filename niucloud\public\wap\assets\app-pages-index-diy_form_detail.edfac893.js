import{a4 as t,a5 as e,a6 as i,a7 as o,a8 as r,i as s,j as a,o as l,c as d,w as p,b as u,A as n,n as m,B as c,g as _,I as j,k as y,d as x,r as f,s as h,e as v,F as g}from"./index-dd56d0cc.js";import{_ as b}from"./u-line.ddd38835.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang.fd82930c.js";import"./index.2c75d097.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-popup.457e1f1f.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-safe-bottom.22d4d63b.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./u-button.b6743e99.js";import"./u-loading-icon.f15d7447.js";import"./u-input.ef44c0c4.js";import"./u-picker.1af38a2a.js";import"./u-upload.44346d61.js";import"./u-radio-group.3476fb8a.js";import"./diy_form.2c8e4c0e.js";import"./u-action-sheet.daa5fa92.js";import"./u-avatar.ea828bd7.js";import"./u-text.1f240d34.js";import"./u-parse.ae2d35cb.js";import"./tabbar.0d5e534b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";/* empty css                                                                */import"./goods.dbf7b09d.js";import"./useGoods.392f2eb1.js";import"./add-cart-popup.4746de5d.js";import"./u-number-box.41986fc4.js";import"./coupon.506f719c.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./bind-mobile.9929c841.js";import"./u-form.b5669646.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-modal.0666cf44.js";import"./newcomer.c56b90d6.js";import"./order.22f5d222.js";const C=S({name:"u-divider",mixins:[e,i,{props:{dashed:{type:Boolean,default:()=>t.divider.dashed},hairline:{type:Boolean,default:()=>t.divider.hairline},dot:{type:Boolean,default:()=>t.divider.dot},textPosition:{type:String,default:()=>t.divider.textPosition},text:{type:[String,Number],default:()=>t.divider.text},textSize:{type:[String,Number],default:()=>t.divider.textSize},textColor:{type:String,default:()=>t.divider.textColor},lineColor:{type:String,default:()=>t.divider.lineColor}}}],computed:{textStyle(){const t={};return t.fontSize=o(this.textSize),t.color=this.textColor,t},leftLineStyle(){const t={};return"left"===this.textPosition?t.width="80rpx":t.flex=1,t},rightLineStyle(){const t={};return"right"===this.textPosition?t.width="80rpx":t.flex=1,t}},emits:["click"],methods:{addStyle:r,click(){this.$emit("click")}}},[["render",function(t,e,i,o,r,x){const f=s(a("u-line"),b),h=j,v=y;return l(),d(v,{class:"u-divider",style:m([x.addStyle(t.customStyle)]),onClick:x.click},{default:p((()=>[u(f,{color:t.lineColor,customStyle:x.leftLineStyle,hairline:t.hairline,dashed:t.dashed},null,8,["color","customStyle","hairline","dashed"]),t.dot?(l(),d(h,{key:0,class:"u-divider__dot"},{default:p((()=>[n("●")])),_:1})):t.text?(l(),d(h,{key:1,class:"u-divider__text",style:m([x.textStyle])},{default:p((()=>[n(c(t.text),1)])),_:1},8,["style"])):_("v-if",!0),u(f,{color:t.lineColor,customStyle:x.rightLineStyle,hairline:t.hairline,dashed:t.dashed},null,8,["color","customStyle","hairline","dashed"])])),_:1},8,["style","onClick"])}],["__scopeId","data-v-14eab0c7"]]),w=x({__name:"diy_form_detail",setup(t){const e=f(0);return h((t=>{e.value=t.record_id})),(t,i)=>{const o=y,r=s(a("u-divider"),C);return l(),d(o,{style:m(t.themeColor())},{default:p((()=>[u(o,{class:"w-screen h-screen bg-[var(--page-bg-color)] min-h-[100vh]"},{default:p((()=>[u(o,{class:"bg-white p-3"},{default:p((()=>[u(o,{class:"text-[30rpx] font-500 leading-[45rpx]"},{default:p((()=>[n(c(v(g)("diyForm.detailInformation")),1)])),_:1}),u(r,{text:""}),_(" 动态渲染表单组件详情 "),u(k,{record_id:e.value,completeLayout:"style-1"},null,8,["record_id"])])),_:1})])),_:1})])),_:1},8,["style"])}}});export{w as default};

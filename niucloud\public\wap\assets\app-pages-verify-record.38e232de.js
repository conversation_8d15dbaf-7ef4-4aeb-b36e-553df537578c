import{d as e,r as t,s as a,o as l,c as s,w as r,b as o,C as c,g as n,R as i,a3 as p,S as x,e as u,n as d,ah as m,ai as f,I as _,ag as v,k as g,i as h,j as y,A as b,B as j,aj as k,E as w,a as C,G as V}from"./index-dd56d0cc.js";import{_ as I}from"./u--image.cd475bba.js";import{M}from"./mescroll-body.520ba8e3.js";import{M as S}from"./mescroll-empty.8630a00e.js";import{u as U}from"./useMescroll.26ccf5de.js";import{c as z}from"./verify.b474c75d.js";import{s as R}from"./select-date.491a2cb8.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.dfca355c.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./mescroll-i18n.92d484d5.js";import"./u-popup.457e1f1f.js";import"./u-safe-bottom.22d4d63b.js";const F=E(e({__name:"record",setup(e){const E=t(""),F=t([]),q=t([]),A=t(!1),{mescrollInit:B,downCallback:D,getMescroll:G}=U(f,m);t(null),a((()=>{}));const O=e=>{A.value=!1;let t={page:e.num,limit:e.size,keyword:E.value,create_time:F.value};z(t).then((t=>{let a=t.data.data;1==e.num&&(q.value=[]),q.value=q.value.concat(a),e.endSuccess(a.length),A.value=!0})).catch((()=>{A.value=!0,e.endErr()}))},P=()=>{G().resetUpScroll()},Q=t(),H=()=>{Q.value.show=!0},J=e=>{F.value=e,q.value=[],G().resetUpScroll()};return(e,t)=>{const a=_,m=v,f=g,U=V,z=h(y("u--image"),I);return l(),s(f,{class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden",style:d(e.themeColor())},{default:r((()=>[o(f,{class:"fixed left-0 right-0 top-0 z-99 bg-[#fff]"},{default:r((()=>[o(f,{class:"py-[14rpx] flex items-center justify-between px-[20rpx]"},{default:r((()=>[o(f,{class:"flex-1 search-input mr-[20rpx]"},{default:r((()=>[o(a,{onClick:t[0]||(t[0]=c((e=>P()),["stop"])),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"}),o(m,{class:"input",maxlength:"50",type:"text",modelValue:E.value,"onUpdate:modelValue":t[1]||(t[1]=e=>E.value=e),placeholder:"请输入搜索关键词",placeholderClass:"text-[var(--text-color-light9)] text-[24rpx]","confirm-type":"search",onConfirm:t[2]||(t[2]=e=>P())},null,8,["modelValue"]),E.value?(l(),s(a,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[3]||(t[3]=e=>E.value="")})):n("v-if",!0)])),_:1}),o(f,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[30rpx] leading-[36rpx] text-[var(--text-color-light6)]",onClick:H})])),_:1})])),_:1}),o(M,{ref:"mescrollRef",top:"88rpx",onInit:u(B),down:{use:!1},onUp:O},{default:r((()=>[q.value.length?(l(),s(f,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:r((()=>[(l(!0),i(x,null,p(q.value,((e,t)=>(l(),s(f,{key:e.id,class:"w-full flex flex-col mb-[var(--top-m)] card-template",onClick:t=>{C({url:"/app/pages/verify/detail",param:{code:e.code}})}},{default:r((()=>[o(f,{class:"flex items-center mb-[30rpx] leading-[1] text-[26rpx]"},{default:r((()=>[o(f,{class:"nc-iconfont nc-icon-hexiaotaiV6xx !text-[26rpx] pr-[10rpx]"}),o(a,{class:"truncate text-[#303133] text-[26rpx]"},{default:r((()=>[b("核销码:")])),_:1}),o(a,{class:"ml-[10rpx] max-w-[494rpx]"},{default:r((()=>[b(j(e.code),1)])),_:2},1024),o(a,{class:"text-[#303133] text-[26rpx] font-400 nc-iconfont nc-icon-fuzhiV6xx1 ml-[11rpx]",onClick:c((t=>u(k)(e.code)),["stop"])},null,8,["onClick"])])),_:2},1024),(l(!0),i(x,null,p(e.value.list,((t,a)=>(l(),s(f,{class:"flex flex-1 mb-2",key:a},{default:r((()=>[o(z,{width:"130rpx",height:"130rpx",radius:"var(--goods-rounded-big)",src:u(w)(t.cover?t.cover:""),mode:"aspectFill"},{error:r((()=>[o(U,{class:"w-[130rpx] h-[130rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:u(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"]),o(f,{class:"flex flex-col flex-1 ml-[20rpx] py-[4rpx]"},{default:r((()=>[o(f,{class:"max-w-[490rpx] leading-[1.3] truncate text-[28rpx] text-[#303133]"},{default:r((()=>[b(j(t.name),1)])),_:2},1024),e.sub_name?(l(),s(f,{key:0,class:"mt-[14rpx] truncate text-[24rpx] text-[var(--text-color-light9)] max-w-[490rpx]"},{default:r((()=>[b(j(e.sub_name),1)])),_:2},1024)):n("v-if",!0),o(f,{class:"text-[24rpx] mt-[10rpx] text-[var(--text-color-light9)]"},{default:r((()=>[b("x"+j(t.verify_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),o(f,{class:"flex bg-[var(--temp-bg)] py-[20rpx] px-[20rpx] rounded-[12rpx] mt-[20rpx]"},{default:r((()=>[o(f,{class:"flex-1"},{default:r((()=>[o(f,{class:"text-[22rpx] text-[var(--text-color-light9)] mb-[10rpx] leading-[30rpx]"},{default:r((()=>[b("核销时间")])),_:1}),o(f,{class:"text-[26rpx] text-[#303133] leading-[36rpx]"},{default:r((()=>[b(j(e.create_time),1)])),_:2},1024)])),_:2},1024),o(f,{class:"flex-1"},{default:r((()=>[o(f,{class:"text-[22rpx] text-[var(--text-color-light9)] mb-[10rpx] leading-[30rpx]"},{default:r((()=>[b("核销员")])),_:1}),o(f,{class:"text-[26rpx] text-[#303133] leading-[36rpx]"},{default:r((()=>[b(j(e.member?e.member.nickname:"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})):n("v-if",!0),!q.value.length&&A.value?(l(),s(S,{key:1,option:{tip:"暂无核销记录"}})):n("v-if",!0)])),_:1},8,["onInit"]),n(" 时间选择 "),o(R,{ref_key:"selectDateRef",ref:Q,onConfirm:J},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-0c14380a"]]);export{F as default};

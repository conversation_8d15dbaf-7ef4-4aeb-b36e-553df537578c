import{ao as e,bo as t,x as s,y as a,d as l,q as o,l as u,r as i,a9 as r,o as d,c,w as n,b as p,e as x,C as v,A as _,B as m,g,R as h,a3 as f,S as k,T as b,b2 as y,G as L,i as w,j,I as V,k as R,ag as C,ap as F,H as I,E as O,bp as N}from"./index-4b8dc7db.js";import{_ as E}from"./u--image.892273b2.js";import{_ as S}from"./u-number-box.cd35fabd.js";import{_ as M}from"./u-popup.e2790691.js";import{b as T}from"./goods.f34d2594.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";function A(t){return e.post("shop/cart",t)}function B(t){return e.put("shop/cart",t)}function $(t){return e.put("shop/cart/delete",t)}function q(t){return e.get("shop/cart/goods",t)}function z(t){return e.get("shop/cart/calculate",t)}const G=t("cart",{state:()=>({cartList:{},totalNum:0,totalMoney:0,isRepeat:!1}),actions:{getList(t=null){if(!s()){for(let e in this.cartList)delete this.cartList[e];return this.totalNum=0,void(this.totalMoney=0)}var a;(a={},e.get("shop/cart",a)).then((e=>{let s=e.data;for(let t in this.cartList)delete this.cartList[t];s&&s.forEach((e=>{if(1==e.goods.status&&0==e.goods.delete_time&&e.goodsSku){let t={id:e.id,goods_id:e.goods_id,sku_id:e.sku_id,stock:e.goodsSku.stock,num:e.num,sale_price:e.goodsSku.show_price};this.cartList["goods_"+t.goods_id]||(this.cartList["goods_"+t.goods_id]={}),this.cartList["goods_"+t.goods_id]["sku_"+t.sku_id]=t}})),this.calculateNum(),t&&"function"==typeof t&&t()}))},increase(e,t=1,l=null){if(!e||e&&0==Object.keys(e).length||!e.goods_id||!e.sku_id)return;if(!s())return;let o=(e.num||0)+t,u=e.id?B:A;o>parseInt(e.stock)?a({title:"商品库存不足",icon:"none"}):this.isRepeat||(this.isRepeat=!0,e.id?this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id].num=o:(this.cartList["goods_"+e.goods_id]||(this.cartList["goods_"+e.goods_id]={}),this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id]={id:e.id,goods_id:e.goods_id,sku_id:e.sku_id,stock:e.stock,num:o,sale_price:e.sale_price}),this.calculateNum(),u({id:e.id,goods_id:e.goods_id,sku_id:e.sku_id,num:o}).then((e=>{this.getList(l),this.isRepeat=!1})).catch((e=>{this.isRepeat=!1})))},reduce(e,t=1,a=null){if(!e||e&&0==Object.keys(e).length||!e.goods_id||!e.sku_id)return;if(!s())return;let l=(e.num||0)-t,o=l>0?B:$;this.isRepeat||(this.isRepeat=!0,l>0?this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id].num=l:(delete this.cartList["goods_"+e.goods_id]["sku_"+e.sku_id],0==Object.keys(this.cartList["goods_"+e.goods_id]).length&&delete this.cartList["goods_"+e.goods_id]),this.calculateNum(),o({ids:e.id,id:e.id,goods_id:e.goods_id,sku_id:e.sku_id,num:l}).then((e=>{this.getList(a),this.isRepeat=!1})).catch((e=>{this.isRepeat=!1})))},delete(e,t=null){e&&$({ids:e}).then((e=>{this.getList(),this.isRepeat=!1,t&&t()})).catch((e=>{this.isRepeat=!1}))},calculateNum(){if(this.totalNum=0,this.totalMoney=0,Object.keys(this.cartList).length)for(let e in this.cartList){let t=0,s=0;for(let a in this.cartList[e])"object"==typeof this.cartList[e][a]&&(t+=this.cartList[e][a].num,s+=this.cartList[e][a].num*this.cartList[e][a].sale_price);this.cartList[e].totalNum=t,this.cartList[e].totalMoney=s.toFixed(2),this.totalNum+=t,this.totalMoney+=s}this.totalMoney=this.totalMoney.toFixed(2)}}}),H=U(l({__name:"add-cart-popup",setup(e,{expose:t}){const s=o(),l=G(),U=u((()=>l.cartList)),A=i(!1),B=i({skuId:"",name:[]}),$=i({}),q=i({}),z=i(1),H=i(0),P=i(0),D=i(0),J=i(0),K=()=>{setTimeout((()=>{(!z.value||z.value<=P.value)&&(z.value=P.value||1),z.value>=H.value&&(z.value=H.value),P.value>z.value&&(z.value=0)}),0)},Q=()=>{setTimeout((()=>{(!z.value||z.value<=P.value)&&(z.value=P.value||1),z.value>=H.value&&(z.value=H.value),P.value>z.value&&(z.value=0,a({title:"库存数量小于起购数量",icon:"none"}))}),0)},W=()=>{A.value=!1},X=u((()=>{let e=y($.value);if(Object.keys(e).length){if(Object.keys(B.value.name).length||(B.value.name=e.sku_spec_format.split(",")),e.goodsSpec.forEach(((e,t)=>{let s=e.spec_values.split(",");e.values=[],s.forEach(((s,a)=>{e.values[a]={},e.values[a].name=s,e.values[a].selected=!1,e.values[a].disabled=!1,B.value.name.forEach(((l,o)=>{o==t&&l==s&&(e.values[a].selected=!0)}))}))})),Y(),e.skuList&&Object.keys(e.skuList).length&&e.skuList.forEach((e=>{e.sku_id==B.value.skuId&&(q.value=e)})),H.value=q.value.stock,e.goods.is_limit){if(e.goods&&e.goods.max_buy){let t=0;if(1==e.goods.limit_type)t=e.goods.max_buy;else{let s=e.goods.max_buy-(e.has_buy||0);t=s>0?s:0}t>q.value.stock?H.value=q.value.stock:t<=q.value.stock&&(H.value=t)}D.value=e.goods.max_buy,0==H.value&&(z.value=0)}P.value=e.goods.min_buy,z.value=P.value>0?e.goods.min_buy:1,J.value=e.goods.min_buy,P.value>q.value.stock&&(z.value=0)}return e}));r((()=>q.value),((e,t)=>{if(U.value["goods_"+q.value.goods_id]&&U.value["goods_"+q.value.goods_id]["sku_"+q.value.sku_id])z.value=N(U.value["goods_"+q.value.goods_id]["sku_"+q.value.sku_id].num),q.value.cart_id=N(U.value["goods_"+q.value.goods_id]["sku_"+q.value.sku_id].id);else{let e=1;P.value>0&&(e=P.value),P.value>q.value.stock&&(e=0),z.value=e,q.value.cart_id=""}}));const Y=()=>{$.value.skuList.forEach(((e,t)=>{let s=e.sku_spec_format.split(",");B.value.name.every((e=>s.includes(e)))&&(B.value.skuId=e.sku_id)}))},Z=()=>{if(P.value&&P.value>q.value.stock)a({title:"商品库存小于起购数量",icon:"none"});else if(X.value.goods.is_limit){let e=`该商品单次限购${X.value.goods.max_buy}件`;1!=X.value.goods.limit_type&&(e=`该商品每人限购${X.value.goods.max_buy}件`),z.value>=X.value.goods.max_buy&&a({title:e,icon:"none"})}},ee=()=>{if(X.value.goods.is_limit&&P.value){let e=`该商品起购${P.value}件`;z.value<=P.value&&a({title:e,icon:"none"})}},te=()=>{0==z.value?l.reduce({id:q.value.cart_id||"",goods_id:q.value.goods_id,sale_price:q.value.show_price,sku_id:q.value.sku_id},1,(()=>{s.isAddCartRecommend=!0})):l.increase({id:q.value.cart_id||"",goods_id:q.value.goods_id,sku_id:q.value.sku_id,stock:q.value.stock,sale_price:q.value.show_price,num:z.value},0,(()=>{s.isAddCartRecommend=!0,a({title:"加入购物车成功",icon:"none"})})),A.value=!1},se=e=>{let t="0.00";return t=e.show_price,t},ae=e=>{let t="";return t=e.show_type,t};return t({open:e=>{(e=>{T(e).then((t=>{$.value=t.data,B.value.sku_id=e,$.value.skuList&&Object.keys($.value.skuList).length&&$.value.skuList.forEach((t=>{t.sku_id==e&&(q.value=t,B.value.name=t.sku_spec_format.split(","))})),A.value=!0}))})(e)}}),(e,t)=>{const s=L,a=w(j("u--image"),E),l=V,o=R,u=C,i=w(j("u-number-box"),S),r=F,y=I,N=w(j("u-popup"),M);return d(),c(o,{onTouchmove:t[9]||(t[9]=v((()=>{}),["prevent","stop"]))},{default:n((()=>[p(N,{show:A.value,onClose:W,mode:"bottom",zIndex:"999999"},{default:n((()=>[Object.keys(x(X)).length?(d(),c(o,{key:0,onTouchmove:t[8]||(t[8]=v((()=>{}),["prevent","stop"])),class:"rounded-t-[20rpx] overflow-hidden bg-[#fff] py-[32rpx] relative"},{default:n((()=>[p(o,{class:"flex px-[32rpx] mb-[58rpx]"},{default:n((()=>[p(a,{width:"180rpx",height:"180rpx",radius:"var(--goods-rounded-big)",src:x(O)(q.value.sku_image),model:"aspectFill"},{error:n((()=>[p(s,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:x(O)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["radius","src"]),p(o,{class:"flex flex-1 flex-col justify-between ml-[20rpx] py-[10rpx]"},{default:n((()=>[p(o,{class:"w-[100%]"},{default:n((()=>[p(o,{class:"text-[var(--price-text-color)] flex items-baseline"},{default:n((()=>[p(l,{class:"text-[32rpx] font-bold price-font mr-[4rpx]"},{default:n((()=>[_("￥")])),_:1}),p(l,{class:"text-[48rpx] price-font"},{default:n((()=>[_(m(parseFloat(se(q.value)).toFixed(2).split(".")[0]),1)])),_:1}),p(l,{class:"text-[32rpx] price-font"},{default:n((()=>[_("."+m(parseFloat(se(q.value)).toFixed(2).split(".")[1]),1)])),_:1}),"member_price"==ae(q.value)?(d(),c(s,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:x(O)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==ae(q.value)?(d(),c(s,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:x(O)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==ae(q.value)?(d(),c(s,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:x(O)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):g("v-if",!0)])),_:1}),p(o,{class:"text-[26rpx] leading-[32rpx] text-[#303133] mt-[12rpx]"},{default:n((()=>[_("库存"+m(q.value.stock)+m(x(X).goods.unit),1)])),_:1})])),_:1}),x(X).goodsSpec&&x(X).goodsSpec.length?(d(),c(o,{key:0,class:"w-[100%] text-[26rpx] leading-[30rpx] text-[var(--text-color-light6)] multi-hidden max-h-[60rpx]"},{default:n((()=>[_(" 已选规格："+m(q.value.sku_spec_format),1)])),_:1})):g("v-if",!0)])),_:1})])),_:1}),p(r,{class:"h-[500rpx] box-border px-[32rpx] mb-[30rpx]","scroll-y":"true"},{default:n((()=>[(d(!0),h(k,null,f(x(X).goodsSpec,((e,t)=>(d(),c(o,{class:b({"mt-[36rpx]":0!=t}),key:t},{default:n((()=>[p(o,{class:"text-[26rpx] leading-[36rpx] mb-[24rpx]"},{default:n((()=>[_(m(e.spec_name),1)])),_:2},1024),p(o,{class:"flex flex-wrap"},{default:n((()=>[(d(!0),h(k,null,f(e.values,((e,s)=>(d(),c(o,{class:b(["box-border bg-[#f2f2f2] text-[24rpx] px-[44rpx] text-center h-[56rpx] leading-[52rpx] mr-[20rpx] mb-[20rpx] border-1 border-solid rounded-[50rpx] border-[#f2f2f2]",{"!border-[var(--primary-color)] text-[var(--primary-color)] !bg-[var(--primary-color-light)]":e.selected}]),key:s,onClick:s=>((e,t)=>{B.value.name[t]=e.name})(e,t)},{default:n((()=>[_(m(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1032,["class"])))),128)),p(o,{class:"flex justify-between items-center mt-[8rpx]"},{default:n((()=>[p(o,{class:"text-[26rpx]"},{default:n((()=>[_("购买数量")])),_:1}),D.value>0&&J.value>1?(d(),c(l,{key:0,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:n((()=>[_("("+m(J.value)+m(x(X).goods.unit)+"起售，限购"+m(D.value)+m(x(X).goods.unit)+")",1)])),_:1})):D.value>0?(d(),c(l,{key:1,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:n((()=>[_("(限购"+m(D.value)+m(x(X).goods.unit)+")",1)])),_:1})):J.value>1?(d(),c(l,{key:2,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:n((()=>[_("("+m(J.value)+m(x(X).goods.unit)+"起售)",1)])),_:1})):g("v-if",!0),x(U)["goods_"+q.value.goods_id]&&x(U)["goods_"+q.value.goods_id]["sku_"+q.value.sku_id]?(d(),c(i,{key:3,modelValue:z.value,"onUpdate:modelValue":t[3]||(t[3]=e=>z.value=e),min:P.value,max:H.value,integer:"",step:1,"input-width":"98rpx","input-height":"54rpx"},{minus:n((()=>[p(o,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[0]||(t[0]=e=>ee())},{default:n((()=>[p(l,{class:b(["text-[34rpx] nc-iconfont nc-icon-jianV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":z.value<=P.value}])},null,8,["class"])])),_:1})])),input:n((()=>[p(u,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:K,onBlur:Q,modelValue:z.value,"onUpdate:modelValue":t[1]||(t[1]=e=>z.value=e)},null,8,["modelValue"])])),plus:n((()=>[p(o,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[2]||(t[2]=e=>Z())},{default:n((()=>[p(l,{class:b(["text-[34rpx] nc-iconfont nc-icon-jiahaoV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":z.value>=H.value}])},null,8,["class"])])),_:1})])),_:1},8,["modelValue","min","max"])):(d(),c(i,{key:4,modelValue:z.value,"onUpdate:modelValue":t[7]||(t[7]=e=>z.value=e),min:P.value,max:H.value,integer:"",step:1,"input-width":"98rpx","input-height":"54rpx"},{minus:n((()=>[p(o,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[4]||(t[4]=e=>ee())},{default:n((()=>[p(l,{class:b(["text-[34rpx] nc-iconfont nc-icon-jianV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":z.value<=P.value}])},null,8,["class"])])),_:1})])),input:n((()=>[p(u,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:K,onBlur:Q,modelValue:z.value,"onUpdate:modelValue":t[5]||(t[5]=e=>z.value=e)},null,8,["modelValue"])])),plus:n((()=>[p(o,{class:"relative w-[34rpx] h-[34rpx]",onClick:t[6]||(t[6]=e=>Z())},{default:n((()=>[p(l,{class:b(["text-[34rpx] nc-iconfont nc-icon-jiahaoV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":z.value>=H.value}])},null,8,["class"])])),_:1})])),_:1},8,["modelValue","min","max"]))])),_:1})])),_:1}),p(o,{class:"px-[20rpx]"},{default:n((()=>[q.value.stock>0?(d(),c(y,{key:0,class:"!h-[80rpx] font-500 primary-btn-bg leading-[80rpx] text-[26rpx] rounded-[50rpx]",type:"primary",onClick:te},{default:n((()=>[_("确定")])),_:1})):(d(),c(y,{key:1,class:"!h-[80rpx] leading-[80rpx] font-500 text-[26rpx] text-[#fff] bg-[#ccc] rounded-[50rpx]"},{default:n((()=>[_("已售罄")])),_:1}))])),_:1})])),_:1})):g("v-if",!0)])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-58119339"]]);export{H as a,z as b,q as g,G as u};

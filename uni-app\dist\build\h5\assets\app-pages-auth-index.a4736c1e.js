import{d as e,l as r,r as t,m as a,p as l,q as s,s as o,t as i,x as n,y as p,a as u,z as c,o as x,c as d,w as f,b as m,e as _,A as g,B as b,g as v,C as y,n as h,D as k,E as w,F as j,G as C,k as S,H as L,i as A,j as T,I as F,J as q}from"./index-4b8dc7db.js";import{_ as z}from"./u-checkbox.598bfa18.js";import{_ as I}from"./u-checkbox-group.0c3be417.js";import{_ as B}from"./uni-popup.740829da.js";import{b as D}from"./bind-mobile.259f3837.js";import{t as N}from"./topTabbar.1aa95d14.js";import{_ as O}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-form.a144799c.js";import"./u-line.b0d89d5b.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-input.7a7ec88f.js";import"./u-modal.775e667c.js";import"./u-loading-icon.11ef83b8.js";import"./u-popup.e2790691.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-safe-bottom.987908cd.js";const P=O(e({__name:"index",setup(e){let O={};N().setTopTabbarParam({title:"",topStatusBar:{textColor:"#333"}}),r((()=>Object.keys(O).length?k(Number(O.height))+k(O.top)+k(8)+"rpx":"auto"));const P=t(""),R=t(!1),E=a(),G=r((()=>E.login)),H=q();l(),r((()=>l().info));const J=s();r((()=>!R.value&&E.login.agreement_show?"":"getPhoneNumber")),t(null);const M=r((()=>!E.login.is_auth_register)),V=t(!1);t(!1);const Y=t(),Z=()=>{Y.value.close()},$=()=>{R.value=!0,Y.value.close(),W()},K=t(null),Q=()=>{K.value.open()};o((async()=>{await J.getSiteInfoFn(),await E.getLoginConfig();let e=!E.login.is_username&&!E.login.is_mobile&&!E.login.is_bind_mobile;i()?!n()&&e&&M.value&&(p({title:"商家未开启登录注册",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100)):!n()&&e&&(p({title:"商家未开启登录注册",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100))})),c((()=>{V.value=!1}));const U=r((()=>{var e="";return E.login.bg_url&&(e+="background-image:url("+w(E.login.bg_url)+");",e+="background-size: 100%;",e+="background-position: top;",e+="background-repeat: no-repeat;"),e})),W=(e=null,r=null)=>{((e="")=>!(R.value||!E.login.agreement_show||(e?p({title:j("isAgreeTips"),icon:"none"}):Y.value.open(),0)))()||V.value||(V.value=!0,e||(e=()=>{V.value=!1}),X())},X=()=>{if(i()){let e=uni.getStorageSync("login_config");if(uni.getStorageSync("member_lock"))return p({title:j("memberLock"),icon:"none"}),void setTimeout((()=>{uni.removeStorageSync("member_lock"),u({url:"/app/pages/index/index",mode:"reLaunch"})}),1e3);if(e.wechat_error)return V.value=!1,void p({title:e.wechat_error,icon:"none"});P.value=uni.getStorageSync("wap_member_mobile"),P.value||(P.value=uni.getStorageSync("wap_member_not_control_mobile")),e.is_auth_register?!P.value&&e.is_bind_mobile?Q():e.is_force_access_user_info?H.getAuthCode({scopes:"snsapi_userinfo"}):e.is_force_access_user_info||H.getAuthCode({scopes:"snsapi_base"}):!P.value&&e.is_bind_mobile?Q():H.getAuthCode({scopes:"snsapi_base"}),V.value=!1}},ee=()=>{R.value=!R.value};return(e,r)=>{const t=C,a=S,l=L,s=A(T("u-checkbox"),z),o=A(T("u-checkbox-group"),I),n=F,p=A(T("uni-popup"),B),c=A(T("bind-mobile"),D);return x(),d(a,{class:"w-screen h-screen",style:h(e.themeColor())},{default:f((()=>[m(a,{class:"w-screen h-screen",style:h(_(U))},{default:f((()=>[m(a,{class:"mx-[var(--sidebar-m)] px-[var(--pad-sidebar-m)]"},{default:f((()=>[m(a,{class:"pt-[154rpx] flex justify-center"},{default:f((()=>{var e,r;return[(null==(e=_(J).site)?void 0:e.front_end_logo)?(x(),d(a,{key:0,class:"h-[90rpx] w-[300rpx]"},{default:f((()=>{var e;return[m(t,{class:"h-[90rpx] w-[300rpx]",src:_(w)(null==(e=_(J).site)?void 0:e.front_end_logo),mode:"aspectFit"},null,8,["src"])]})),_:1})):(null==(r=_(J).site)?void 0:r.front_end_icon)?(x(),d(a,{key:1,class:"h-[250rpx] w-[250rpx]"},{default:f((()=>{var e;return[m(t,{class:"h-[250rpx] w-[250rpx]",src:_(w)(null==(e=_(J).site)?void 0:e.front_end_icon),mode:"aspectFit"},null,8,["src"])]})),_:1})):(x(),d(a,{key:2,class:"h-[90rpx] w-[300rpx]"}))]})),_:1}),m(a,{class:"text-[var(--text-color-light6)]] text-[28rpx] text-center leading-[34rpx] min-h-[34rpx] mt-[40rpx]"},{default:f((()=>[g(b(_(G).desc),1)])),_:1}),m(a,{class:"mt-[181rpx]"},{default:f((()=>[v(" 微信公众号快捷登录，开启自动注册的情况下才能使用 "),_(i)()&&_(G).is_auth_register?(x(),d(a,{key:0,class:"w-full flex items-center justify-center mb-[40rpx]"},{default:f((()=>[m(l,{class:"w-[630rpx] h-[88rpx] !mx-[0] !bg-[var(--primary-color)] text-[26rpx] rounded-[44rpx] leading-[88rpx] font-500 !text-[#fff]",onClick:r[0]||(r[0]=e=>W())},{default:f((()=>[g(b(_(j)("quickLoginOrLogout")),1)])),_:1})])),_:1})):v("v-if",!0),v(" 手机号登录 "),_(G).is_mobile?(x(),d(a,{key:1,class:"mb-[40rpx] w-full flex items-center justify-center"},{default:f((()=>[m(l,{class:"w-[630rpx] h-[88rpx] !mx-[0] !bg-[#fff] border-[var(--primary-color)] border-solid border-[2rpx] text-[26rpx] rounded-[44rpx] leading-[84rpx] !text-[var(--primary-color)]",onClick:r[1]||(r[1]=e=>_(u)({url:"/app/pages/auth/login",param:{type:"mobile"}}))},{default:f((()=>[g(b(_(j)("mobileLogin")),1)])),_:1})])),_:1})):!_(G).is_mobile&&_(G).is_username?(x(),d(a,{key:2,class:"w-full flex items-center justify-center"},{default:f((()=>[m(l,{class:"w-[630rpx] h-[88rpx] !mx-[0] !bg-[#fff] !border-[var(--primary-color)] border-solid border-[2rpx] text-[26rpx] rounded-[44rpx] leading-[84rpx] !text-[var(--primary-color)]",onClick:r[2]||(r[2]=e=>_(u)({url:"/app/pages/auth/login",param:{type:"username"}}))},{default:f((()=>[g(b(_(j)("accountLogin")),1)])),_:1})])),_:1})):v("v-if",!0),_(G).agreement_show?(x(),d(a,{key:3,class:"w-full flex items-center justify-center mt-[28rpx]"},{default:f((()=>[m(a,{class:"flex items-center justify-center mt-[28rpx] py-[14rpx] px-[50rpx]",onClick:y(ee,["stop"])},{default:f((()=>[m(o,{onChange:ee},{default:f((()=>[m(s,{activeColor:"var(--primary-color)",checked:R.value,shape:"circle",size:"30rpx"},null,8,["checked"])])),_:1}),m(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap leading-[30rpx]"},{default:f((()=>[m(n,null,{default:f((()=>[g(b(_(j)("agreeTips")),1)])),_:1}),m(n,{onClick:r[3]||(r[3]=y((e=>_(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(_(j)("privacyAgreement"))+"》",1)])),_:1}),m(n,null,{default:f((()=>[g(b(_(j)("and")),1)])),_:1}),m(n,{onClick:r[4]||(r[4]=y((e=>_(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(_(j)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])])),_:1})):v("v-if",!0),_(G).is_mobile&&_(G).is_username?(x(),d(a,{key:4,class:"footer w-full"},{default:f((()=>[m(a,{class:"text-[26rpx] leading-[36rpx] text-[333] text-center mb-[30rpx] font-400"},{default:f((()=>[g(b(_(j)("otherLogin")),1)])),_:1}),m(a,{class:"flex justify-center"},{default:f((()=>[m(a,{class:"h-[80rpx] w-[80rpx] text-center leading-[78rpx] border-[2rpx] text-[#FF7100] rounded-[50%] border-solid border-[#ddd] nc-iconfont nc-icon-wodeV6mm3 text-[46rpx] overflow-hidden",onClick:r[5]||(r[5]=e=>_(u)({url:"/app/pages/auth/login",param:{type:"username"}}))})])),_:1}),m(a,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)] text-center font-400 mt-[30rpx]"},{default:f((()=>[g(b(_(j)("accountLogin")),1)])),_:1})])),_:1})):v("v-if",!0)])),_:1})])),_:1})])),_:1},8,["style"]),m(p,{ref_key:"popupRef",ref:Y,type:"dialog"},{default:f((()=>[m(a,{class:"bg-[#fff] flex flex-col justify-between w-[600rpx] min-h-[280rpx] rounded-[var(--rounded-big)] box-border px-[35rpx] pt-[35rpx] pb-[8rpx] relative"},{default:f((()=>[m(a,{class:"flex justify-center"},{default:f((()=>[m(n,{class:"text-[33rpx] font-700"},{default:f((()=>[g(" 用户协议及隐私保护")])),_:1})])),_:1}),m(a,{class:"flex items-center mb-[20rpx] mt-[20rpx] py-[20rpx]",onClick:y(ee,["stop"])},{default:f((()=>[m(a,{class:"text-[26rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:f((()=>[m(n,null,{default:f((()=>[g(b(_(j)("agreeTips")),1)])),_:1}),m(n,{onClick:r[6]||(r[6]=y((e=>_(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(_(j)("privacyAgreement"))+"》",1)])),_:1}),m(n,null,{default:f((()=>[g(b(_(j)("and")),1)])),_:1}),m(n,{onClick:r[7]||(r[7]=y((e=>_(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:f((()=>[g("《"+b(_(j)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"]),m(a,null,{default:f((()=>[m(a,{class:"w-[100%] flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] font-500 rounded-[50rpx]",onClick:$},{default:f((()=>[g("同意并登录")])),_:1}),m(a,{class:"w-[100%] flex justify-center h-[70rpx] leading-[70rpx] text-[#999] text-[24rpx] border-[0] font-500 rounded-[50rpx]",onClick:Z},{default:f((()=>[g("不同意")])),_:1})])),_:1})])),_:1})])),_:1},512),v(" 强制绑定手机号 "),m(c,{ref_key:"bindMobileRef",ref:K},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-f9ad1904"]]);export{P as default};

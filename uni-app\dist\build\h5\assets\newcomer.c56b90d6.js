import{a4 as t,a5 as e,a6 as i,o as s,c as n,w as o,$ as a,b as m,A as r,B as c,I as h,k as u,ao as l}from"./index-dd56d0cc.js";import{_ as d}from"./_plugin-vue_export-helper.1b428a4d.js";const f={props:{time:{type:[String,Number],default:()=>t.countDown.time},format:{type:String,default:()=>t.countDown.format},autoStart:{type:Boolean,default:()=>t.countDown.autoStart},millisecond:{type:Boolean,default:()=>t.countDown.millisecond}}};function T(t,e=2){let i=`${t}`;for(;i.length<e;)i=`0${i}`;return i}const p=1e3,g=6e4,w=36e5,D=24*w;function x(t){return{days:Math.floor(t/D),hours:Math.floor(t%D/w),minutes:Math.floor(t%w/g),seconds:Math.floor(t%g/p),milliseconds:Math.floor(t%p)}}const _=d({name:"u-count-down",mixins:[e,i,f],data:()=>({timer:null,timeData:x(0),formattedTime:"0",runing:!1,endTime:0,remainTime:0}),watch:{time(t){this.reset()}},mounted(){this.init()},emits:["change","finish"],methods:{init(){this.reset()},start(){this.runing||(this.runing=!0,this.endTime=Date.now()+this.remainTime,this.toTick())},toTick(){this.millisecond?this.microTick():this.macroTick()},macroTick(){this.clearTimeout(),this.timer=setTimeout((()=>{const t=this.getRemainTime();var e,i;e=t,i=this.remainTime,(Math.floor(e/1e3)!==Math.floor(i/1e3)||0===t)&&this.setRemainTime(t),0!==this.remainTime&&this.macroTick()}),30)},microTick(){this.clearTimeout(),this.timer=setTimeout((()=>{this.setRemainTime(this.getRemainTime()),0!==this.remainTime&&this.microTick()}),50)},getRemainTime(){return Math.max(this.endTime-Date.now(),0)},setRemainTime(t){this.remainTime=t;const e=x(t);this.$emit("change",e),this.formattedTime=function(t,e){let{days:i,hours:s,minutes:n,seconds:o,milliseconds:a}=e;return-1===t.indexOf("DD")?s+=24*i:t=t.replace("DD",T(i)),-1===t.indexOf("HH")?n+=60*s:t=t.replace("HH",T(s)),-1===t.indexOf("mm")?o+=60*n:t=t.replace("mm",T(n)),-1===t.indexOf("ss")?a+=1e3*o:t=t.replace("ss",T(o)),t.replace("SSS",T(a,3))}(this.format,e),t<=0&&(this.pause(),this.$emit("finish"))},reset(){this.pause(),this.remainTime=this.time,this.setRemainTime(this.remainTime),this.autoStart&&this.start()},pause(){this.runing=!1,this.clearTimeout()},clearTimeout(){clearTimeout(this.timer),this.timer=null}},beforeUnmount(){this.clearTimeout()}},[["render",function(t,e,i,l,d,f){const T=h,p=u;return s(),n(p,{class:"u-count-down"},{default:o((()=>[a(t.$slots,"default",{},(()=>[m(T,{class:"u-count-down__text"},{default:o((()=>[r(c(d.formattedTime),1)])),_:1})]),!0)])),_:3})}],["__scopeId","data-v-1f3dc818"]]);function k(t){return l.get("shop/newcomer/goods",t)}function M(){return l.get("shop/newcomer/config")}function S(t){return l.get("shop/newcomer/goods/components",t)}export{_,M as a,k as b,S as g};

import{d as e,r as t,p as l,l as a,z as r,a9 as s,o as i,c as d,w as o,b as u,g as n,n as c,e as v,R as p,a3 as x,S as _,A as y,B as m,T as f,aT as b,y as k,an as g,a as h,k as w,G as j,I as C,ag as F,i as V,j as S,H as T,E as D,bG as O}from"./index-dd56d0cc.js";import{_ as B}from"./u--image.cd475bba.js";import{_ as $,a as z,b as R,n as I,c as A}from"./index.9bf7e6bb.js";import{_ as E}from"./u-tabbar.3565fd74.js";import{_ as G}from"./pay.1561f7e1.js";import{o as P,d as U}from"./point.00412433.js";import{h as Q}from"./order.22f5d222.js";import{t as q}from"./topTabbar.986d54c4.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.dfca355c.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.f15d7447.js";import"./u-empty.354f2b69.js";import"./u-popup.457e1f1f.js";import"./u-safe-bottom.22d4d63b.js";import"./u-form.b5669646.js";import"./u-line.ddd38835.js";import"./u-input.ef44c0c4.js";import"./pay.5385ae25.js";const J=H(e({__name:"payment",setup(e){const H=t({order_key:"",member_remark:"",discount:{},invoice:{},delivery:{delivery_type:""}}),J=l(),L=a((()=>J.info));q().setTopTabbarParam({title:"待付款订单"});const W=t(null),Z=t(),K=t(),M=t(),N=t(!1),X=t(0),Y=t([]),ee=t();uni.getStorageSync("orderCreateData")&&Object.assign(H.value,uni.getStorageSync("orderCreateData"));const te=t(0),le=t({}),ae=t(null),re=()=>{ae.value?ae.value.show=!0:"store"==H.value.delivery.delivery_type&&k({title:"请选择自提点",icon:"none"})},se=t(!1),ie=e=>{"local_delivery"==H.value.delivery.delivery_type?(se.value=!0,e.includes("立即配送")?(H.value.delivery.buyer_ask_delivery_time="",H.value.delivery.local_delivery_type="now"):(H.value.delivery.buyer_ask_delivery_time=e,H.value.delivery.local_delivery_type="subscribe")):H.value.delivery.buyer_ask_delivery_time=e},de=t(null),oe=e=>{"local_delivery"==H.value.delivery.delivery_type&&e.includes("立即配送")?de.value=null:de.value=e},ue=t({});r((()=>{Q().then((e=>{ue.value=e.data}))}));const ne=()=>{Z.value&&(H.value.delivery.take_store_id||Z.value.getData((e=>{e.length&&(H.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))),Z.value.open()},ce=uni.getStorageSync("selectAddressCallback");ce&&(H.value.order_key="",H.value.delivery.delivery_type=ce.delivery,H.value.delivery.take_address_id=ce.address_id,uni.removeStorage({key:"selectAddressCallback"}));const ve=(e,t)=>{"store"==e&&0==te.value&&(te.value++,Z.value.getData((e=>{var t;e.length&&(H.value.delivery.take_store_id=(null==(t=e[0])?void 0:t.store_id)??0,pe())}))),H.value.delivery.delivery_type!=e&&(X.value=t,H.value.order_key="",H.value.delivery.delivery_type=e,H.value.delivery.take_address_id=0,H.value.delivery.buyer_ask_delivery_time="",le.value={},pe())},pe=()=>{P(H.value).then((({data:e})=>{var t,l;W.value=e,H.value.order_key=e.order_key,W.value.delivery.delivery_type_list&&(Y.value=Object.values(W.value.delivery.delivery_type_list)),"store"==H.value.delivery.delivery_type?(H.value.delivery.taker_name=L.value.nickname,H.value.delivery.taker_mobile=L.value.mobile):W.value.delivery&&W.value.delivery.take_address&&(H.value.delivery.taker_name=W.value.delivery.take_address.name,H.value.delivery.taker_mobile=W.value.delivery.take_address.mobile),"local_delivery"==H.value.delivery.delivery_type&&(le.value={time_interval:ue.value.time_interval,time_week:(t=ue.value.time_type,l=ue.value.time_week,0===t?["1","2","3","4","5","6","0"]:l?l.split(",").map((e=>e.trim())):[]),trade_time_json:ue.value.delivery_time,most_day:ue.value.most_day,advance_day:ue.value.advance_day,type:"subscribe"}),W.value.delivery.take_store&&(le.value={time_interval:W.value.delivery.take_store.time_interval,time_week:W.value.delivery.take_store.time_week,trade_time_json:W.value.delivery.take_store.trade_time_json}),ce&&(X.value=Y.value.findIndex((e=>e.key===W.value.delivery.delivery_type))),!H.value.delivery.delivery_type&&e.delivery.delivery_type&&(H.value.delivery.delivery_type=e.delivery.delivery_type),g((()=>{setTimeout((()=>{Y.value&&Object.keys(Y.value).length&&"store"==Y.value[0].key&&Z.value&&Z.value.getData((e=>{e.length&&(H.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))}),500)}))})).catch()};pe(),s((()=>Y.value.length),((e,t)=>{Y.value.length&&uni.getStorageSync("distributionType")&&(Y.value.forEach(((e,t)=>{e.name==uni.getStorageSync("distributionType")&&(X.value=t,ve(e.key,t))})),uni.removeStorage({key:"distributionType"}))}));let xe=0;const _e=()=>{ye()&&!N.value&&(N.value=!0,U(H.value).then((({data:e})=>{var t;xe=e.order_id,0==W.value.basic.order_money?h({url:"/addon/shop/pages/order/detail",param:{order_id:xe},mode:"redirectTo"}):null==(t=K.value)||t.open(e.trade_type,e.order_id,`/addon/shop/pages/order/detail?order_id=${e.order_id}`)})).catch((e=>{401==e.code&&(k({icon:"none",title:"登录过期，请重新登录"}),h({url:"/addon/shop/pages/index",mode:"reLaunch"})),N.value=!1})))},ye=()=>{const e=H.value;if(W.value.basic.has_goods_types.includes("real")){if(["express","local_delivery"].includes(e.delivery.delivery_type)&&!W.value.delivery.take_address)return k({title:"请选择收货地址",icon:"none"}),!1;if("store"==e.delivery.delivery_type&&!e.delivery.take_store_id)return k({title:"请选择自提点",icon:"none"}),!1;if("store"==e.delivery.delivery_type){if(!e.delivery.taker_name)return k({title:"请输入姓名",icon:"none"}),!1;if(!e.delivery.taker_mobile)return k({title:"请输入手机号",icon:"none"}),!1;if(!/^1[3-9]\d{9}$/.test(e.delivery.taker_mobile))return k({title:"请输入正确的手机号",icon:"none"}),!1;if(!e.delivery.buyer_ask_delivery_time)return k({title:"请选择自提时间",icon:"none"}),!1}if("local_delivery"==e.delivery.delivery_type&&!se.value)return k({title:"当前时间不支持配送",icon:"none"}),!1}return!0},me=()=>{h({url:"/addon/shop/pages/order/detail",param:{order_id:xe},mode:"redirectTo"})},fe=()=>{let e={};e.delivery=H.value.delivery.delivery_type,e.type="local_delivery"==H.value.delivery.delivery_type?"location_address":"address",e.id=W.value.delivery.take_address.id,ee.value.open(e)},be=e=>{H.value.delivery.take_store_id=e&&e.store_id?e.store_id:0,e?le.value={time_interval:e.time_interval,time_week:e.time_week,trade_time_json:e.trade_time_json}:(le.value={},H.value.delivery.buyer_ask_delivery_time=""),pe()},ke=e=>{H.value.invoice=e},ge=e=>{H.value.order_key="",H.value.delivery.delivery_type=e.delivery,H.value.delivery.take_address_id=e.address_id,pe()};return(e,t)=>{const l=w,a=j,r=C,s=F,k=V(S("u--image"),B),g=V(S("u-alert"),A),h=T,P=V(S("u-tabbar"),E),U=V(S("pay"),G);return i(),d(l,{style:c(e.themeColor()),class:"payment-wrap"},{default:o((()=>[W.value?(i(),d(l,{key:0,class:"payment-body min-h-[100vh]"},{default:o((()=>[u(l,{class:"pt-[30rpx] sidebar-margin payment-bottom"},{default:o((()=>[n(" 配送方式 "),W.value.basic.has_goods_types.includes("real")&&Y.value.length?(i(),d(l,{key:0,class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] bg-white",style:c({backgroundImage:`url(${v(D)("addon/shop/payment/head_bg.png")})`,backgroundSize:"100%",backgroundRepeat:"no-repeat",backgroundPosition:"bottom"})},{default:o((()=>[Y.value.length>1?(i(),d(l,{key:0,class:"rounded-tl-[var(--rounded-big)] rounded-tr-[var(--rounded-big)] head-tab flex items-center w-full bg-[var(--shop-payment-header-tab-color)]"},{default:o((()=>[(i(!0),p(_,null,x(Y.value,((e,t)=>(i(),d(l,{key:t,class:f(["head-tab-item flex-1 relative",{active:t===X.value}])},{default:o((()=>[u(l,{class:"h-[74rpx] relative z-10 text-center leading-[74rpx] text-[28rpx]",onClick:l=>ve(e.key,t)},{default:o((()=>[y(m(e.name),1)])),_:2},1032,["onClick"]),t===X.value&&3==Y.value.length?(i(),d(a,{key:0,class:"tab-image absolute bottom-[-2rpx] h-[94rpx] w-[240rpx]",src:v(D)(`addon/shop/payment/tab_${t}.png`),mode:"aspectFit"},null,8,["src"])):t===X.value&&2==Y.value.length?(i(),d(a,{key:1,class:"tab-img absolute bottom-[-2rpx] h-[95rpx] w-[354rpx]",src:v(D)(`addon/shop/payment/tabstyle_${t}.png`),mode:"aspectFit"},null,8,["src"])):n("v-if",!0)])),_:2},1032,["class"])))),128))])),_:1})):n("v-if",!0),u(l,{class:"min-h-[140rpx] flex items-center px-[30rpx]"},{default:o((()=>[n(" 收货地址 "),["express","local_delivery"].includes(H.value.delivery.delivery_type)?(i(),d(l,{key:0,class:"w-full",onClick:fe},{default:o((()=>[e.$u.test.isEmpty(W.value.delivery.take_address)?(i(),d(l,{key:1,class:"flex items-center"},{default:o((()=>[u(a,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:v(D)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),u(r,{class:"text-[28rpx]"},{default:o((()=>[y("添加收货地址")])),_:1}),u(r,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)] ml-auto"})])),_:1})):(i(),d(l,{key:0,class:"pt-[20rpx] pb-[30rpx] flex items-center"},{default:o((()=>[u(a,{class:"w-[60rpx] h-[60rpx] mr-[20rpx] flex-shrink-0",src:v(D)("addon/shop/payment/position_01.png"),mode:"aspectFit"},null,8,["src"]),u(l,{class:"flex flex-col overflow-hidden"},{default:o((()=>[u(r,{class:"text-[26rpx] text-[var(--text-color-light9)] mt-[16rpx] truncate max-w-[536rpx]"},{default:o((()=>[y(m(W.value.delivery.take_address.full_address.split(W.value.delivery.take_address.address)[0]),1)])),_:1}),u(r,{class:"font-500 text-[30rpx] mt-[14rpx] text-[#333] truncate max-w-[536rpx]"},{default:o((()=>[y(m(W.value.delivery.take_address.address),1)])),_:1}),u(l,{class:"flex items-center text-[26rpx] text-[var(--text-color-light6)] mt-[16rpx]"},{default:o((()=>[u(r,{class:"mr-[16rpx]"},{default:o((()=>[y(m(W.value.delivery.take_address.name),1)])),_:1}),u(r,null,{default:o((()=>[y(m(v(O)(W.value.delivery.take_address.mobile)),1)])),_:1})])),_:1})])),_:1}),u(r,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):n("v-if",!0),n(" 自提点 "),"store"==H.value.delivery.delivery_type?(i(),d(l,{key:1,class:"flex items-center w-full flex items-center",onClick:ne},{default:o((()=>[e.$u.test.isEmpty(W.value.delivery.take_store)?(i(),d(l,{key:1,class:"flex items-center w-full"},{default:o((()=>[u(a,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:v(D)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),u(r,{class:"text-[28rpx]"},{default:o((()=>[y("请选择自提点")])),_:1}),u(r,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):(i(),d(l,{key:0,class:"pt-[26rpx] pb-[30rpx] w-full flex items-center"},{default:o((()=>[u(l,{class:"flex flex-col"},{default:o((()=>[u(l,{class:"text-[30rpx] font-500 text-[#303133] mb-[20rpx]"},{default:o((()=>[y(m(W.value.delivery.take_store.store_name),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[14rpx]"},{default:o((()=>[y("门店地址："+m(W.value.delivery.take_store.full_address),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[14rpx]"},{default:o((()=>[y("联系电话："+m(W.value.delivery.take_store.store_mobile),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:o((()=>[y("营业时间："+m(W.value.delivery.take_store.trade_time),1)])),_:1})])),_:1}),u(r,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):n("v-if",!0)])),_:1}),"store"==H.value.delivery.delivery_type?(i(),d(l,{key:1},{default:o((()=>[n(" 姓名 "),u(l,{class:"px-[20rpx] py-[14rpx]"},{default:o((()=>[u(l,{class:"flex justify-between items-center"},{default:o((()=>[u(l,{class:"text-color text-[26rpx]",onClick:re},{default:o((()=>[y("姓名")])),_:1}),u(s,{class:"text-right",maxlength:"20","placeholder-style":"color:#B1B3B5;font-size:26rpx",placeholder:"请输入",modelValue:H.value.delivery.taker_name,"onUpdate:modelValue":t[0]||(t[0]=e=>H.value.delivery.taker_name=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(" 预留手机 "),u(l,{class:"px-[20rpx] py-[14rpx]"},{default:o((()=>[u(l,{class:"flex justify-between items-center"},{default:o((()=>[u(l,{class:"text-color text-[26rpx]"},{default:o((()=>[y("预留手机")])),_:1}),u(s,{class:"text-right",maxlength:"11","placeholder-style":"color:#B1B3B5;font-size:26rpx",placeholder:"请输入",modelValue:H.value.delivery.taker_mobile,"onUpdate:modelValue":t[1]||(t[1]=e=>H.value.delivery.taker_mobile=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(" 提货时间 "),u(l,{class:"flex justify-between items-center box-border pt-[14rpx] pb-[24rpx] px-[20rpx]"},{default:o((()=>[u(l,{class:"text-color text-[26rpx]"},{default:o((()=>[y("提货时间")])),_:1}),u(l,{class:"flex",onClick:re},{default:o((()=>[u(l,{class:f(["text-[26rpx] ml-2 text-right",{"text-[#63676D]":!H.value.delivery.buyer_ask_delivery_time}])},{default:o((()=>[y(m(H.value.delivery.buyer_ask_delivery_time?de.value:"选择提货时间"),1)])),_:1},8,["class"]),u(r,{class:"text-[26rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1})])),_:1})):n("v-if",!0),"local_delivery"==H.value.delivery.delivery_type?(i(),d(l,{key:2,class:""},{default:o((()=>[u(l,{class:"flex justify-between items-center px-[20rpx] pt-[14rpx] pb-[24rpx]",onClick:re},{default:o((()=>[u(l,{class:"text-color text-[26rpx]"},{default:o((()=>[y(m("subscribe"==H.value.delivery.local_delivery_type?"预约配送":""),1)])),_:1}),u(l,{class:"flex items-center"},{default:o((()=>["subscribe"==H.value.delivery.local_delivery_type?(i(),d(l,{key:0,class:f(["text-[26rpx] ml-2 text-right",{"text-[#63676D]":!H.value.delivery.buyer_ask_delivery_time}])},{default:o((()=>[y(m(H.value.delivery.buyer_ask_delivery_time?de.value:"选择时间"),1)])),_:1},8,["class"])):(i(),d(l,{key:1,class:"text-[26rpx] ml-2 text-right"},{default:o((()=>[y(m(se.value?"立即配送":"当前时间不支持配送"),1)])),_:1})),u(r,{class:"text-[26rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1})])),_:1})):n("v-if",!0)])),_:1},8,["style"])):n("v-if",!0),W.value.basic.has_goods_types.includes("real")&&!Y.value.length?(i(),d(l,{key:1,class:"mb-[var(--top-m)] card-template h-[100rpx] flex items-center"},{default:o((()=>[b("p",{class:"text-[28rpx] text-[var(--primary-color)]"},"商家尚未配置配送方式")])),_:1})):n("v-if",!0),u(l,{class:"mb-[var(--top-m)] card-template"},{default:o((()=>[u(l,{class:"mb-[30rpx]"},{default:o((()=>[(i(!0),p(_,null,x(W.value.goods_data,((e,t,s)=>(i(),d(l,{class:f(["flex",{"pb-[40rpx]":s+1!=Object.keys(W.value.goods_data).length}]),key:s},{default:o((()=>[u(k,{radius:"var(--goods-rounded-big)",width:"180rpx",height:"180rpx",src:v(D)(e.sku_image.split(",")[0]),model:"aspectFill"},{error:o((()=>[u(a,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:v(D)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),u(l,{class:"flex flex-1 w-0 flex-col justify-between ml-[20rpx] py-[6rpx]"},{default:o((()=>[u(l,{class:"line-normal"},{default:o((()=>[u(l,{class:"truncate text-[#303133] text-[28rpx] leading-[32rpx]"},{default:o((()=>[y(m(e.goods.goods_name),1)])),_:2},1024),e.sku_name?(i(),d(l,{key:0,class:"mt-[14rpx] flex"},{default:o((()=>[u(r,{class:"truncate text-[24rpx] text-[var(--text-color-light9)] leading-[28rpx]"},{default:o((()=>[y(m(e.sku_name),1)])),_:2},1024)])),_:2},1024)):n("v-if",!0)])),_:2},1024),e.not_support_delivery?(i(),d(l,{key:0,class:f(["mb-auto",{"mt-[6rpx]":!e.sku_name}])},{default:o((()=>[u(g,{type:"error",description:"该商品不支持当前所选配送方式",class:"leading-[30rpx] !inline-block",fontSize:"11"})])),_:2},1032,["class"])):n("v-if",!0),u(l,{class:"flex justify-between items-baseline"},{default:o((()=>[u(l,{class:"text-[var(--price-text-color)] flex items-baseline price-font"},{default:o((()=>[u(l,{class:"flex items-baseline price-font"},{default:o((()=>[u(r,{class:"text-[40rpx] font-200"},{default:o((()=>[y(m(e.exchange_info.point),1)])),_:2},1024),u(r,{class:"text-[32rpx]"},{default:o((()=>[y("积分")])),_:1})])),_:2},1024),parseFloat(e.price)?(i(),p(_,{key:0},[u(r,{class:"mx-[4rpx] text-[32rpx]"},{default:o((()=>[y("+")])),_:1}),u(l,{class:"flex items-baseline price-font"},{default:o((()=>[u(r,{class:"text-[40rpx] font-200"},{default:o((()=>[y(m(parseFloat(e.price).toFixed(2)),1)])),_:2},1024),u(r,{class:"text-[32rpx]"},{default:o((()=>[y("元")])),_:1})])),_:2},1024)],64)):n("v-if",!0)])),_:2},1024),u(l,{class:"font-400 text-[28rpx] text-[#303133]"},{default:o((()=>[u(r,null,{default:o((()=>[y("x")])),_:1}),u(r,null,{default:o((()=>[y(m(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1}),n(" 买家留言 "),u(l,{class:"bg-white flex items-center leading-[30rpx]"},{default:o((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:o((()=>[y("买家留言")])),_:1}),u(l,{class:"flex-1 text-[#303133]"},{default:o((()=>[u(s,{type:"text",modelValue:H.value.member_remark,"onUpdate:modelValue":t[2]||(t[2]=e=>H.value.member_remark=e),class:"text-right text-[#333] text-[28rpx]",maxlength:"50",placeholder:"请输入留言信息给卖家","placeholder-class":"text-[var(--text-color-light9)] text-[28rpx]"},null,8,["modelValue"])])),_:1}),n(' <text class="nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"></text> ')])),_:1}),n(" 发票 "),M.value&&M.value.invoiceOpen?(i(),d(l,{key:0,class:"flex items-center text-[#303133] leading-[30rpx] mt-[30rpx]",onClick:t[3]||(t[3]=e=>M.value.open())},{default:o((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:o((()=>[y("发票信息")])),_:1}),u(l,{class:"flex-1 w-0 text-right truncate"},{default:o((()=>[u(r,{class:"text-[28rpx] text-[#333]"},{default:o((()=>[y(m(H.value.invoice.header_name||"不需要发票"),1)])),_:1})])),_:1}),u(r,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):n("v-if",!0)])),_:1}),u(l,{class:"card-template"},{default:o((()=>[u(l,{class:"title"},{default:o((()=>[y("价格明细")])),_:1}),u(l,{class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[28rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:o((()=>[y("商品金额")])),_:1}),u(l,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:o((()=>[u(l,{class:"inline-block"},{default:o((()=>[u(r,{class:"text-[32rpx] mr-[2rpx]"},{default:o((()=>[y(m(W.value.basic.point_sum),1)])),_:1}),u(r,{class:"text-[30rpx]"},{default:o((()=>[y("积分")])),_:1})])),_:1}),W.value.basic&&parseFloat(W.value.basic.goods_money)?(i(),p(_,{key:0},[u(r,{class:"text-[28rpx] mx-[4rpx]"},{default:o((()=>[y("+")])),_:1}),u(l,{class:"inline-block"},{default:o((()=>[u(r,{class:"text-[32rpx] mr-[2rpx]"},{default:o((()=>[y(m(parseFloat(W.value.basic.goods_money).toFixed(2)),1)])),_:1}),u(r,{class:"text-[30rpx]"},{default:o((()=>[y("元")])),_:1})])),_:1})],64)):n("v-if",!0)])),_:1})])),_:1}),W.value.basic.delivery_money?(i(),d(l,{key:0,class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[26rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:o((()=>[y("配送费用")])),_:1}),u(l,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:o((()=>[y("￥"+m(parseFloat(W.value.basic.delivery_money)),1)])),_:1})])),_:1})):n("v-if",!0),W.value.basic.discount_money?(i(),d(l,{key:1,class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[26rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:o((()=>[y("优惠金额")])),_:1}),u(l,{class:"flex-1 w-0 text-right text-[var(--price-text-color)] text-[32rpx] price-font leading-[1]"},{default:o((()=>[y("-￥"+m(parseFloat(W.value.basic.discount_money)),1)])),_:1})])),_:1})):n("v-if",!0)])),_:1})])),_:1}),u(P,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0},{default:o((()=>[u(l,{class:"flex-1 flex items-center justify-between pl-[30rpx] pr-[20rpx]"},{default:o((()=>[u(l,{class:"flex items-baseline"},{default:o((()=>[u(r,{class:"text-[26rpx] text-[#333] leading-[32rpx]"},{default:o((()=>[y("合计：")])),_:1}),u(r,{class:"text-[var(--price-text-color)] price-font inline-block"},{default:o((()=>[u(r,{class:"text-[44rpx]"},{default:o((()=>[y(m(W.value.basic.point_sum),1)])),_:1}),u(r,{class:"text-[38rpx]"},{default:o((()=>[y("积分")])),_:1})])),_:1}),W.value.basic&&parseFloat(W.value.basic.goods_money)?(i(),p(_,{key:0},[u(r,{class:"text-[38rpx] text-[var(--price-text-color)] price-font mx-[4rpx]"},{default:o((()=>[y("+")])),_:1}),u(l,{class:"inline-block"},{default:o((()=>[u(r,{class:"text-[44rpx] text-[var(--price-text-color)] price-font"},{default:o((()=>[y(m(parseFloat(W.value.basic.order_money).toFixed(2)),1)])),_:1}),u(r,{class:"text-[38rpx] text-[var(--price-text-color)] price-font"},{default:o((()=>[y("元")])),_:1})])),_:1})],64)):n("v-if",!0)])),_:1}),u(h,{class:"primary-btn-bg w-[196rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] !border-[0] rounded-[100rpx] m-0","hover-class":"none",onClick:_e},{default:o((()=>[y("提交订单 ")])),_:1})])),_:1})])),_:1}),n(" 选择自提点 "),u(v($),{ref_key:"storeRef",ref:Z,onConfirm:be},null,512),n(" 发票 "),u(v(z),{ref_key:"invoiceRef",ref:M,onConfirm:ke},null,512),n(" 地址 "),u(v(R),{ref_key:"addressRef",ref:ee,onConfirm:ge,back:"/addon/shop/pages/point/payment"},null,512),u(U,{ref_key:"payRef",ref:K,onClose:me,"ignore-pay":["friendspay"]},null,512),Object.keys(le.value).length?(i(),d(v(I),{key:0,ref_key:"selectTime",ref:ae,rules:le.value,isQuantum:!0,isOpen:ue.value.time_is_open,onChange:ie,onGetDate:oe},null,8,["rules","isOpen"])):n("v-if",!0)])),_:1})):n("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-5afd8abe"]]);export{J as default};

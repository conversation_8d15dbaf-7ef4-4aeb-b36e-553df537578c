import{_ as o}from"./loading-page.vue_vue_type_script_setup_true_lang.c88f563e.js";import{d as t,p as e,l as s,u as r,r as a,a as p,o as i,c as u,w as m,b as n,e as _,f as l,v as j,g as d,n as g,i as c,j as f,k as v}from"./index-dd56d0cc.js";import{u as y}from"./useDiy.7a879ac9.js";import{d as b}from"./index.2c75d097.js";import{_ as x}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.f15d7447.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-popup.457e1f1f.js";import"./u-safe-bottom.22d4d63b.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./u-button.b6743e99.js";import"./u-input.ef44c0c4.js";import"./u-picker.1af38a2a.js";import"./u-upload.44346d61.js";import"./u-radio-group.3476fb8a.js";import"./diy_form.2c8e4c0e.js";import"./u-action-sheet.daa5fa92.js";import"./u-line.ddd38835.js";import"./u-avatar.ea828bd7.js";import"./u-text.1f240d34.js";import"./u-parse.ae2d35cb.js";import"./tabbar.0d5e534b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";/* empty css                                                                */import"./goods.dbf7b09d.js";import"./useGoods.392f2eb1.js";import"./add-cart-popup.4746de5d.js";import"./u-number-box.41986fc4.js";import"./coupon.506f719c.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./bind-mobile.9929c841.js";import"./u-form.b5669646.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-modal.0666cf44.js";import"./newcomer.c56b90d6.js";import"./order.22f5d222.js";const h=x(t({__name:"index",setup(t){const x=e(),h=s((()=>x.info)),{setShare:k}=r(),S=y({name:"DIY_MEMBER_INDEX"}),w=a(null);return a(null),S.onLoad(),S.onShow((o=>{var t;o.value||o.page&&p({url:o.page,mode:"reLaunch"});let s=o.share?JSON.parse(o.share):null;k(s),null==(t=w.value)||t.refresh(),h.value&&e().getMemberInfo()})),S.onHide(),S.onUnload(),S.onPageScroll(),(t,e)=>{const s=c(f("loading-page"),o),r=v;return i(),u(r,{style:g(t.themeColor())},{default:m((()=>[n(s,{loading:_(S).getLoading()},null,8,["loading"]),l(n(r,null,{default:m((()=>[d(" 自定义模板渲染 "),n(r,{class:"diy-template-wrap bg-index",style:g(_(S).pageStyle())},{default:m((()=>[n(b,{ref_key:"diyGroupRef",ref:w,data:_(S).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[j,!_(S).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-93171cda"]]);export{h as default};

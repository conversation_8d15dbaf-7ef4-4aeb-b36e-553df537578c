import{d as e,r as t,N as r,o as a,c as l,w as o,b as n,A as s,R as p,a3 as d,S as u,T as i,B as x,y as c,k as m,bs as b,bt as f,H as v,i as g,j as y}from"./index-dd56d0cc.js";import{_ as h}from"./u-popup.457e1f1f.js";import{_ as w}from"./_plugin-vue_export-helper.1b428a4d.js";const D=w(e({__name:"select-date",emits:["confirm"],setup(e,{expose:w,emit:D}){const _=t(!1),$=t([]),k=()=>{const e=new Date,t=[],r=[],a=[],l=e.getFullYear(),o=e.getMonth(),n=e.getDate();for(let v=1990;v<=e.getFullYear()+2;v++)t.push(v);for(let v=1;v<=12;v++)r.push(v);let s=Y(l,o+1);for(let v=1;v<=s;v++)a.push(v);let p=[t.indexOf(l),o,n-1],d=`${l}-${o+1<10?"0"+(o+1):o+1}-${n<10?"0"+n:n}`,u=`${l}-${o+1<10?"0"+(o+1):o+1}-${n<10?"0"+n:n} 00:00:00`,i=`${l}-${o+1<10?"0"+(o+1):o+1}-${n<10?"0"+n:n} 23:59:59`,x=new Date(e.getFullYear(),o-1,e.getDate()),c=new Date(e.getFullYear(),o-3,e.getDate()),m=new Date(e.getFullYear(),o-6,e.getDate()),b=new Date(e.getFullYear()-1,o,e.getDate());const f=e=>{const t=e.getFullYear(),r=e.getMonth()+1,a=e.getDate();return`${t}-${r<10?"0"+r:r}-${a<10?"0"+a:a} 00:00:00`};return{years:t,months:r,days:a,curIndex:p,nowDate:d,nowDateStart:u,nowDateEnd:i,lastMonth:f(x),lastThreeMonth:f(c),halfYear:f(m),lastYear:f(b)}},Y=(e,t)=>new Date(e,t,0).getDate(),j=r({years:k().years,months:k().months,days:k().days,curIndex:k().curIndex,nowDate:[k().nowDateStart,k().nowDateEnd]}),C=e=>{const t=e.detail.value;let r=j.years[t[0]],a=j.months[t[1]],l=j.days[t[2]];j.days=((e,t)=>{let r=Y(e,t),a=[];for(let l=1;l<=r;l++)a.push(l);return a})(r,a),"first"==M.value.type?j.nowDate[0]=`${r}-${a<10?"0"+a:a}-${l<10?"0"+l:l} 00:00:00`:"second"==M.value.type&&(j.nowDate[1]=`${r}-${a<10?"0"+a:a}-${l<10?"0"+l:l} 23:59:59`)},I=r([{time:[k().lastMonth,k().nowDateEnd],type:"lastMonth",name:"近1个月"},{time:[k().lastThreeMonth,k().nowDateEnd],type:"lastThreeMonth",name:"近3个月"},{time:[k().halfYear,k().nowDateEnd],type:"halfYear",name:"近半年"},{time:[k().lastYear,k().nowDateEnd],type:"lastYear",name:"近一年"}]),M=t({type:"first",time:[]}),E=()=>{if("first"==M.value.type||"second"==M.value.type){if($.value=j.nowDate,new Date($.value[0]).getTime()>new Date($.value[1]).getTime())return void c({title:"开始时间不能大于结束时间",icon:"none"})}else $.value=M.value.time;D("confirm",$.value),_.value=!1},F=()=>{M.value.type="first",j.curIndex=k().curIndex,j.nowDate=[k().nowDateStart,k().nowDateEnd]},T=()=>{D("confirm",[]),_.value=!1};return w({show:_}),(e,t)=>{const r=m,c=b,w=f,D=v,$=g(y("u-popup"),h);return a(),l($,{show:_.value,onClose:t[2]||(t[2]=e=>_.value=!1),mode:"bottom",round:10,zIndex:"10090"},{default:o((()=>[n(r,{class:"popup-common relative"},{default:o((()=>[n(r,{class:"title"},{default:o((()=>[s("选择时间")])),_:1}),n(r,{class:"absolute top-[36rpx] right-[36rpx] text-[24rpx] text-[var(--text-color-light6)] leading-[30rpx] z-10",onClick:T},{default:o((()=>[s("清除")])),_:1}),n(r,{class:"px-[var(--popup-sidebar-m)] mb-[20rpx] mt-[10rpx]"},{default:o((()=>[n(r,{class:"flex items-center justify-between mb-[30rpx]"},{default:o((()=>[(a(!0),p(u,null,d(I,((e,t)=>(a(),l(r,{class:i(["w-[160rpx] h-[66rpx] box-border flex-center rounded-[33rpx] bg-[var(--temp-bg)] text-center text-[26rpx] text-[var(--text-color-light6)] border-[2rpx] border-solid border-[var(--temp-bg)]",{"text-primary !border-[var(--primary-color)] !bg-[var(--primary-color-light)]":M.value.type==e.type}]),key:"a"+t,onClick:t=>{return r=e,M.value.type=r.type,M.value.time=r.time,j.nowDate[0]=M.value.time[0],void(j.nowDate[1]=M.value.time[1]);var r}},{default:o((()=>[s(x(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1}),n(r,{class:"flex items-center justify-between"},{default:o((()=>[n(r,{class:i(["w-[316rpx] h-[66rpx] box-border leading-[62rpx] rounded-[33rpx] bg-[var(--temp-bg)] text-center text-[26rpx] text-[var(--text-color-light6)] border-[2rpx] border-solid border-[var(--temp-bg)]",{"text-primary !border-[var(--primary-color)] !bg-[var(--primary-color-light)]":"first"==M.value.type}]),onClick:t[0]||(t[0]=e=>M.value.type="first")},{default:o((()=>[s(x(j.nowDate[0].substr(0,10)),1)])),_:1},8,["class"]),n(r,{class:"nc-iconfont nc-icon-jianV6xx"}),n(r,{class:i(["w-[316rpx] h-[66rpx] box-border leading-[62rpx] rounded-[33rpx] bg-[var(--temp-bg)] text-center text-[26rpx] text-[var(--text-color-light6)] border-[2rpx] border-solid border-[var(--temp-bg)]",{"text-primary !border-[var(--primary-color)] !bg-[var(--primary-color-light)]":"second"==M.value.type}]),onClick:t[1]||(t[1]=e=>M.value.type="second")},{default:o((()=>[s(x(j.nowDate[1].substr(0,10)),1)])),_:1},8,["class"])])),_:1})])),_:1}),n(r,{class:"gradation-picker"},{default:o((()=>[n(w,{"indicator-class":"!h-[80rpx] !bg-[var(--temp-bg)]",value:j.curIndex,onChange:C,class:"w-[750rpx] px-[60rpx] h-[396rpx] box-border"},{default:o((()=>[n(c,null,{default:o((()=>[(a(!0),p(u,null,d(j.years,((e,t)=>(a(),l(r,{class:"text-center leading-[80rpx] text-[28rpx]",key:t},{default:o((()=>[s(x(e)+"年",1)])),_:2},1024)))),128))])),_:1}),n(c,null,{default:o((()=>[(a(!0),p(u,null,d(j.months,((e,t)=>(a(),l(r,{class:"text-center leading-[80rpx] text-[28rpx]",key:t},{default:o((()=>[s(x(e)+"月",1)])),_:2},1024)))),128))])),_:1}),n(c,null,{default:o((()=>[(a(!0),p(u,null,d(j.days,((e,t)=>(a(),l(r,{class:"text-center leading-[80rpx] text-[28rpx]",key:t},{default:o((()=>[s(x(e)+"日",1)])),_:2},1024)))),128))])),_:1})])),_:1},8,["value"])])),_:1}),n(r,{class:"btn-wrap flex justify-between items-center !pt-[30rpx]"},{default:o((()=>[n(D,{class:"btn flex-1 !leading-[76rpx] mr-[20rpx] !text-[var(--primary-color)] border-[2rpx] border-solid border-[var(--primary-color)] rounded-[100rpx] bg-transparent box-border","hover-class":"none",onClick:F},{default:o((()=>[s("重置")])),_:1}),n(D,{class:"btn flex-1 border-[0] rounded-[100rpx] primary-btn-bg",shape:"circle","hover-class":"none",onClick:E},{default:o((()=>[s("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}}),[["__scopeId","data-v-4bff8688"]]);export{D as s};

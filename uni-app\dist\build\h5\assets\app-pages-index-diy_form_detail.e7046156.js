import{a4 as t,a5 as e,a6 as i,a7 as o,a8 as r,i as s,j as a,o as l,c as d,w as p,b as u,A as n,n as m,B as c,g as _,I as j,k as y,d as x,r as f,s as h,e as v,F as g}from"./index-4b8dc7db.js";import{_ as b}from"./u-line.b0d89d5b.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang.73703bcc.js";import"./index.106c0c40.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-popup.e2790691.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-safe-bottom.987908cd.js";import"./top-tabbar.59f1aa86.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.5d9e7fd9.js";import"./u-checkbox.598bfa18.js";import"./u-checkbox-group.0c3be417.js";import"./u-button.7aa0e948.js";import"./u-loading-icon.11ef83b8.js";import"./u-input.7a7ec88f.js";import"./u-picker.0f983101.js";import"./u-upload.899e5f6a.js";import"./u-radio-group.b3c6fbb7.js";import"./diy_form.03750fb6.js";import"./u-action-sheet.8e2525cc.js";import"./u-avatar.db5bcfa1.js";import"./u-text.40d19739.js";import"./u-parse.1f53da94.js";import"./tabbar.805b8203.js";import"./u-badge.e47151e6.js";import"./u-tabbar.bc2ea30a.js";import"./category.146302da.js";import"./common.eabc72c7.js";import"./project.df03875d.js";import"./index.3da4012a.js";import"./u--image.892273b2.js";import"./u-image.18977c35.js";/* empty css                                                                */import"./goods.f34d2594.js";import"./useGoods.0898f296.js";import"./add-cart-popup.da2aa6c6.js";import"./u-number-box.cd35fabd.js";import"./coupon.d71b475a.js";import"./point.a8a5a12b.js";import"./rank.1f5790d0.js";import"./bind-mobile.259f3837.js";import"./u-form.a144799c.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-modal.775e667c.js";import"./newcomer.2ec60692.js";import"./order.b56aabaa.js";const C=S({name:"u-divider",mixins:[e,i,{props:{dashed:{type:Boolean,default:()=>t.divider.dashed},hairline:{type:Boolean,default:()=>t.divider.hairline},dot:{type:Boolean,default:()=>t.divider.dot},textPosition:{type:String,default:()=>t.divider.textPosition},text:{type:[String,Number],default:()=>t.divider.text},textSize:{type:[String,Number],default:()=>t.divider.textSize},textColor:{type:String,default:()=>t.divider.textColor},lineColor:{type:String,default:()=>t.divider.lineColor}}}],computed:{textStyle(){const t={};return t.fontSize=o(this.textSize),t.color=this.textColor,t},leftLineStyle(){const t={};return"left"===this.textPosition?t.width="80rpx":t.flex=1,t},rightLineStyle(){const t={};return"right"===this.textPosition?t.width="80rpx":t.flex=1,t}},emits:["click"],methods:{addStyle:r,click(){this.$emit("click")}}},[["render",function(t,e,i,o,r,x){const f=s(a("u-line"),b),h=j,v=y;return l(),d(v,{class:"u-divider",style:m([x.addStyle(t.customStyle)]),onClick:x.click},{default:p((()=>[u(f,{color:t.lineColor,customStyle:x.leftLineStyle,hairline:t.hairline,dashed:t.dashed},null,8,["color","customStyle","hairline","dashed"]),t.dot?(l(),d(h,{key:0,class:"u-divider__dot"},{default:p((()=>[n("●")])),_:1})):t.text?(l(),d(h,{key:1,class:"u-divider__text",style:m([x.textStyle])},{default:p((()=>[n(c(t.text),1)])),_:1},8,["style"])):_("v-if",!0),u(f,{color:t.lineColor,customStyle:x.rightLineStyle,hairline:t.hairline,dashed:t.dashed},null,8,["color","customStyle","hairline","dashed"])])),_:1},8,["style","onClick"])}],["__scopeId","data-v-14eab0c7"]]),w=x({__name:"diy_form_detail",setup(t){const e=f(0);return h((t=>{e.value=t.record_id})),(t,i)=>{const o=y,r=s(a("u-divider"),C);return l(),d(o,{style:m(t.themeColor())},{default:p((()=>[u(o,{class:"w-screen h-screen bg-[var(--page-bg-color)] min-h-[100vh]"},{default:p((()=>[u(o,{class:"bg-white p-3"},{default:p((()=>[u(o,{class:"text-[30rpx] font-500 leading-[45rpx]"},{default:p((()=>[n(c(v(g)("diyForm.detailInformation")),1)])),_:1}),u(r,{text:""}),_(" 动态渲染表单组件详情 "),u(k,{record_id:e.value,completeLayout:"style-1"},null,8,["record_id"])])),_:1})])),_:1})])),_:1},8,["style"])}}});export{w as default};

import{d as e,q as s,l as t,r as a,i as n,j as o,o as r,c as l,w as p,$ as c,b as d,C as u,A as i,e as m,B as _,aU as g,H as x,I as f,k as v,E as h,n as y,G as b,F as w}from"./index-4b8dc7db.js";import{_ as j}from"./u-popup.e2790691.js";import{_ as k}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-icon.33002907.js";/* empty css                                                               */import"./u-safe-bottom.987908cd.js";const C=k(e({__name:"nc-contact",props:{sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""}},setup(e,{expose:h}){const y=e,b=s(),w=t((()=>b.site)),k=a(!1),C=()=>{k.value=!0},I=()=>{g({phoneNumber:w.value.phone})};return h({}),(e,s)=>{const t=x,a=f,g=v,h=n(o("u-popup"),j);return r(),l(g,{class:"contact-wrap"},{default:p((()=>[c(e.$slots,"default",{},void 0,!0),d(t,{type:"default","hover-class":"none","open-type":"contact",class:"contact-button",onClick:C,"send-message-title":y.sendMessageTitle,"send-message-path":y.sendMessagePath,"send-message-img":y.sendMessageImg,"show-message-card":!0},null,8,["send-message-title","send-message-path","send-message-img"]),d(h,{show:k.value,onClose:s[3]||(s[3]=e=>k.value=!1),mode:"center",round:5,safeAreaInsetBottom:!1},{default:p((()=>[d(g,{onTouchmove:s[2]||(s[2]=u((()=>{}),["prevent","stop"]))},{default:p((()=>[d(g,{class:"py-[25rpx] text-sm leading-none border-0 border-solid border-b-[2rpx] border-[#eee] flex items-center justify-between"},{default:p((()=>[d(a,{class:"ml-[30rpx]"},{default:p((()=>[i("联系客服")])),_:1}),d(a,{class:"mr-[20rpx] nc-iconfont nc-icon-guanbiV6xx text-[35rpx]",onClick:s[0]||(s[0]=e=>k.value=!1)})])),_:1}),m(w)&&m(w).phone?(r(),l(g,{key:0,class:"px-6 py-3 w-[480rpx] h-[100rpx] text-sm"},{default:p((()=>[d(g,{class:"mb-[10rpx]"},{default:p((()=>[i("客服电话")])),_:1}),d(g,{onClick:I,class:"text-primary truncate"},{default:p((()=>[i(_(m(w).phone),1)])),_:1})])),_:1})):(r(),l(g,{key:1,class:"px-6 py-3 w-[480rpx] h-[100rpx] leading-[100rpx] text-sm"},{default:p((()=>[i("抱歉，商家暂无客服，请线下联系")])),_:1})),d(t,{onClick:s[1]||(s[1]=e=>k.value=!1),class:"!mx-[30rpx] !mb-[40rpx] !w-auto !h-[70rpx] text-[24rpx] leading-[70rpx] rounded-full text-white !bg-[#ff4500] !text-[#fff]"},{default:p((()=>[i("我知道了")])),_:1})])),_:1})])),_:1},8,["show"])])),_:3})}}}),[["__scopeId","data-v-690ae063"]]),I=k(e({__name:"contact",setup(e){const s=a(""),t=a(""),c=a("");return c.value=h("static/resource/images/member/contact_service.png"),(e,a)=>{const u=b,g=v,f=x,j=n(o("nc-contact"),C);return r(),l(g,{class:"min-h-[100vh]",style:y(e.themeColor())},{default:p((()=>[d(g,{class:"contact-wrap pt-[22%]"},{default:p((()=>[d(u,{src:m(h)("static/resource/images/member/contact_service.png"),mode:"widthFix"},null,8,["src"]),d(g,{class:"mt-[40rpx] text-[28rpx]"},{default:p((()=>[i("欢迎您联系我们，提供您宝贵的意见！")])),_:1}),d(j,{"send-message-title":s.value,"send-message-path":t.value,"send-message-img":c.value},{default:p((()=>[d(f,{type:"primary",class:"btn-wrap font-500 primary-btn-bg"},{default:p((()=>[i(_(m(w)("customerService")),1)])),_:1})])),_:1},8,["send-message-title","send-message-path","send-message-img"])])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-49fc01d4"]]);export{I as default};

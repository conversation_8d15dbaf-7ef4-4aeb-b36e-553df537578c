import{d as e,r as t,l as a,p as l,o as s,c as r,w as o,b as u,e as i,C as n,A as d,B as p,g as c,R as x,S as f,a3 as m,T as v,x as _,b2 as g,J as h,a as b,E as y,aw as k,G as w,i as j,j as C,k as S,I as F,ag as V,ap as T,H as E,F as L,u as I,s as O,M as z,aW as A,b7 as R,L as B,an as P,z as U,a1 as D,ai as M,am as G,av as N,n as W,aq as q,W as J,X as H,ar as Q,ba as X}from"./index-4b8dc7db.js";import{_ as Y}from"./u-swiper.b114a269.js";import{_ as Z}from"./u-avatar.db5bcfa1.js";import{_ as $}from"./u-icon.33002907.js";import{_ as K}from"./u--image.892273b2.js";import{_ as ee}from"./u-parse.1f53da94.js";import{_ as te}from"./u-popup.e2790691.js";import{_ as ae}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{j as le}from"./goods.f34d2594.js";import{c as se}from"./point.a8a5a12b.js";import{_ as re}from"./u-number-box.cd35fabd.js";import{b as oe}from"./bind-mobile.259f3837.js";import{_ as ue}from"./_plugin-vue_export-helper.1b428a4d.js";import{s as ie}from"./share-poster.ce0cf721.js";import{u as ne}from"./useGoods.0898f296.js";import"./u-loading-icon.11ef83b8.js";import"./u-text.40d19739.js";/* empty css                                                               */import"./u-image.18977c35.js";import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.987908cd.js";import"./u-form.a144799c.js";import"./u-line.b0d89d5b.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-input.7a7ec88f.js";import"./u-modal.775e667c.js";import"./u-checkbox.598bfa18.js";import"./u-checkbox-group.0c3be417.js";const de=ue(e({__name:"goods-sku",props:["goodsDetail"],emits:["change"],setup(e,{expose:I,emit:O}){const z=e,A=t(!1),R=t(null),B=t({skuId:"",name:[]}),P=t(""),U=t(1);a((()=>{let e="0.00";return e=Object.keys(q.value).length&&Object.keys(q.value.goods).length&&q.value.goods.member_discount&&_()&&q.value.member_price!=q.value.price&&q.value.member_price?q.value.member_price:q.value.price,e}));const D=()=>{setTimeout((()=>{(!U.value||U.value<=0)&&(U.value=1),U.value>=Number(q.value.detail.limit_num)&&(U.value=q.value.detail.limit_num)}),0)},M=()=>{setTimeout((()=>{(!U.value||U.value<=0)&&(U.value=1),U.value>=Number(q.value.detail.limit_num)&&(U.value=q.value.detail.limit_num)}),0)},G=l(),N=a((()=>G.info)),W=()=>{A.value=!1},q=a((()=>{let e=g(z.goodsDetail);return Object.keys(e).length&&(Object.keys(B.value.name).length||(B.value.name=e.sku_spec_format.split(",")),e.goodsSpec.forEach(((e,t)=>{let a=e.spec_values.split(",");e.values=[],a.forEach(((a,l)=>{e.values[l]={},e.values[l].name=a,e.values[l].selected=!1,e.values[l].disabled=!1,B.value.name.forEach(((s,r)=>{r==t&&s==a&&(e.values[l].selected=!0)}))}))})),Q(),e.skuList&&Object.keys(e.skuList).length&&e.skuList.forEach(((t,a)=>{t.sku_id==B.value.skuId&&(e.detail=t,e.is_join_exchange=t.is_join_exchange)})),B.value.name&&H(e,B.value.name)),e})),J=t(!0),H=(e,t)=>{const a=[];e.skuList.forEach(((e,t)=>{a.push(e.sku_name)})),J.value=!0,t&&t.length&&!a.includes(t.join(" "))&&(J.value=!1)},Q=()=>{z.goodsDetail.skuList.forEach(((e,t)=>{e.sku_spec_format==B.value.name.toString()&&(B.value.skuId=e.sku_id,O("change",e.sku_id))}))},X=t(null);t(uni.getStorageSync("isBindMobile"));const Y=()=>{if(!N.value&&uni.getStorageSync("isBindMobile"))return uni.setStorage({key:"loginBack",data:{url:"/addon/shop/pages/point/detail",param:{id:q.value.exchange_id}}}),X.value.open(),!1;if(!N.value)return h().setLoginBack({url:"/addon/shop/pages/point/detail",param:{id:q.value.exchange_id}}),!1;var e={sku_id:q.value.sku_id,num:U.value};uni.setStorage({key:"orderCreateData",data:{sku_data:[e]},success:()=>{const e=q.value.is_join_exchange?"/addon/shop/pages/point/payment":"/addon/shop/pages/order/payment";b({url:e})}}),W()};return I({open:(e="",t="")=>{P.value=e,A.value=!0,R.value=t}}),(e,t)=>{const a=w,l=j(C("u--image"),K),_=S,g=F,h=V,b=j(C("u-number-box"),re),I=T,O=E,z=j(C("u-popup"),te);return s(),r(_,{onTouchmove:t[4]||(t[4]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[u(z,{class:"popup-type",show:A.value,onClose:W,mode:"bottom"},{default:o((()=>[i(q).detail?(s(),r(_,{key:0,class:"py-[32rpx] relative",onTouchmove:t[3]||(t[3]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[u(_,{class:"flex px-[32rpx] mb-[40rpx]"},{default:o((()=>[u(_,{class:"w-[180rpx] h-[180rpx]"},{default:o((()=>[u(l,{width:"180rpx",height:"180rpx",radius:"var(--goods-rounded-big)",src:i(y)(i(q).detail.sku_image),onClick:t[0]||(t[0]=e=>(e=>{if(""===e)return!1;var t=[];t.push(y(e)),k({indicator:"number",loop:!0,urls:t})})(i(q).detail.sku_image)),model:"aspectFill"},{error:o((()=>[u(a,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:i(y)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["radius","src"])])),_:1}),u(_,{class:"flex flex-1 flex-col justify-between ml-[20rpx] py-[10rpx]"},{default:o((()=>[u(_,{class:"w-[100%]"},{default:o((()=>[u(_,{class:"text-[var(--price-text-color)] flex items-baseline"},{default:o((()=>[i(q).point?(s(),r(g,{key:0,class:"price-font"},{default:o((()=>[u(g,{class:"text-[44rpx]"},{default:o((()=>[d(p(i(q).point),1)])),_:1}),u(g,{class:"text-[38rpx]"},{default:o((()=>[d(p(i(L)("point")),1)])),_:1})])),_:1})):c("v-if",!0),i(q).point&&parseFloat(i(q).price)?(s(),r(g,{key:1,class:"text-[38rpx]"},{default:o((()=>[d("+ ")])),_:1})):c("v-if",!0),i(q).point&&parseFloat(i(q).price)?(s(),x(f,{key:2},[u(g,{class:"text-[44rpx] price-font"},{default:o((()=>[d(p(parseFloat(i(q).price).toFixed(2)),1)])),_:1}),u(g,{class:"text-[38rpx] price-font"},{default:o((()=>[d("元")])),_:1})],64)):c("v-if",!0),!i(q).point&&parseFloat(i(q).price)?(s(),x(f,{key:3},[u(g,{class:"text-[26rpx] price-font"},{default:o((()=>[d("￥")])),_:1}),u(g,{class:"text-[44rpx] price-font"},{default:o((()=>[d(p(parseFloat(i(q).price).toFixed(2).split(".")[0]),1)])),_:1}),u(g,{class:"text-[26rpx] mr-[6rpx] price-font"},{default:o((()=>[d("."+p(parseFloat(i(q).price).toFixed(2).split(".")[1]),1)])),_:1})],64)):c("v-if",!0)])),_:1}),u(_,{class:"text-[26rpx] leading-[32rpx] text-[var(--text-color-light6)] mt-[10rpx]"},{default:o((()=>[d("库存"+p(i(q).detail.stock)+p(i(q).goods.unit),1)])),_:1})])),_:1}),i(q).goodsSpec&&i(q).goodsSpec.length?(s(),r(_,{key:0,class:"text-[26rpx] leading-[30rpx] text-[var(--text-color-light6)] w-[100%] max-h-[60rpx] multi-hidden"},{default:o((()=>[d("已选规格："+p(i(q).detail.sku_spec_format),1)])),_:1})):c("v-if",!0)])),_:1})])),_:1}),u(I,{class:"h-[500rpx] px-[32rpx] box-border mb-[60rpx]","scroll-y":"true"},{default:o((()=>[(s(!0),x(f,null,m(i(q).goodsSpec,((e,t)=>(s(),r(_,{class:v({"mt-[20rpx]":0!=t}),key:t},{default:o((()=>[u(_,{class:"text-[28rpx] leading-[36rpx] mb-[24rpx]"},{default:o((()=>[d(p(e.spec_name),1)])),_:2},1024),u(_,{class:"flex flex-wrap"},{default:o((()=>[(s(!0),x(f,null,m(e.values,((e,a)=>(s(),r(_,{class:v(["box-border bg-[var(--temp-bg)] text-[24rpx] px-[44rpx] text-center h-[56rpx] flex-center mr-[20rpx] mb-[20rpx] border-1 border-solid rounded-[50rpx] border-[var(--temp-bg)]",{"!border-[var(--primary-color)] text-[var(--primary-color)] !bg-[var(--primary-color-light)]":e.selected}]),key:a,onClick:a=>((e,t)=>{B.value.name[t]=e.name,U.value=1,Q(),H(q.value,B.value.name)})(e,t)},{default:o((()=>[d(p(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1032,["class"])))),128)),u(_,{class:"flex justify-between items-center mt-[8rpx]"},{default:o((()=>[u(_,{class:"text-[28rpx]"},{default:o((()=>[d("购买数量")])),_:1}),i(q).detail.limit_num>0?(s(),r(g,{key:0,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:o((()=>[d("(限购"+p(i(q).detail.limit_num)+p(i(q).goods.unit)+")",1)])),_:1})):c("v-if",!0),u(b,{min:1,max:parseInt(i(q).detail.limit_num)<i(q).stock?parseInt(i(q).detail.limit_num):i(q).stock,integer:"",step:1,"input-width":"68rpx",modelValue:U.value,"onUpdate:modelValue":t[2]||(t[2]=e=>U.value=e),"input-height":"52rpx"},{minus:o((()=>[u(g,{class:v(["text-[30rpx] nc-iconfont nc-icon-jianV6xx font-500",{"!text-[var(--text-color-light9)]":U.value<=1}])},null,8,["class"])])),input:o((()=>[u(h,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:D,onBlur:M,modelValue:U.value,"onUpdate:modelValue":t[1]||(t[1]=e=>U.value=e)},null,8,["modelValue"])])),plus:o((()=>[u(g,{class:v(["text-[30rpx] nc-iconfont nc-icon-jiahaoV6xx font-500",{"!text-[var(--text-color-light9)]":U.value>=i(q).stock||U.value==parseInt(i(q).detail.limit_num)}])},null,8,["class"])])),_:1},8,["max","modelValue"])])),_:1})])),_:1}),u(_,{class:"px-[20rpx]"},{default:o((()=>[i(q).detail.stock>0?(s(),x(f,{key:0},[J.value?(s(),r(O,{key:0,"hover-class":"none",class:"!h-[80rpx] primary-btn-bg leading-[80rpx] text-[26rpx] rounded-[50rpx] font-500",type:"primary",onClick:Y},{default:o((()=>[d("确定")])),_:1})):(s(),r(O,{key:1,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] rounded-[50rpx] font-500",disabled:""},{default:o((()=>[d("该规格未参与积分商品")])),_:1}))],64)):(s(),r(O,{key:1,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] text-[#fff] bg-[#ccc] rounded-[50rpx] font-500"},{default:o((()=>[d("已售罄")])),_:1}))])),_:1})])),_:1})):c("v-if",!0)])),_:1},8,["show"]),c(" 强制绑定手机号 "),u(oe,{ref_key:"bindMobileRef",ref:X},null,512)])),_:1})}}}),[["__scopeId","data-v-43eadc80"]]),pe=ue(e({__name:"detail",setup(e){const _=ne(),{setShare:g}=I(),h=t(1),V=t("img"),re=t(null),oe=l(),ue=a((()=>oe.info)),pe=t(""),ce=t(""),xe=t(""),fe=t(null),me=t({}),ve=t(!1),_e=t(!1),ge=t(!1),he=t(!1);t(null),O((e=>{se(e.id||"").then((e=>{if("[]"===JSON.stringify(e.data)){return z({url:"/addon/shop/pages/index",title:"找不到该商品",mode:"reLaunch"}),!1}me.value=A(e.data),me.value.delivery_type_list=me.value.goods.delivery_type_list?Object.values(me.value.goods.delivery_type_list):[],me.value.goods.goods_image=me.value.goods.goods_image_thumb_big,me.value.goods.goods_image.forEach(((e,t)=>{me.value.goods.goods_image[t]=y(e)}));let t=A(e.data);if(me.value.goods.attr_format=[],t.goods&&t.goods.attr_format){A(t.goods.attr_format).forEach(((e,t)=>{(e.attr_child_value_name&&!(e.attr_child_value_name instanceof Array)||e.attr_child_value_name instanceof Array&&e.attr_child_value_name.length)&&me.value.goods.attr_format.push(e)}))}""!=me.value.goods.goods_video&&(V.value="video",re.value=R("goodsVideo")),pe.value=me.value.goods.goods_name,ce.value="/addon/shop/pages/point/detail?sku_id="+me.value.sku_id,xe.value=y(me.value.goods.goods_cover_thumb_mid);let a={title:me.value.goods.goods_name,desc:me.value.goods.sub_title,url:me.value.goods.goods_cover_thumb_mid};B({title:me.value.goods.goods_name}),g({wechat:{...a},weapp:{...a}}),Ce(),De(),P((()=>{setTimeout((()=>{const e=q().in(Ie);e.select(".swiper-box").boundingClientRect((e=>{Oe=e?e.height:0})).exec(),e.select(".detail-head").boundingClientRect((e=>{e&&(ze=e.height?e.height:0)})).exec()}),400)}))}))})),U((()=>{uni.removeStorageSync("distributionType")}));const be=e=>{me.value.skuList.forEach(((t,a)=>{t.sku_id==e&&Object.assign(me.value,t)}))},ye=a((()=>{let e=!1;return me.value.skuList.length&&me.value.skuList.forEach(((t,a)=>{t.sku_spec_format&&(e=!0)})),!(!e&&me.value.stock<=0)&&(!e&&me.value.stock,!0)})),ke=a((()=>{let e=!1;return(me.value.service&&me.value.service.length||me.value.goodsSpec&&me.value.goodsSpec.length||"real"==me.value.goods.goods_type&&me.value.delivery_type_list&&me.value.delivery_type_list.length)&&(e=!0),e})),we=e=>{fe.value.open(e)},je=t({count:0}),Ce=()=>{le(me.value.goods_id).then((e=>{je.value=e.data}))},Se=(e,t)=>{if(Array.isArray(e)){var a=e;if(!e.length)return!1;k({indicator:"number",current:t,loop:!0,urls:a})}else{if(""===e)return!1;(a=[]).push(y(e)),k({indicator:"number",loop:!0,urls:a})}},Fe=t(0),Ve=()=>{he.value=!0};let Te=D().platform;const Ee=a((()=>{let e="";return e+="height: 100rpx;",e+="padding-right: 30rpx;",e+="padding-left: 30rpx;",e+="font-size: 32rpx;","ios"===Te?e+="font-weight: 500;":"android"===Te&&(e+="font-size: 36rpx;"),e})),Le=a((()=>{let e="";return e+="font-size: 26px;","font-size: 26px;"})),Ie=Q();let Oe=0,ze=0;const Ae=t(!1);M((e=>{let t=Oe-ze-20;Ae.value=!1,e.scrollTop>=t&&(Ae.value=!0)}));const Re=e=>{"number"==typeof e&&Se(me.value.goods.goods_image,e)},Be=t(null),Pe=t("");let Ue={};const De=()=>{Pe.value="?id="+me.value.exchange_id,ue.value&&ue.value.member_id&&(Pe.value+="&mid="+ue.value.member_id)},Me=()=>{Ue.id=me.value.exchange_id,ue.value&&ue.value.member_id&&(Ue.member_id=ue.value.member_id),Be.value.openShare()};G((()=>{try{N()}catch(e){}}));const Ge=e=>{h.value=e.current+1};return(e,t)=>{const a=F,l=S,g=j(C("u-swiper"),Y),k=X,I=w,O=j(C("u-avatar"),Z),z=j(C("u-icon"),$),A=j(C("u--image"),K),R=j(C("u-parse"),ee),B=E,P=T,U=j(C("u-popup"),te),D=j(C("loading-page"),ae);return s(),r(l,{style:W(e.themeColor())},{default:o((()=>[Object.keys(me.value).length?(s(),r(l,{key:0,class:"bg-[var(--page-bg-color)] min-h-[100vh] relative"},{default:o((()=>[c(" 自定义头部 "),u(l,{class:v(["flex items-center fixed left-0 right-0 z-10 bg-transparent detail-head",{"!bg-[#fff]":Ae.value}]),style:W(i(Ee))},{default:o((()=>[u(a,{class:"nc-iconfont nc-icon-zuoV6xx",style:W(i(Le)),onClick:t[0]||(t[0]=e=>{J().length>1?H({delta:1}):b({url:"/addon/shop/pages/index",mode:"reLaunch"})})},null,8,["style"]),u(l,{class:v(["ml-auto !pt-[12rpx] !pb-[8rpx] p-[10rpx] bg-[rgba(255,255,255,.4)] rounded-full border-[2rpx] border-solid border-transparent box-border nc-iconfont nc-icon-fenxiangV6xx font-bold text-[#303133] text-[36rpx]",{"border-[#d8d8d8]":Ae.value}]),onClick:Me},null,8,["class"])])),_:1},8,["class","style"]),u(l,{class:"w-full h-[100vw] relative overflow-hidden"},{default:o((()=>[u(l,{class:v(["absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-linear transform",{"translate-x-0":"img"===V.value,"translate-x-full":"img"!=V.value}])},{default:o((()=>[u(l,{class:"swiper-box"},{default:o((()=>[u(g,{list:me.value.goods.goods_image,indicator:me.value.goods.goods_image.length,onChange:Ge,indicatorStyle:{bottom:"68rpx"},autoplay:!0,height:"100vw",onClick:Re},null,8,["list","indicator"])])),_:1})])),_:1},8,["class"]),u(l,{onTouchmove:t[1]||(t[1]=n((()=>{}),["stop","prevent"])),class:v(["media-mode absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-linear transform",{"translate-x-0":"video"===V.value,"-translate-x-full":"video"!=V.value}]),style:W({background:"url("+i(y)(me.value.goods.goods_cover_thumb_mid)+") left bottom / cover no-repeat"})},{default:o((()=>[u(l,{class:"goods-video-height"},{default:o((()=>[u(k,{id:"goodsVideo",class:"w-full h-full",src:i(y)(me.value.goods.goods_video),poster:i(y)(me.value.goods.goods_cover_thumb_mid),objectFit:"cover","play-btn-position":"center"},null,8,["src","poster"])])),_:1})])),_:1},8,["class","style"]),c(" 切换视频、图片 "),""!=me.value.goods.goods_video?(s(),r(l,{key:0,class:"media-mode bg-[rgb(0,0,0,.5)] rounded-[50rpx] p-[4rpx] absolute bottom-[130rpx] right-[20rpx] text-center leading-[46rpx]"},{default:o((()=>[u(a,{class:v(["tab-item",{"!bg-[#fff] !text-[#666]":"video"==V.value}]),onClick:t[2]||(t[2]=e=>V.value="video")},{default:o((()=>[d("视频")])),_:1},8,["class"]),u(l,{class:v(["tab-item flex items-center",{"!bg-[#fff] !text-[#666]":"img"==V.value}]),onClick:t[3]||(t[3]=e=>(V.value="img",re.value.pause()))},{default:o((()=>{var e,t,l;return[u(a,{class:"mr-[4rpx]"},{default:o((()=>[d("图片")])),_:1}),"img"==V.value&&(null==(l=null==(t=null==(e=me.value)?void 0:e.goods)?void 0:t.goods_image)?void 0:l.length)>1?(s(),r(a,{key:0},{default:o((()=>{var e,t,a;return[d(p(h.value)+"/"+p(null==(a=null==(t=null==(e=me.value)?void 0:e.goods)?void 0:t.goods_image)?void 0:a.length),1)]})),_:1})):c("v-if",!0)]})),_:1},8,["class"])])),_:1})):c("v-if",!0)])),_:1}),u(l,{class:"rounded-t-[40rpx] -mt-[40rpx] relative flex items-center justify-between !bg-cover box-border pb-[26rpx] h-[136rpx] px-[30rpx]",style:W({background:"url("+i(y)("addon/shop/detail/discount_price_bg.png")+") no-repeat"})},{default:o((()=>[u(l,{class:"flex items-baseline text-[#fff]"},{default:o((()=>[u(l,{class:"flex items-center"},{default:o((()=>[me.value.point?(s(),r(l,{key:0,class:"inline-block price-font"},{default:o((()=>[u(a,{class:"text-[44rpx]"},{default:o((()=>[d(p(me.value.point),1)])),_:1}),u(a,{class:"text-[38rpx]"},{default:o((()=>[d(p(i(L)("point")),1)])),_:1})])),_:1})):c("v-if",!0),me.value.point&&parseFloat(me.value.price)?(s(),r(a,{key:1,class:"text-[38rpx]"},{default:o((()=>[d("+")])),_:1})):c("v-if",!0),parseFloat(me.value.price)?(s(),r(l,{key:2,class:"inline-block price-font"},{default:o((()=>[u(a,{class:"text-[44rpx]"},{default:o((()=>[d(p(parseFloat(me.value.price).toFixed(2)),1)])),_:1}),u(a,{class:"text-[38rpx]"},{default:o((()=>[d(p(i(L)("priceUnit")),1)])),_:1})])),_:1})):c("v-if",!0)])),_:1})])),_:1})])),_:1},8,["style"]),u(l,{class:"bg-[var(--page-bg-color)] overflow-hidden rounded-[40rpx] -mt-[28rpx] relative"},{default:o((()=>[u(l,{class:"detail-title relative px-[30rpx] pt-[40rpx]"},{default:o((()=>[u(l,{class:"text-[#333] font-medium text-[30rpx] multi-hidden leading-[40rpx]"},{default:o((()=>[me.value.goods.goods_brand?(s(),r(l,{key:0,class:"brand-tag middle",style:W(i(_).baseTagStyle(me.value.goods.goods_brand))},{default:o((()=>[d(p(me.value.goods.goods_brand.brand_name),1)])),_:1},8,["style"])):c("v-if",!0),d(" "+p(me.value.goods.goods_name),1)])),_:1}),u(l,{class:"text-[26rpx] text-[#666] truncate my-[16rpx] leading-[33rpx]"},{default:o((()=>[d(p(me.value.goods.sub_title),1)])),_:1}),me.value.label_info&&me.value.label_info.length?(s(),r(l,{key:0,class:"flex flex-wrap mt-[16rpx]"},{default:o((()=>[(s(!0),x(f,null,m(me.value.label_info,(e=>(s(),x(f,{key:e.label_id},["icon"==e.style_type&&e.icon?(s(),r(I,{key:0,class:"img-tag middle",src:i(y)(e.icon),mode:"heightFix",onError:t=>i(_).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?c("v-if",!0):(s(),r(l,{key:1,class:"base-tag middle",style:W(i(_).baseTagStyle(e))},{default:o((()=>[d(p(e.label_name),1)])),_:2},1032,["style"]))],64)))),128))])),_:1})):c("v-if",!0),u(l,{class:"flex justify-between items-start mt-[24rpx]"},{default:o((()=>[me.value.market_price&&parseFloat(me.value.market_price)?(s(),r(l,{key:0,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:o((()=>[u(a,{class:"whitespace-nowrap mr-[4rpx]"},{default:o((()=>[d("划线价:")])),_:1}),u(a,{class:"line-through"},{default:o((()=>[d("￥"+p(me.value.market_price),1)])),_:1})])),_:1})):c("v-if",!0),me.value.stock&&parseFloat(me.value.stock)?(s(),r(l,{key:1,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:o((()=>[u(a,{class:"whitespace-nowrap mr-[4rpx]"},{default:o((()=>[d("库存:")])),_:1}),u(a,null,{default:o((()=>[d(p(me.value.stock),1)])),_:1}),u(a,null,{default:o((()=>[d(p(me.value.goods.unit),1)])),_:1})])),_:1})):c("v-if",!0),u(l,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)] flex items-baseline"},{default:o((()=>[u(a,{class:"whitespace-nowrap mr-[4rpx]"},{default:o((()=>[d("销量:")])),_:1}),u(a,{class:"mx-[2rpx]"},{default:o((()=>[d(p(me.value.goods.sale_num),1)])),_:1}),u(a,null,{default:o((()=>[d(p(me.value.goods.unit),1)])),_:1})])),_:1})])),_:1})])),_:1}),i(ke)?(s(),r(l,{key:0,class:"mt-[24rpx] sidebar-margin card-template"},{default:o((()=>[me.value.service&&me.value.service.length?(s(),r(l,{key:0,onClick:t[4]||(t[4]=e=>ge.value=!ge.value),class:"card-template-item"},{default:o((()=>[u(a,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:o((()=>[d("服务")])),_:1}),u(l,{class:"text-[#343434] text-[26rpx] leading-[30rpx] font-400 truncate ml-auto"},{default:o((()=>[d(p(me.value.service[0].service_name),1)])),_:1}),u(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):c("v-if",!0),me.value.goodsSpec&&me.value.goodsSpec.length?(s(),r(l,{key:1,onClick:we,class:"card-template-item"},{default:o((()=>[u(a,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:o((()=>[d("已选")])),_:1}),u(l,{class:"ml-auto text-right truncate flex-1 text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:o((()=>[d(p(me.value.sku_spec_format),1)])),_:1}),u(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):c("v-if",!0),"real"==me.value.goods.goods_type&&me.value.delivery_type_list&&me.value.delivery_type_list.length?(s(),r(l,{key:2,class:"card-template-item",onClick:Ve},{default:o((()=>[u(a,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:o((()=>[d("配送")])),_:1}),u(l,{class:"ml-auto flex items-center text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:o((()=>[d(p(me.value.delivery_type_list[Fe.value].name),1)])),_:1}),u(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):c("v-if",!0)])),_:1})):c("v-if",!0),u(l,{class:"mt-[var(--top-m)] sidebar-margin card-template"},{default:o((()=>[u(l,{class:v(["flex items-center justify-between min-h-[40rpx]",{"mb-[30rpx]":je.value&&je.value.list&&je.value.list.length}])},{default:o((()=>[u(a,{class:"title !mb-[0]"},{default:o((()=>[d("宝贝评价("+p(je.value.count)+")",1)])),_:1}),je.value.count?(s(),r(l,{key:0,class:"h-[40rpx] flex items-center",onClick:t[5]||(t[5]=e=>(me.value.goods_id,void b({url:"/addon/shop/pages/evaluate/list",param:{goods_id:me.value.goods_id}})))},{default:o((()=>[u(a,{class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d("查看全部")])),_:1}),u(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):c("v-if",!0),je.value.count?c("v-if",!0):(s(),r(a,{key:1,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:o((()=>[d("暂无评价 ")])),_:1}))])),_:1},8,["class"]),u(l,null,{default:o((()=>[(s(!0),x(f,null,m(je.value.list,((e,t)=>(s(),r(l,{class:v({"pb-[34rpx]":t!=je.value.list.length-1}),key:t},{default:o((()=>[u(l,{class:"flex items-center w-full"},{default:o((()=>[u(O,{"default-url":i(y)("static/resource/images/default_headimg.png"),src:i(y)(e.member_head),size:"50rpx",leftIcon:"none"},null,8,["default-url","src"]),u(a,{class:"ml-[10rpx] text-[28rpx] text-[#333]"},{default:o((()=>[d(p(e.member_name),1)])),_:2},1024)])),_:2},1024),u(l,{class:"flex justify-between w-full mt-[16rpx]"},{default:o((()=>[u(l,{class:"flex-1 w-[540rpx] text-[26rpx] text-[#333] max-h-[72rpx] leading-[36rpx] multi-hidden mr-[50rpx]"},{default:o((()=>[d(p(e.content),1)])),_:2},1024),u(l,{class:"w-[80rpx] shrink-0"},{default:o((()=>[e.image_mid&&e.image_mid.length?(s(),r(A,{key:0,width:"80rpx",height:"80rpx",radius:"16rpx",src:i(y)(e.image_mid[0]),model:"aspectFill",onClick:t=>Se(e.images[0])},{error:o((()=>[u(z,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"])):c("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1}),me.value.goods&&me.value.goods.attr_format&&Object.keys(me.value.goods.attr_format).length?(s(),r(l,{key:1,class:"my-[var(--top-m)] goods-sku sidebar-margin card-template"},{default:o((()=>[u(l,{class:"title mb-[30rpx]"},{default:o((()=>[d("商品属性")])),_:1}),u(l,null,{default:o((()=>[(s(!0),x(f,null,m(me.value.goods.attr_format,((e,t)=>(s(),x(f,{key:t},[t<4||ve.value?(s(),r(l,{key:0,class:"card-template-item"},{default:o((()=>[u(l,{class:"text-[26rpx] leading-[30rpx] w-[160rpx] font-400 shrink-0 text-[var(--text-color-light9)]"},{default:o((()=>[d(p(e.attr_value_name),1)])),_:2},1024),u(l,{class:"text-[#333] box-border value-wid text-[26rpx] leading-[30rpx] font-400 pl-[20rpx]"},{default:o((()=>[d(p(Array.isArray(e.attr_child_value_name)?e.attr_child_value_name.join(","):e.attr_child_value_name),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)],64)))),128)),me.value.goods.attr_format.length>4?(s(),r(l,{key:0,class:"flex-center",onClick:t[6]||(t[6]=e=>ve.value=!ve.value)},{default:o((()=>[u(a,{class:"text-[24rpx] mr-[10rpx]"},{default:o((()=>[d(p(ve.value?"展开":"收起"),1)])),_:1}),u(a,{class:v(["nc-iconfont !text-[22rpx]",{"nc-icon-xiaV6xx":ve.value,"nc-icon-shangV6xx-1":!ve.value}])},null,8,["class"])])),_:1})):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0),u(l,{class:"my-[var(--top-m)] sidebar-margin card-template px-[var(--pad-sidebar-m)]"},{default:o((()=>[u(l,{class:"title"},{default:o((()=>[d("商品详情")])),_:1}),u(l,{class:"u-content"},{default:o((()=>[u(R,{content:me.value.goods.goods_desc,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;"}},null,8,["content"])])),_:1})])),_:1}),u(l,{class:"tab-bar-placeholder"}),u(l,{class:"border-[0] border-t-[2rpx] border-solid border-[#f5f5f5] w-[100%] flex justify-between pl-[32rpx] pr-[4rpx] bg-[#fff] box-border fixed left-0 bottom-0 tab-bar z-1 items-center"},{default:o((()=>[u(l,{class:"flex items-center"},{default:o((()=>[u(l,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:t[7]||(t[7]=e=>i(b)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:o((()=>[u(l,{class:"nc-iconfont nc-icon-shouyeV6xx text-[36rpx]"}),u(a,{class:"text-[20rpx] mt-[10rpx]"},{default:o((()=>[d("首页")])),_:1})])),_:1}),u(l,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:Me},{default:o((()=>[u(l,{class:"nc-iconfont nc-icon-fenxiangV6xx text-[36rpx]"}),u(a,{class:"text-[20rpx] mt-[10rpx]"},{default:o((()=>[d("分享")])),_:1})])),_:1})])),_:1}),1==me.value.goods.status?(s(),r(l,{key:0,class:"flex flex-1"},{default:o((()=>[i(ye)?(s(),r(B,{key:0,class:"primary-btn-bg flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border",onClick:t[8]||(t[8]=e=>we("buy_now"))},{default:o((()=>[d("立即兑换")])),_:1})):(s(),r(B,{key:1,class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border"},{default:o((()=>[d("已售罄")])),_:1}))])),_:1})):(s(),r(l,{key:1,class:"flex flex-1"},{default:o((()=>[u(B,{class:"w-[100%] !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:o((()=>[d("该商品已下架")])),_:1})])),_:1}))])),_:1})])),_:1}),c(" 服务 "),u(l,{onTouchmove:t[11]||(t[11]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[u(U,{class:"popup-type",show:ge.value,onClose:t[10]||(t[10]=e=>ge.value=!1)},{default:o((()=>[u(l,{class:"popup-common min-h-[480rpx]",onTouchmove:t[9]||(t[9]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[u(l,{class:"title"},{default:o((()=>[d("商品服务")])),_:1}),u(P,{class:"h-[520rpx]","scroll-y":"true"},{default:o((()=>[u(l,{class:"pl-[22rpx] pb-[28rpx] pr-[37rpx]"},{default:o((()=>[(s(!0),x(f,null,m(me.value.service,((e,t)=>(s(),r(l,{class:"flex mb-[28rpx]"},{default:o((()=>[u(I,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:i(y)(e.image||"addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),u(l,{class:"flex-1"},{default:o((()=>[u(l,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:o((()=>[d(p(e.service_name),1)])),_:2},1024),u(l,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d(p(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),c(" 配送 "),u(l,{onTouchmove:t[14]||(t[14]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[u(U,{class:"popup-type",show:he.value,onClose:t[13]||(t[13]=e=>he.value=!1)},{default:o((()=>[u(l,{class:"min-h-[360rpx] popup-common",onTouchmove:t[12]||(t[12]=n((()=>{}),["prevent","stop"]))},{default:o((()=>[u(l,{class:"title"},{default:o((()=>[d("配送方式")])),_:1}),u(P,{class:"h-[520rpx]","scroll-y":"true"},{default:o((()=>[u(l,{class:"px-[var(--popup-sidebar-m)]"},{default:o((()=>[(s(!0),x(f,null,m(me.value.delivery_type_list,((e,t)=>(s(),r(l,{class:"flex mb-[40rpx]",onClick:a=>((e,t)=>{Fe.value=t,he.value=!1,uni.setStorageSync("distributionType",e.name)})(e,t)},{default:o((()=>[u(I,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:i(y)("addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),u(l,{class:"flex-1"},{default:o((()=>[u(l,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:o((()=>[d(p(e.name),1)])),_:2},1024),u(l,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d(p(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),u(de,{ref_key:"goodsSkuRef",ref:fe,"goods-detail":me.value,onChange:be},null,8,["goods-detail"]),u(ie,{ref_key:"sharePosterRef",ref:Be,posterType:"shop_point_goods",posterParam:i(Ue),copyUrlParam:Pe.value},null,8,["posterParam","copyUrlParam"])])),_:1})):c("v-if",!0),u(D,{loading:_e.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-439d82c2"]]);export{pe as default};

import{d as e,r as t,s as a,z as r,an as l,o as s,c as o,w as i,b as n,R as d,a3 as p,S as x,g as u,e as c,n as f,ah as _,ai as m,k as v,ap as g,i as y,j as h,T as b,A as k,B as F,C as j,F as w,aj as C,E as S,a as E,bE as I,m as U,I as z,G as M}from"./index-4b8dc7db.js";import{_ as R}from"./u--image.892273b2.js";import{_ as T}from"./pay.2b11dfa6.js";import{d as B,e as O,o as P,b as q}from"./order.b56aabaa.js";import{g as A}from"./shop.61e7c9c0.js";import{M as G}from"./mescroll-body.0a32866d.js";import{M as N}from"./mescroll-empty.925a8be3.js";import{u as Q}from"./useMescroll.26ccf5de.js";import{_ as V}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.18977c35.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.e2790691.js";import"./u-safe-bottom.987908cd.js";import"./pay.eee54be7.js";import"./mescroll-i18n.d05488f8.js";const $=V(e({__name:"list",setup(e){const{mescrollInit:V,downCallback:$,getMescroll:D}=Q(m,_),H=t([]),J=t(!1),K=t(!1),L=t(""),W=t([]),X=t(""),Y=t(""),Z=t(!1);t(null),a((e=>{L.value=e.status||"",ee(),ae()})),r((()=>{l((()=>{D()&&D().resetUpScroll()}))}));const ee=()=>{A().then((e=>{X.value=e.data}))},te=e=>{J.value=!1;let t={page:e.num,limit:e.size,status:L.value};O(t).then((t=>{let a=t.data.data;1==e.num&&(H.value=[]),a.forEach((e=>{e.is_show_evaluate=!0;let t=0;for(let a=0;a<e.order_goods.length;a++)1==e.order_goods[a].status&&1!=e.order_goods[a].is_enable_refund||t++;t==e.order_goods.length&&(e.is_show_evaluate=!1)})),H.value=H.value.concat(a),e.endSuccess(a.length),Y.value=t.data.mch_id,Z.value=t.data.is_trade_managed,J.value=!0})).catch((()=>{J.value=!0,e.endErr()}))},ae=()=>{K.value=!1,W.value=[];W.value.push({name:"全部",status:""}),B().then((e=>{Object.values(e.data).forEach(((e,t)=>{W.value.push(e)})),K.value=!0})).catch((()=>{K.value=!0}))},re=t(null),le=(e,t="")=>{var a;"pay"==t?null==(a=re.value)||a.open(e.order_type,e.order_id,`/addon/shop/pages/order/detail?order_id=${e.order_id}`):"close"==t?se(e):"finish"==t?oe(e):"evaluate"==t&&(e.is_evaluate?E({url:"/addon/shop/pages/evaluate/order_evaluate_view",param:{order_id:e.order_id}}):E({url:"/addon/shop/pages/evaluate/order_evaluate",param:{order_id:e.order_id}}))},se=e=>{I({title:"提示",content:"您确定要关闭该订单吗？",confirmColor:U().themeColor["--primary-color"],success:t=>{t.confirm&&P(e.order_id).then((e=>{D().resetUpScroll()}))}})},oe=e=>{I({title:"提示",content:"您确定物品已收到吗？",confirmColor:U().themeColor["--primary-color"],success:t=>{t.confirm&&q(e.order_id).then((e=>{D().resetUpScroll()}))}})};return(e,t)=>{const a=v,r=g,l=z,_=M,m=y(h("u--image"),R),I=y(h("pay"),T);return s(),o(a,{class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden order-list",style:f(e.themeColor())},{default:i((()=>[K.value?(s(),o(a,{key:0,class:"fixed left-0 top-0 right-0 z-10"},{default:i((()=>[n(r,{"scroll-x":!0,class:"tab-style-2"},{default:i((()=>[n(a,{class:"tab-content"},{default:i((()=>[(s(!0),d(x,null,p(W.value,((e,t)=>(s(),o(a,{class:b(["tab-items",{"class-select":L.value===e.status.toString()}]),onClick:t=>{return a=e.status,L.value=a.toString(),H.value=[],void D().resetUpScroll();var a}},{default:i((()=>[k(F(e.name),1)])),_:2},1032,["class","onClick"])))),256))])),_:1})])),_:1})])),_:1})):u("v-if",!0),n(G,{ref:"mescrollRef",top:"88rpx",onInit:c(V),down:{use:!1},onUp:te},{default:i((()=>[H.value.length?(s(),o(a,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:i((()=>[(s(!0),d(x,null,p(H.value,((e,t)=>(s(),o(a,{key:t,class:"mb-[var(--top-m)] card-template"},{default:i((()=>[n(a,{onClick:j((t=>{E({url:"/addon/shop/pages/order/detail",param:{order_id:e.order_id}})}),["stop"])},{default:i((()=>[n(a,{class:"flex justify-between items-center"},{default:i((()=>[n(a,{class:"text-[#303133] text-[26rpx] font-400 leading-[36rpx]"},{default:i((()=>[n(l,null,{default:i((()=>[k(F(c(w)("orderNo"))+":",1)])),_:1}),n(l,{class:"ml-[10rpx]"},{default:i((()=>[k(F(e.order_no),1)])),_:2},1024),n(l,{class:"text-[#303133] text-[26rpx] font-400 nc-iconfont nc-icon-fuzhiV6xx1 ml-[11rpx]",onClick:j((t=>c(C)(e.order_no)),["stop"])},null,8,["onClick"])])),_:2},1024),-1==e.status?(s(),o(a,{key:0,class:b(["text-[#303133] text-[26rpx] max-w-[85px] leading-[34rpx] truncate",{"text-primary":1==e.status,"!text-[var(--text-color-light9)]":5==e.status||-1==e.status}])},{default:i((()=>[k(F(e.close_type_name),1)])),_:2},1032,["class"])):(s(),o(a,{key:1,class:b(["text-[#303133] text-[26rpx] leading-[34rpx]",{"text-primary":1==e.status,"!text-[var(--text-color-light9)]":5==e.status||-1==e.status}])},{default:i((()=>[k(F(e.status_name.name),1)])),_:2},1032,["class"]))])),_:2},1024),(s(!0),d(x,null,p(e.order_goods,((t,r)=>(s(),d(x,{key:r},[n(a,{class:"flex box-border mt-[20rpx]"},{default:i((()=>[n(m,{width:"150rpx",height:"150rpx",radius:"var(--goods-rounded-big)",src:c(S)(t.goods_image_thumb_small?t.goods_image_thumb_small:""),mode:"aspectFill"},{error:i((()=>[n(_,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:c(S)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"]),n(a,{class:"ml-[20rpx] flex flex-1 flex-col box-border"},{default:i((()=>[n(a,{class:"flex justify-between items-baseline"},{default:i((()=>[n(a,{class:"max-w-[322rpx] text-[28rpx] leading-[40rpx] font-400 truncate text-[#303133]"},{default:i((()=>[k(F(t.goods_name),1)])),_:2},1024),"exchange"==e.activity_type?(s(),d(x,{key:0},[parseFloat(t.price)?(s(),o(a,{key:0,class:"text-right ml-[10rpx] leading-[42rpx]"},{default:i((()=>[n(l,{class:"text-[22rpx] font-400 price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 price-font"},{default:i((()=>[k(F(parseFloat(t.price).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 price-font"},{default:i((()=>[k("."+F(parseFloat(t.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024)):u("v-if",!0)],64)):t.extend&&t.extend.is_impulse_buy?(s(),d(x,{key:1},[parseFloat(t.goods_money)?(s(),o(a,{key:0,class:"text-right ml-[10rpx] leading-[42rpx]"},{default:i((()=>[n(l,{class:"text-[22rpx] font-400 price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 price-font"},{default:i((()=>[k(F(parseFloat(t.goods_money).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 price-font"},{default:i((()=>[k("."+F(parseFloat(t.goods_money).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024)):u("v-if",!0)],64)):(s(),o(a,{key:2,class:"text-right leading-[42rpx] ml-[10rpx]"},{default:i((()=>[n(l,{class:"text-[22rpx] price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 price-font"},{default:i((()=>[k(F(parseFloat(t.price).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 price-font"},{default:i((()=>[k("."+F(parseFloat(t.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024))])),_:2},1024),n(a,{class:"flex justify-between items-baseline text-[#303133] mt-[14rpx]"},{default:i((()=>[n(a,null,{default:i((()=>[t.sku_name?(s(),o(a,{key:0,class:"text-[24rpx] text-[var(--text-color-light6)] font-400 truncate leading-[34rpx] max-w-[369rpx] mb-[10rpx]"},{default:i((()=>[k(F(t.sku_name),1)])),_:2},1024)):u("v-if",!0),"virtual"!=e.delivery_type?(s(),o(a,{key:1,class:"text-[24rpx] font-400 leading-[34rpx] text-[var(--text-color-light6)]"},{default:i((()=>[k(F(c(w)("deliveryType"))+" ： "+F(e.delivery_type_name),1)])),_:2},1024)):(s(),o(a,{key:2,class:"text-[24rpx] font-400 leading-[34rpx] text-[var(--text-color-light6)]"},{default:i((()=>[k(F(c(w)("createTime"))+" ："+F(e.create_time),1)])),_:2},1024))])),_:2},1024),n(l,{class:"text-right text-[26rpx] font-400 w-[90rpx] leading-[36rpx]"},{default:i((()=>[k("x"+F(t.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),t.extend&&t.extend.is_newcomer&&t.num>1?(s(),o(a,{key:0,class:"flex items-center box-border mt-[8rpx]"},{default:i((()=>[n(_,{class:"h-[24rpx] w-[56rpx]",src:c(S)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"]),n(a,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:i((()=>[k("第1"+F(t.unit)+"，￥"+F(parseFloat(t.extend.newcomer_price).toFixed(2))+"/"+F(t.unit)+"；第"+F(t.num>2?"2~"+t.num:"2")+F(t.unit)+"，￥"+F(parseFloat(t.price).toFixed(2))+"/"+F(t.unit),1)])),_:2},1024)])),_:2},1024)):u("v-if",!0)],64)))),128))])),_:2},1032,["onClick"]),n(a,{class:"flex justify-end items-center mt-[20rpx]"},{default:i((()=>[n(a,{class:"flex items-baseline"},{default:i((()=>[parseFloat(e.delivery_money)?(s(),o(a,{key:0,class:"text-[22rpx] text-[var(--text-color-light9)] leading-[30rpx] mr-[6rpx]"},{default:i((()=>[k(F(c(w)("service")),1)])),_:1})):u("v-if",!0),n(a,{class:"text-[22rpx] font-400 leading-[30rpx] text-[#303133]"},{default:i((()=>[k(F(c(w)("actualPayment"))+"：",1)])),_:1}),n(a,{class:"leading-[1] text-[var(--price-text-color)]"},{default:i((()=>["exchange"==e.activity_type?(s(),d(x,{key:0},[n(l,{class:"text-[36rpx] mr-[2rpx] leading-[40rpx] price-font font-500"},{default:i((()=>[k(F(e.point),1)])),_:2},1024),n(l,{class:"text-[20rpx] leading-[28rpx] font-500"},{default:i((()=>[k(F(c(w)("point")),1)])),_:1}),parseFloat(e.order_money)?(s(),d(x,{key:0},[n(l,{class:"text-[20rpx] mx-[4rpx] font-500 leading-[28rpx]"},{default:i((()=>[k("+")])),_:1}),n(l,{class:"text-[36rpx] font-500 leading-[40rpx] price-font"},{default:i((()=>[k(F(parseFloat(e.order_money).toFixed(2)),1)])),_:2},1024),n(l,{class:"text-[20rpx] font-500 leading-[28rpx] ml-[2rpx]"},{default:i((()=>[k(F(c(w)("money")),1)])),_:1})],64)):u("v-if",!0)],64)):(s(),d(x,{key:1},[n(l,{class:"text-[22rpx] leading-[26rpx] price-font"},{default:i((()=>[k("￥")])),_:1}),n(l,{class:"text-[36rpx] font-500 leading-[40rpx] price-font"},{default:i((()=>[k(F(parseFloat(e.order_money).toFixed(2).split(".")[0]),1)])),_:2},1024),n(l,{class:"text-[22rpx] font-500 leading-[28rpx] price-font"},{default:i((()=>[k("."+F(parseFloat(e.order_money).toFixed(2).split(".")[1]),1)])),_:2},1024)],64))])),_:2},1024)])),_:2},1024)])),_:2},1024),1==e.status||3==e.status||5==e.status&&X.value.evaluate_is_show&&1==X.value.is_evaluate?(s(),o(a,{key:0,class:"flex justify-end text-[28rpx] mt-[20rpx] items-center"},{default:i((()=>[1==e.status?(s(),o(a,{key:0,class:"text-[24rpx] font-500 leading-[52rpx] h-[56rpx] min-w-[150rpx] text-center border-[2rpx] border-solid border-[var(--text-color-light9)] rounded-full text-[var(--text-color-light6)] box-border",onClick:j((t=>le(e,"close")),["stop"])},{default:i((()=>[k(F(c(w)("orderClose")),1)])),_:2},1032,["onClick"])):u("v-if",!0),1==e.status?(s(),o(a,{key:1,class:"text-[24rpx] font-500 flex-center h-[56rpx] min-w-[150rpx] text-center border-[0] text-[#fff] primary-btn-bg rounded-full ml-[20rpx] box-border",onClick:j((t=>le(e,"pay")),["stop"])},{default:i((()=>[k(F(c(w)("topay")),1)])),_:2},1032,["onClick"])):u("v-if",!0),3==e.status?(s(),o(a,{key:2,class:"text-[24rpx] font-500 flex-center h-[56rpx] min-w-[150rpx] text-center border-[0] text-[#fff] primary-btn-bg rounded-full ml-[20rpx] box-border",onClick:j((t=>le(e,"finish")),["stop"])},{default:i((()=>[k(F(c(w)("orderFinish")),1)])),_:2},1032,["onClick"])):u("v-if",!0),5==e.status&&X.value.evaluate_is_show&&1==X.value.is_evaluate?(s(),o(a,{key:3,class:"text-[24rpx] font-500 leading-[52rpx] h-[56rpx] min-w-[150rpx] text-center border-[2rpx] border-solid border-[var(--text-color-light9)] rounded-full ml-[20rpx] text-[var(--text-color-light6)] box-border",onClick:j((t=>le(e,"evaluate")),["stop"])},{default:i((()=>[k(F(1==e.is_evaluate?c(w)("selectedEvaluate"):c(w)("evaluate")),1)])),_:2},1032,["onClick"])):u("v-if",!0)])),_:2},1024)):u("v-if",!0)])),_:2},1024)))),128))])),_:1})):u("v-if",!0),!H.value.length&&J.value?(s(),o(N,{key:1,option:{tip:"暂无订单"}})):u("v-if",!0)])),_:1},8,["onInit"]),n(I,{ref_key:"payRef",ref:re,onClose:e.payClose},null,8,["onClose"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-30cbe3e9"]]);export{$ as default};

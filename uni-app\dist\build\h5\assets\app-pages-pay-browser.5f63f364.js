import{d as e,s,t as a,o as t,c as r,w as p,b as i,e as n,A as o,n as l,G as c,k as d,E as x}from"./index-4b8dc7db.js";const _=e({__name:"browser",setup:e=>(s((e=>{!a()&&e.alipay&&(uni.setStorageSync("paymenting",{trade_type:e.trade_type,trade_id:e.trade_id}),location.href=e.alipay)})),(e,s)=>{const a=c,_=d;return t(),r(_,{class:"w-screen h-screen bg-[#424040] text-right",style:l(e.themeColor())},{default:p((()=>[i(a,{src:n(x)("static/resource/images/pay/invite_friends_share.png"),mode:"heightFix",class:"pt-[30rpx] pr-[30rpx] h-[200rpx] w-[auto]"},null,8,["src"]),i(_,{class:"text-white font-bold pt-[30rpx] pr-[30rpx]"},{default:p((()=>[o("点击右上角跳转到浏览器打开")])),_:1})])),_:1},8,["style"])})});export{_ as default};

import{d as e,r as t,l,p as a,o,c as s,w as r,b as u,e as i,C as d,T as n,A as p,B as c,g as x,R as v,a3 as m,S as _,x as f,y as g,b2 as y,J as h,bp as b,a as k,E as w,aw as j,G as C,i as F,j as S,k as V,I as T,ag as I,ap as L,H as D,Q as O,u as z,s as E,z as B,M as N,aW as R,b7 as A,L as P,an as $,a1 as M,ai as U,am as G,av as H,n as q,aq as J,W,X as Q,ar as X,ba as K}from"./index-dd56d0cc.js";import{_ as Y}from"./u-swiper.01028265.js";import{_ as Z}from"./newcomer.c56b90d6.js";import{_ as ee}from"./u-avatar.ea828bd7.js";import{_ as te}from"./u-icon.5895f8fc.js";import{_ as le}from"./u--image.cd475bba.js";import{_ as ae}from"./u-parse.ae2d35cb.js";import{_ as oe,a as se}from"./u-popup.457e1f1f.js";import{f as re,h as ue,i as ie,j as de,k as ne,l as pe}from"./goods.dbf7b09d.js";import{g as ce,b as xe}from"./coupon.506f719c.js";import{_ as ve}from"./u-number-box.41986fc4.js";import{u as me}from"./add-cart-popup.4746de5d.js";import{b as _e}from"./bind-mobile.9929c841.js";import{d as fe}from"./index.aece06fa.js";import{_ as ge}from"./_plugin-vue_export-helper.1b428a4d.js";import{n as ye}from"./ns-goods-manjian.9fddf49d.js";import{s as he}from"./share-poster.ac22557c.js";import{u as be}from"./useGoods.392f2eb1.js";import{_ as ke}from"./ns-goods-recommend.vue_vue_type_script_setup_true_lang.3a16444a.js";import"./u-loading-icon.f15d7447.js";import"./u-text.1f240d34.js";/* empty css                                                               */import"./u-image.dfca355c.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.22d4d63b.js";import"./u-form.b5669646.js";import"./u-line.ddd38835.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-input.ef44c0c4.js";import"./u-modal.0666cf44.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./useDiyForm.1a531152.js";import"./diy_form.2c8e4c0e.js";import"./index.2c75d097.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-button.b6743e99.js";import"./u-picker.1af38a2a.js";import"./u-upload.44346d61.js";import"./u-radio-group.3476fb8a.js";import"./u-action-sheet.daa5fa92.js";import"./tabbar.0d5e534b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./order.22f5d222.js";const we=ge(e({__name:"ns-goods-sku",props:["goodsDetail"],emits:["change"],setup(e,{expose:O,emit:z}){const E=e,B=t(!1),N=t(null),R=t({skuId:"",name:[]}),A=t(""),P=t(1),$=t(0),M=t(0),U=t(0),G=t(0),H=l((()=>{let e="0.00";return e="newcomer_discount"==te.value.type&&f()&&te.value.newcomer_price?te.value.newcomer_price:te.value.show_price,e})),q=()=>{let e="";return e="newcomer_discount"==te.value.type&&f()&&te.value.newcomer_price?"newcomer_price":te.value.show_type,e},J=a(),W=l((()=>J.info)),Q=me();Q.getList();const X=l((()=>te.value&&Q.cartList["goods_"+te.value.goods_id]&&Q.cartList["goods_"+te.value.goods_id]["sku_"+te.value.sku_id]?Q.cartList["goods_"+te.value.goods_id]["sku_"+te.value.sku_id]:{})),K=l((()=>Q.cartList)),Y=()=>{setTimeout((()=>{P.value=parseInt(P.value),(!P.value||P.value<=M.value)&&(P.value=M.value||1),P.value>=$.value&&(P.value=$.value),M.value>te.value.detail.stock&&(P.value=0)}),0)},Z=()=>{setTimeout((()=>{P.value=parseInt(P.value),(!P.value||P.value<=M.value)&&(P.value=M.value||1),P.value>=$.value&&(P.value=$.value),M.value>te.value.detail.stock&&(P.value=0,g({title:"商品库存小于起购数量",icon:"none"}))}),0)},ee=()=>{B.value=!1},te=l((()=>{let e=y(E.goodsDetail);if(Object.keys(e).length&&(Object.keys(R.value.name).length||(R.value.name=e.sku_spec_format.split(",")),e.goodsSpec.forEach(((e,t)=>{let l=e.spec_values.split(",");e.values=[],l.forEach(((l,a)=>{e.values[a]={},e.values[a].name=l,e.values[a].selected=!1,e.values[a].disabled=!1,R.value.name.forEach(((o,s)=>{s==t&&o==l&&(e.values[a].selected=!0)}))}))})),ae(),e.skuList&&Object.keys(e.skuList).length&&e.skuList.forEach(((t,l)=>{t.sku_id==R.value.skuId&&(e.detail=t)}))),e.goods.is_limit){if(e.goods.max_buy){let t=0;if(1==e.goods.limit_type)t=e.goods.max_buy;else{let l=e.goods.max_buy-(e.goods.has_buy||0);t=l>0?l:0}t>e.detail.stock?$.value=e.detail.stock:t<=e.detail.stock&&($.value=t),0==$.value&&(P.value=0)}U.value=e.goods.max_buy}else $.value=e.detail.stock;return M.value=e.goods.min_buy>0?e.goods.min_buy:1,M.value>e.detail.stock?P.value=0:P.value=M.value,G.value=e.goods.min_buy,e})),ae=()=>{E.goodsDetail.skuList.forEach(((e,t)=>{e.sku_spec_format==R.value.name.toString()&&(R.value.skuId=e.sku_id,z("change",e.sku_id))}))},re=()=>{if(M.value&&M.value>te.value.detail.stock)g({title:"商品库存小于起购数量",icon:"none"});else if(te.value.goods.is_limit){let e=`该商品单次限购${te.value.goods.max_buy}件`;1!=te.value.goods.limit_type&&(e=`该商品每人限购${te.value.goods.max_buy}件`,te.value.goods.max_buy-$.value&&(e+=`,已购${te.value.goods.max_buy-$.value}件`)),P.value>=$.value&&g({title:e,icon:"none"})}},ue=()=>{if(M.value>1){let e=`该商品起购${M.value}件`;P.value<=M.value&&g({title:e,icon:"none"})}},ie=t(null);t(uni.getStorageSync("isBindMobile"));const de=t(null),ne=()=>{if(de.value.verify()&&!(P.value<1)){if(!W.value&&uni.getStorageSync("isBindMobile"))return uni.setStorage({key:"loginBack",data:{url:"/addon/shop/pages/goods/detail",param:{sku_id:te.value.sku_id,type:te.value.type}}}),ie.value.open(),!1;if(!W.value)return h().setLoginBack({url:"/addon/shop/pages/goods/detail",param:{sku_id:te.value.sku_id,type:te.value.type}}),!1;if("join_cart"==A.value){let e=0,t=0,l="";if(K.value["goods_"+te.value.goods_id]&&K.value["goods_"+te.value.goods_id]["sku_"+te.value.sku_id]&&(e=b(K.value["goods_"+te.value.goods_id]["sku_"+te.value.sku_id].num),l=b(K.value["goods_"+te.value.goods_id]["sku_"+te.value.sku_id].id)),K.value["goods_"+te.value.goods_id]&&K.value["goods_"+te.value.goods_id]&&(t=b(K.value["goods_"+te.value.goods_id].totalNum)),e+=Number(P.value),t+=Number(P.value),te.value.goods.is_limit){let e=`该商品单次限购${te.value.goods.max_buy}件`;if(1!=te.value.goods.limit_type&&(e=`该商品每人限购${te.value.goods.max_buy}件`,te.value.goods.max_buy-$.value&&(e+=`,已购${te.value.goods.max_buy-$.value}件`)),t>$.value)return g({title:e,icon:"none"}),!1}Q.increase({id:l||"",goods_id:te.value.goods_id,sku_id:te.value.sku_id,stock:te.value.stock,sale_price:te.value.sale_price,num:e},0,(()=>{g({title:"加入购物车成功",icon:"none"})}))}else if("buy_now"==A.value){var e={sku_id:te.value.sku_id,num:P.value};uni.setStorage({key:"orderCreateData",data:{sku_data:[e],extend_data:{relate_id:"discount_price"==te.value.show_type?te.value.discount_info.discount_id:"",activity_type:"discount_price"==te.value.show_type?"discount":""}},success:()=>{k({url:"/addon/shop/pages/order/payment"})}})}ee()}};return O({open:(e="",t="")=>{A.value=e,B.value=!0,N.value=t}}),(e,t)=>{const l=C,a=F(S("u--image"),le),f=V,g=T,y=I,h=F(S("u-number-box"),ve),b=L,k=D,O=F(S("u-popup"),oe),z=F(S("u-overlay"),se);return o(),s(f,{onTouchmove:t[4]||(t[4]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(z,{show:B.value,onClick:ee,zIndex:"490"},{default:r((()=>[u(O,{class:"popup-type",show:B.value,onClose:ee,mode:"bottom",overlay:!1,zIndex:"500"},{default:r((()=>[i(te).detail?(o(),s(f,{key:0,class:"py-[32rpx] relative",onTouchmove:t[3]||(t[3]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(f,{class:n(["flex px-[32rpx]",{"mb-[58rpx]":!(i(te).is_newcomer&&i(te).newcomer_price!=i(te).price&&(Object.keys(i(X)).length?parseInt(i(X).num)+P.value:P.value)>1)}])},{default:r((()=>[u(f,{class:"rounded-[var(--goods-rounded-big)] overflow-hidden w-[180rpx] h-[180rpx]"},{default:r((()=>[u(a,{width:"180rpx",height:"180rpx",src:i(w)(i(te).detail.sku_image),onClick:t[0]||(t[0]=e=>(e=>{if(""===e)return!1;var t=[];t.push(w(e)),j({indicator:"number",loop:!0,urls:t})})(i(te).detail.sku_image)),model:"aspectFill"},{error:r((()=>[u(l,{class:"w-[180rpx] h-[180rpx]",src:i(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1}),u(f,{class:"flex flex-1 flex-col justify-between ml-[24rpx] py-[10rpx]"},{default:r((()=>[u(f,{class:"w-[100%]"},{default:r((()=>[u(f,{class:"text-[var(--price-text-color)] flex items-baseline"},{default:r((()=>[u(g,{class:"text-[32rpx] font-bold price-font"},{default:r((()=>[p("￥")])),_:1}),u(g,{class:"text-[48rpx] price-font"},{default:r((()=>[p(c(parseFloat(i(H)).toFixed(2).split(".")[0]),1)])),_:1}),u(g,{class:"text-[32rpx] mr-[6rpx] price-font"},{default:r((()=>[p("."+c(parseFloat(i(H)).toFixed(2).split(".")[1]),1)])),_:1}),"newcomer_price"==q()?(o(),s(l,{key:0,class:"h-[24rpx] ml-[6rpx] max-w-[60rpx]",src:i(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):x("v-if",!0),"member_price"==q()?(o(),s(l,{key:1,class:"h-[24rpx] ml-[6rpx] max-w-[44rpx]",src:i(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):x("v-if",!0),"discount_price"==q()?(o(),s(l,{key:2,class:"h-[24rpx] ml-[6rpx] max-w-[80rpx]",src:i(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):x("v-if",!0)])),_:1}),u(f,{class:"text-[26rpx] leading-[32rpx] text-[var(--text-color-light6)] mt-[12rpx]"},{default:r((()=>[p("库存"+c(i(te).detail.stock)+c(i(te).goods.unit),1)])),_:1})])),_:1}),i(te).goodsSpec&&i(te).goodsSpec.length?(o(),s(f,{key:0,class:"text-[26rpx] leading-[30rpx] text-[var(--text-color-light6)] w-[100%] max-h-[60rpx] multi-hidden"},{default:r((()=>[p("已选规格："+c(i(te).detail.sku_spec_format),1)])),_:1})):x("v-if",!0),x(' \t<view v-if="goodsDetail.goodsSpec && goodsDetail.goodsSpec.length">\r\n                                          <text>已选规格：{{goodsDetail.detail.sku_spec_format}}</text>\r\n                                        </view> ')])),_:1})])),_:1},8,["class"]),i(te).is_newcomer&&i(te).newcomer_price!=i(te).price&&(Object.keys(i(X)).length?parseInt(i(X).num)+P.value:P.value)>1?(o(),s(f,{key:0,class:"flex items-center px-[32rpx] pt-[8rpx] pb-[16rpx] h-[58rpx] box-border"},{default:r((()=>[u(l,{class:"h-[24rpx] w-[56rpx]",src:i(w)("addon/shop/newcomer.png"),mode:"aspectFit"},null,8,["src"]),u(f,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:r((()=>[p(" 第1"+c(i(te).goods.unit)+"，￥"+c(parseFloat(i(te).newcomer_price).toFixed(2))+"/"+c(i(te).goods.unit)+"；第"+c(parseInt(i(X).num||0)+P.value>2?"2~"+(parseInt(i(X).num||0)+P.value):"2")+c(i(te).goods.unit)+"，￥"+c(parseFloat(parseFloat(i(H))).toFixed(2))+"/"+c(i(te).goods.unit),1)])),_:1})])),_:1})):x("v-if",!0),u(b,{class:"h-[500rpx] px-[32rpx] box-border mb-[60rpx]","scroll-y":"true"},{default:r((()=>[(o(!0),v(_,null,m(i(te).goodsSpec,((e,t)=>(o(),s(f,{class:n({"mt-[20rpx]":0!=t}),key:t},{default:r((()=>[u(f,{class:"text-[28rpx] leading-[36rpx] mb-[24rpx]"},{default:r((()=>[p(c(e.spec_name),1)])),_:2},1024),u(f,{class:"flex flex-wrap"},{default:r((()=>[(o(!0),v(_,null,m(e.values,((e,l)=>(o(),s(f,{class:n(["box-border bg-[var(--temp-bg)] text-[24rpx] px-[44rpx] text-center h-[56rpx] flex-center mr-[20rpx] mb-[20rpx] border-1 border-solid rounded-[50rpx] border-[var(--temp-bg)]",{"!border-[var(--primary-color)] text-[var(--primary-color)] !bg-[var(--primary-color-light)]":e.selected}]),key:l,onClick:l=>((e,t)=>{R.value.name[t]=e.name,ae()})(e,t)},{default:r((()=>[p(c(e.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1032,["class"])))),128)),u(f,{class:"flex justify-between items-center my-[20rpx]"},{default:r((()=>[u(f,{class:"text-[28rpx]"},{default:r((()=>[p("购买数量")])),_:1}),U.value>0&&G.value>1?(o(),s(g,{key:0,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:r((()=>[p("("+c(G.value)+c(i(te).goods.unit)+"起售，限购"+c(U.value)+c(i(te).goods.unit)+")",1)])),_:1})):U.value>0?(o(),s(g,{key:1,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:r((()=>[p("(限购"+c(U.value)+c(i(te).goods.unit)+")",1)])),_:1})):G.value>1?(o(),s(g,{key:2,class:"ml-[20rpx] mr-[auto] text-[24rpx] text-[var(--primary-color)]"},{default:r((()=>[p("("+c(G.value)+c(i(te).goods.unit)+"起售)",1)])),_:1})):x("v-if",!0),u(h,{min:M.value,max:$.value,integer:"",step:1,"input-width":"68rpx",modelValue:P.value,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value=e),"input-height":"52rpx"},{minus:r((()=>[u(f,{class:"relative w-[30rpx] h-[30rpx]",onClick:ue},{default:r((()=>[u(g,{class:n(["text-[30rpx] nc-iconfont nc-icon-jianV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":P.value<=M.value}])},null,8,["class"])])),_:1})])),input:r((()=>[u(y,{class:"text-[#303133] text-[28rpx] mx-[10rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:Y,onBlur:Z,modelValue:P.value,"onUpdate:modelValue":t[1]||(t[1]=e=>P.value=e)},null,8,["modelValue"])])),plus:r((()=>[u(f,{class:"relative w-[30rpx] h-[30rpx]",onClick:re},{default:r((()=>[u(g,{class:n(["text-[30rpx] nc-iconfont nc-icon-jiahaoV6xx font-500 absolute flex items-center justify-center -left-[8rpx] -bottom-[8rpx] -right-[8rpx] -top-[8rpx]",{"!text-[var(--text-color-light9)]":P.value>=$.value||0==P.value}])},null,8,["class"])])),_:1})])),_:1},8,["min","max","modelValue"])])),_:1}),u(f,{class:"mt-[40rpx]"},{default:r((()=>[u(fe,{ref_key:"diyFormRef",ref:de,form_id:i(te).goods.form_id,storage_name:"diyFormStorageByGoodsDetail_"+i(te).sku_id},null,8,["form_id","storage_name"])])),_:1})])),_:1}),u(f,{class:"px-[20rpx]"},{default:r((()=>[i(te).detail.stock>0?(o(),s(k,{key:0,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] font-500 rounded-[50rpx] primary-btn-bg",type:"primary",onClick:ne},{default:r((()=>[p("确定")])),_:1})):(o(),s(k,{key:1,"hover-class":"none",class:"!h-[80rpx] leading-[80rpx] text-[26rpx] font-500 text-[#fff] bg-[#ccc] rounded-[50rpx]"},{default:r((()=>[p("已售罄")])),_:1}))])),_:1})])),_:1})):x("v-if",!0)])),_:1},8,["show"])])),_:1},8,["show"]),x(" 强制绑定手机号 "),u(_e,{ref_key:"bindMobileRef",ref:ie},null,512)])),_:1})}}}),[["__scopeId","data-v-daed0afb"]]),je=e({__name:"sow-show",props:{items:{type:Object,required:!0,default:()=>({})}},setup(e){const l=e,a=t(null);O((()=>{l.items&&l.items.url&&(a.value=function(e){const t=e.match(/treasure_id=(\d+)/);return t?t[1]:null}(l.items.url))}));const d=()=>{k({url:"/addon/sow_community/pages/sow_show",param:{treasure_id:a.value}})};return(t,l)=>{const a=T,n=V,f=C;return e.items.list&&e.items.list.length>0?(o(),s(n,{key:0,class:"card-template mt-[var(--top-m)]"},{default:r((()=>[u(n,{class:"flex justify-between items-center",onClick:d},{default:r((()=>[u(n,{class:"text-[30rpx]"},{default:r((()=>[u(a,null,{default:r((()=>[p("种草秀")])),_:1}),e.items.count?(o(),s(a,{key:0,class:"ml-[6rpx] text-[24rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p(" ("+c(e.items.count)+") ",1)])),_:1})):x("v-if",!0)])),_:1}),u(a,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1}),u(n,{class:"grid grid-cols-3 gap-2 mt-[20rpx]"},{default:r((()=>[(o(!0),v(_,null,m(e.items.list,((e,t)=>(o(),s(n,{class:"w-[210rpx] h-[210rpx]",key:t,onClick:t=>(e=>{1==e.content_type?k({url:"/addon/sow_community/pages/image/detail",param:{content_id:e.content_id}}):k({url:"/addon/sow_community/pages/video/detail",param:{content_id:e.content_id}})})(e)},{default:r((()=>[u(f,{src:i(w)(e.content_cover),mode:"aspectFill",class:"w-[210rpx] h-[210rpx] rounded-[20rpx]"},null,8,["src"])])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):x("v-if",!0)}}}),Ce=ge(e({__name:"detail",setup(e){const y=be(),b=t({}),I=e=>{b.value=e},O=t(1),{setShare:se}=z(),ve=a(),_e=l((()=>ve.info)),fe=me();l((()=>fe.totalNum));const ge=t(null),Ce=t({}),Fe=t("img"),Se=t(null),Ve=t(!1),Te=t(!1),Ie=t(!1),Le=t(!1),De=t(!1),Oe=t(!1),ze=t(0),Ee=t(""),Be=t(""),Ne=t("");t(null);const Re=t(null);let Ae={};E((e=>{Ae=e})),B((()=>{setTimeout((()=>{uni.getStorageSync("sku_form_refresh")?uni.removeStorageSync("sku_form_refresh"):(Ie.value=!1,uni.removeStorageSync("distributionType"),fe.getList(),Ue(),Pe())}))}));const Pe=()=>{re({goods_id:Ae.goods_id||"",sku_id:Ae.sku_id||"",type:Ae.type||""}).then((e=>{if(!e.data.goods||"[]"===JSON.stringify(e.data)){return N({url:"/addon/shop/pages/index",title:"找不到该商品",mode:"reLaunch"}),!1}Ce.value=R(e.data),Ke.value=Ce.value.goods.is_collect,Ce.value.delivery_type_list=Ce.value.goods.delivery_type_list?Object.values(Ce.value.goods.delivery_type_list):[],Ce.value.goods.goods_image=Ce.value.goods.goods_image.split(","),Ce.value.goods.goods_image.forEach(((e,t)=>{Ce.value.goods.goods_image[t]=w(e)})),Ie.value=!0;let t=R(e.data);if(Ce.value.goods.attr_format=[],t.goods&&t.goods.attr_format){R(t.goods.attr_format).forEach(((e,t)=>{(e.attr_child_value_name&&!(e.attr_child_value_name instanceof Array)||e.attr_child_value_name instanceof Array&&e.attr_child_value_name.length)&&Ce.value.goods.attr_format.push(e)}))}""!=Ce.value.goods.goods_video&&(Fe.value="video",Re.value=A("goodsVideo")),Ee.value=Ce.value.goods.goods_name,Be.value="/addon/shop/pages/goods/detail?sku_id="+Ce.value.sku_id,Ce.value.type&&(Be.value+="&type="+Ce.value.type),Ne.value=w(Ce.value.goods.goods_cover_thumb_mid);let l={title:Ce.value.goods.goods_name,desc:Ce.value.goods.sub_title,url:Ce.value.goods.goods_cover_thumb_mid};if(P({title:Ce.value.goods.goods_name}),se({wechat:{...l},weapp:{...l}}),Object.keys(Ce.value.goods).length&&"discount"==Ce.value.type&&Ce.value.goods.is_discount&&Object.keys(Ce.value.discount_info).length){let e=(new Date).getTime();ze.value=1e3*Ce.value.discount_info.active.end_time-e.toFixed(0)}He(Ce.value),et(),lt(),f()&&$e(Ce.value.goods.goods_id),gt(),$((()=>{setTimeout((()=>{const e=J().in(nt);e.select(".swiper-box").boundingClientRect((e=>{pt=e?e.height:0})).exec(),e.select(".detail-head").boundingClientRect((e=>{e&&(ct=e.height?e.height:0)})).exec(),mt.value&&(ft.sku_id=Ce.value.sku_id,Ae.type&&(ft.active=Ae.type),_e.value&&_e.value.member_id&&(ft.member_id=_e.value.member_id),mt.value.loadPoster())}),400)}))}))},$e=e=>{ue({goods_id:e}).then((e=>{}))};let Me=t({});const Ue=()=>{ie({goods_id:Ae.goods_id||"",sku_id:Ae.sku_id||""}).then((e=>{Object.keys(e.data).length&&(Me.value.condition_type=e.data.condition_type,Me.value.rule_json=e.data.rule_json,Me.value.name=e.data.manjian_name)}))},Ge=t(-1),He=(e={})=>{if(e.goods.is_limit&&_e.value&&e.goods.stock>0&&e.goods.max_buy){let t=0;if(1==e.goods.limit_type)t=e.goods.max_buy;else{let l=e.goods.max_buy-(e.goods.has_buy||0);t=l>0?l:0}t>e.goods.stock?Ge.value=e.goods.stock:t<=e.goods.stock&&(Ge.value=t)}},qe=e=>{Ce.value.skuList.forEach(((t,l)=>{t.sku_id==e&&Object.assign(Ce.value,t)}))},Je=l((()=>{let e=!1;return Ce.value.skuList.forEach(((t,l)=>{t.sku_spec_format&&(e=!0)})),!(!e&&Ce.value.stock<=0)&&(!e&&Ce.value.stock,!0)})),We=l((()=>{let e=!1;return(Ce.value.service&&Ce.value.service.length||Ce.value.goodsSpec&&Ce.value.goodsSpec.length||"real"==Ce.value.goods.goods_type&&Ce.value.delivery_type_list&&Ce.value.delivery_type_list.length||Ze.value.length)&&(e=!0),e})),Qe=()=>{Se.value.open(Me.value)},Xe=e=>{ge.value&&ge.value.open(e)},Ke=t(0),Ye=()=>{if(!_e.value)return g({title:"未登录，请先登录后再收藏商品",icon:"none"}),!1;(Ke.value?ne({goods_ids:[Ce.value.goods_id]}):pe(Ce.value.goods_id)).then((e=>{Ke.value=!Ke.value,Ke.value?g({title:"收藏成功",icon:"none"}):g({title:"取消收藏",icon:"none"})}))},Ze=t([]),et=()=>{ce({category_id:Ce.value.goods.goods_category||"",goods_id:Ce.value.goods_id||""}).then((e=>{Ze.value=e.data.data.map((e=>(-1!=e.sum_count&&e.receive_count===e.sum_count&&(e.btnType="collected"),_e.value&&e.is_receive&&e.limit_count===e.member_receive_count?e.btnType="using":e.btnType="collecting",e)))}))},tt=t({count:0}),lt=()=>{de(Ce.value.goods_id).then((e=>{tt.value=e.data}))},at=(e,t)=>{if(Array.isArray(e)){if(!e.length)return!1;j({indicator:"number",current:t,loop:!0,urls:l=e})}else{if(""===e)return!1;var l;(l=[]).push(w(e)),j({indicator:"number",loop:!0,urls:l})}},ot=t(0),st=()=>{De.value=!0};let rt=M().platform;const ut=l((()=>{let e="";return e+="height: 100rpx;",e+="padding-right: 30rpx;",e+="padding-left: 30rpx;",e+="font-size: 32rpx;","ios"===rt?e+="font-weight: 500;":"android"===rt&&(e+="font-size: 36rpx;"),e})),it=l((()=>"")),dt=l((()=>{let e="";return e+="top: 100rpx;",e+="left: 30rpx;","top: 100rpx;left: 30rpx;"})),nt=X();let pt=0,ct=0;const xt=t(!1);U((e=>{if(0==pt||0==ct)return;let t=pt-ct-20;xt.value=!1,e.scrollTop>=t&&(xt.value=!0)}));const vt=e=>{"number"==typeof e&&at(Ce.value.goods.goods_image,e)},mt=t(null),_t=t("");let ft={};const gt=()=>{_t.value="?sku_id="+Ce.value.sku_id,Ce.value.type&&(_t.value+="&type="+Ce.value.type),_e.value&&_e.value.member_id&&(_t.value+="&mid="+_e.value.member_id)},yt=()=>{mt.value.openShare()},ht=l((()=>{let e="";return e="newcomer_discount"==Ce.value.type&&f()&&Ce.value.newcomer_price?"newcomer_price":Ce.value.show_type,e})),bt=l((()=>{let e="0.00";return e="newcomer_discount"==Ce.value.type&&f()&&Ce.value.newcomer_price?Ce.value.newcomer_price:Ce.value.show_price,e}));G((()=>{try{H()}catch(e){}}));const kt=e=>{O.value=e.current+1};return(e,t)=>{const l=T,a=V,f=F(S("u-swiper"),Y),g=K,j=C,z=F(S("up-count-down"),Z),E=F(S("u-avatar"),ee),B=F(S("u-icon"),te),N=F(S("u--image"),le),R=F(S("u-parse"),ae),A=D,P=L,$=F(S("u-popup"),oe);return o(),s(a,{style:q(e.themeColor())},{default:r((()=>[Object.keys(Ce.value).length?(o(),s(a,{key:0,class:"bg-[var(--page-bg-color)] min-h-[100vh] relative"},{default:r((()=>[x(" 自定义头部 "),u(a,{class:n(["flex items-center fixed left-0 right-0 z-10 bg-transparent detail-head",{"!bg-[#fff]":xt.value}]),style:q(i(ut))},{default:r((()=>[u(a,{class:"flex-center h-[60rpx] rounded-[30rpx] box-border arrow-left px-[20rpx] leading-[1]",style:q(i(it))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-zuoV6xx text-[18px]",onClick:t[0]||(t[0]=e=>{W().length>1?Q({delta:1}):k({url:"/addon/shop/pages/index",mode:"reLaunch"})})}),u(l,{class:"w-[2rpx] h-[26rpx] bg-[#999] mx-[14rpx]"}),u(l,{class:"nc-iconfont nc-icon-liebiao-xiV6xx1 text-[16px]",onClick:t[1]||(t[1]=e=>Te.value=!0)})])),_:1},8,["style"]),u(a,{class:n(["ml-auto !pt-[12rpx] !pb-[8rpx] p-[10rpx] bg-[rgba(255,255,255,.4)] rounded-full border-[2rpx] border-solid border-transparent box-border nc-iconfont nc-icon-fenxiangV6xx font-bold text-[#303133] text-[36rpx]",{"border-[#d8d8d8]":xt.value}]),onClick:yt},null,8,["class"])])),_:1},8,["class","style"]),Te.value?(o(),s(a,{key:0,class:"fixed top-0 left-0 right-0 bottom-0 z-100 bg-transparent",onClick:t[6]||(t[6]=e=>Te.value=!1)},{default:r((()=>[u(a,{class:"search-box w-[202rpx] bg-[#fff] rounded-[12rpx] relative",style:q(i(dt))},{default:r((()=>[u(a,{class:"px-[20rpx] flex-center",onClick:t[2]||(t[2]=e=>i(k)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333] border-0 border-[#ddd] border-b-[1rpx] border-solid"},{default:r((()=>[p("首页")])),_:1})])),_:1}),u(a,{class:"px-[20rpx] flex-center",onClick:t[3]||(t[3]=e=>i(k)({url:"/addon/shop/pages/goods/search"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333] border-0 border-[#ddd] border-b-[1rpx] border-solid"},{default:r((()=>[p("搜索")])),_:1})])),_:1}),u(a,{class:"px-[20rpx] flex-center",onClick:t[4]||(t[4]=e=>i(k)({url:"/addon/shop/pages/goods/cart"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-gouwucheV6xx1 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333] border-0 border-[#ddd] border-b-[1rpx] border-solid"},{default:r((()=>[p("购物车")])),_:1})])),_:1}),u(a,{class:"px-[20rpx] flex-center",onClick:t[5]||(t[5]=e=>i(k)({url:"/addon/shop/pages/member/index"}))},{default:r((()=>[u(l,{class:"nc-iconfont nc-icon-a-wodeV6xx-36 text-[30rpx] mr-[10rpx]"}),u(l,{class:"pl-[14rpx] py-[20rpx] flex-1 text-[24rpx] text-[#333]"},{default:r((()=>[p("个人中心")])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})):x("v-if",!0),u(a,{class:"w-full h-[100vw] relative overflow-hidden"},{default:r((()=>[u(a,{class:n(["absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-linear transform",{"translate-x-0":"img"===Fe.value,"translate-x-full":"img"!=Fe.value}])},{default:r((()=>[u(a,{class:"swiper-box"},{default:r((()=>[u(f,{list:Ce.value.goods.goods_image,onChange:kt,indicator:Ce.value.goods.goods_image.length,indicatorStyle:{bottom:"70rpx"},autoplay:"img"===Fe.value,height:"100vw",radius:"0",onClick:vt},null,8,["list","indicator","autoplay"])])),_:1})])),_:1},8,["class"]),u(a,{onTouchmove:t[7]||(t[7]=d((()=>{}),["stop","prevent"])),class:n(["media-mode absolute top-0 left-0 w-full h-full transition-transform duration-300 ease-linear transform",{"translate-x-0":"video"===Fe.value,"-translate-x-full":"video"!=Fe.value}]),style:q({background:"url("+i(w)(Ce.value.goods.goods_cover_thumb_mid)+") left bottom / cover no-repeat"})},{default:r((()=>[u(a,{class:"goods-video-height"},{default:r((()=>[u(g,{id:"goodsVideo",class:"w-full h-full",src:i(w)(Ce.value.goods.goods_video),poster:i(w)(Ce.value.goods.goods_cover_thumb_mid),objectFit:"cover","play-btn-position":"center"},null,8,["src","poster"])])),_:1})])),_:1},8,["class","style"]),x(" 切换视频、图片 "),""!=Ce.value.goods.goods_video?(o(),s(a,{key:0,class:"media-mode bg-[rgb(0,0,0,.5)] rounded-[50rpx] p-[4rpx] absolute bottom-[130rpx] right-[20rpx] text-center leading-[46rpx]"},{default:r((()=>[u(l,{class:n(["tab-item",{"!bg-[#fff] !text-[#666]":"video"==Fe.value}]),onClick:t[8]||(t[8]=e=>Fe.value="video")},{default:r((()=>[p("视频")])),_:1},8,["class"]),u(a,{class:n(["tab-item flex items-center",{"!bg-[#fff] !text-[#666]":"img"==Fe.value}]),onClick:t[9]||(t[9]=e=>(Fe.value="img",Re.value.pause()))},{default:r((()=>{var e,t,a;return[u(l,{class:"mr-[4rpx]"},{default:r((()=>[p("图片")])),_:1}),"img"==Fe.value&&(null==(a=null==(t=null==(e=Ce.value)?void 0:e.goods)?void 0:t.goods_image)?void 0:a.length)>1?(o(),s(l,{key:0},{default:r((()=>{var e,t,l;return[p(c(O.value)+"/"+c(null==(l=null==(t=null==(e=Ce.value)?void 0:e.goods)?void 0:t.goods_image)?void 0:l.length),1)]})),_:1})):x("v-if",!0)]})),_:1},8,["class"])])),_:1})):x("v-if",!0)])),_:1}),"original_price"!=i(ht)?(o(),s(a,{key:1,class:"rounded-t-[40rpx] -mt-[44rpx] relative flex items-center justify-between !bg-cover box-border pb-[26rpx] h-[136rpx] px-[30rpx]",style:q({background:"url("+i(w)("addon/shop/detail/discount_price_bg.png")+") no-repeat"})},{default:r((()=>[u(a,{class:"text-[#fff]"},{default:r((()=>["newcomer_price"==i(ht)?(o(),s(l,{key:0,class:"text-[26rpx] mr-[10rpx] font-500 leading-[36rpx]"},{default:r((()=>[p("新人价")])),_:1})):"discount_price"==i(ht)?(o(),s(l,{key:1,class:"text-[26rpx] mr-[10rpx] font-500 leading-[36rpx]"},{default:r((()=>[p("折扣价")])),_:1})):"member_price"==i(ht)?(o(),s(l,{key:2,class:"text-[26rpx] mr-[10rpx] font-500 leading-[36rpx]"},{default:r((()=>[p("会员价")])),_:1})):x("v-if",!0),u(a,{class:"inline-block mr-[14rpx]"},{default:r((()=>[u(l,{class:"text-[32rpx] price-font mr-[4rpx]"},{default:r((()=>[p("￥")])),_:1}),u(l,{class:"text-[48rpx] -mb-[4rpx] price-font"},{default:r((()=>[p(c(parseFloat(i(bt)).toFixed(2).split(".")[0]),1)])),_:1}),u(l,{class:"text-[32rpx] price-font"},{default:r((()=>[p("."+c(parseFloat(i(bt)).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),u(a,{class:"inline-block"},{default:r((()=>[Ce.value.price?(o(),s(l,{key:0,class:"text-[26rpx] mr-[6rpx]"},{default:r((()=>[p("售价:")])),_:1})):x("v-if",!0),u(l,{class:"text-[26rpx] price-font leading-[36rpx]"},{default:r((()=>[p("￥"+c(Ce.value.price),1)])),_:1})])),_:1})])),_:1}),"discount_price"==i(ht)?(o(),s(a,{key:0,class:"flex flex-col text-[#fff] items-end h-[59rpx] justify-between"},{default:r((()=>[u(j,{class:"h-[28rpx] w-[auto] mr-[2rpx]",src:i(w)("addon/shop/detail/discount_price.png"),mode:"heightFix"},null,8,["src"]),u(a,{class:"flex items-center text-[24rpx] -mb-[10rpx] overflow-hidden h-[28rpx]"},{default:r((()=>[u(l,{class:"mr-[4rpx] whitespace-nowrap"},{default:r((()=>[p("距结束")])),_:1}),u(z,{class:"text-[#fff] text-[28rpx]",time:ze.value,format:"DD:HH:mm:ss",onChange:I},{default:r((()=>[u(a,{class:"flex"},{default:r((()=>[b.value.days>0?(o(),s(a,{key:0,class:"text-[24rpx] flex items-center"},{default:r((()=>[u(l,null,{default:r((()=>[p(c(b.value.days),1)])),_:1}),u(l,{class:"ml-[4rpx] text-[20rpx]"},{default:r((()=>[p("天")])),_:1})])),_:1})):x("v-if",!0),u(a,{class:"text-[24rpx] flex items-center"},{default:r((()=>[b.value.hours?(o(),s(l,{key:0,class:"min-w-[30rpx] text-center"},{default:r((()=>[p(c(b.value.hours>=10?b.value.hours:"0"+b.value.hours),1)])),_:1})):(o(),s(l,{key:1,class:"min-w-[30rpx] text-center"},{default:r((()=>[p("00")])),_:1})),u(l,{class:"text-[20rpx]"},{default:r((()=>[p("时")])),_:1})])),_:1}),u(a,{class:"text-[24rpx] flex items-center"},{default:r((()=>[u(l,{class:"min-w-[30rpx] text-center"},{default:r((()=>[p(c(b.value.minutes>=10?b.value.minutes:"0"+b.value.minutes),1)])),_:1}),u(l,{class:"text-[20rpx]"},{default:r((()=>[p("分")])),_:1})])),_:1}),u(a,{class:"text-[24rpx] flex items-center"},{default:r((()=>[u(l,{class:"min-w-[30rpx] text-center"},{default:r((()=>[p(c(b.value.seconds<10?"0"+b.value.seconds:b.value.seconds),1)])),_:1}),u(l,{class:"text-[20rpx]"},{default:r((()=>[p("秒")])),_:1})])),_:1})])),_:1})])),_:1},8,["time"])])),_:1})])),_:1})):x("v-if",!0)])),_:1},8,["style"])):x("v-if",!0),u(a,{class:"bg-[var(--page-bg-color)] rounded-[40rpx] overflow-hidden -mt-[34rpx] relative"},{default:r((()=>[u(a,{class:n(["detail-title relative px-[30rpx]",{"pt-[40rpx]":"original_price"!=i(ht),"pt-[30rpx]":"original_price"==i(ht)}])},{default:r((()=>["original_price"==i(ht)?(o(),s(a,{key:0,class:"text-[var(--price-text-color)] flex items-baseline mb-[12rpx]"},{default:r((()=>[u(a,{class:"inline-block goods-price-time"},{default:r((()=>[u(l,{class:"price-font text-[32rpx]"},{default:r((()=>[p("￥")])),_:1}),u(l,{class:"price-font text-[48rpx]"},{default:r((()=>[p(c(parseFloat(i(bt)).toFixed(2).split(".")[0]),1)])),_:1}),u(l,{class:"price-font text-[32rpx] mr-[10rpx]"},{default:r((()=>[p("."+c(parseFloat(i(bt)).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),x(' <text class="text-[26rpx] text-[var(--text-color-light9)] line-through price-font" v-if="goodsDetail.market_price && parseFloat(goodsDetail.market_price)">\r\n                          ￥{{ goodsDetail.market_price }}\r\n                        </text> ')])),_:1})):x("v-if",!0),u(a,{class:"text-[#333] font-medium text-[30rpx] multi-hidden leading-[40rpx]"},{default:r((()=>[Ce.value.goods.goods_brand?(o(),s(a,{key:0,class:"brand-tag middle",style:q(i(y).baseTagStyle(Ce.value.goods.goods_brand))},{default:r((()=>[p(c(Ce.value.goods.goods_brand.brand_name),1)])),_:1},8,["style"])):x("v-if",!0),p(" "+c(Ce.value.goods.goods_name),1)])),_:1}),u(a,{class:"text-[26rpx] text-[#666] truncate my-[16rpx] leading-[33rpx]"},{default:r((()=>[p(c(Ce.value.goods.sub_title),1)])),_:1}),Ce.value.label_info&&Ce.value.label_info.length?(o(),s(a,{key:1,class:"flex flex-wrap mt-[16rpx]"},{default:r((()=>[(o(!0),v(_,null,m(Ce.value.label_info,(e=>(o(),v(_,{key:e.label_id},["icon"==e.style_type&&e.icon?(o(),s(j,{key:0,class:"img-tag middle",src:i(w)(e.icon),mode:"heightFix",onError:t=>i(y).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?x("v-if",!0):(o(),s(a,{key:1,class:"base-tag middle",style:q(i(y).baseTagStyle(e))},{default:r((()=>[p(c(e.label_name),1)])),_:2},1032,["style"]))],64)))),128))])),_:1})):x("v-if",!0),u(a,{class:"flex justify-between items-start mt-[24rpx]"},{default:r((()=>[Ce.value.market_price&&parseFloat(Ce.value.market_price)?(o(),s(a,{key:0,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:r((()=>[u(l,{class:"whitespace-nowrap mr-[4rpx]"},{default:r((()=>[p("划线价:")])),_:1}),u(l,{class:"line-through"},{default:r((()=>[p("￥"+c(Ce.value.market_price),1)])),_:1})])),_:1})):x("v-if",!0),u(a,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:r((()=>[u(l,{class:"whitespace-nowrap mr-[4rpx]"},{default:r((()=>[p("库存:")])),_:1}),u(l,null,{default:r((()=>[p(c(Ce.value.stock),1)])),_:1}),u(l,null,{default:r((()=>[p(c(Ce.value.goods.unit),1)])),_:1})])),_:1}),u(a,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)] flex items-baseline"},{default:r((()=>[u(l,{class:"whitespace-nowrap mr-[4rpx]"},{default:r((()=>[p("销量:")])),_:1}),u(l,{class:"mx-[2rpx]"},{default:r((()=>[p(c(Ce.value.goods.sale_num),1)])),_:1}),u(l,null,{default:r((()=>[p(c(Ce.value.goods.unit),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["class"]),i(We)?(o(),s(a,{key:0,class:"mt-[24rpx] sidebar-margin card-template"},{default:r((()=>[Ce.value.service&&Ce.value.service.length?(o(),s(a,{key:0,onClick:t[10]||(t[10]=e=>Le.value=!Le.value),class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:r((()=>[p("服务")])),_:1}),u(a,{class:"text-[#343434] text-[26rpx] leading-[30rpx] font-400 truncate ml-auto"},{default:r((()=>[p(c(Ce.value.service[0].service_name),1)])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),Ce.value.goodsSpec&&Ce.value.goodsSpec.length?(o(),s(a,{key:1,onClick:Xe,class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:r((()=>[p("已选")])),_:1}),u(a,{class:"ml-auto text-right truncate flex-1 text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:r((()=>[p(c(Ce.value.sku_spec_format),1)])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),"real"==Ce.value.goods.goods_type&&Ce.value.delivery_type_list&&Ce.value.delivery_type_list.length?(o(),s(a,{key:2,class:"card-template-item",onClick:st},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0"},{default:r((()=>[p("配送")])),_:1}),u(a,{class:"ml-auto flex items-center text-[#343434] text-[26rpx] leading-[30rpx] font-400"},{default:r((()=>[p(c(Ce.value.delivery_type_list[ot.value].name),1)])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),Ze.value.length?(o(),s(a,{key:3,onClick:t[11]||(t[11]=e=>Oe.value=!0),class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:r((()=>[p("领券")])),_:1}),u(a,{class:"ml-auto flex-1 flex-nowrap flex items-center overflow-hidden h-[44rpx] content-between"},{default:r((()=>[(o(!0),v(_,null,m(Ze.value,((e,t)=>(o(),v(_,{key:t},[t<3?(o(),s(l,{key:0,class:n(["tag-item whitespace-nowrap border-[2rpx] px-[6rpx] h-[40rpx] border-solid border-[var(--primary-color)] text-[var(--primary-color)] mt-[4rpx]",{"mr-[12rpx]":Ze.value.length!=t+1&&t<2,"ml-auto":0==t}])},{default:r((()=>[p(c(e.title),1)])),_:2},1032,["class"])):x("v-if",!0)],64)))),128))])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0),Object.keys(i(Me)).length?(o(),s(a,{key:4,class:"card-template-item",onClick:Qe},{default:r((()=>[u(l,{class:"text-[#333] text-[26rpx] leading-[30rpx] font-400 shrink-0 mr-[20rpx]"},{default:r((()=>[p("优惠")])),_:1}),u(a,{class:"ml-auto flex-1 flex-nowrap flex items-center overflow-hidden h-[44rpx] justify-end"},{default:r((()=>[u(a,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[22rpx] flex items-center justify-center w-[86rpx] h-[34rpx] mr-[6rpx]"},{default:r((()=>[p("满减送")])),_:1}),u(a,{class:"truncate max-w-[430rpx] text-[26rpx]"},{default:r((()=>[p(c(i(Me).name),1)])),_:1})])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"})])),_:1})):x("v-if",!0)])),_:1})):x("v-if",!0),Ce.value.sow_show_list?(o(),s(a,{key:1,class:"sidebar-margin"},{default:r((()=>[u(je,{items:Ce.value.sow_show_list},null,8,["items"])])),_:1})):x("v-if",!0),Ce.value.evaluate_is_show?(o(),s(a,{key:2,class:"mt-[var(--top-m)] sidebar-margin card-template"},{default:r((()=>[u(a,{class:n(["flex items-center justify-between min-h-[40rpx]",{"mb-[30rpx]":tt.value&&tt.value.list&&tt.value.list.length}])},{default:r((()=>[u(l,{class:"title !mb-[0]"},{default:r((()=>[p("宝贝评价("+c(tt.value.count)+")",1)])),_:1}),tt.value.count?(o(),s(a,{key:0,class:"h-[40rpx] flex items-center",onClick:t[12]||(t[12]=e=>(Ce.value.goods_id,void k({url:"/addon/shop/pages/evaluate/list",param:{goods_id:Ce.value.goods_id}})))},{default:r((()=>[u(l,{class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p("查看全部")])),_:1}),u(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):x("v-if",!0),tt.value.count?x("v-if",!0):(o(),s(l,{key:1,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:r((()=>[p("暂无评价 ")])),_:1}))])),_:1},8,["class"]),u(a,null,{default:r((()=>[(o(!0),v(_,null,m(tt.value.list,((e,t)=>(o(),s(a,{class:n({"pb-[34rpx]":t!=tt.value.list.length-1}),key:t},{default:r((()=>[u(a,{class:"flex items-center w-full"},{default:r((()=>[u(E,{"default-url":i(w)("static/resource/images/default_headimg.png"),src:i(w)(e.member_head),size:"50rpx",leftIcon:"none"},null,8,["default-url","src"]),u(l,{class:"ml-[10rpx] text-[28rpx] text-[#333]"},{default:r((()=>[p(c(e.member_name),1)])),_:2},1024)])),_:2},1024),u(a,{class:"flex justify-between w-full mt-[16rpx]"},{default:r((()=>[u(a,{class:"flex-1 w-[540rpx] text-[26rpx] text-[#333] max-h-[72rpx] leading-[36rpx] multi-hidden mr-[50rpx]"},{default:r((()=>[p(c(e.content),1)])),_:2},1024),u(a,{class:"w-[80rpx] shrink-0"},{default:r((()=>[e.image_mid&&e.image_mid.length?(o(),s(N,{key:0,width:"80rpx",height:"80rpx",radius:"var(--goods-rounded-mid)",src:i(w)(e.image_mid[0]),model:"aspectFill",onClick:t=>at(e.images[0])},{error:r((()=>[u(B,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"])):x("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1})):x("v-if",!0),Ce.value.goods&&Ce.value.goods.attr_format&&Object.keys(Ce.value.goods.attr_format).length?(o(),s(a,{key:3,class:"my-[var(--top-m)] goods-sku sidebar-margin card-template"},{default:r((()=>[u(a,{class:"title mb-[30rpx]"},{default:r((()=>[p("商品属性")])),_:1}),u(a,null,{default:r((()=>[(o(!0),v(_,null,m(Ce.value.goods.attr_format,((e,t)=>(o(),v(_,{key:t},[t<4||Ve.value?(o(),s(a,{key:0,class:"card-template-item"},{default:r((()=>[u(l,{class:"text-[26rpx] leading-[30rpx] w-[160rpx] font-400 shrink-0 text-[var(--text-color-light9)]"},{default:r((()=>[p(c(e.attr_value_name),1)])),_:2},1024),u(a,{class:"text-[#333] box-border value-wid text-[26rpx] leading-[30rpx] font-400 pl-[20rpx]"},{default:r((()=>[p(c(Array.isArray(e.attr_child_value_name)?e.attr_child_value_name.join(","):e.attr_child_value_name),1)])),_:2},1024),x(' <text class="nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light6)] ml-[8rpx]"></text> ')])),_:2},1024)):x("v-if",!0)],64)))),128)),Ce.value.goods.attr_format.length>4?(o(),s(a,{key:0,class:"flex-center",onClick:t[13]||(t[13]=e=>Ve.value=!Ve.value)},{default:r((()=>[u(l,{class:"text-[24rpx] mr-[10rpx]"},{default:r((()=>[p(c(Ve.value?"收起":"展开"),1)])),_:1}),u(l,{class:n(["nc-iconfont !text-[22rpx]",{"nc-icon-xiaV6xx":!Ve.value,"nc-icon-shangV6xx-1":Ve.value}])},null,8,["class"])])),_:1})):x("v-if",!0)])),_:1})])),_:1})):x("v-if",!0),u(a,{class:"my-[var(--top-m)] sidebar-margin card-template px-[var(--pad-sidebar-m)]"},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("商品详情")])),_:1}),u(a,{class:"u-content"},{default:r((()=>[u(R,{content:Ce.value.goods.goods_desc,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;"}},null,8,["content"])])),_:1})])),_:1}),u(ke),u(a,{class:"tab-bar-placeholder"}),u(a,{class:"border-[0] border-t-[2rpx] border-solid border-[#f5f5f5] w-[100%] flex justify-between pl-[32rpx] pr-[4rpx] bg-[#fff] box-border fixed left-0 bottom-0 tab-bar z-1 items-center"},{default:r((()=>[u(a,{class:"flex items-center"},{default:r((()=>[u(a,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:t[14]||(t[14]=e=>i(k)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:r((()=>[u(a,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[36rpx]"}),u(l,{class:"text-[20rpx] mt-[10rpx]"},{default:r((()=>[p("首页")])),_:1})])),_:1}),u(a,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:t[15]||(t[15]=e=>i(k)({url:"/addon/shop/pages/goods/cart"}))},{default:r((()=>[u(a,{class:"iconfont icongouwuche2 text-[38rpx]"}),u(l,{class:"text-[20rpx] mt-[10rpx]"},{default:r((()=>[p("购物车")])),_:1})])),_:1}),u(a,{class:"flex flex-col justify-center items-center mr-[38rpx]",onClick:Ye},{default:r((()=>[u(l,{class:n(["nc-iconfont text-[36rpx]",{"text-[#ff0000] nc-icon-xihuanV6mm":Ke.value,"text-[#303133] nc-icon-guanzhuV6xx":!Ke.value}])},null,8,["class"]),u(l,{class:"text-[20rpx] mt-[10rpx]"},{default:r((()=>[p("收藏")])),_:1})])),_:1})])),_:1}),1==Ce.value.goods.status?(o(),s(a,{key:0,class:"flex flex-1"},{default:r((()=>[Ce.value.goods.is_gift?(o(),s(A,{key:0,class:"!w-[420rpx] flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("商品为赠品不可购买 ")])),_:1})):Ge.value>0||-1==Ge.value?(o(),v(_,{key:1},["newcomer_discount"!=i(Ae).type&&("real"==Ce.value.goods.goods_type||"virtual"==Ce.value.goods.goods_type&&"verify"!=Ce.value.goods.virtual_receive_type)?(o(),s(A,{key:0,class:"cart-btn-bg flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border",onClick:t[16]||(t[16]=e=>Xe("join_cart"))},{default:r((()=>[p(" 加入购物车 ")])),_:1})):x("v-if",!0),i(Je)?(o(),s(A,{key:1,style:q({width:"real"==Ce.value.goods.goods_type||"virtual"==Ce.value.goods.goods_type&&"verify"!=Ce.value.goods.virtual_receive_type?"200rpx":"400rpx!important"}),class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] primary-btn-bg !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border",onClick:t[17]||(t[17]=e=>Xe("buy_now"))},{default:r((()=>[p("立即购买 ")])),_:1},8,["style"])):(o(),s(A,{key:2,style:q({width:"real"==Ce.value.goods.goods_type||"virtual"==Ce.value.goods.goods_type&&"verify"!=Ce.value.goods.virtual_receive_type?"200rpx":"400rpx!important"}),class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 !mr-[16rpx] leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("已售罄 ")])),_:1},8,["style"]))],64)):0==Ge.value?(o(),s(A,{key:2,style:{width:"420rpx!important"},class:"flex-1 !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("已达限购数量 ")])),_:1})):x("v-if",!0)])),_:1})):(o(),s(a,{key:1,class:"flex flex-1"},{default:r((()=>[u(A,{class:"w-[100%] !h-[70rpx] font-500 text-[26rpx] !text-[#fff] !bg-[#ccc] !m-0 leading-[70rpx] rounded-full remove-border"},{default:r((()=>[p("该商品已下架")])),_:1})])),_:1}))])),_:1})])),_:1}),x(" 服务 "),u(a,{onTouchmove:t[20]||(t[20]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u($,{class:"popup-type",show:Le.value,onClose:t[19]||(t[19]=e=>Le.value=!1)},{default:r((()=>[u(a,{class:"min-h-[480rpx] popup-common",onTouchmove:t[18]||(t[18]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("商品服务")])),_:1}),u(P,{class:"h-[520rpx]","scroll-y":"true"},{default:r((()=>[u(a,{class:"pl-[22rpx] pb-[28rpx] pr-[37rpx]"},{default:r((()=>[(o(!0),v(_,null,m(Ce.value.service,((e,t)=>(o(),s(a,{class:"flex mb-[28rpx]"},{default:r((()=>[u(j,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:i(w)(e.image||"addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),u(a,{class:"flex-1"},{default:r((()=>[u(a,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:r((()=>[p(c(e.service_name),1)])),_:2},1024),u(a,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p(c(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),x(" 配送 "),u(a,{onTouchmove:t[23]||(t[23]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u($,{class:"popup-type",show:De.value,onClose:t[22]||(t[22]=e=>De.value=!1)},{default:r((()=>[u(a,{class:"min-h-[360rpx] popup-common",onTouchmove:t[21]||(t[21]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("配送方式")])),_:1}),u(P,{class:"h-[520rpx]","scroll-y":"true"},{default:r((()=>[u(a,{class:"px-[var(--popup-sidebar-m)]"},{default:r((()=>[(o(!0),v(_,null,m(Ce.value.delivery_type_list,((e,t)=>(o(),s(a,{class:"flex mb-[40rpx]",onClick:l=>((e,t)=>{ot.value=t,De.value=!1,uni.setStorageSync("distributionType",e.name)})(e,t)},{default:r((()=>[u(j,{class:"mt-[4rpx] w-[32rpx] h-[32rpx] mr-[14rpx]",src:i(w)("addon/shop/icon_service.png"),mode:"aspectFit"},null,8,["src"]),u(a,{class:"flex-1"},{default:r((()=>[u(a,{class:"text-[30rpx] leading-[36rpx] text-[#333] mb-[8rpx]"},{default:r((()=>[p(c(e.name),1)])),_:2},1024),u(a,{class:"text-[24rpx] leading-[36rpx] text-[var(--text-color-light9)]"},{default:r((()=>[p(c(e.desc),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),256))])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),x(" 优惠券 "),u(a,{onTouchmove:t[27]||(t[27]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u($,{class:"popup-type",show:Oe.value,onClose:t[26]||(t[26]=e=>Oe.value=!1)},{default:r((()=>[u(a,{class:"min-h-[480rpx] popup-common",onTouchmove:t[25]||(t[25]=d((()=>{}),["prevent","stop"]))},{default:r((()=>[u(a,{class:"title"},{default:r((()=>[p("优惠券")])),_:1}),u(P,{class:"h-[520rpx]","scroll-y":"true"},{default:r((()=>[u(a,{class:"px-[32rpx]"},{default:r((()=>[(o(!0),v(_,null,m(Ze.value,((e,t)=>(o(),s(a,{class:"mb-[30rpx] flex items-center border-[2rpx] border-solid border-[rgba(0,0,0,.1)] rounded-[var(--rounded-small)]",key:t},{default:r((()=>[u(a,{class:"flex flex-col items-center my-[20rpx] w-[200rpx] border-0 border-r-[2rpx] border-dashed border-[rgba(0,0,0,.1)]"},{default:r((()=>[u(a,{class:"text-xs price-font"},{default:r((()=>[u(l,{class:"text-[28rpx]"},{default:r((()=>[p("￥")])),_:1}),u(l,{class:"text-[48rpx]"},{default:r((()=>[p(c(Number(e.price)),1)])),_:2},1024)])),_:2},1024),u(l,{class:"text-xs mt-[12rpx]"},{default:r((()=>[p(c(Number(e.min_condition_money)?"满"+Number(e.min_condition_money)+"元可用":"无门槛"),1)])),_:2},1024)])),_:2},1024),u(a,{class:"ml-[20rpx] flex-1 flex flex-col py-[20rpx]"},{default:r((()=>[u(l,{class:"text-xs font-500"},{default:r((()=>[p(c(e.title),1)])),_:2},1024),u(l,{class:"text-xs text-[var(--text-color-light6)] mt-[12rpx]"},{default:r((()=>[p(c(1==e.valid_type&&"领取之日起"+e.length+"天内有效"||2==e.valid_type&&"领取之日起至"+e.valid_end_time),1)])),_:2},1024)])),_:2},1024),"collecting"===e.btnType?(o(),s(l,{key:0,class:"bg-[var(--primary-color)] mr-[20rpx] w-[106rpx] box-border text-center text-[#fff] h-[50rpx] text-[22rpx] px-[20rpx] leading-[50rpx] rounded-[100rpx]",onClick:t=>((e,t)=>{if(!_e.value)return h().setLoginBack({url:"/addon/shop/pages/goods/detail",param:{sku_id:Ce.value.sku_id,type:Ce.value.type}}),!1;xe({coupon_id:e.id||"",number:1}).then((e=>{et()}))})(e)},{default:r((()=>[p("领取")])),_:2},1032,["onClick"])):(o(),s(l,{key:1,class:"!bg-[var(--primary-help-color4)] mr-[20rpx] text-[#fff] mr-[20rpx] h-[50rpx] text-[22rpx] px-[20rpx] leading-[50rpx] rounded-[100rpx]"},{default:r((()=>[p(c("collected"===e.btnType?"已领完":"已领取"),1)])),_:2},1024))])),_:2},1024)))),128))])),_:1})])),_:1}),u(a,{class:"btn-wrap"},{default:r((()=>[u(A,{class:"primary-btn-bg btn",onClick:t[24]||(t[24]=e=>Oe.value=!1)},{default:r((()=>[p("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),Ie.value?(o(),s(we,{key:2,ref_key:"goodsSkuRef",ref:ge,"goods-detail":Ce.value,onChange:qe},null,8,["goods-detail"])):x("v-if",!0),u(ye,{ref_key:"manjianShowRef",ref:Se},null,512),u(he,{ref_key:"sharePosterRef",ref:mt,posterType:"shop_goods",posterId:Ce.value.goods.poster_id,posterParam:i(ft),copyUrlParam:_t.value},null,8,["posterId","posterParam","copyUrlParam"])])),_:1})):x("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-839b2db4"]]);export{Ce as default};

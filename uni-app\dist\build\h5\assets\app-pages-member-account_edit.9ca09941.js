import{d as e,r as a,N as l,l as t,s as o,L as r,F as n,bQ as c,o as d,c as p,w as u,b as s,R as i,A as m,B as _,e as f,S as x,g as b,n as h,cb as y,cc as g,a as k,ax as v,ca as A,aw as C,k as V,i as w,j,G as T,I as P,H as R,ap as N,E as W}from"./index-4b8dc7db.js";import{_ as z}from"./u-input.7a7ec88f.js";import{_ as S,a as M}from"./u-form.a144799c.js";import{_ as U}from"./u-upload.899e5f6a.js";import{_ as B}from"./u-modal.775e667c.js";import{_ as D}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.********.js";/* empty css                                                               */import"./u-line.b0d89d5b.js";import"./u-loading-icon.11ef83b8.js";import"./u-popup.e2790691.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-safe-bottom.987908cd.js";const I=D(e({__name:"account_edit",setup(e){const D=a(!1),I=a(null),q=a("get"),E=a(!1),F=l({account_id:0,account_type:"bank",bank_name:"",realname:"",account_no:"",transfer_payment_code:""}),G=t((()=>({realname:{type:"string",required:!0,message:"bank"==F.account_type?n("bankRealnamePlaceholder"):n("alipayRealnamePlaceholder"),trigger:["blur","change"]},bank_name:{type:"string",required:"bank"==F.account_type,message:n("bankNamePlaceholder"),trigger:["blur","change"]},transfer_payment_code:{validator(e,a,l){if(a&&a.length)l();else{let e="alipay"==F.account_type?n("alipayAccountImgPlaceholder"):n("wechatCodeAccountImgPlaceholder");l(new Error(e))}}}})));o((e=>{e.type&&(F.account_type=e.type),e.mode&&(q.value=e.mode),e.id&&(F.account_id=e.id||"",F.account_id?r({title:n("editAccountTitle")}):r({title:n("addAccountTitle")}),c({account_id:e.id}).then((e=>{e.data&&Object.keys(F).forEach((a=>{null!=e.data[a]&&(F[a]=e.data[a])}))})))}));const H=()=>{const e=F.account_id?y:g;I.value.validate().then((()=>{D.value||(D.value=!0,e(F).then((e=>{"get"==q.value?k({url:"/app/pages/member/account",param:{type:F.account_type,mode:q.value}}):k({url:"/app/pages/member/apply_cash_out",param:{account_id:F.account_id?F.account_id:e.data.id,type:F.account_type},mode:"redirectTo"})})).catch((()=>{D.value=!1})))}))},L=e=>{v({filePath:e.file.url,name:"file"}).then((e=>{e.data&&(F.transfer_payment_code="",F.transfer_payment_code=e.data.url)})).catch((()=>{}))},O=e=>{F.transfer_payment_code=""},Q=()=>{A(F.account_id).then((()=>{k({url:"/app/pages/member/account",mode:"redirectTo"})}))},Z=e=>{C({current:0,urls:[e]})};return(e,a)=>{const l=V,t=w(j("u-input"),z),o=w(j("u-form-item"),S),r=w(j("u-form"),M),c=T,y=P,g=w(j("u-upload"),U),k=R,v=N,A=w(j("u-modal"),B);return d(),p(l,{class:"w-screen h-screen bg-[var(--page-bg-color)] overflow-hidden",style:h(e.themeColor())},{default:u((()=>[s(v,{"scroll-y":"true"},{default:u((()=>[s(l,{class:"sidebar-margin card-template top-mar account pb-[20rpx]"},{default:u((()=>["bank"==F.account_type?(d(),i(x,{key:0},[s(l,{class:"text-center text-[32rpx] font-500 mt-[10rpx] text-[#333] leading-[42rpx]"},{default:u((()=>[m(_(F.account_id?f(n)("editBankCard"):f(n)("addBankCard")),1)])),_:1}),s(l,{class:"text-center text-[24rpx] mt-[16rpx] text-[var(--text-color-light9)]"},{default:u((()=>[m(_(F.account_id?f(n)("editBankCardTips"):f(n)("addBankCardTips")),1)])),_:1}),s(l,{class:"mt-[70rpx] px-[10rpx]"},{default:u((()=>[s(r,{labelPosition:"left",model:F,errorType:"toast",rules:f(G),ref_key:"formRef",ref:I},{default:u((()=>[s(l,null,{default:u((()=>[s(o,{label:f(n)("bankRealname"),prop:"realname",labelWidth:"200rpx"},{default:u((()=>[s(t,{modelValue:F.realname,"onUpdate:modelValue":a[0]||(a[0]=e=>F.realname=e),modelModifiers:{trim:!0},fontSize:"28rpx",maxlength:"30",border:"none",clearable:"",placeholder:f(n)("bankRealnamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("bankName"),prop:"bank_name",labelWidth:"200rpx"},{default:u((()=>[s(t,{modelValue:F.bank_name,"onUpdate:modelValue":a[1]||(a[1]=e=>F.bank_name=e),modelModifiers:{trim:!0},fontSize:"28rpx",maxlength:"30",border:"none",clearable:"",placeholder:f(n)("bankNamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("bankAccountNo"),prop:"account_no",labelWidth:"200rpx"},{default:u((()=>[s(t,{modelValue:F.account_no,"onUpdate:modelValue":a[2]||(a[2]=e=>F.account_no=e),modelModifiers:{trim:!0},fontSize:"28rpx",maxlength:"30",border:"none",clearable:"",placeholder:f(n)("bankAccountNoPlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1})])),_:1},8,["model","rules"])])),_:1})],64)):b("v-if",!0),"alipay"==F.account_type?(d(),i(x,{key:1},[s(l,{class:"text-center text-[32rpx] font-500 mt-[20rpx] text-[#333] leading-[42rpx]"},{default:u((()=>[m(_(F.account_id?f(n)("editAlipayAccount"):f(n)("addAlipayAccount")),1)])),_:1}),b(" <view class=\"text-center text-[28rpx] mt-[16rpx] text-[var(--text-color-light9)] leading-[36rpx]\">{{ formData.account_id ? t('editAlipayAccountTips') : t('addAlipayAccountTips') }}</view> "),s(l,{class:"mt-[70rpx] px-[10rpx]"},{default:u((()=>[s(r,{labelPosition:"left",model:F,labelWidth:"200rpx",errorType:"toast",rules:f(G),ref_key:"formRef",ref:I},{default:u((()=>[s(l,null,{default:u((()=>[s(o,{label:f(n)("alipayRealname"),prop:"realname"},{default:u((()=>[s(t,{modelValue:F.realname,"onUpdate:modelValue":a[3]||(a[3]=e=>F.realname=e),modelModifiers:{trim:!0},maxlength:"30",border:"none",fontSize:"28rpx",clearable:"",placeholder:f(n)("alipayRealnamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("alipayAccountNo"),prop:"account_no"},{default:u((()=>[s(t,{modelValue:F.account_no,"onUpdate:modelValue":a[4]||(a[4]=e=>F.account_no=e),modelModifiers:{trim:!0},border:"none",maxlength:"30",fontSize:"28rpx",clearable:"",placeholder:f(n)("alipayAccountNoPlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:"收款码",prop:"transfer_payment_code"},{default:u((()=>[F.transfer_payment_code?(d(),p(l,{key:0,class:"relative w-[160rpx] h-[160rpx]"},{default:u((()=>[s(c,{class:"w-[160rpx] h-[160rpx]",src:f(W)(F.transfer_payment_code),mode:"aspectFill",onClick:a[5]||(a[5]=e=>Z(f(W)(F.transfer_payment_code)))},null,8,["src"]),s(l,{class:"absolute top-0 right-0 bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:O},{default:u((()=>[s(y,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:1})])),_:1})):(d(),p(g,{key:1,onAfterRead:L,onDelete:O,maxCount:1}))])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})],64)):b("v-if",!0),"wechat_code"==F.account_type?(d(),i(x,{key:2},[s(l,{class:"text-center text-[32rpx] font-500 mt-[20rpx] text-[#333] leading-[42rpx]"},{default:u((()=>[m(_(F.account_id?f(n)("editWechatCodeAccount"):f(n)("addWechatCodeAccount")),1)])),_:1}),b(" <view class=\"text-center text-[28rpx] mt-[16rpx] text-[var(--text-color-light9)] leading-[36rpx]\">{{ formData.account_id ? t('editWechatCodeAccountTips') : t('addWechatCodeAccountTips') }}</view> "),s(l,{class:"mt-[70rpx] px-[10rpx]"},{default:u((()=>[s(r,{labelPosition:"left",model:F,labelWidth:"200rpx",errorType:"toast",rules:f(G),ref_key:"formRef",ref:I},{default:u((()=>[s(l,null,{default:u((()=>[s(o,{label:f(n)("alipayRealname"),prop:"realname"},{default:u((()=>[s(t,{modelValue:F.realname,"onUpdate:modelValue":a[6]||(a[6]=e=>F.realname=e),modelModifiers:{trim:!0},maxlength:"30",border:"none",fontSize:"28rpx",clearable:"",placeholder:f(n)("alipayRealnamePlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:f(n)("wechatCodeAccountNo"),prop:"account_no"},{default:u((()=>[s(t,{modelValue:F.account_no,"onUpdate:modelValue":a[7]||(a[7]=e=>F.account_no=e),modelModifiers:{trim:!0},border:"none",maxlength:"30",fontSize:"28rpx",clearable:"",placeholder:f(n)("wechatCodeAccountNoPlaceholder")},null,8,["modelValue","placeholder"])])),_:1},8,["label"])])),_:1}),s(l,{class:"mt-[16rpx]"},{default:u((()=>[s(o,{label:"收款码",prop:"transfer_payment_code"},{default:u((()=>[F.transfer_payment_code?(d(),p(l,{key:0,class:"relative w-[160rpx] h-[160rpx]"},{default:u((()=>[s(c,{class:"w-[160rpx] h-[160rpx]",src:f(W)(F.transfer_payment_code),mode:"aspectFill",onClick:a[8]||(a[8]=e=>Z(f(W)(F.transfer_payment_code)))},null,8,["src"]),s(l,{class:"absolute top-0 right-0 bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:O},{default:u((()=>[s(y,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:1})])),_:1})):(d(),p(g,{key:1,onAfterRead:L,onDelete:O,maxCount:1}))])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})],64)):b("v-if",!0)])),_:1}),s(l,{class:"common-tab-bar-placeholder"}),s(l,{class:"common-tab-bar fixed left-[var(--sidebar-m)] right-[var(--sidebar-m)] bottom-[0]"},{default:u((()=>[s(k,{loading:D.value,class:"primary-btn-bg text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",onClick:H},{default:u((()=>[m(_(f(n)("save")),1)])),_:1},8,["loading"])])),_:1})])),_:1}),s(A,{show:E.value,content:f(n)("deleteConfirm"),confirmText:f(n)("confirm"),cancelText:f(n)("cancel"),showCancelButton:!0,onConfirm:Q,onCancel:a[9]||(a[9]=e=>E.value=!1),confirmColor:"var(--primary-color)"},null,8,["show","content","confirmText","cancelText"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-a52c840c"]]);export{I as default};

import{a4 as t,a5 as e,a6 as a,a8 as o,i as l,j as i,o as n,c as r,w as s,b as c,R as u,S as d,$ as b,n as p,A as m,B as h,k as f,I as v,d as y,m as g,N as k,a9 as x,l as C,b1 as _,at as S,an as j,aq as I,a3 as D,e as O,E as P,g as A,b2 as $,a as w,ar as H}from"./index-4b8dc7db.js";import{_ as B}from"./u-icon.33002907.js";import{_ as L}from"./u-badge.e47151e6.js";import{_ as N}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as z}from"./u-tabbar.bc2ea30a.js";const E=N({name:"u-tabbar-item",mixins:[e,a,{props:{name:{type:[String,Number,null],default:()=>t.tabbarItem.name},icon:{icon:String,default:()=>t.tabbarItem.icon},badge:{type:[String,Number,null],default:()=>t.tabbarItem.badge},dot:{type:Boolean,default:()=>t.tabbarItem.dot},text:{type:String,default:()=>t.tabbarItem.text},badgeStyle:{type:[Object,String],default:()=>t.tabbarItem.badgeStyle}}}],data:()=>({isActive:!1,parentData:{value:null,activeColor:"",inactiveColor:""}}),options:{virtualHost:!0},created(){this.init()},emits:["click","change"],methods:{addStyle:o,init(){this.updateParentData(),this.parent;const t=this.parent.children.indexOf(this);this.isActive=(this.name||t)===this.parentData.value},updateParentData(){this.getParentData("u-tabbar")},updateFromParent(){this.init()},clickHandler(){this.$nextTick((()=>{const t=this.parent.children.indexOf(this),e=this.name||t;e!==this.parent.value&&this.parent.$emit("change",e),this.$emit("click",e)}))}}},[["render",function(t,e,a,o,y,g){const k=l(i("u-icon"),B),x=l(i("u-badge"),L),C=f,_=v;return n(),r(C,{class:"u-tabbar-item cursor-pointer",style:p([g.addStyle(t.customStyle)]),onClick:g.clickHandler},{default:s((()=>[c(C,{class:"u-tabbar-item__icon"},{default:s((()=>[t.icon?(n(),r(k,{key:0,name:t.icon,color:y.isActive?y.parentData.activeColor:y.parentData.inactiveColor,size:20},null,8,["name","color"])):(n(),u(d,{key:1},[y.isActive?b(t.$slots,"active-icon",{key:0},void 0,!0):b(t.$slots,"inactive-icon",{key:1},void 0,!0)],64)),c(x,{absolute:"",offset:[0,t.dot?"34rpx":t.badge>9?"14rpx":"20rpx"],customStyle:t.badgeStyle,isDot:t.dot,value:t.badge||(t.dot?1:null),show:t.dot||t.badge>0},null,8,["offset","customStyle","isDot","value","show"])])),_:3}),b(t.$slots,"text",{},(()=>[c(_,{class:"u-tabbar-item__text",style:p({color:y.isActive?y.parentData.activeColor:y.parentData.inactiveColor})},{default:s((()=>[m(h(t.text),1)])),_:1},8,["style"])]),!0)])),_:3},8,["style","onClick"])}],["__scopeId","data-v-ed2788ef"]]),R=N(y({__name:"tabbar",props:{addon:{type:String,default:""},color:{type:Object,default:()=>({backgroundColor:"",textColor:"",textHoverColor:""})},border:{type:Boolean,default:!0}},setup(t){const e=t;let a=e.addon;const o=g();!a&&o.addon&&(a=o.addon);const b=k({}),p=()=>{let t=$(g().tabbarList);if(1==t.length)Object.assign(b,t[0]);else{let e=!1;for(let o=0;o<t.length;o++)if(t[o].key==a){Object.assign(b,t[o]),e=!0;break}if(!e){let e=0,a={};try{t&&t.forEach((t=>{"app"==t.info.type&&(e++,a=t)})),1==e&&Object.assign(b,a)}catch(o){}}}if(e.color)for(let a in e.color)e.color[a]&&b.value[a]&&(b.value[a]=e.color[a])};p(),x((()=>e.addon),((t,e)=>{t&&e&&t!=e&&p()}),{immediate:!0}),x((()=>e.color),((t,e)=>{t&&e&&t!=e&&p()}),{immediate:!0,deep:!0}),e.addon||x((()=>g().tabbarList),((t,e)=>{t&&p()}),{deep:!0,immediate:!0});const m=C((()=>{let t=_().params,e=[];for(let a in t)e.push(a+"="+t[a]);return"/"+S()+(e.length>0?"?"+e.join("&"):"")})),h=t=>{if(-1!=t.indexOf("http")||-1!=t.indexOf("http"))window.location.href=t;else{let e=_().params,a=[];for(let t in e)a.push(t+"="+e[t]);if(t=="/"+S()&&!a.length)return;w({url:t,mode:"reLaunch"})}},v=H();return j((()=>{I().in(v).select(".tab-bar-placeholder").boundingClientRect((t=>{let e={height:t?t.height:0};uni.setStorageSync("tabbarInfo",e)})).exec()})),(t,a)=>{const o=l(i("u-tabbar-item"),E),p=l(i("u-tabbar"),z),v=f;return b&&Object.keys(b).length?(n(),u(d,{key:0},[c(p,{value:O(m),zIndex:"9999",fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,"inactive-color":b.value.textColor,"active-color":b.value.textHoverColor,border:e.border,class:"custom-tabbar"},{default:s((()=>[(n(!0),u(d,null,D(b.value.list,(t=>(n(),u(d,null,[1==b.value.type?(n(),r(o,{key:0,class:"py-[5rpx]","custom-style":{"background-color":b.value.backgroundColor},text:t.text,icon:O(P)(O(m)==t.link.url?t.iconSelectPath:t.iconPath),name:t.link.url,onClick:e=>h(t.link.url)},null,8,["custom-style","text","icon","name","onClick"])):A("v-if",!0),2==b.value.type?(n(),r(o,{key:1,class:"py-[5rpx]","custom-style":{"background-color":b.value.backgroundColor},icon:O(P)(O(m)==t.link.url?t.iconSelectPath:t.iconPath),name:t.link.url,onClick:e=>h(t.link.url)},null,8,["custom-style","icon","name","onClick"])):A("v-if",!0),3==b.value.type?(n(),r(o,{key:2,class:"py-[5rpx]","custom-style":{"background-color":b.value.backgroundColor},text:t.text,name:t.link.url,onClick:e=>h(t.link.url)},null,8,["custom-style","text","name","onClick"])):A("v-if",!0)],64)))),256))])),_:1},8,["value","inactive-color","active-color","border"]),c(v,{class:"tab-bar-placeholder"})],64)):A("v-if",!0)}}}),[["__scopeId","data-v-2e678a14"]]);export{R as _};

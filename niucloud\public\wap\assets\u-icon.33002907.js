import{a4 as i,a5 as o,a6 as c,b5 as l,a7 as n,a8 as e,o as u,c as t,w as r,n as a,T as s,A as f,B as d,g as p,G as m,I as h,k as g}from"./index-4b8dc7db.js";/* empty css                                                               */import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";const y={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""};const w=b({name:"u-icon",data:()=>({}),emits:["click"],mixins:[o,c,{props:{name:{type:String,default:()=>i.icon.name},color:{type:String,default:()=>i.icon.color},size:{type:[String,Number],default:()=>i.icon.size},bold:{type:Boolean,default:()=>i.icon.bold},index:{type:[String,Number],default:()=>i.icon.index},hoverClass:{type:String,default:()=>i.icon.hoverClass},customPrefix:{type:String,default:()=>i.icon.customPrefix},label:{type:[String,Number],default:()=>i.icon.label},labelPos:{type:String,default:()=>i.icon.labelPos},labelSize:{type:[String,Number],default:()=>i.icon.labelSize},labelColor:{type:String,default:()=>i.icon.labelColor},space:{type:[String,Number],default:()=>i.icon.space},imgMode:{type:String,default:()=>i.icon.imgMode},width:{type:[String,Number],default:()=>i.icon.width},height:{type:[String,Number],default:()=>i.icon.height},top:{type:[String,Number],default:()=>i.icon.top},stop:{type:Boolean,default:()=>i.icon.stop}}}],computed:{uClasses(){let i=[];return i.push(this.customPrefix+"-"+this.name),"uicon"==this.customPrefix?i.push("u-iconfont"):i.push(this.customPrefix),this.color&&l.type.includes(this.color)&&i.push("u-icon__icon--"+this.color),i},iconStyle(){let i={};return i={fontSize:n(this.size),lineHeight:n(this.size),fontWeight:this.bold?"bold":"normal",top:n(this.top)},this.color&&!l.type.includes(this.color)&&(i.color=this.color),i},isImg(){return-1!==this.name.indexOf("/")},imgStyle(){let i={};return i.width=this.width?n(this.width):n(this.size),i.height=this.height?n(this.height):n(this.size),i},icon(){return"uicon"!==this.customPrefix?"":y["uicon-"+this.name]||this.name}},methods:{addStyle:e,addUnit:n,clickHandler(i){this.$emit("click",this.index),this.stop&&this.preventEvent(i)}}},[["render",function(i,o,c,l,n,e){const b=m,y=h,w=g;return u(),t(w,{class:s(["u-icon",["u-icon--"+i.labelPos]]),onClick:e.clickHandler},{default:r((()=>[e.isImg?(u(),t(b,{key:0,class:"u-icon__img",src:i.name,mode:i.imgMode,style:a([e.imgStyle,e.addStyle(i.customStyle)])},null,8,["src","mode","style"])):(u(),t(y,{key:1,class:s(["u-icon__icon",e.uClasses]),style:a([e.iconStyle,e.addStyle(i.customStyle)]),"hover-class":i.hoverClass},{default:r((()=>[f(d(e.icon),1)])),_:1},8,["class","style","hover-class"])),p(' 这里进行空字符串判断，如果仅仅是v-if="label"，可能会出现传递0的时候，结果也无法显示 '),""!==i.label?(u(),t(y,{key:2,class:"u-icon__label",style:a({color:i.labelColor,fontSize:e.addUnit(i.labelSize),marginLeft:"right"==i.labelPos?e.addUnit(i.space):0,marginTop:"bottom"==i.labelPos?e.addUnit(i.space):0,marginRight:"left"==i.labelPos?e.addUnit(i.space):0,marginBottom:"top"==i.labelPos?e.addUnit(i.space):0})},{default:r((()=>[f(d(i.label),1)])),_:1},8,["style"])):p("v-if",!0)])),_:1},8,["onClick","class"])}],["__scopeId","data-v-2d10e721"]]);export{w as _};

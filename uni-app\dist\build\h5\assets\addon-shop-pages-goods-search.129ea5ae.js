import{d as e,r as t,s as a,z as s,an as o,o as l,c as r,w as i,b as n,g as c,C as u,A as d,n as _,R as x,a3 as p,S as h,bE as v,m as f,a as y,aq as m,b0 as g,I as k,ag as S,k as C,B as b}from"./index-4b8dc7db.js";import{c as w}from"./goods.f34d2594.js";/* empty css                                                               *//* empty css                                                                     *//* empty css                                                                */import{_ as j}from"./_plugin-vue_export-helper.1b428a4d.js";const H=j(e({__name:"search",setup(e){const j=t(""),H=t([]),V=t(!1),R=t({});a((e=>{!uni.getStorageSync("goodsSearchHistory")&&uni.setStorageSync("goodsSearchHistory",[])})),s((()=>{j.value="",I(),U(),o((()=>{B()}))}));const I=()=>{H.value=uni.getStorageSync("goodsSearchHistory").reverse()},z=()=>{v({title:"提示",content:"确认删除全部历史记录？",confirmColor:f().themeColor["--primary-color"],success:e=>{e.confirm&&(uni.setStorageSync("goodsSearchHistory",[]),I())}})},U=()=>{w().then((e=>{R.value=e.data,R.value.search_words&&(R.value.search_words=R.value.search_words.filter((e=>e&&""!==e.trim())))}))},q=()=>{R.value.default_word&&""==j.value.trim()&&(j.value=R.value.default_word);let e=uni.getStorageSync("goodsSearchHistory"),t=[];e.length?(t=e.filter((e=>e!=j.value.trim())),j.value.trim()&&t.push(j.value.trim())):j.value.trim()&&t.push(j.value.trim()),uni.setStorageSync("goodsSearchHistory",t),y({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(j.value)},mode:"navigateTo"})},A=e=>{j.value=e,q()},B=()=>{m().in(this).select("#history-list").boundingClientRect((e=>{e&&e.height>2*g(70)+2*g(35)&&(V.value=!0)})).exec()};return(e,t)=>{const a=k,s=S,o=C;return l(),r(o,{style:_(e.themeColor())},{default:i((()=>[n(o,{class:"flex items-center px-[20rpx] h-[120rpx]"},{default:i((()=>[n(o,{class:"h-[68rpx] bg-[var(--temp-bg)] px-[30rpx] flex items-center rounded-[100rpx] flex-1"},{default:i((()=>[n(a,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[var(--text-color-light9)] text-[26rpx] mr-[18rpx]"}),n(s,{class:"text-[28rpx] flex-1",maxlength:"50",type:"text",modelValue:j.value,"onUpdate:modelValue":t[0]||(t[0]=e=>j.value=e),placeholder:R.value.default_word?R.value.default_word:"请搜索您想要的商品","confirm-type":"search",placeholderClass:"text-[var(--text-color-light9)] text-[28rpx]",onConfirm:q},null,8,["modelValue","placeholder"]),j.value?(l(),r(a,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 text-[24rpx] text-[var(--text-color-light9)]",onClick:t[1]||(t[1]=e=>j.value="")})):c("v-if",!0)])),_:1}),n(a,{onClick:t[2]||(t[2]=u((e=>q()),["stop"])),class:"text-[26rpx] ml-[32rpx] -mb-[2rpx]"},{default:i((()=>[d("搜索")])),_:1})])),_:1}),n(o,{class:"search-content"},{default:i((()=>[c(" 历史搜索 "),H.value.length?(l(),r(o,{key:0,class:"history"},{default:i((()=>[n(o,{class:"history-box"},{default:i((()=>[n(o,{class:"history-top"},{default:i((()=>[n(o,{class:"title font-500"},{default:i((()=>[d("历史搜索")])),_:1}),n(o,{class:"icon nc-iconfont nc-icon-shanchu-yuangaizhiV6xx !text-[24rpx] text-[var(--text-color-light6)]",onClick:z})])),_:1}),n(o,{class:"history-bottom",id:"history-list",style:_({maxHeight:V.value?"148rpx":"100%"})},{default:i((()=>[(l(!0),x(h,null,p(H.value,((e,t)=>(l(),x(h,{key:t},[e?(l(),r(o,{key:0,class:"history-li",onClick:t=>A(e)},{default:i((()=>[n(o,null,{default:i((()=>[d(b(e),1)])),_:2},1024)])),_:2},1032,["onClick"])):c("v-if",!0)],64)))),128)),V.value?(l(),r(o,{key:0,class:"history-li history_more",onClick:t[3]||(t[3]=e=>V.value=!1)},{default:i((()=>[n(o,{class:"content-box"},{default:i((()=>[n(a,{class:"text-[30rpx] nc-iconfont nc-icon-xiaV6xx"})])),_:1})])),_:1})):c("v-if",!0)])),_:1},8,["style"])])),_:1})])),_:1})):c("v-if",!0),R.value.search_words&&R.value.search_words.length?(l(),r(o,{key:1,class:"history"},{default:i((()=>[n(o,{class:"history-box"},{default:i((()=>[n(o,{class:"history-top"},{default:i((()=>[n(o,{class:"title font-500"},{default:i((()=>[d("热门搜索")])),_:1})])),_:1}),n(o,{class:"history-bottom"},{default:i((()=>[(l(!0),x(h,null,p(R.value.search_words,((e,t)=>(l(),x(h,{key:t},[e?(l(),r(o,{key:0,class:"history-li",onClick:t=>A(e)},{default:i((()=>[n(o,null,{default:i((()=>[d(b(e),1)])),_:2},1024)])),_:2},1032,["onClick"])):c("v-if",!0)],64)))),128))])),_:1})])),_:1})])),_:1})):c("v-if",!0)])),_:1}),c('        <view class="px-[20rpx] pt-[10rpx]">'),c('            <diy-shop-goods-ranking :component="rankingComponent" />'),c("        </view>")])),_:1},8,["style"])}}}),[["__scopeId","data-v-36256ff9"]]);export{H as default};

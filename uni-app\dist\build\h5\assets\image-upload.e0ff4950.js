import{ao as e,y as t,aw as a,o as i,c as s,w as l,b as o,g as r,R as n,a3 as u,S as m,f as d,v as p,A as c,B as f,i as h,j as x,I as g,k as L,C as _}from"./index-4b8dc7db.js";import{_ as C}from"./u-icon.33002907.js";import{_ as v}from"./u--image.892273b2.js";import{_ as w}from"./u-upload.899e5f6a.js";import{i as y}from"./common.eabc72c7.js";import{_ as b}from"./_plugin-vue_export-helper.1b428a4d.js";function j(t){return e.get(`agreement/${t}`)}const V=b({name:"ImageUpload",props:{modelValue:{type:[String,Array],default:""},maxCount:{type:Number,default:1},sizeLimit:{type:Number,default:5},multiple:{type:Boolean,default:!1},showTips:{type:Boolean,default:!0}},emits:["update:modelValue","change"],data:()=>({imageList:[]}),computed:{value:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e),this.$emit("change",e)}}},watch:{modelValue:{handler(e){this.initImageList(e)},immediate:!0}},methods:{initImageList(e){e?"string"==typeof e?this.imageList=e?[e]:[]:Array.isArray(e)&&(this.imageList=[...e]):this.imageList=[]},afterRead(e){this.multiple?e.file.forEach((e=>{this.upload({file:e})})):this.upload(e)},upload(a){if(this.imageList.length>=this.maxCount)return t({title:`最多允许上传${this.maxCount}张图片`,icon:"none"}),!1;var i;(i={filePath:a.file.url,name:"file"},e.upload("file/image",i,{showErrorMessage:!0})).then((e=>{var a,i;if(console.log("上传成功响应:",e),this.imageList.length<this.maxCount){const s=(null==(a=e.data)?void 0:a.url)||(null==(i=e.data)?void 0:i.file_url)||e.data;s?(this.imageList.push(s),this.updateValue(),t({title:"上传成功",icon:"success"})):(console.error("上传响应中没有找到图片URL:",e),t({title:"上传失败：无法获取图片地址",icon:"none"}))}})).catch((e=>{console.error("上传失败:",e),t({title:"上传失败",icon:"none"})}))},deleteImg(e){this.imageList.splice(e,1),this.updateValue()},imgListPreview(e){if(""===e)return!1;const t=[];t.push(this.img(e)),a({indicator:"number",loop:!0,urls:t})},img:e=>y(e),updateValue(){let e;e=1===this.maxCount?this.imageList[0]||"":[...this.imageList],this.value=e}}},[["render",function(e,t,a,y,b,j){const V=h(x("u-icon"),C),I=h(x("u--image"),v),k=g,A=L,R=h(x("u-upload"),w);return i(),s(A,{class:"image-upload"},{default:l((()=>[o(A,{class:"flex flex-wrap"},{default:l((()=>[r(" 已上传的图片预览 "),(i(!0),n(m,null,u(b.imageList,((e,t)=>(i(),s(A,{key:t,class:"mb-[18rpx] relative mr-[18rpx]"},{default:l((()=>[o(I,{class:"rounded-[10rpx] overflow-hidden",width:"140rpx",height:"140rpx",src:j.img(e||""),mode:"aspectFill",onClick:t=>j.imgListPreview(e)},{error:l((()=>[o(V,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"]),o(A,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:_((e=>j.deleteImg(t)),["stop"])},{default:l((()=>[o(k,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),r(" 上传按钮 "),d(o(A,{class:"w-[140rpx] h-[140rpx]"},{default:l((()=>[o(R,{onAfterRead:j.afterRead,maxCount:a.maxCount,multiple:a.multiple},{default:l((()=>[o(A,{class:"flex items-center justify-center w-[140rpx] h-[140rpx] border-[2rpx] border-dashed border-[#ddd] text-center text-[var(--text-color-light9)] rounded-[10rpx]"},{default:l((()=>[o(A,null,{default:l((()=>[o(A,{class:"nc-iconfont nc-icon-xiangjiV6xx text-[50rpx]"}),o(A,{class:"text-[24rpx] mt-[12rpx]"},{default:l((()=>[c(f(b.imageList.length)+"/"+f(a.maxCount),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["onAfterRead","maxCount","multiple"])])),_:1},512),[[p,b.imageList.length<a.maxCount]])])),_:1}),r(" 上传提示 "),a.showTips?(i(),s(A,{key:0,class:"upload-tips"},{default:l((()=>[o(k,null,{default:l((()=>[c("最多上传"+f(a.maxCount)+"张图片，单张不超过"+f(a.sizeLimit)+"MB",1)])),_:1})])),_:1})):r("v-if",!0)])),_:1})}],["__scopeId","data-v-8755123d"]]);export{V as I,j as g};

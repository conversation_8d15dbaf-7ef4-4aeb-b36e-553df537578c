import{r as a,az as e,N as l,l as o,E as t,s as g,z as n,W as r,aA as i,L as d,al as p,am as u,ai as v}from"./index-dd56d0cc.js";function b(b={}){const S=a(!0),s=e(),c=a(0),m=a(b.name||""),h=a(""),y=a(""),B=l({}),x=l({pageMode:"diy",title:"",global:{},value:[]}),C=o((()=>"decorate"==s.mode?s:x)),k=a(!1);return{getLoading:()=>S.value,data:C.value,isShowTopTabbar:k,pageStyle:()=>{var a="";return C.value.global.pageStartBgColor&&(C.value.global.pageStartBgColor&&C.value.global.pageEndBgColor?a+=`background:linear-gradient(${C.value.global.pageGradientAngle},${C.value.global.pageStartBgColor},${C.value.global.pageEndBgColor});`:a+="background-color:"+C.value.global.pageStartBgColor+";"),C.value.global.bottomTabBarSwitch?a+="min-height:calc(100vh - 50px);":a+="min-height:calc(100vh);",C.value.global.bgUrl&&(a+=`background-image:url('${t(C.value.global.bgUrl)}');`),C.value.global.bgHeightScale&&(a+=`background-size: 100% ${C.value.global.bgHeightScale}%;`),a},onLoad:()=>{g((a=>{s.mode=a.mode||"","decorate"==s.mode&&(S.value=!1),c.value=a.id||"",""==m.value&&(m.value=a.name||""),h.value=a.template||""}))},onShow:(a=null)=>{n((()=>{let e=r();y.value=e[e.length-1]?e[e.length-1].route:"";let l=[];uni.getStorageSync("diyPageBlank")&&(l=uni.getStorageSync("diyPageBlank")),!l.length||l.length&&-1==l.indexOf(y.value)?s.topFixedStatus="home":l.length&&-1!=l.indexOf(y.value)&&(s.topFixedStatus="diy"),"decorate"==s.mode?s.init():i({id:c.value,name:m.value,template:h.value}).then((e=>{if(Object.assign(B,e.data),B.value){x.pageMode=B.mode,x.title=B.title;let a=JSON.parse(B.value);x.global=a.global,x.global.popWindow&&x.global.popWindow.show&&(x.global.popWindow.id=B.id),x.value=a.value,x.value.forEach(((a,e)=>{a.pageStyle="",a.pageStartBgColor&&(a.pageStartBgColor&&a.pageEndBgColor?a.pageStyle+=`background:linear-gradient(${a.pageGradientAngle},${a.pageStartBgColor},${a.pageEndBgColor});`:a.pageStyle+="background-color:"+a.pageStartBgColor+";"),a.margin&&(a.margin.top>0&&(a.pageStyle+="padding-top:"+2*a.margin.top+"rpx;"),a.pageStyle+="padding-bottom:"+2*a.margin.bottom+"rpx;",a.pageStyle+="padding-right:"+2*a.margin.both+"rpx;",a.pageStyle+="padding-left:"+2*a.margin.both+"rpx;")})),k.value=x.value.some((a=>a&&a.position&&"top_fixed"==a.position)),d({title:x.title})}S.value=!1,a&&a(B)}))}))},onHide:(a=null)=>{p((()=>{let e=[];uni.getStorageSync("diyPageBlank")&&(e=uni.getStorageSync("diyPageBlank")),e.length&&(e=Array.from(new Set(e)),e.forEach(((a,e,l)=>{a==y.value&&l.splice(e,1)}))),"diy"==s.topFixedStatus&&e.push(y.value),uni.setStorageSync("diyPageBlank",e),a&&a()}))},onUnload:()=>{u((()=>{}))},onPageScroll:()=>{v((a=>{a.scrollTop>0&&(s.scrollTop=a.scrollTop)}))}}}export{b as u};

import{d as e,r as t,F as l,l as s,o as a,c as r,w as o,b as i,e as d,A as n,B as p,R as c,a3 as u,S as x,g as f,T as m,n as _,ah as h,ai as v,y as g,I as b,k as w,i as k,j as y,H as j,C as F,E as C,a as E,G as I}from"./index-dd56d0cc.js";import{_ as M}from"./u--image.cd475bba.js";import{_ as R}from"./u-swipe-action-item.1ddd95ff.js";import{_ as S}from"./u-swipe-action.1eb5b0f2.js";import{m as z,k as U}from"./goods.dbf7b09d.js";import{M as A}from"./mescroll-body.520ba8e3.js";import{M as P}from"./mescroll-empty.8630a00e.js";import{u as B}from"./useMescroll.26ccf5de.js";import{_ as G}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.dfca355c.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./mescroll-i18n.92d484d5.js";const H=G(e({__name:"collect",setup(e){const{mescrollInit:G,downCallback:H,getMescroll:N}=B(v,h),O=t(!1),Q=t(!1),T=t([]),V=t(!1),q=e=>{O.value=!1;let t={page:e.num,limit:e.size};z(t).then((t=>{let l=t.data.data;1===Number(e.num)&&(T.value=[]),l=l.map((e=>(e.checked=!1,e))),T.value=T.value.concat(l),e.endSuccess(l.length),O.value=!0})).catch((()=>{O.value=!0,e.endErr()}))},D=t([{text:l("delete"),style:{backgroundColor:"#EF000C",width:"100rpx",height:"100%",borderRadius:"10rpx"}}]),J=()=>{if(!K.value)return void g({title:"请先选择收藏的商品",icon:"none"});if(Q.value)return;Q.value=!0;const e=[];T.value.forEach((t=>{t.checked&&e.push(t.goods_id)})),U({goods_ids:e}).then((e=>{Q.value=!1,N().resetUpScroll()}))},K=s((()=>{let e=0;return T.value.forEach((t=>{t.checked&&(e+=1)})),e})),L=()=>{const e=T.value.length!=K.value;T.value.forEach((t=>{t.checked=e}))};return(e,t)=>{const l=b,s=w,h=I,v=k(y("u--image"),M),g=k(y("u-swipe-action-item"),R),z=k(y("u-swipe-action"),S),B=j;return a(),r(s,{style:_(e.themeColor())},{default:o((()=>[i(s,{class:"bg-page min-h-screen overflow-hidden"},{default:o((()=>[i(A,{ref:"mescrollRef",top:"0",onInit:d(G),down:{use:!1},onUp:q},{default:o((()=>[T.value.length?(a(),r(s,{key:0,class:"py-[var(--top-m)] sidebar-margin"},{default:o((()=>[i(s,{class:"bg-[#fff] pb-[10rpx] box-border rounded-[var(--rounded-big)]"},{default:o((()=>[i(s,{class:"flex mx-[var(--rounded-big)] pt-[var(--pad-top-m)] justify-between items-center box-border font-400 text-[24rpx] mb-[24rpx] leading-[30rpx]"},{default:o((()=>[i(s,{class:"flex items-baseline text-[24rpx] text-[#333]"},{default:o((()=>[i(l,null,{default:o((()=>[n("共")])),_:1}),i(l,{class:"text-[32rpx] mx-[2rpx] text-[var(--price-text-color)]"},{default:o((()=>[n(p(T.value.length),1)])),_:1}),i(l,null,{default:o((()=>[n("件商品")])),_:1})])),_:1}),i(l,{onClick:t[0]||(t[0]=e=>V.value=!V.value),class:"text-[var(--text-color-light6)] text-[24rpx]"},{default:o((()=>[n(p(V.value?"完成":"管理"),1)])),_:1})])),_:1}),i(z,{ref:"swipeActive"},{default:o((()=>[(a(!0),c(x,null,u(T.value,((e,t)=>(a(),r(s,{key:t,class:"py-[20rpx] overflow-hidden w-full"},{default:o((()=>[i(g,{options:D.value,onClick:t=>{return l=e,void(Q.value||(Q.value=!0,U({goods_ids:[l.goods_id]}).then((e=>{Q.value=!1,N().resetUpScroll()}))));var l}},{default:o((()=>[i(s,{class:"flex px-[var(--pad-sidebar-m)]"},{default:o((()=>[V.value?(a(),r(s,{key:0,class:"self-center w-[58rpx] h-[60rpx] flex items-center",onClick:F((t=>e.checked=!e.checked),["stop"])},{default:o((()=>[i(l,{class:m(["iconfont text-primary text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:e.checked,"bg-[#F5F5F5]":!e.checked}])},null,8,["class"])])),_:2},1032,["onClick"])):f("v-if",!0),i(s,{class:"flex flex-1",onClick:t=>{E({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.goods_id}})}},{default:o((()=>[i(s,{class:"relative w-[200rpx] h-[200rpx] flex items-center justify-center rounded-[var(--goods-rounded-big)] overflow-hidden"},{default:o((()=>[i(v,{radius:"var(--goods-rounded-big)",width:"200rpx",height:"200rpx",src:d(C)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:o((()=>[i(h,{class:"w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:d(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),0==e.status?(a(),r(s,{key:0,class:"absolute left-0 top-0 w-[200rpx] h-[200rpx] leading-[200rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:o((()=>[i(l,{class:"text-[#fff] text-[28rpx]"},{default:o((()=>[n("已失效")])),_:1})])),_:1})):f("v-if",!0)])),_:2},1024),i(s,{class:"flex flex-1 flex-wrap ml-[20rpx]"},{default:o((()=>[i(s,{class:"w-[100%] flex flex-col items-baseline"},{default:o((()=>[i(s,{class:"text-[#333] text-[28rpx] max-h-[80rpx] leading-[40rpx] multi-hidden font-400"},{default:o((()=>[n(p(e.goods_name),1)])),_:2},1024),e.sku_name?(a(),r(s,{key:0,class:"box-border max-w-[376rpx] mt-[10rpx] px-[14rpx] h-[36rpx] leading-[36rpx] truncate text-[var(--text-color-light6)] bg-[#F5F5F5] text-[22rpx] rounded-[20rpx]"},{default:o((()=>[n(p(e.sku_name),1)])),_:2},1024)):f("v-if",!0)])),_:2},1024),i(s,{class:"flex justify-between items-end self-end mt-[10rpx] w-[100%]"},{default:o((()=>[i(s,{class:"text-[var(--price-text-color)] price-font truncate max-w-[200rpx]"},{default:o((()=>[i(l,{class:"text-[24rpx] font-500"},{default:o((()=>[n("￥")])),_:1}),i(l,{class:"text-[40rpx] font-500"},{default:o((()=>[n(p(parseFloat(e.show_price).toFixed(2).split(".")[0]),1)])),_:2},1024),i(l,{class:"text-[24rpx] font-500"},{default:o((()=>[n("."+p(parseFloat(e.show_price).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==e.show_type?(a(),r(h,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:d(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==e.show_type?(a(),r(h,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:d(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==e.show_type?(a(),r(h,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:d(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):f("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1032,["options","onClick"])])),_:2},1024)))),128))])),_:1},512)])),_:1})])),_:1})):f("v-if",!0),!T.value.length&&O.value?(a(),r(P,{key:1,option:{tip:"暂无收藏的商品"}})):f("v-if",!0)])),_:1},8,["onInit"]),T.value.length&&V.value?(a(),r(s,{key:0,class:"flex h-[96rpx] items-center bg-[#fff] fixed left-0 right-0 bottom-0 pl-[30rpx] pr-[20rpx] box-solid mb-ios justify-between"},{default:o((()=>[i(s,{class:"flex items-center",onClick:L},{default:o((()=>[i(l,{class:m(["self-center iconfont text-primary text-[34rpx] mr-[10rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden flex-shrink-0",{iconxuanze1:T.value.length==d(K),"bg-color":T.value.length!=d(K)}])},null,8,["class"]),i(l,{class:"font-400 text-[#303133] text-[26rpx]"},{default:o((()=>[n("全选")])),_:1})])),_:1}),i(B,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:J},{default:o((()=>[n("取消收藏")])),_:1})])),_:1})):f("v-if",!0)])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-0f24586e"]]);export{H as default};

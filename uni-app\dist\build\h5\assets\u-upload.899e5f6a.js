import{bh as e,bi as t,bj as a,a4 as i,a5 as s,a6 as l,a7 as o,a8 as u,aN as d,aw as r,aM as n,i as c,j as p,o as m,c as f,w as h,b as _,R as y,S as g,a3 as b,n as v,A as w,B as k,g as x,C,$ as I,T as z,G as j,I as F,k as S}from"./index-4b8dc7db.js";import{_ as R}from"./u-icon.33002907.js";import{_ as T}from"./u-loading-icon.11ef83b8.js";import{_ as B}from"./_plugin-vue_export-helper.1b428a4d.js";function D(e,t){return["[object Object]","[object File]"].includes(Object.prototype.toString.call(e))?Object.keys(e).reduce(((a,i)=>(t.includes(i)||(a[i]=e[i]),a)),{}):{}}function O(e){return e.tempFiles.map((e=>({...D(e,["path"]),url:e.path,size:e.size,name:e.name,type:e.type})))}function P({accept:i,multiple:s,capture:l,compressed:o,maxDuration:u,sizeType:d,camera:r,maxCount:n}){return new Promise(((c,p)=>{switch(i){case"image":a({count:s?Math.min(n,9):1,sourceType:l,sizeType:d,success:e=>c(function(e){return e.tempFiles.map((e=>({...D(e,["path"]),type:"image",url:e.path,thumb:e.path,size:e.size,name:e.name})))}(e)),fail:p});break;case"video":t({sourceType:l,compressed:o,maxDuration:u,camera:r,success:e=>c(function(e){return[{...D(e,["tempFilePath","thumbTempFilePath","errMsg"]),type:"video",url:e.tempFilePath,thumb:e.thumbTempFilePath,size:e.size,name:e.name}]}(e)),fail:p});break;case"file":e({count:s?n:1,type:i,success:e=>c(O(e)),fail:p});break;default:e({count:s?n:1,type:"all",success:e=>c(O(e)),fail:p})}}))}const $=B({name:"u-upload",mixins:[s,l,{watch:{accept:{immediate:!0,handler(e){}}}},{props:{accept:{type:String,default:()=>i.upload.accept},capture:{type:[String,Array],default:()=>i.upload.capture},compressed:{type:Boolean,default:()=>i.upload.compressed},camera:{type:String,default:()=>i.upload.camera},maxDuration:{type:Number,default:()=>i.upload.maxDuration},uploadIcon:{type:String,default:()=>i.upload.uploadIcon},uploadIconColor:{type:String,default:()=>i.upload.uploadIconColor},useBeforeRead:{type:Boolean,default:()=>i.upload.useBeforeRead},afterRead:{type:Function,default:null},beforeRead:{type:Function,default:null},previewFullImage:{type:Boolean,default:()=>i.upload.previewFullImage},maxCount:{type:[String,Number],default:()=>i.upload.maxCount},disabled:{type:Boolean,default:()=>i.upload.disabled},imageMode:{type:String,default:()=>i.upload.imageMode},name:{type:String,default:()=>i.upload.name},sizeType:{type:Array,default:()=>i.upload.sizeType},multiple:{type:Boolean,default:()=>i.upload.multiple},deletable:{type:Boolean,default:()=>i.upload.deletable},maxSize:{type:[String,Number],default:()=>i.upload.maxSize},fileList:{type:Array,default:()=>i.upload.fileList},uploadText:{type:String,default:()=>i.upload.uploadText},width:{type:[String,Number],default:()=>i.upload.width},height:{type:[String,Number],default:()=>i.upload.height},previewImage:{type:Boolean,default:()=>i.upload.previewImage}}}],data:()=>({lists:[],isInCount:!0}),watch:{fileList:{handler(){this.formatFileList()},immediate:!0,deep:!0}},emits:["error","beforeRead","oversize","afterRead","delete","clickPreview"],methods:{addUnit:o,addStyle:u,formatFileList(){const{fileList:e=[],maxCount:t}=this,a=e.map((e=>Object.assign(Object.assign({},e),{isImage:"image"===this.accept||d.image(e.url||e.thumb),isVideo:"video"===this.accept||d.video(e.url||e.thumb),deletable:"boolean"==typeof e.deletable?e.deletable:this.deletable})));this.lists=a,this.isInCount=a.length<t},chooseFile(){const{maxCount:e,multiple:t,lists:a,disabled:i}=this;if(i)return;let s;try{s=d.array(this.capture)?this.capture:this.capture.split(",")}catch(l){s=[]}P(Object.assign({accept:this.accept,multiple:this.multiple,capture:s,compressed:this.compressed,maxDuration:this.maxDuration,sizeType:this.sizeType,camera:this.camera},{maxCount:e-a.length})).then((e=>{this.onBeforeRead(t?e:e[0])})).catch((e=>{this.$emit("error",e)}))},onBeforeRead(e){const{beforeRead:t,useBeforeRead:a}=this;let i=!0;d.func(t)&&(i=t(e,this.getDetail())),a&&(i=new Promise(((t,a)=>{this.$emit("beforeRead",Object.assign(Object.assign({file:e},this.getDetail()),{callback:e=>{e?t():a()}}))}))),i&&(d.promise(i)?i.then((t=>this.onAfterRead(t||e))):this.onAfterRead(e))},getDetail(e){return{name:this.name,index:null==e?this.fileList.length:e}},onAfterRead(e){const{maxSize:t,afterRead:a}=this;(Array.isArray(e)?e.some((e=>e.size>t)):e.size>t)?this.$emit("oversize",Object.assign({file:e},this.getDetail())):("function"==typeof a&&a(e,this.getDetail()),this.$emit("afterRead",Object.assign({file:e},this.getDetail())))},deleteItem(e){this.$emit("delete",Object.assign(Object.assign({},this.getDetail(e)),{file:this.fileList[e]}))},onPreviewImage(e){e.isImage&&this.previewFullImage&&r({urls:this.lists.filter((e=>"image"===this.accept||d.image(e.url||e.thumb))).map((e=>e.url||e.thumb)),current:e.url||e.thumb,fail(){n("预览图片失败")}})},onPreviewVideo(e){this.data.previewFullImage&&(e.currentTarget.dataset,this.data)},onClickPreview(e){const{index:t}=e.currentTarget.dataset,a=this.data.lists[t];if(this.data.previewFullImage){if("video"===a.type)this.onPreviewVideo(e);this.$emit("clickPreview",Object.assign(Object.assign({},a),this.getDetail(t)))}}}},[["render",function(e,t,a,i,s,l){const o=j,u=c(p("u-icon"),R),d=F,r=S,n=c(p("u-loading-icon"),T);return m(),f(r,{class:"u-upload",style:v([l.addStyle(e.customStyle)])},{default:h((()=>[_(r,{class:"u-upload__wrap"},{default:h((()=>[e.previewImage?(m(!0),y(g,{key:0},b(s.lists,((t,a)=>(m(),f(r,{class:"u-upload__wrap__preview",key:a},{default:h((()=>[t.isImage||t.type&&"image"===t.type?(m(),f(o,{key:0,src:t.thumb||t.url,mode:e.imageMode,class:"u-upload__wrap__preview__image",onClick:e=>l.onPreviewImage(t),style:v([{width:l.addUnit(e.width),height:l.addUnit(e.height)}])},null,8,["src","mode","onClick","style"])):(m(),f(r,{key:1,class:"u-upload__wrap__preview__other",onClick:e=>l.onClickPreview(e,t)},{default:h((()=>[_(u,{color:"#80CBF9",size:"26",name:t.isVideo||t.type&&"video"===t.type?"movie":"folder"},null,8,["name"]),_(d,{class:"u-upload__wrap__preview__other__text"},{default:h((()=>[w(k(t.isVideo||t.type&&"video"===t.type?"视频":"文件"),1)])),_:2},1024)])),_:2},1032,["onClick"])),"uploading"===t.status||"failed"===t.status?(m(),f(r,{key:2,class:"u-upload__status"},{default:h((()=>[_(r,{class:"u-upload__status__icon"},{default:h((()=>["failed"===t.status?(m(),f(u,{key:0,name:"close-circle",color:"#ffffff",size:"25"})):(m(),f(n,{key:1,size:"22",mode:"circle",color:"#ffffff"}))])),_:2},1024),t.message?(m(),f(d,{key:0,class:"u-upload__status__message"},{default:h((()=>[w(k(t.message),1)])),_:2},1024)):x("v-if",!0)])),_:2},1024)):x("v-if",!0),"uploading"!==t.status&&(e.deletable||t.deletable)?(m(),f(r,{key:3,class:"u-upload__deletable",onClick:C((e=>l.deleteItem(a)),["stop"])},{default:h((()=>[_(r,{class:"u-upload__deletable__icon"},{default:h((()=>[_(u,{name:"close",color:"#ffffff",size:"10"})])),_:1})])),_:2},1032,["onClick"])):x("v-if",!0),"success"===t.status?(m(),f(r,{key:4,class:"u-upload__success"},{default:h((()=>[_(r,{class:"u-upload__success__icon"},{default:h((()=>[_(u,{name:"checkmark",color:"#ffffff",size:"12"})])),_:1})])),_:1})):x("v-if",!0)])),_:2},1024)))),128)):x("v-if",!0),s.isInCount?(m(),y(g,{key:1},[e.$slots.default||e.$slots.$default?(m(),f(r,{key:0,onClick:l.chooseFile},{default:h((()=>[I(e.$slots,"default",{},void 0,!0)])),_:3},8,["onClick"])):(m(),f(r,{key:1,class:z(["u-upload__button",[e.disabled&&"u-upload__button--disabled"]]),"hover-class":e.disabled?"":"u-upload__button--hover","hover-stay-time":"150",onClick:l.chooseFile,style:v([{width:l.addUnit(e.width),height:l.addUnit(e.height)}])},{default:h((()=>[_(u,{name:e.uploadIcon,size:"26",color:e.uploadIconColor},null,8,["name","color"]),e.uploadText?(m(),f(d,{key:0,class:"u-upload__button__text"},{default:h((()=>[w(k(e.uploadText),1)])),_:1})):x("v-if",!0)])),_:1},8,["hover-class","onClick","class","style"]))],64)):x("v-if",!0)])),_:3})])),_:3},8,["style"])}],["__scopeId","data-v-a33a03e2"]]);export{$ as _};

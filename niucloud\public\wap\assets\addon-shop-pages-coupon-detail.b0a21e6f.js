import{d as t,r as e,p as n,l as r,s as o,M as i,a,o as s,c as u,w as l,n as c,e as f,b as d,A as h,B as g,g as p,R as m,S as v,J as y,k as w,I as x,G as E,i as b,j as _,E as A}from"./index-4b8dc7db.js";import{_ as C}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{t as T}from"./topTabbar.1aa95d14.js";import{c as B,b as I}from"./coupon.d71b475a.js";import{_ as M}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.11ef83b8.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */var N={},P={},R={};let L;const k=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];R.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},R.getSymbolTotalCodewords=function(t){return k[t]},R.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},R.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');L=t},R.isKanjiModeEnabled=function(){return void 0!==L},R.toSJIS=function(t){return L(t)};var U,S={};function D(){this.buffer=[],this.length=0}(U=S).L={bit:1},U.M={bit:0},U.Q={bit:3},U.H={bit:2},U.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},U.from=function(t,e){if(U.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return U.L;case"m":case"medium":return U.M;case"q":case"quartile":return U.Q;case"h":case"high":return U.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(n){return e}},D.prototype={get:function(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var F=D;function j(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}j.prototype.set=function(t,e,n,r){const o=t*this.size+e;this.data[o]=n,r&&(this.reservedBit[o]=!0)},j.prototype.get=function(t,e){return this.data[t*this.size+e]},j.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},j.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var z=j,H={};!function(t){const e=R.getSymbolSize;t.getRowColCoords=function(t){if(1===t)return[];const n=Math.floor(t/7)+2,r=e(t),o=145===r?26:2*Math.ceil((r-13)/(2*n-2)),i=[r-7];for(let e=1;e<n-1;e++)i[e]=i[e-1]-o;return i.push(6),i.reverse()},t.getPositions=function(e){const n=[],r=t.getRowColCoords(e),o=r.length;for(let t=0;t<o;t++)for(let e=0;e<o;e++)0===t&&0===e||0===t&&e===o-1||t===o-1&&0===e||n.push([r[t],r[e]]);return n}}(H);var J={};const K=R.getSymbolSize;J.getPositions=function(t){const e=K(t);return[[0,0],[e-7,0],[0,e-7]]};var Y={};!function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,n=3,r=40,o=10;function i(e,n,r){switch(e){case t.Patterns.PATTERN000:return(n+r)%2==0;case t.Patterns.PATTERN001:return n%2==0;case t.Patterns.PATTERN010:return r%3==0;case t.Patterns.PATTERN011:return(n+r)%3==0;case t.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case t.Patterns.PATTERN101:return n*r%2+n*r%3==0;case t.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case t.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}t.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(t){const n=t.size;let r=0,o=0,i=0,a=null,s=null;for(let u=0;u<n;u++){o=i=0,a=s=null;for(let l=0;l<n;l++){let n=t.get(u,l);n===a?o++:(o>=5&&(r+=e+(o-5)),a=n,o=1),n=t.get(l,u),n===s?i++:(i>=5&&(r+=e+(i-5)),s=n,i=1)}o>=5&&(r+=e+(o-5)),i>=5&&(r+=e+(i-5))}return r},t.getPenaltyN2=function(t){const e=t.size;let r=0;for(let n=0;n<e-1;n++)for(let o=0;o<e-1;o++){const e=t.get(n,o)+t.get(n,o+1)+t.get(n+1,o)+t.get(n+1,o+1);4!==e&&0!==e||r++}return r*n},t.getPenaltyN3=function(t){const e=t.size;let n=0,o=0,i=0;for(let r=0;r<e;r++){o=i=0;for(let a=0;a<e;a++)o=o<<1&2047|t.get(r,a),a>=10&&(1488===o||93===o)&&n++,i=i<<1&2047|t.get(a,r),a>=10&&(1488===i||93===i)&&n++}return n*r},t.getPenaltyN4=function(t){let e=0;const n=t.data.length;for(let r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*o},t.applyMask=function(t,e){const n=e.size;for(let r=0;r<n;r++)for(let o=0;o<n;o++)e.isReserved(o,r)||e.xor(o,r,i(t,o,r))},t.getBestMask=function(e,n){const r=Object.keys(t.Patterns).length;let o=0,i=1/0;for(let a=0;a<r;a++){n(a),t.applyMask(a,e);const r=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),r<i&&(i=r,o=a)}return o}}(Y);var V={};const O=S,q=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Q=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];V.getBlocksCount=function(t,e){switch(e){case O.L:return q[4*(t-1)+0];case O.M:return q[4*(t-1)+1];case O.Q:return q[4*(t-1)+2];case O.H:return q[4*(t-1)+3];default:return}},V.getTotalCodewordsCount=function(t,e){switch(e){case O.L:return Q[4*(t-1)+0];case O.M:return Q[4*(t-1)+1];case O.Q:return Q[4*(t-1)+2];case O.H:return Q[4*(t-1)+3];default:return}};var $={},X={};const Z=new Uint8Array(512),W=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)Z[e]=t,W[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)Z[e]=Z[e-255]}(),X.log=function(t){if(t<1)throw new Error("log("+t+")");return W[t]},X.exp=function(t){return Z[t]},X.mul=function(t,e){return 0===t||0===e?0:Z[W[t]+W[e]]},function(t){const e=X;t.mul=function(t,n){const r=new Uint8Array(t.length+n.length-1);for(let o=0;o<t.length;o++)for(let i=0;i<n.length;i++)r[o+i]^=e.mul(t[o],n[i]);return r},t.mod=function(t,n){let r=new Uint8Array(t);for(;r.length-n.length>=0;){const t=r[0];for(let i=0;i<n.length;i++)r[i]^=e.mul(n[i],t);let o=0;for(;o<r.length&&0===r[o];)o++;r=r.slice(o)}return r},t.generateECPolynomial=function(n){let r=new Uint8Array([1]);for(let o=0;o<n;o++)r=t.mul(r,new Uint8Array([1,e.exp(o)]));return r}}($);const G=$;function tt(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}tt.prototype.initialize=function(t){this.degree=t,this.genPoly=G.generateECPolynomial(this.degree)},tt.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=G.mod(e,this.genPoly),r=this.degree-n.length;if(r>0){const t=new Uint8Array(this.degree);return t.set(n,r),t}return n};var et=tt,nt={},rt={},ot={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},it={};const at="[0-9]+";let st="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";st=st.replace(/u/g,"\\u");const ut="(?:(?![A-Z0-9 $%*+\\-./:]|"+st+")(?:.|[\r\n]))+";it.KANJI=new RegExp(st,"g"),it.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),it.BYTE=new RegExp(ut,"g"),it.NUMERIC=new RegExp(at,"g"),it.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const lt=new RegExp("^"+st+"$"),ct=new RegExp("^"+at+"$"),ft=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");it.testKanji=function(t){return lt.test(t)},it.testNumeric=function(t){return ct.test(t)},it.testAlphanumeric=function(t){return ft.test(t)},function(t){const e=ot,n=it;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(t,n){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!e.isValid(n))throw new Error("Invalid version: "+n);return n>=1&&n<10?t.ccBits[0]:n<27?t.ccBits[1]:t.ccBits[2]},t.getBestModeForData=function(e){return n.testNumeric(e)?t.NUMERIC:n.testAlphanumeric(e)?t.ALPHANUMERIC:n.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},t.isValid=function(t){return t&&t.bit&&t.ccBits},t.from=function(e,n){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(r){return n}}}(rt),function(t){const e=R,n=V,r=S,o=rt,i=ot,a=e.getBCHDigit(7973);function s(t,e){return o.getCharCountIndicator(t,e)+4}function u(t,e){let n=0;return t.forEach((function(t){const r=s(t.mode,e);n+=r+t.getBitsLength()})),n}t.from=function(t,e){return i.isValid(t)?parseInt(t,10):e},t.getCapacity=function(t,r,a){if(!i.isValid(t))throw new Error("Invalid QR Code version");void 0===a&&(a=o.BYTE);const u=8*(e.getSymbolTotalCodewords(t)-n.getTotalCodewordsCount(t,r));if(a===o.MIXED)return u;const l=u-s(a,t);switch(a){case o.NUMERIC:return Math.floor(l/10*3);case o.ALPHANUMERIC:return Math.floor(l/11*2);case o.KANJI:return Math.floor(l/13);case o.BYTE:default:return Math.floor(l/8)}},t.getBestVersionForData=function(e,n){let i;const a=r.from(n,r.M);if(Array.isArray(e)){if(e.length>1)return function(e,n){for(let r=1;r<=40;r++)if(u(e,r)<=t.getCapacity(r,n,o.MIXED))return r}(e,a);if(0===e.length)return 1;i=e[0]}else i=e;return function(e,n,r){for(let o=1;o<=40;o++)if(n<=t.getCapacity(o,r,e))return o}(i.mode,i.getLength(),a)},t.getEncodedBits=function(t){if(!i.isValid(t)||t<7)throw new Error("Invalid QR Code version");let n=t<<12;for(;e.getBCHDigit(n)-a>=0;)n^=7973<<e.getBCHDigit(n)-a;return t<<12|n}}(nt);var dt={};const ht=R,gt=ht.getBCHDigit(1335);dt.getEncodedBits=function(t,e){const n=t.bit<<3|e;let r=n<<10;for(;ht.getBCHDigit(r)-gt>=0;)r^=1335<<ht.getBCHDigit(r)-gt;return 21522^(n<<10|r)};var pt={};const mt=rt;function vt(t){this.mode=mt.NUMERIC,this.data=t.toString()}vt.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},vt.prototype.getLength=function(){return this.data.length},vt.prototype.getBitsLength=function(){return vt.getBitsLength(this.data.length)},vt.prototype.write=function(t){let e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);const o=this.data.length-e;o>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*o+1))};var yt=vt;const wt=rt,xt=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Et(t){this.mode=wt.ALPHANUMERIC,this.data=t}Et.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},Et.prototype.getLength=function(){return this.data.length},Et.prototype.getBitsLength=function(){return Et.getBitsLength(this.data.length)},Et.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let n=45*xt.indexOf(this.data[e]);n+=xt.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(xt.indexOf(this.data[e]),6)};var bt=Et;const _t=function(t){for(var e=[],n=t.length,r=0;r<n;r++){var o=t.charCodeAt(r);if(o>=55296&&o<=56319&&n>r+1){var i=t.charCodeAt(r+1);i>=56320&&i<=57343&&(o=1024*(o-55296)+i-56320+65536,r+=1)}o<128?e.push(o):o<2048?(e.push(o>>6|192),e.push(63&o|128)):o<55296||o>=57344&&o<65536?(e.push(o>>12|224),e.push(o>>6&63|128),e.push(63&o|128)):o>=65536&&o<=1114111?(e.push(o>>18|240),e.push(o>>12&63|128),e.push(o>>6&63|128),e.push(63&o|128)):e.push(239,191,189)}return new Uint8Array(e).buffer},At=rt;function Ct(t){this.mode=At.BYTE,"string"==typeof t&&(t=_t(t)),this.data=new Uint8Array(t)}Ct.getBitsLength=function(t){return 8*t},Ct.prototype.getLength=function(){return this.data.length},Ct.prototype.getBitsLength=function(){return Ct.getBitsLength(this.data.length)},Ct.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)};var Tt=Ct;const Bt=rt,It=R;function Mt(t){this.mode=Bt.KANJI,this.data=t}Mt.getBitsLength=function(t){return 13*t},Mt.prototype.getLength=function(){return this.data.length},Mt.prototype.getBitsLength=function(){return Mt.getBitsLength(this.data.length)},Mt.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=It.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}};var Nt,Pt=Mt,Rt={};Nt={single_source_shortest_paths:function(t,e,n){var r={},o={};o[e]=0;var i,a,s,u,l,c,f,d=Nt.PriorityQueue.make();for(d.push(e,0);!d.empty();)for(s in a=(i=d.pop()).value,u=i.cost,l=t[a]||{})l.hasOwnProperty(s)&&(c=u+l[s],f=o[s],(void 0===o[s]||f>c)&&(o[s]=c,d.push(s,c),r[s]=a));if(void 0!==n&&void 0===o[n]){var h=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(h)}return r},extract_shortest_path_from_predecessor_list:function(t,e){for(var n=[],r=e;r;)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,e,n){var r=Nt.single_source_shortest_paths(t,e,n);return Nt.extract_shortest_path_from_predecessor_list(r,n)},PriorityQueue:{make:function(t){var e,n=Nt.PriorityQueue,r={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(r[e]=n[e]);return r.queue=[],r.sorter=t.sorter||n.default_sorter,r},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}},{get exports(){return Rt},set exports(t){Rt=t}}.exports=Nt,function(t){const e=rt,n=yt,r=bt,o=Tt,i=Pt,a=it,s=R,u=Rt;function l(t){return unescape(encodeURIComponent(t)).length}function c(t,e,n){const r=[];let o;for(;null!==(o=t.exec(n));)r.push({data:o[0],index:o.index,mode:e,length:o[0].length});return r}function f(t){const n=c(a.NUMERIC,e.NUMERIC,t),r=c(a.ALPHANUMERIC,e.ALPHANUMERIC,t);let o,i;s.isKanjiModeEnabled()?(o=c(a.BYTE,e.BYTE,t),i=c(a.KANJI,e.KANJI,t)):(o=c(a.BYTE_KANJI,e.BYTE,t),i=[]);return n.concat(r,o,i).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function d(t,a){switch(a){case e.NUMERIC:return n.getBitsLength(t);case e.ALPHANUMERIC:return r.getBitsLength(t);case e.KANJI:return i.getBitsLength(t);case e.BYTE:return o.getBitsLength(t)}}function h(t,a){let u;const l=e.getBestModeForData(t);if(u=e.from(a,l),u!==e.BYTE&&u.bit<l.bit)throw new Error('"'+t+'" cannot be encoded with mode '+e.toString(u)+".\n Suggested mode is: "+e.toString(l));switch(u!==e.KANJI||s.isKanjiModeEnabled()||(u=e.BYTE),u){case e.NUMERIC:return new n(t);case e.ALPHANUMERIC:return new r(t);case e.KANJI:return new i(t);case e.BYTE:return new o(t)}}t.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(h(e,null)):e.data&&t.push(h(e.data,e.mode)),t}),[])},t.fromString=function(n,r){const o=function(t){const n=[];for(let r=0;r<t.length;r++){const o=t[r];switch(o.mode){case e.NUMERIC:n.push([o,{data:o.data,mode:e.ALPHANUMERIC,length:o.length},{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.ALPHANUMERIC:n.push([o,{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.KANJI:n.push([o,{data:o.data,mode:e.BYTE,length:l(o.data)}]);break;case e.BYTE:n.push([{data:o.data,mode:e.BYTE,length:l(o.data)}])}}return n}(f(n,s.isKanjiModeEnabled())),i=function(t,n){const r={},o={start:{}};let i=["start"];for(let a=0;a<t.length;a++){const s=t[a],u=[];for(let t=0;t<s.length;t++){const l=s[t],c=""+a+t;u.push(c),r[c]={node:l,lastCount:0},o[c]={};for(let t=0;t<i.length;t++){const a=i[t];r[a]&&r[a].node.mode===l.mode?(o[a][c]=d(r[a].lastCount+l.length,l.mode)-d(r[a].lastCount,l.mode),r[a].lastCount+=l.length):(r[a]&&(r[a].lastCount=l.length),o[a][c]=d(l.length,l.mode)+4+e.getCharCountIndicator(l.mode,n))}}i=u}for(let e=0;e<i.length;e++)o[i[e]].end=0;return{map:o,table:r}}(o,r),a=u.find_path(i.map,"start","end"),c=[];for(let t=1;t<a.length-1;t++)c.push(i.table[a[t]].node);return t.fromArray(function(t){return t.reduce((function(t,e){const n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(c))},t.rawSplit=function(e){return t.fromArray(f(e,s.isKanjiModeEnabled()))}}(pt);const Lt=R,kt=S,Ut=F,St=z,Dt=H,Ft=J,jt=Y,zt=V,Ht=et,Jt=nt,Kt=dt,Yt=rt,Vt=pt;function Ot(t,e,n){const r=t.size,o=Kt.getEncodedBits(e,n);let i,a;for(i=0;i<15;i++)a=1==(o>>i&1),i<6?t.set(i,8,a,!0):i<8?t.set(i+1,8,a,!0):t.set(r-15+i,8,a,!0),i<8?t.set(8,r-i-1,a,!0):i<9?t.set(8,15-i-1+1,a,!0):t.set(8,15-i-1,a,!0);t.set(r-8,8,1,!0)}function qt(t,e,n){const r=new Ut;n.forEach((function(e){r.put(e.mode.bit,4),r.put(e.getLength(),Yt.getCharCountIndicator(e.mode,t)),e.write(r)}));const o=8*(Lt.getSymbolTotalCodewords(t)-zt.getTotalCodewordsCount(t,e));for(r.getLengthInBits()+4<=o&&r.put(0,4);r.getLengthInBits()%8!=0;)r.putBit(0);const i=(o-r.getLengthInBits())/8;for(let a=0;a<i;a++)r.put(a%2?17:236,8);return function(t,e,n){const r=Lt.getSymbolTotalCodewords(e),o=zt.getTotalCodewordsCount(e,n),i=r-o,a=zt.getBlocksCount(e,n),s=r%a,u=a-s,l=Math.floor(r/a),c=Math.floor(i/a),f=c+1,d=l-c,h=new Ht(d);let g=0;const p=new Array(a),m=new Array(a);let v=0;const y=new Uint8Array(t.buffer);for(let _=0;_<a;_++){const t=_<u?c:f;p[_]=y.slice(g,g+t),m[_]=h.encode(p[_]),g+=t,v=Math.max(v,t)}const w=new Uint8Array(r);let x,E,b=0;for(x=0;x<v;x++)for(E=0;E<a;E++)x<p[E].length&&(w[b++]=p[E][x]);for(x=0;x<d;x++)for(E=0;E<a;E++)w[b++]=m[E][x];return w}(r,t,e)}function Qt(t,e,n,r){let o;if(Array.isArray(t))o=Vt.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");{let r=e;if(!r){const e=Vt.rawSplit(t);r=Jt.getBestVersionForData(e,n)}o=Vt.fromString(t,r||40)}}const i=Jt.getBestVersionForData(o,n);if(!i)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<i)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+i+".\n")}else e=i;const a=qt(e,n,o),s=Lt.getSymbolSize(e),u=new St(s);return function(t,e){const n=t.size,r=Ft.getPositions(e);for(let o=0;o<r.length;o++){const e=r[o][0],i=r[o][1];for(let r=-1;r<=7;r++)if(!(e+r<=-1||n<=e+r))for(let o=-1;o<=7;o++)i+o<=-1||n<=i+o||(r>=0&&r<=6&&(0===o||6===o)||o>=0&&o<=6&&(0===r||6===r)||r>=2&&r<=4&&o>=2&&o<=4?t.set(e+r,i+o,!0,!0):t.set(e+r,i+o,!1,!0))}}(u,e),function(t){const e=t.size;for(let n=8;n<e-8;n++){const e=n%2==0;t.set(n,6,e,!0),t.set(6,n,e,!0)}}(u),function(t,e){const n=Dt.getPositions(e);for(let r=0;r<n.length;r++){const e=n[r][0],o=n[r][1];for(let n=-2;n<=2;n++)for(let r=-2;r<=2;r++)-2===n||2===n||-2===r||2===r||0===n&&0===r?t.set(e+n,o+r,!0,!0):t.set(e+n,o+r,!1,!0)}}(u,e),Ot(u,n,0),e>=7&&function(t,e){const n=t.size,r=Jt.getEncodedBits(e);let o,i,a;for(let s=0;s<18;s++)o=Math.floor(s/3),i=s%3+n-8-3,a=1==(r>>s&1),t.set(o,i,a,!0),t.set(i,o,a,!0)}(u,e),function(t,e){const n=t.size;let r=-1,o=n-1,i=7,a=0;for(let s=n-1;s>0;s-=2)for(6===s&&s--;;){for(let n=0;n<2;n++)if(!t.isReserved(o,s-n)){let r=!1;a<e.length&&(r=1==(e[a]>>>i&1)),t.set(o,s-n,r),i--,-1===i&&(a++,i=7)}if(o+=r,o<0||n<=o){o-=r,r=-r;break}}}(u,a),isNaN(r)&&(r=jt.getBestMask(u,Ot.bind(null,u,n))),jt.applyMask(r,u),Ot(u,n,r),{modules:u,version:e,errorCorrectionLevel:n,maskPattern:r,segments:o}}P.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");let n,r,o=kt.M;return void 0!==e&&(o=kt.from(e.errorCorrectionLevel,kt.M),n=Jt.from(e.version),r=jt.from(e.maskPattern),e.toSJISFunc&&Lt.setToSJISFunction(e.toSJISFunc)),Qt(t,n,o,r)};var $t={},Xt={};!function(t){function e(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}t.getOptions=function(t){t||(t={}),t.color||(t.color={});const n=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:r,scale:r?4:o,margin:n,color:{dark:e(t.color.dark||"#000000ff"),light:e(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},t.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},t.getImageWidth=function(e,n){const r=t.getScale(e,n);return Math.floor((e+2*n.margin)*r)},t.qrToImageData=function(e,n,r){const o=n.modules.size,i=n.modules.data,a=t.getScale(o,r),s=Math.floor((o+2*r.margin)*a),u=r.margin*a,l=[r.color.light,r.color.dark];for(let t=0;t<s;t++)for(let n=0;n<s;n++){let c=4*(t*s+n),f=r.color.light;if(t>=u&&n>=u&&t<s-u&&n<s-u){f=l[i[Math.floor((t-u)/a)*o+Math.floor((n-u)/a)]?1:0]}e[c++]=f.r,e[c++]=f.g,e[c++]=f.b,e[c]=f.a}}}(Xt),function(t){const e=Xt;t.render=function(t,n,r){let o=r,i=n;void 0!==o||n&&n.getContext||(o=n,n=void 0),n||(i=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),o=e.getOptions(o);const a=e.getImageWidth(t.modules.size,o),s=i.getContext("2d"),u=s.createImageData(a,a);return e.qrToImageData(u.data,t,o),function(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}(s,i,a),s.putImageData(u,0,0),i},t.renderToDataURL=function(e,n,r){let o=r;void 0!==o||n&&n.getContext||(o=n,n=void 0),o||(o={});const i=t.render(e,n,o),a=o.type||"image/png",s=o.rendererOpts||{};return i.toDataURL(a,s.quality)}}($t);var Zt={};const Wt=Xt;function Gt(t,e){const n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function te(t,e,n){let r=t+e;return void 0!==n&&(r+=" "+n),r}Zt.render=function(t,e,n){const r=Wt.getOptions(e),o=t.modules.size,i=t.modules.data,a=o+2*r.margin,s=r.color.light.a?"<path "+Gt(r.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",u="<path "+Gt(r.color.dark,"stroke")+' d="'+function(t,e,n){let r="",o=0,i=!1,a=0;for(let s=0;s<t.length;s++){const u=Math.floor(s%e),l=Math.floor(s/e);u||i||(i=!0),t[s]?(a++,s>0&&u>0&&t[s-1]||(r+=i?te("M",u+n,.5+l+n):te("m",o,0),o=0,i=!1),u+1<e&&t[s+1]||(r+=te("h",a),a=0)):o++}return r}(i,o,r.margin)+'"/>',l='viewBox="0 0 '+a+" "+a+'"',c='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+l+' shape-rendering="crispEdges">'+s+u+"</svg>\n";return"function"==typeof n&&n(null,c),c};const ee=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},ne=P,re=$t,oe=Zt;function ie(t,e,n,r,o){const i=[].slice.call(arguments,1),a=i.length,s="function"==typeof i[a-1];if(!s&&!ee())throw new Error("Callback required as last argument");if(!s){if(a<1)throw new Error("Too few arguments provided");return 1===a?(n=e,e=r=void 0):2!==a||e.getContext||(r=n,n=e,e=void 0),new Promise((function(o,i){try{const i=ne.create(n,r);o(t(i,e,r))}catch(a){i(a)}}))}if(a<2)throw new Error("Too few arguments provided");2===a?(o=n,n=e,e=r=void 0):3===a&&(e.getContext&&void 0===o?(o=r,r=void 0):(o=r,r=n,n=e,e=void 0));try{const i=ne.create(n,r);o(null,t(i,e,r))}catch(u){o(u)}}N.create=ne.create,N.toCanvas=ie.bind(null,re.render),N.toDataURL=ie.bind(null,re.renderToDataURL),N.toString=ie.bind(null,(function(t,e,n){return oe.render(t,n)}));const ae=M(t({__name:"detail",setup(t){const M=e(!1),P=e(""),R=e({}),L=n(),k=r((()=>L.info));T().setTopTabbarParam({title:"优惠券详情"}),o((t=>{if(t.coupon_id)U(t.coupon_id),S(t.coupon_id);else{i({url:"/addon/shop/pages/coupon/list",title:"优惠券不存在"})}}));const U=t=>{M.value=!0,B(t).then((t=>{R.value=t.data,2==R.value.receive_type?R.value.btnType="collected":k.value?-1!=R.value.sum_count&&R.value.receive_count===R.value.sum_count?R.value.btnType="collected":R.value.is_receive&&R.value.limit_count===R.value.member_receive_count?R.value.btnType="using":R.value.btnType="collecting":-1!=R.value.sum_count&&R.value.receive_count===R.value.sum_count?R.value.btnType="collected":R.value.btnType="collecting",M.value=!1})).catch((()=>{M.value=!1,R.value={},setTimeout((()=>{a({url:"/addon/shop/pages/index",mode:"reLaunch"})}),600)}))},S=t=>{N.toDataURL(window.location.href,{errorCorrectionLevel:"L",margin:0,width:100}).then((t=>{P.value=t}))};return(t,e)=>{const n=w,r=x,o=E,i=b(_("loading-page"),C);return s(),u(n,{style:c(t.themeColor())},{default:l((()=>[Object.keys(R.value).length&&!M.value?(s(),u(n,{key:0,class:"overflow-hidden min-h-screen bg-style relative",style:c({background:"url("+f(A)("addon/shop/coupon/coupon_bg.png")+") no-repeat"})},{default:l((()=>[d(n,{class:"relative mt-[236rpx] w-[100%] h-[932rpx]",style:c({background:"url("+f(A)("addon/shop/coupon/coupon_bg_02.png")+") center / contain no-repeat"})},{default:l((()=>[R.value.limit_count?(s(),u(n,{key:0,style:c({background:"url("+f(A)("addon/shop/coupon/top_tab.png")+") center / cover no-repeat",transform:"translateX(-50%)"}),class:"text-[32rpx] leading-[56rpx] top-[2rpx] left-[50%] px-[30rpx] box-border justify-center absolute min-w-[196rpx] h-[56rpx] flex items-center text-[#FFF9DD]"},{default:l((()=>[h("限领"+g(R.value.limit_count)+"张",1)])),_:1},8,["style"])):p("v-if",!0),d(n,{class:"flex justify-center pt-[90rpx]"},{default:l((()=>[d(r,{class:"max-w-[380rpx] text-[var(--price-text-color)] text-[140rpx] truncate price-font"},{default:l((()=>[h(g(R.value.coupon_price||0),1)])),_:1}),d(r,{class:"flex items-center justify-center text-[44rpx] mt-[54rpx] ml-[8rpx] text-[#F7D894] bg-[var(--price-text-color)] rounded-full w-[70rpx] h-[70rpx]"},{default:l((()=>[h("元")])),_:1})])),_:1}),d(n,{class:"h-[64rpx] leading-[64rpx] text-[42rpx] text-[#E22D17] mt-[10rpx] text-center"},{default:l((()=>["0.00"===R.value.min_condition_money?(s(),u(r,{key:0},{default:l((()=>[h("无门槛")])),_:1})):(s(),u(r,{key:1},{default:l((()=>[h("满"+g(R.value.coupon_min_price)+"元可用",1)])),_:1}))])),_:1}),d(n,{class:"text-[26rpx] h-[36rpx] text-[#E22D17] mt-[44rpx] text-center flex justify-center items-center"},{default:l((()=>[1==R.value.valid_type?(s(),m(v,{key:0},[d(r,null,{default:l((()=>[h("领取之日起")])),_:1}),d(r,null,{default:l((()=>[h(g(R.value.length),1)])),_:1}),d(r,null,{default:l((()=>[h("天内有效")])),_:1})],64)):(s(),m(v,{key:1},[d(r,null,{default:l((()=>[h("有效期至")])),_:1}),d(r,null,{default:l((()=>[h(g(R.value.valid_end_time?R.value.valid_end_time.slice(0,10):""),1)])),_:1})],64))])),_:1}),d(n,{class:"flex justify-center items-center mt-[20rpx]"},{default:l((()=>["collected"===R.value.btnType?(s(),u(r,{key:0,class:"!leading-[100rpx] text-center text-[rgba(255,255,255,1)] text-[46rpx] min-w-[240rpx] h-[106rpx]",style:c({background:"url("+f(A)("addon/shop/coupon/coupon_btn_02.png")+")  center / contain no-repeat"})},{default:l((()=>[h("已领完")])),_:1},8,["style"])):p("v-if",!0),"collecting"===R.value.btnType?(s(),u(r,{key:1,class:"!leading-[100rpx] text-center text-[#E22D17] text-[46rpx] min-w-[240rpx] h-[106rpx]",style:c({background:"url("+f(A)("addon/shop/coupon/coupon_btn.png")+") center / contain no-repeat"}),onClick:e[0]||(e[0]=t=>(t=>{if(!k.value)return y().setLoginBack({url:"/addon/shop/pages/coupon/detail",param:{coupon_id:R.value.id}}),!1;I({coupon_id:t,number:1}).then((t=>{R.value.btnType="using"}))})(R.value.id))},{default:l((()=>[h("领取")])),_:1},8,["style"])):p("v-if",!0),"using"===R.value.btnType?(s(),u(r,{key:2,class:"!leading-[100rpx] text-center text-[#E22D17] text-[46rpx] min-w-[240rpx] h-[106rpx]",style:c({background:"url("+f(A)("addon/shop/coupon/coupon_btn.png")+") center / contain no-repeat"}),onClick:e[1]||(e[1]=t=>{return e=R.value.id,void a({url:"/addon/shop/pages/goods/list",param:{coupon_id:e}});var e})},{default:l((()=>[h("去使用")])),_:1},8,["style"])):p("v-if",!0)])),_:1}),d(n,{class:"w-[230rpx] h-[230rpx] box-border p-[15rpx] bg-[#fff] mx-[auto] mt-[50rpx]"},{default:l((()=>[d(o,{class:"w-[200rpx] h-[200rpx]",src:P.value,mode:"aspectFill"},null,8,["src"])])),_:1}),d(n,{class:"text-[24rpx] text-[rgba(255,255,255,0.7)] mt-[30rpx] text-center"},{default:l((()=>[h("注:扫描二维码或点击右上角进行分享")])),_:1})])),_:1},8,["style"])])),_:1},8,["style"])):p("v-if",!0),d(i,{loading:M.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-31ed8dfa"]]);export{ae as default};

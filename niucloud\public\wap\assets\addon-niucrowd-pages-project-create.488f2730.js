import{N as e,r as a,s as t,Q as l,bE as o,b8 as s,X as n,bL as c,y as r,o as i,c as u,w as d,k as m,g as _,b as f,A as g,R as p,T as y,B as v,S as b,a3 as k,C as h,b9 as x,I as w,ag as j,ay as V,H as T,bs as A,bt as C,ap as U}from"./index-4b8dc7db.js";import{a as I}from"./category.146302da.js";import{k as q}from"./project.df03875d.js";import{g as P}from"./member.90cbfaed.js";import{I as F,g as S}from"./image-upload.e0ff4950.js";import{_ as L}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u--image.892273b2.js";import"./u-image.18977c35.js";import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-upload.899e5f6a.js";import"./u-loading-icon.11ef83b8.js";import"./common.eabc72c7.js";const M=L({__name:"create",setup(L){const M=e({project_name:"",category_id:"",cover_image:"",description:"",target_amount:"",duration_days:30,rewards:[{amount:"",limit_count:"",content:""}],agreeAgreement:!1}),H=a([]),$=a(null),z=a(!1),B=a(!1),G=a(!0),N=a(!1),Q=a(!1),E=a({title:"",content:""});t((async()=>{await J()})),l((()=>{K()}));const J=async()=>{try{G.value=!0;const e=uni.getStorageSync("wapToken"),a=uni.getStorageSync("wap_member_info");if(!e||!a)return void o({title:"登录提示",content:"发布项目需要先登录，是否立即前往登录？",confirmText:"去登录",cancelText:"返回",success:e=>{e.confirm?s({url:"/pages/member/login"}):n()}});const t=await P();if(console.log("认证状态检查结果:",t),1!==t.code)return void o({title:"检查失败",content:"无法获取认证状态，请检查网络连接后重试",confirmText:"重试",cancelText:"返回",success:e=>{e.confirm?J():n()}});{const e=t.data,a=e.status;if(-1===a){if(!e.can_publish)return void o({title:"身份认证提示",content:"发布项目需要先完成身份认证，是否立即前往认证？",confirmText:"去认证",cancelText:"返回",success:e=>{e.confirm?c({url:"/addon/niucrowd/pages/member/auth-router"}):n()}});console.log("系统配置不需要认证，允许发布项目")}else{if(0===a)return void o({title:"认证审核中",content:"您的身份认证正在审核中，请耐心等待审核结果。审核通过后即可发布项目。",confirmText:"查看状态",cancelText:"返回",success:e=>{e.confirm?c({url:"/addon/niucrowd/pages/member/auth-status"}):n()}});if(2===a)return void o({title:"认证未通过",content:"您的身份认证未通过审核，请重新提交认证资料后再发布项目。",confirmText:"重新认证",cancelText:"返回",success:e=>{e.confirm?c({url:"/addon/niucrowd/pages/member/auth-router"}):n()}});if(1!==a)return void o({title:"状态异常",content:`认证状态异常(${a})，请重新登录或联系客服`,confirmText:"确定",success:()=>{n()}});if(!e.can_publish)return void o({title:"发布权限不足",content:"当前系统配置不允许发布项目，请联系管理员",confirmText:"确定",success:()=>{n()}});console.log("身份认证检查通过，允许发布项目")}}}catch(e){return console.error("检查认证状态失败:",e),void o({title:"检查失败",content:"检查认证状态时发生错误，请重试",confirmText:"重试",cancelText:"返回",success:e=>{e.confirm?J():n()}})}finally{G.value=!1}},K=async()=>{try{const e=await I();if(console.log("分类API响应:",e),e&&e.data&&1===e.code){const a=Array.isArray(e.data)?e.data:e.data.data||[];a.length>0?(H.value=a.map((e=>({category_id:e.category_id,category_name:e.category_name}))),console.log("成功加载分类:",H.value)):(console.warn("API返回空分类列表，使用默认分类"),O())}else console.warn("API响应格式错误，使用默认分类"),O()}catch(e){console.error("加载分类失败:",e),O(),r({title:"加载分类失败，使用默认分类",icon:"none"})}},O=()=>{H.value=[{category_id:1,category_name:"科技产品"},{category_id:2,category_name:"创意设计"},{category_id:3,category_name:"生活用品"},{category_id:4,category_name:"文化艺术"},{category_id:5,category_name:"公益慈善"}],console.log("使用默认分类:",H.value)},R=e=>{let a=e.detail.value;a=a.replace(/[^\d.]/g,"");const t=a.split(".");t[0].length>8&&(t[0]=t[0].substring(0,8)),t.length>1&&(t[1]=t[1].substring(0,2),a=t.join("."));parseFloat(a)>99999999&&(a="99999999",r({title:"目标金额不能超过99,999,999元",icon:"none"})),M.target_amount=a},X=e=>{const a=e.detail.value[0];H.value&&H.value.length>a?($.value=H.value[a],M.category_id=$.value.category_id,z.value=!1):(console.error("分类选择错误：索引超出范围或分类列表为空"),r({title:"分类选择失败",icon:"none"}))},D=()=>{M.rewards.push({amount:"",limit_count:"",content:""})},W=async()=>{if(M.project_name)if(M.category_id)if(M.cover_image)if(M.description)if(!M.target_amount||parseFloat(M.target_amount)<=0)r({title:"请输入有效的目标金额",icon:"none"});else if(parseFloat(M.target_amount)>99999999)r({title:"目标金额不能超过99,999,999元",icon:"none"});else if(!M.duration_days||parseInt(M.duration_days)<=0)r({title:"请输入有效的筹款天数",icon:"none"});else{for(let e=0;e<M.rewards.length;e++){const a=M.rewards[e];if(!a.amount||!a.content)return void r({title:`请完善回报${e+1}的信息`,icon:"none"})}if(M.agreeAgreement){B.value=!0;try{const e={project_name:M.project_name,category_id:M.category_id,project_desc:M.description,project_content:M.description,cover_image:M.cover_image,target_amount:parseFloat(M.target_amount),duration_days:parseInt(M.duration_days),agreeAgreement:M.agreeAgreement,rewards:M.rewards.map((e=>({reward_name:`回报档位 ¥${e.amount}`,reward_desc:e.content,content:e.content,amount:parseFloat(e.amount),stock:e.limit_count?parseInt(e.limit_count):0,delivery_time:"项目成功后30天内发货"})))};await q(e);o({title:"项目提交成功",content:"您的项目已提交，正在等待审核。请选择下一步操作：",confirmText:"查看我的项目",cancelText:"返回首页",success:e=>{e.confirm?s({url:"/addon/niucrowd/pages/member/projects"}):x({url:"/pages/index"})}})}catch(e){console.error("提交项目失败:",e),r({title:e.message||"提交失败，请重试",icon:"none"})}finally{B.value=!1}}else r({title:"请先阅读并同意项目发布协议",icon:"none"})}else r({title:"请输入项目描述",icon:"none"});else r({title:"请上传项目封面",icon:"none"});else r({title:"请选择项目分类",icon:"none"});else r({title:"请输入项目名称",icon:"none"})},Y=()=>{M.agreeAgreement=!M.agreeAgreement},Z=async()=>{N.value=!0,await ee()},ee=async()=>{try{Q.value=!0;const e=await S("project_publish");1===e.code&&e.data?E.value=e.data:E.value={title:"项目发布协议",content:"暂无协议内容，请联系管理员设置。"}}catch(e){console.error("加载协议失败:",e),E.value={title:"项目发布协议",content:"加载协议内容失败，请稍后重试。"}}finally{Q.value=!1}},ae=()=>{M.agreeAgreement=!0,N.value=!1};return(e,a)=>{const t=m,l=w,o=j,s=V,n=T,c=A,r=C,x=U;return i(),u(t,{class:"create-page"},{default:d((()=>[_(" 认证检查加载状态 "),G.value?(i(),u(t,{key:0,class:"auth-checking"},{default:d((()=>[f(t,{class:"loading-container"},{default:d((()=>[f(t,{class:"loading-icon"},{default:d((()=>[g("🔄")])),_:1}),f(l,{class:"loading-text"},{default:d((()=>[g("正在验证身份认证状态...")])),_:1})])),_:1})])),_:1})):(i(),p(b,{key:1},[_(" 项目创建表单 "),f(t,{class:"form-container"},{default:d((()=>[f(t,{class:"form-section"},{default:d((()=>[f(t,{class:"section-title"},{default:d((()=>[g("基本信息")])),_:1}),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("项目名称 "),f(l,{class:"required"},{default:d((()=>[g("*")])),_:1})])),_:1}),f(o,{modelValue:M.project_name,"onUpdate:modelValue":a[0]||(a[0]=e=>M.project_name=e),placeholder:"请输入项目名称",class:"input",maxlength:"50"},null,8,["modelValue"])])),_:1}),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("项目分类 "),f(l,{class:"required"},{default:d((()=>[g("*")])),_:1})])),_:1}),f(t,{class:"select-box",onClick:a[1]||(a[1]=e=>z.value=!0)},{default:d((()=>[f(l,{class:y(["select-text",{placeholder:!$.value}])},{default:d((()=>[g(v($.value?$.value.category_name:"请选择项目分类"),1)])),_:1},8,["class"]),f(l,{class:"iconfont iconxiangyoujiantou"})])),_:1})])),_:1}),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("项目封面 "),f(l,{class:"required"},{default:d((()=>[g("*")])),_:1})])),_:1}),f(t,{class:"upload-container"},{default:d((()=>[f(F,{modelValue:M.cover_image,"onUpdate:modelValue":a[2]||(a[2]=e=>M.cover_image=e),"max-count":1,"size-limit":5,"show-tips":!0},null,8,["modelValue"]),f(t,{class:"upload-tip"},{default:d((()=>[g("请上传清晰的项目封面图片，支持JPG、PNG格式，大小不超过5MB")])),_:1})])),_:1})])),_:1}),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("项目描述 "),f(l,{class:"required"},{default:d((()=>[g("*")])),_:1})])),_:1}),f(s,{modelValue:M.description,"onUpdate:modelValue":a[3]||(a[3]=e=>M.description=e),placeholder:"请详细描述您的项目...",class:"textarea",maxlength:"500"},null,8,["modelValue"])])),_:1})])),_:1}),f(t,{class:"form-section"},{default:d((()=>[f(t,{class:"section-title"},{default:d((()=>[g("筹款信息")])),_:1}),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("目标金额 "),f(l,{class:"required"},{default:d((()=>[g("*")])),_:1})])),_:1}),f(t,{class:"input-group"},{default:d((()=>[f(l,{class:"currency"},{default:d((()=>[g("¥")])),_:1}),f(o,{modelValue:M.target_amount,"onUpdate:modelValue":a[4]||(a[4]=e=>M.target_amount=e),placeholder:"0.00",class:"input amount-input",type:"digit",onInput:R},null,8,["modelValue"])])),_:1}),f(t,{class:"form-tip"},{default:d((()=>[g("最高支持99,999,999元")])),_:1})])),_:1}),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("筹款天数 "),f(l,{class:"required"},{default:d((()=>[g("*")])),_:1})])),_:1}),f(t,{class:"input-group"},{default:d((()=>[f(o,{modelValue:M.duration_days,"onUpdate:modelValue":a[5]||(a[5]=e=>M.duration_days=e),placeholder:"30",class:"input",type:"number"},null,8,["modelValue"]),f(l,{class:"unit"},{default:d((()=>[g("天")])),_:1})])),_:1})])),_:1})])),_:1}),f(t,{class:"form-section"},{default:d((()=>[f(t,{class:"section-title"},{default:d((()=>[g("回报设置")])),_:1}),f(t,{class:"section-desc"},{default:d((()=>[g("为支持者设置不同金额的回报")])),_:1}),f(t,{class:"reward-list"},{default:d((()=>[(i(!0),p(b,null,k(M.rewards,((e,a)=>(i(),u(t,{key:a,class:"reward-item"},{default:d((()=>[f(t,{class:"reward-header"},{default:d((()=>[f(l,{class:"reward-title"},{default:d((()=>[g("回报 "+v(a+1),1)])),_:2},1024),M.rewards.length>1?(i(),u(l,{key:0,class:"remove-btn",onClick:e=>(e=>{M.rewards.splice(e,1)})(a)},{default:d((()=>[g("删除")])),_:2},1032,["onClick"])):_("v-if",!0)])),_:2},1024),f(t,{class:"reward-form"},{default:d((()=>[f(t,{class:"form-row"},{default:d((()=>[f(t,{class:"form-col"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("支持金额")])),_:1}),f(t,{class:"input-group"},{default:d((()=>[f(l,{class:"currency"},{default:d((()=>[g("¥")])),_:1}),f(o,{modelValue:e.amount,"onUpdate:modelValue":a=>e.amount=a,placeholder:"0.00",class:"input",type:"digit"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024),f(t,{class:"form-col"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("限制数量")])),_:1}),f(o,{modelValue:e.limit_count,"onUpdate:modelValue":a=>e.limit_count=a,placeholder:"不限制",class:"input",type:"number"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024),f(t,{class:"form-item"},{default:d((()=>[f(t,{class:"label"},{default:d((()=>[g("回报内容")])),_:1}),f(s,{modelValue:e.content,"onUpdate:modelValue":a=>e.content=a,placeholder:"请描述支持者将获得的回报...",class:"textarea small",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),f(t,{class:"add-reward-btn",onClick:D},{default:d((()=>[f(l,{class:"iconfont icontianjia"}),f(l,null,{default:d((()=>[g("添加回报")])),_:1})])),_:1})])),_:1}),_(" 协议同意 "),f(t,{class:"form-section"},{default:d((()=>[f(t,{class:"agreement-section"},{default:d((()=>[f(t,{class:"agreement-checkbox",onClick:Y},{default:d((()=>[f(t,{class:y(["checkbox",{checked:M.agreeAgreement}])},{default:d((()=>[M.agreeAgreement?(i(),u(l,{key:0,class:"iconfont iconduihao"})):_("v-if",!0)])),_:1},8,["class"]),f(l,{class:"agreement-text"},{default:d((()=>[g(" 我已阅读并同意 "),f(l,{class:"agreement-link",onClick:h(Z,["stop"])},{default:d((()=>[g("《项目发布协议》")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})],2112)),f(t,{class:"bottom-bar"},{default:d((()=>[f(n,{class:"submit-btn",onClick:W,disabled:B.value},{default:d((()=>[g(v(B.value?"提交中...":"提交项目"),1)])),_:1},8,["disabled"])])),_:1}),_(" 分类选择器 "),z.value?(i(),u(r,{key:2,class:"category-picker",onChange:X},{default:d((()=>[f(c,null,{default:d((()=>[(i(!0),p(b,null,k(H.value,(e=>(i(),u(t,{key:e.category_id,class:"picker-item"},{default:d((()=>[g(v(e.category_name),1)])),_:2},1024)))),128))])),_:1})])),_:1})):_("v-if",!0),z.value?(i(),u(t,{key:3,class:"picker-mask",onClick:a[6]||(a[6]=e=>z.value=!1)})):_("v-if",!0),_(" 协议弹窗 "),N.value?(i(),u(t,{key:4,class:"agreement-modal"},{default:d((()=>[f(t,{class:"agreement-modal-content"},{default:d((()=>[f(t,{class:"agreement-header"},{default:d((()=>[f(l,{class:"agreement-title"},{default:d((()=>[g("项目发布协议")])),_:1}),f(l,{class:"close-btn",onClick:a[7]||(a[7]=e=>N.value=!1)},{default:d((()=>[g("✕")])),_:1})])),_:1}),f(x,{class:"agreement-content","scroll-y":"true"},{default:d((()=>[Q.value?(i(),u(t,{key:0,class:"loading-text"},{default:d((()=>[g("加载中...")])),_:1})):E.value.content?(i(),u(t,{key:1,class:"agreement-text",innerHTML:E.value.content},null,8,["innerHTML"])):(i(),u(t,{key:2,class:"no-agreement"},{default:d((()=>[g("暂无协议内容")])),_:1}))])),_:1}),f(t,{class:"agreement-footer"},{default:d((()=>[f(n,{class:"agree-btn",onClick:ae},{default:d((()=>[g("同意协议")])),_:1}),f(n,{class:"cancel-btn",onClick:a[8]||(a[8]=e=>N.value=!1)},{default:d((()=>[g("取消")])),_:1})])),_:1})])),_:1})])),_:1})):_("v-if",!0),N.value?(i(),u(t,{key:5,class:"modal-mask",onClick:a[9]||(a[9]=e=>N.value=!1)})):_("v-if",!0)])),_:1})}}},[["__scopeId","data-v-d36bed62"]]);export{M as default};

import{r as e,Q as a,o as s,c as r,w as t,bL as o,k as l,b as u,A as c,g as n,B as d,I as i,H as g}from"./index-dd56d0cc.js";import{_ as m}from"./_plugin-vue_export-helper.1b428a4d.js";const f=m({__name:"auth-router",setup(m){const f=e(!0),_=e(""),p=async()=>{f.value=!0,_.value="";try{const e=await v();h(e)}catch(e){console.error("检查认证状态失败:",e),_.value="检查认证状态失败，请重试",f.value=!1}},v=()=>new Promise((e=>{setTimeout((()=>{e({status:-1,message:b(-1)})}),1e3)})),b=e=>{switch(e){case-1:return"未认证";case 0:return"认证审核中";case 1:return"认证已通过";case 2:return"认证被拒绝";default:return"未知状态"}},h=e=>{const{status:a}=e;switch(console.log("认证状态:",a,e.message),a){case-1:console.log("跳转到认证页面"),o({url:"/addon/niucrowd/pages/member/auth"});break;case 0:console.log("跳转到认证状态查看页面（审核中）"),o({url:"/addon/niucrowd/pages/member/auth-status"});break;case 1:console.log("跳转到认证详情页面（已通过）"),o({url:"/addon/niucrowd/pages/member/auth-status"});break;case 2:console.log("跳转到重新认证页面（被拒绝）"),o({url:"/addon/niucrowd/pages/member/auth"});break;default:_.value=`未知的认证状态: ${a}`,f.value=!1}};return a((()=>{p()})),console.log("身份认证路由页面加载完成"),(e,a)=>{const o=l,m=i,v=g;return s(),r(o,{class:"auth-router-page"},{default:t((()=>[f.value?(s(),r(o,{key:0,class:"loading-container"},{default:t((()=>[u(o,{class:"loading-icon"},{default:t((()=>[c("🔄")])),_:1}),u(m,{class:"loading-text"},{default:t((()=>[c("正在检查认证状态...")])),_:1})])),_:1})):n("v-if",!0),_.value?(s(),r(o,{key:1,class:"error-container"},{default:t((()=>[u(o,{class:"error-icon"},{default:t((()=>[c("❌")])),_:1}),u(m,{class:"error-text"},{default:t((()=>[c(d(_.value),1)])),_:1}),u(v,{class:"retry-btn",onClick:p},{default:t((()=>[c("重试")])),_:1})])),_:1})):n("v-if",!0)])),_:1})}}},[["__scopeId","data-v-593e6c33"]]);export{f as default};

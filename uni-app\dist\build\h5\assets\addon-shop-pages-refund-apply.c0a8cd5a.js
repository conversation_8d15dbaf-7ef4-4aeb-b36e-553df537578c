import{d as e,r as a,s as t,ab as l,M as r,l as o,o as s,c as d,w as u,b as i,e as n,A as p,B as c,g as x,C as m,R as _,a3 as v,S as f,n as g,E as y,ax as h,y as b,a as w,G as j,i as k,j as C,k as V,I,ap as N,aH as S,ay as A,H as D,aI as E}from"./index-4b8dc7db.js";import{_ as F}from"./u--image.892273b2.js";import{_ as P}from"./u-upload.899e5f6a.js";import{_ as z,a as B}from"./u-radio-group.b3c6fbb7.js";import{_ as H}from"./u-popup.e2790691.js";import{g as L}from"./order.b56aabaa.js";import{g as M,a as R,b as T}from"./refund.d5f80cbc.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.18977c35.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.11ef83b8.js";import"./u-safe-bottom.987908cd.js";const q=U(e({__name:"apply",setup(e){var U;const q=a(null),G=a({}),O=a(0),Q=a(0),W=a(!1),J=a({order_id:null==(U=q.value)?void 0:U.order_id,order_goods_id:O.value,refund_type:"",apply_money:"",reason:"",remark:"",voucher:[]}),K=a({}),X=a([]),Y=a("");M().then((({data:e})=>{X.value=e,X.value&&X.value.length&&(Y.value=X.value[0])})),t((e=>{if(O.value=e.order_goods_id||0,J.value.order_goods_id=O.value,J.value.order_id=e.order_id||0,e.order_id&&e.order_goods_id)L(e.order_id).then((({data:e})=>{q.value=e,q.value.order_goods.forEach(((e,a)=>{O.value==e.order_goods_id&&(G.value=e)})),J.value.apply_money=l(K.value.refund_money)})),R({order_goods_id:e.order_goods_id}).then((e=>{K.value=e.data}));else{r({url:"/addon/shop/pages/order/list",title:"缺少订单id"})}})),o((e=>function(e){return""==e||0==e?"70rpx":17*String(e).length+"rpx"}));const Z=e=>{J.value.refund_type=e,Q.value=1},$=o((()=>J.value.voucher.map((e=>({url:y(e)}))))),ee=e=>{e.file.forEach((e=>{h({filePath:e.url,name:"file"}).then((e=>{J.value.voucher.length<9&&J.value.voucher.push(e.data.url)}))}))},ae=e=>{J.value.voucher.splice(e.index,1)},te=a(!1),le=()=>J.value.reason?Number(J.value.apply_money).toFixed(2)<0?(b({title:"退款金额不能为0,保留两位小数",icon:"none"}),!1):Number(J.value.apply_money)>Number(K.value.refund_money)?(b({title:"退款金额不能大于可退款总额",icon:"none"}),!1):void(te.value||(te.value=!0,T(J.value).then((e=>{te.value=!1,setTimeout((()=>{w({url:"/addon/shop/pages/order/detail",param:{order_id:J.value.order_id}})}),1e3)})).catch((()=>{te.value=!1})))):(b({title:"请选择退款原因",icon:"none"}),!1),re=()=>{J.value.reason=Y.value,W.value=!1};return(e,a)=>{const t=j,l=k(C("u--image"),F),r=V,o=I,h=N,b=S,w=k(C("u-upload"),P),L=A,M=D,R=k(C("u-radio"),z),T=k(C("u-radio-group"),B),U=k(C("u-popup"),H),O=E;return s(),d(r,{style:g(e.themeColor())},{default:u((()=>[q.value?(s(),d(O,{key:0,"indicator-dots":!1,autoplay:!1,"disable-touch":!0,current:Q.value,class:"h-screen",duration:300},{default:u((()=>[i(b,null,{default:u((()=>[i(h,{"scroll-y":"true",class:"bg-page min-h-screen overflow-hidden"},{default:u((()=>[i(r,{class:"m-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] rounded-[var(--rounded-big)] bg-white"},{default:u((()=>[i(r,{class:"flex"},{default:u((()=>[i(r,{class:"w-[120rpx] h-[120rpx] flex items-center justify-center"},{default:u((()=>[i(l,{radius:"var(--goods-rounded-small)",width:"120rpx",height:"120rpx",src:n(y)(G.value.sku_image.split(",")[0]),model:"aspectFill"},{error:u((()=>[i(t,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-small)] overflow-hidden",src:n(y)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["radius","src"])])),_:1}),i(r,{class:"flex-1 w-0 ml-[20rpx]"},{default:u((()=>[i(r,{class:"text-ellipsis text-[28rpx] leading-normal truncate"},{default:u((()=>[p(c(G.value.goods_name),1)])),_:1}),G.value.sku_name?(s(),d(r,{key:0,class:"mt-[6rpx] text-[24rpx] leading-[1.3] text-[var(--text-color-light9)] truncate"},{default:u((()=>[p(c(G.value.sku_name),1)])),_:1})):x("v-if",!0)])),_:1})])),_:1})])),_:1}),i(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:u((()=>[i(r,{class:"py-[var(--pad-top-m)] flex items-center",onClick:a[0]||(a[0]=e=>Z(1))},{default:u((()=>[i(r,{class:"flex-1"},{default:u((()=>[i(r,{class:"text-[30rpx]"},{default:u((()=>[p("仅退款")])),_:1}),"real"==G.value.goods_type?(s(),d(r,{key:0,class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:u((()=>[p("未收到货，或与商家协商一致不用退货只退款")])),_:1})):"virtual"==G.value.goods_type?(s(),d(r,{key:1,class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:u((()=>[p("与商家协商一致不用退货只退款")])),_:1})):x("v-if",!0)])),_:1}),i(o,{class:"nc-iconfont nc-icon-youV6xx text-[28rpx] text-[var(--text-color-light9)]"})])),_:1}),"real"!=G.value.goods_type||G.value.delivery_status&&"wait_delivery"==G.value.delivery_status?x("v-if",!0):(s(),d(r,{key:0,class:"py-[var(--pad-top-m)] flex items-center border-0 !border-t !border-[#f5f5f5] border-solid",onClick:a[1]||(a[1]=e=>Z(2))},{default:u((()=>[i(r,{class:"flex-1"},{default:u((()=>[i(r,{class:"text-[30rpx]"},{default:u((()=>[p("退货退款")])),_:1}),i(r,{class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:u((()=>[p("已收到货，需退还收到的货物")])),_:1})])),_:1}),i(o,{class:"nc-iconfont nc-icon-youV6xx text-[28rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})])),_:1})])),_:1}),i(b,null,{default:u((()=>[i(h,{"scroll-y":"true",class:"bg-page min-h-screen overflow-hidden"},{default:u((()=>[i(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:u((()=>[i(r,{class:"py-[var(--pad-top-m)] flex justify-between items-center"},{default:u((()=>[i(r,{class:"text-[28rpx]"},{default:u((()=>[p("退款原因")])),_:1}),i(r,{class:"flex ml-[auto] items-center h-[30rpx]",onClick:a[2]||(a[2]=e=>W.value=!0)},{default:u((()=>[i(o,{class:"text-[26rpx] text-[var(--text-color-light9)] truncate max-w-[460rpx]"},{default:u((()=>[p(c(J.value.reason||"请选择"),1)])),_:1}),i(o,{class:"nc-iconfont nc-icon-youV6xx pt-[4rpx] text-[24rpx] text-[var(--text-color-light9)]"})])),_:1})])),_:1})])),_:1}),i(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:u((()=>[i(r,{class:"py-[var(--pad-top-m)]"},{default:u((()=>[i(r,{class:"flex items-center justify-between"},{default:u((()=>[i(r,{class:"text-[28rpx] font-500"},{default:u((()=>[p("退款金额")])),_:1}),i(r,{class:"flex justify-end items-center text-[var(--price-text-color)] price-font"},{default:u((()=>[i(o,{class:"font-500 text-[36rpx] leading-none"},{default:u((()=>[p("￥")])),_:1}),x(' <input type="digit" v-model.number="formData.apply_money" class="font-500 text-[36rpx] leading-none" :style="{ width: inputWidth(formData.apply_money) }" @blur="handleInput"> '),i(o,{class:"font-500 text-[36rpx] leading-none"},{default:u((()=>[p(c(J.value.apply_money),1)])),_:1})])),_:1})])),_:1}),i(r,{class:"text-right text-[24rpx] text-[var(--text-color-light9)] mt-[10rpx]"},{default:u((()=>[x(" <text>最多可退￥{{ refundMoney.refund_money }}</text> "),1===K.value.is_refund_delivery&&Number(K.value.refund_delivery_money)>0?(s(),d(o,{key:0,class:"ml-[10rpx]"},{default:u((()=>[p("(包含运费￥"+c(K.value.refund_delivery_money)+")",1)])),_:1})):x("v-if",!0)])),_:1})])),_:1})])),_:1}),i(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:u((()=>[i(r,{class:"pt-[var(--pad-top-m)] pb-[14rpx]"},{default:u((()=>[i(r,{class:"text-[28rpx] flex items-center"},{default:u((()=>[i(o,{class:"font-500"},{default:u((()=>[p("上传凭证")])),_:1}),i(o,{class:"text-[24rpx] text-[var(--text-color-light9)] ml-[10rpx]"},{default:u((()=>[p("选填")])),_:1})])),_:1}),i(r,{class:"mt-[30rpx]"},{default:u((()=>[i(w,{fileList:n($),onAfterRead:ee,onDelete:ae,multiple:"",maxCount:9},null,8,["fileList"])])),_:1})])),_:1})])),_:1}),i(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:u((()=>[i(r,{class:"py-[var(--pad-top-m)]"},{default:u((()=>[i(r,{class:"text-[28rpx] flex items-center"},{default:u((()=>[i(o,{class:"font-500"},{default:u((()=>[p("补充描述")])),_:1}),i(o,{class:"text-[24rpx] text-[var(--text-color-light9)] ml-[10rpx]"},{default:u((()=>[p("选填")])),_:1})])),_:1}),i(r,{class:"mt-[30rpx] h-[200rpx]"},{default:u((()=>[i(L,{class:"leading-[1.5] h-[100%] w-[100%] text-[28rpx]",modelValue:J.value.remark,"onUpdate:modelValue":a[3]||(a[3]=e=>J.value.remark=e),cols:"30",rows:"5",placeholder:"补充描述,有助于更好的处理售后问题","placeholder-class":"text-[26rpx] text-[var(--text-color-light9)]"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(r,{class:"w-full"},{default:u((()=>[i(r,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] box-border"},{default:u((()=>[i(M,{class:"primary-btn-bg !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",loading:te.value,onClick:le},{default:u((()=>[p("提交")])),_:1},8,["loading"])])),_:1})])),_:1}),x(" 退款原因 "),i(U,{show:W.value,onClose:a[6]||(a[6]=e=>W.value=!1)},{default:u((()=>[i(r,{class:"popup-common",onTouchmove:a[5]||(a[5]=m((()=>{}),["prevent","stop"]))},{default:u((()=>[i(r,{class:"title"},{default:u((()=>[p("退款原因")])),_:1}),i(h,{"scroll-y":"true",class:"h-[450rpx] px-[30rpx] box-border"},{default:u((()=>[i(T,{modelValue:Y.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.value=e),placement:"column",iconPlacement:"right"},{default:u((()=>[(s(!0),_(f,null,v(X.value,((e,a)=>(s(),d(R,{activeColor:"var(--primary-color)",labelSize:"30rpx",labelColor:"#333",customStyle:{marginBottom:"34rpx"},key:a,label:e,name:e},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(r,{class:"btn-wrap"},{default:u((()=>[i(M,{class:"primary-btn-bg btn",onClick:re},{default:u((()=>[p("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})])),_:1})])),_:1},8,["current"])):x("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-352a32b6"]]);export{q as default};

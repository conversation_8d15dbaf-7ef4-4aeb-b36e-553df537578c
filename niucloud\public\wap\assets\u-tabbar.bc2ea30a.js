import{a4 as e,a5 as t,a6 as a,bc as r,a8 as o,aK as l,i as s,j as d,o as i,c as n,w as h,b as u,T as c,C as b,n as p,$ as f,g as m,k as _}from"./index-4b8dc7db.js";import{_ as y}from"./u-safe-bottom.987908cd.js";import{_ as v}from"./_plugin-vue_export-helper.1b428a4d.js";const g=v({name:"u-tabbar",mixins:[t,a,{props:{value:{type:[String,Number,null],default:()=>e.tabbar.value},safeAreaInsetBottom:{type:Boolean,default:()=>e.tabbar.safeAreaInsetBottom},border:{type:Boolean,default:()=>e.tabbar.border},zIndex:{type:[String,Number],default:()=>e.tabbar.zIndex},activeColor:{type:String,default:()=>e.tabbar.activeColor},inactiveColor:{type:String,default:()=>e.tabbar.inactiveColor},fixed:{type:Boolean,default:()=>e.tabbar.fixed},placeholder:{type:Boolean,default:()=>e.tabbar.placeholder}}}],data:()=>({placeholderHeight:0}),computed:{tabbarStyle(){const e={zIndex:this.zIndex};return r(e,o(this.customStyle))},updateChild(){return[this.value,this.activeColor,this.inactiveColor]},updatePlaceholder(){return[this.fixed,this.placeholder]}},watch:{updateChild(){this.updateChildren()},updatePlaceholder(){this.setPlaceholderHeight()}},created(){this.children=[]},mounted(){this.setPlaceholderHeight()},methods:{updateChildren(){this.children.length&&this.children.map((e=>e.updateFromParent()))},async setPlaceholderHeight(){this.fixed&&this.placeholder&&(await l(20),this.$uGetRect(".u-tabbar__content").then((({height:e=50})=>{this.placeholderHeight=e})))}}},[["render",function(e,t,a,r,o,l){const v=_,g=s(d("u-safe-bottom"),y);return i(),n(v,{class:"u-tabbar"},{default:h((()=>[u(v,{class:c(["u-tabbar__content",[e.border&&"u-border-top",e.fixed&&"u-tabbar--fixed"]]),ref:"u-tabbar__content",onTouchmove:b(e.noop,["stop","prevent"]),style:p([l.tabbarStyle])},{default:h((()=>[u(v,{class:"u-tabbar__content__item-wrapper"},{default:h((()=>[f(e.$slots,"default",{},void 0,!0)])),_:3}),e.safeAreaInsetBottom?(i(),n(g,{key:0})):m("v-if",!0)])),_:3},8,["onTouchmove","class","style"]),e.placeholder?(i(),n(v,{key:0,class:"u-tabbar__placeholder",style:p({height:o.placeholderHeight+"px"})},null,8,["style"])):m("v-if",!0)])),_:3})}],["__scopeId","data-v-71f6d5da"]]);export{g as _};

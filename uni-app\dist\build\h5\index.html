<!DOCTYPE html>
<html lang="zh-cn">
    <head>
      <link rel="stylesheet" href="/wap/assets/uni.dbb3df19.css">

        <meta charset="UTF-8" />
        <script>
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
                CSS.supports('top: constant(a)'))
            document.write(
                '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
                (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <title></title>
        <!--preload-links-->
        <!--app-context-->
      <script type="module" crossorigin src="/wap/assets/index-dd56d0cc.js"></script>
      <link rel="stylesheet" href="/wap/assets/index-d5a1f6e8.css">
    </head>
    <body>
        <div id="app"><!--app-html--></div>
        
    </body>
</html>

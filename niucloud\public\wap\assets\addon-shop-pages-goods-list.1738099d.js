import{d as e,r as l,s as a,Q as s,F as t,o as r,c as o,w as n,b as c,C as d,g as i,T as u,A as p,R as x,a3 as m,S as _,e as f,n as g,ah as v,ai as y,I as b,ag as h,k,i as w,j,B as F,E as C,a as T,G as E}from"./index-dd56d0cc.js";import{_ as V}from"./u-popup.457e1f1f.js";import{_ as S}from"./tabbar.0d5e534b.js";import{d as I,e as P}from"./goods.dbf7b09d.js";import{M as U}from"./mescroll-body.520ba8e3.js";import{M}from"./mescroll-empty.8630a00e.js";import{u as R}from"./useMescroll.26ccf5de.js";import{u as z}from"./useGoods.392f2eb1.js";import{_ as G}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-safe-bottom.22d4d63b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./mescroll-i18n.92d484d5.js";const N=G(e({__name:"list",setup(e){const{mescrollInit:G,downCallback:N,getMescroll:q}=R(y,v),A=z(),B=l([]),H=l([]),J=l(""),L=l(""),Q=l(null),D=l(!1),K=l(!1),O=l(""),W=l(""),X=l(""),Y=l("all"),Z=l(!0);a((async e=>{L.value=e.curr_goods_category||"",O.value=e.goods_name?decodeURIComponent(e.goods_name):"",J.value=e.coupon_id||"",await I().then((e=>{B.value.push({category_name:"全部",category_id:""}),B.value=B.value.concat(e.data)}))}));const $=e=>{D.value=!1;let l={goods_category:L.value,page:e.num,limit:e.size,keyword:O.value,coupon_id:J.value,order:"all"===Y.value?"":Y.value,sort:"price"==Y.value?W.value:X.value};P(l).then((l=>{let a=l.data.data;1===Number(e.num)&&(H.value=[]),H.value=H.value.concat(a),e.endSuccess(a.length),D.value=!0})).catch((()=>{D.value=!0,e.endErr()}))},ee=e=>{Y.value=e,"all"==e&&(X.value="",W.value=""),"price"==e&&(X.value="",W.value?W.value="asc"==W.value?"desc":"asc":W.value="asc"),"sale_num"==e&&(W.value="",X.value?X.value="asc"==X.value?"desc":"asc":X.value="asc"),"label"==e?(X.value="asc",W.value="asc",K.value=!0):(K.value=!1,H.value=[],q().resetUpScroll())},le=()=>{Z.value=!Z.value},ae=e=>{T({url:"/addon/shop/pages/goods/detail",param:{goods_id:e},mode:"navigateTo"})};return s((()=>{setTimeout((()=>{q().optUp.textNoMore=t("end")}),500)})),(e,l)=>{const a=b,s=h,t=k,v=w(j("u-popup"),V),y=E,I=w(j("tabbar"),S);return r(),o(t,{class:"bg-gray-100 min-h-[100vh]",style:g(e.themeColor())},{default:n((()=>[c(t,{class:"fixed left-0 right-0 top-0 product-warp bg-[#fff]"},{default:n((()=>[c(t,{class:"py-[14rpx] flex items-center justify-between px-[20rpx]"},{default:n((()=>[c(t,{class:"flex-1 search-input mr-[20rpx]"},{default:n((()=>[c(a,{onClick:l[0]||(l[0]=d((e=>ee("all")),["stop"])),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"}),c(s,{class:"input",maxlength:"50",type:"text",modelValue:O.value,"onUpdate:modelValue":l[1]||(l[1]=e=>O.value=e),placeholder:"请搜索您想要的商品",placeholderClass:"text-[var(--text-color-light9)] text-[24rpx]","confirm-type":"search",onConfirm:l[2]||(l[2]=e=>ee("all"))},null,8,["modelValue"]),O.value?(r(),o(a,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:l[3]||(l[3]=e=>O.value="")})):i("v-if",!0)])),_:1}),c(t,{class:u(["iconfont text-[32rpx] text-[#333] -mb-[2rpx]",Z.value?"icona-yingyongzhongxinV6xx-32":"icona-yingyongliebiaoV6xx-32"]),onClick:le},null,8,["class"])])),_:1}),c(t,{class:"flex justify-between tems-center h-[88rpx] px-[30rpx]"},{default:n((()=>[c(t,{class:"flex items-center justify-between text-[26rpx] flex-1"},{default:n((()=>[c(a,{class:u(["text-[#333]",{"text-[var(--primary-color)] font-500":"all"==Y.value}]),onClick:l[4]||(l[4]=e=>ee("all"))},{default:n((()=>[p("综合排序")])),_:1},8,["class"]),c(t,{class:u(["flex items-center text-[#333]",{"text-[var(--primary-color)] font-500":"sale_num"==Y.value}]),onClick:l[5]||(l[5]=e=>ee("sale_num"))},{default:n((()=>[c(a,{class:"mr-[4rpx]"},{default:n((()=>[p("销量")])),_:1}),"asc"==X.value?(r(),o(a,{key:0,class:u(["text-[16rpx] nc-iconfont nc-icon-a-xiangshangV6xx1",{"text-[var(--primary-color)]":"sale_num"==Y.value}])},null,8,["class"])):(r(),o(a,{key:1,class:u(["text-[16rpx] nc-iconfont nc-icon-a-xiangxiaV6xx1",{"text-[var(--primary-color)]":"sale_num"==Y.value}])},null,8,["class"]))])),_:1},8,["class"]),c(t,{class:u(["flex items-center text-[#333]",{"text-[var(--primary-color)] font-500":"price"==Y.value}]),onClick:l[6]||(l[6]=e=>ee("price"))},{default:n((()=>[c(a,{class:"mr-[4rpx]"},{default:n((()=>[p("价格")])),_:1}),"asc"==W.value?(r(),o(a,{key:0,class:u(["text-[16rpx] nc-iconfont nc-icon-a-xiangshangV6xx1",{"text-[var(--primary-color)]":"price"==Y.value}])},null,8,["class"])):(r(),o(a,{key:1,class:u(["text-[16rpx] nc-iconfont nc-icon-a-xiangxiaV6xx1",{"text-[var(--primary-color)]":"price"==Y.value}])},null,8,["class"]))])),_:1},8,["class"]),c(t,{class:u(["flex items-center",{"text-[var(--primary-color)] font-500":"label"==Y.value,"text-[#333]":"label"!=Y.value}]),onClick:l[7]||(l[7]=e=>ee("label"))},{default:n((()=>[c(a,{class:"mr-[8rpx]"},{default:n((()=>[p("筛选")])),_:1}),c(a,{class:"iconfont font-500 icona-shaixuanV6xx-34 -mb-[4rpx] !text-[26rpx]"})])),_:1},8,["class"])])),_:1})])),_:1})])),_:1}),c(v,{show:K.value,mode:"top",onClose:l[9]||(l[9]=e=>K.value=!1)},{default:n((()=>[c(t,{onTouchmove:l[8]||(l[8]=d((()=>{}),["prevent","stop"]))},{default:n((()=>[c(t,{class:"text-[28rpx] px-[30rpx] mt-[40rpx]"},{default:n((()=>[p("全部分类")])),_:1}),c(t,{class:"flex flex-wrap pl-[30rpx] pt-[30rpx]"},{default:n((()=>[(r(!0),x(_,null,m(B.value,((e,l)=>(r(),o(a,{onClick:l=>{return a=e.category_id,L.value=a,H.value=[],q().resetUpScroll(),void(K.value=!1);var a},key:e.category_id,class:u([{"label-select":L.value==e.category_id},"truncate text-[#333] px-[10rpx] border-[2rpx] border-solid border-transparent w-[184rpx] h-[56rpx] flex items-center justify-center mr-[30rpx] mb-[30rpx] box-border bg-[var(--temp-bg)] rounded-[50rpx] text-[24rpx]"])},{default:n((()=>[p(F(e.category_name),1)])),_:2},1032,["onClick","class"])))),128))])),_:1})])),_:1})])),_:1},8,["show"]),c(U,{ref_key:"mescrollRef",ref:Q,top:"176rpx",bottom:"60px",onInit:f(G),down:{use:!1},onUp:$},{default:n((()=>[H.value.length?(r(),o(t,{key:0,class:u(["sidebar-margin",Z.value?"":"biserial-goods-list"])},{default:n((()=>[Z.value?(r(!0),x(_,{key:0},m(H.value,((e,l)=>(r(),o(t,{key:l,class:u(["bg-white flex px-[20rpx] py-[24rpx] rounded-[var(--rounded-small)] overflow-hidden top-mar",{"mb-[20rpx]":l+1==H.value.length}]),onClick:l=>ae(e.goods_id)},{default:n((()=>[e.goods_cover_thumb_mid?(r(),o(y,{key:0,class:"w-[190rpx] h-[190rpx] rounded-[var(--rounded-mid)]",src:f(C)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:l=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),o(y,{key:1,class:"w-[190rpx] h-[190rpx] rounded-[var(--rounded-mid)]",src:f(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])),c(t,{class:"flex-1 flex flex-col ml-[20rpx] py-[6rpx]"},{default:n((()=>[c(t,{class:"text-[28rpx] text-[#333] leading-[40rpx] multi-hidden mb-[10rpx]"},{default:n((()=>[e.goods_brand?(r(),o(t,{key:0,class:"brand-tag",style:g(f(A).baseTagStyle(e.goods_brand))},{default:n((()=>[p(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),p(" "+F(e.goods_name),1)])),_:2},1024),c(t,{class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden mb-[8rrpx]"},{default:n((()=>[p(F(e.sub_title),1)])),_:2},1024),e.goods_label_name&&e.goods_label_name.length?(r(),o(t,{key:0,class:"flex flex-wrap"},{default:n((()=>[(r(!0),x(_,null,m(e.goods_label_name,((e,l)=>(r(),x(_,null,["icon"==e.style_type&&e.icon?(r(),o(y,{key:0,class:"img-tag",src:f(C)(e.icon),mode:"heightFix",onError:l=>f(A).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),o(t,{key:1,class:"base-tag",style:g(f(A).baseTagStyle(e))},{default:n((()=>[p(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),c(t,{class:"mt-auto flex justify-between items-baseline"},{default:n((()=>[c(t,{class:"flex items-baseline mt-[20rpx]"},{default:n((()=>[c(t,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:n((()=>[c(a,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:n((()=>[p("￥")])),_:1}),c(a,{class:"text-[40rpx] font-500"},{default:n((()=>[p(F(f(A).goodsPrice(e).toFixed(2).split(".")[0]),1)])),_:2},1024),c(a,{class:"text-[24rpx] font-500"},{default:n((()=>[p("."+F(f(A).goodsPrice(e).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),"member_price"==f(A).priceType(e)?(r(),o(y,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==f(A).priceType(e)?(r(),o(y,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==f(A).priceType(e)?(r(),o(y,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024),c(a,{class:"text-[22rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:n((()=>[p(" 已售"+F(e.sale_num)+F(e.unit),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)):(r(),x(_,{key:1},[c(t,null,{default:n((()=>[(r(!0),x(_,null,m(H.value,((e,l)=>(r(),x(_,null,[l%2==0?(r(),o(t,{key:0,class:"flex flex-col bg-[#fff] box-border rounded-[var(--rounded-mid)] overflow-hidden mt-[var(--top-m)]",onClick:l=>ae(e.goods_id)},{default:n((()=>[e.goods_cover_thumb_mid?(r(),o(y,{key:0,class:"w-[100%] h-[344rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(C)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:l=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),o(y,{key:1,class:"w-[100%] h-[344rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])),c(t,{class:"px-[20rpx] flex-1 pt-[16rpx] pb-[24rpx] flex flex-col justify-between"},{default:n((()=>[c(t,{class:"text-[#303133] leading-[40rpx] text-[28rpx] multi-hidden"},{default:n((()=>[e.goods_brand?(r(),o(t,{key:0,class:"brand-tag",style:g(f(A).baseTagStyle(e.goods_brand))},{default:n((()=>[p(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),p(" "+F(e.goods_name),1)])),_:2},1024),c(t,{class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden my-[5rpx]"},{default:n((()=>[p(F(e.sub_title),1)])),_:2},1024),e.goods_label_name&&e.goods_label_name.length?(r(),o(t,{key:0,class:"flex flex-wrap"},{default:n((()=>[(r(!0),x(_,null,m(e.goods_label_name,((e,l)=>(r(),x(_,null,["icon"==e.style_type&&e.icon?(r(),o(y,{key:0,class:"img-tag",src:f(C)(e.icon),mode:"heightFix",onError:l=>f(A).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),o(t,{key:1,class:"base-tag",style:g(f(A).baseTagStyle(e))},{default:n((()=>[p(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),c(t,{class:"flex justify-between flex-wrap items-end"},{default:n((()=>[c(t,{class:"flex items-baseline mt-[20rpx]"},{default:n((()=>[c(t,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:n((()=>[c(a,{class:"text-[24rpx] font-500"},{default:n((()=>[p("￥")])),_:1}),c(a,{class:"text-[40rpx] font-500"},{default:n((()=>[p(F(f(A).goodsPrice(e).toFixed(2).split(".")[0]),1)])),_:2},1024),c(a,{class:"text-[24rpx] font-500"},{default:n((()=>[p("."+F(f(A).goodsPrice(e).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),"member_price"==f(A).priceType(e)?(r(),o(y,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==f(A).priceType(e)?(r(),o(y,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==f(A).priceType(e)?(r(),o(y,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024),c(a,{class:"text-[22rpx] text-[var(--text-color-light9)] mt-[20rpx]"},{default:n((()=>[p("已售"+F(e.sale_num)+F(e.unit),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])):i("v-if",!0)],64)))),256))])),_:1}),c(t,null,{default:n((()=>[(r(!0),x(_,null,m(H.value,((e,l)=>(r(),x(_,null,[l%2==1?(r(),o(t,{key:0,class:"flex flex-col bg-[#fff] box-border rounded-[var(--rounded-mid)] overflow-hidden mt-[var(--top-m)]",onClick:l=>ae(e.goods_id)},{default:n((()=>[e.goods_cover_thumb_mid?(r(),o(y,{key:0,class:"w-[100%] h-[344rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(C)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:l=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),o(y,{key:1,class:"w-[100%] h-[344rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])),c(t,{class:"px-[20rpx] flex-1 pt-[16rpx] pb-[24rpx] flex flex-col justify-between"},{default:n((()=>[c(t,{class:"text-[#303133] leading-[40rpx] text-[28rpx] multi-hidden"},{default:n((()=>[e.goods_brand?(r(),o(t,{key:0,class:"brand-tag",style:g(f(A).baseTagStyle(e.goods_brand))},{default:n((()=>[p(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),p(" "+F(e.goods_name),1)])),_:2},1024),c(t,{class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden my-[5rpx]"},{default:n((()=>[p(F(e.sub_title),1)])),_:2},1024),e.goods_label_name&&e.goods_label_name.length?(r(),o(t,{key:0,class:"flex flex-wrap"},{default:n((()=>[(r(!0),x(_,null,m(e.goods_label_name,((e,l)=>(r(),x(_,null,["icon"==e.style_type&&e.icon?(r(),o(y,{key:0,class:"img-tag",src:f(C)(e.icon),mode:"heightFix",onError:l=>f(A).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),o(t,{key:1,class:"base-tag",style:g(f(A).baseTagStyle(e))},{default:n((()=>[p(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),c(t,{class:"flex justify-between flex-wrap items-baseline"},{default:n((()=>[c(t,{class:"flex items-baseline mt-[20rpx]"},{default:n((()=>[c(t,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:n((()=>[c(a,{class:"text-[24rpx] font-500"},{default:n((()=>[p("￥")])),_:1}),c(a,{class:"text-[40rpx] font-500"},{default:n((()=>[p(F(f(A).goodsPrice(e).toFixed(2).split(".")[0]),1)])),_:2},1024),c(a,{class:"text-[24rpx] font-500"},{default:n((()=>[p("."+F(f(A).goodsPrice(e).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),"member_price"==f(A).priceType(e)?(r(),o(y,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==f(A).priceType(e)?(r(),o(y,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==f(A).priceType(e)?(r(),o(y,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:f(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024),c(a,{class:"mt-[20rpx] text-[22rpx] text-[var(--text-color-light9)]"},{default:n((()=>[p("已售"+F(e.sale_num)+F(e.unit),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])):i("v-if",!0)],64)))),256))])),_:1})],64))])),_:1},8,["class"])):i("v-if",!0),!H.value.length&&D.value?(r(),o(M,{key:1,option:{tip:"暂无商品",btnText:"去逛逛"},onEmptyclick:l[10]||(l[10]=e=>f(T)({url:"/addon/shop/pages/index",mode:"reLaunch"}))})):i("v-if",!0)])),_:1},8,["onInit"]),c(I)])),_:1},8,["style"])}}}),[["__scopeId","data-v-6effefa1"]]);export{N as default};

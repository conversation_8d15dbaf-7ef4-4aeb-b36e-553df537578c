import{a4 as e,a5 as a,a6 as i,a7 as t,bc as o,a8 as r,i as s,j as d,o as n,c as l,w as h,b as g,n as u,g as m,$ as c,G as p,k as y}from"./index-4b8dc7db.js";import{_ as w}from"./u-icon.33002907.js";import{_ as f}from"./u-transition.5763ee65.js";/* empty css                                                                */import{_}from"./_plugin-vue_export-helper.1b428a4d.js";const b={props:{src:{type:String,default:()=>e.image.src},mode:{type:String,default:()=>e.image.mode},width:{type:[String,Number],default:()=>e.image.width},height:{type:[String,Number],default:()=>e.image.height},shape:{type:String,default:()=>e.image.shape},radius:{type:[String,Number],default:()=>e.image.radius},lazyLoad:{type:Boolean,default:()=>e.image.lazyLoad},showMenuByLongpress:{type:Boolean,default:()=>e.image.showMenuByLongpress},loadingIcon:{type:String,default:()=>e.image.loadingIcon},errorIcon:{type:String,default:()=>e.image.errorIcon},showLoading:{type:Boolean,default:()=>e.image.showLoading},showError:{type:Boolean,default:()=>e.image.showError},fade:{type:Boolean,default:()=>e.image.fade},webp:{type:Boolean,default:()=>e.image.webp},duration:{type:[String,Number],default:()=>e.image.duration},bgColor:{type:String,default:()=>e.image.bgColor}}};const S=_({name:"u-image",mixins:[a,i,b],data(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{},show:!1}},watch:{src:{immediate:!0,handler(e){e?(this.isError=!1,this.loading=!0):this.isError=!0}}},computed:{wrapStyle(){let e={};return e.width=t(this.width),e.height=t(this.height),e.borderRadius="circle"==this.shape?"10000px":t(this.radius),e.overflow=this.radius>0?"hidden":"visible",o(e,r(this.customStyle))}},mounted(){this.show=!0},emits:["click","error","load"],methods:{addUnit:t,onClick(){this.$emit("click")},onErrorHandler(e){this.loading=!1,this.isError=!0,this.$emit("error",e)},onLoadHandler(e){this.loading=!1,this.isError=!1,this.$emit("load",e),this.removeBgColor()},removeBgColor(){this.backgroundStyle={backgroundColor:"transparent"}}}},[["render",function(e,a,i,t,o,r){const _=p,b=s(d("u-icon"),w),S=y,k=s(d("u-transition"),f);return n(),l(k,{mode:"fade",show:o.show,duration:e.fade?1e3:0},{default:h((()=>[g(S,{class:"u-image",onClick:r.onClick,style:u([r.wrapStyle,o.backgroundStyle])},{default:h((()=>[o.isError?m("v-if",!0):(n(),l(_,{key:0,src:e.src,mode:e.mode,onError:r.onErrorHandler,onLoad:r.onLoadHandler,"show-menu-by-longpress":e.showMenuByLongpress,"lazy-load":e.lazyLoad,class:"u-image__image",style:u({borderRadius:"circle"==e.shape?"10000px":r.addUnit(e.radius),width:r.addUnit(e.width),height:r.addUnit(e.height)})},null,8,["src","mode","onError","onLoad","show-menu-by-longpress","lazy-load","style"])),e.showLoading&&o.loading?(n(),l(S,{key:1,class:"u-image__loading",style:u({borderRadius:"circle"==e.shape?"50%":r.addUnit(e.radius),backgroundColor:this.bgColor,width:r.addUnit(e.width),height:r.addUnit(e.height)})},{default:h((()=>[c(e.$slots,"loading",{},(()=>[g(b,{name:e.loadingIcon,width:e.width,height:e.height},null,8,["name","width","height"])]),!0)])),_:3},8,["style"])):m("v-if",!0),e.showError&&o.isError&&!o.loading?(n(),l(S,{key:2,class:"u-image__error",style:u({borderRadius:"circle"==e.shape?"50%":r.addUnit(e.radius),width:r.addUnit(e.width),height:r.addUnit(e.height)})},{default:h((()=>[c(e.$slots,"error",{},(()=>[g(b,{name:e.errorIcon,width:e.width,height:e.height},null,8,["name","width","height"])]),!0)])),_:3},8,["style"])):m("v-if",!0)])),_:3},8,["onClick","style"])])),_:3},8,["show","duration"])}],["__scopeId","data-v-febc2405"]]);export{S as _,b as p};

import{d as e,l as o,p as t,r as a,N as l,s as r,F as s,o as i,c as n,w as p,b as d,A as u,B as m,e as c,C as b,g,n as x,D as _,m as f,O as h,P as v,a as y,J as k,k as j,i as S,j as C,I as T,H as V}from"./index-4b8dc7db.js";import{_ as w}from"./u-input.7a7ec88f.js";import{_ as F,a as P}from"./u-form.a144799c.js";import{_ as O}from"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import{_ as q}from"./u-checkbox.598bfa18.js";import{_ as z}from"./u-checkbox-group.0c3be417.js";import{t as A}from"./topTabbar.1aa95d14.js";import{_ as B}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-line.b0d89d5b.js";import"./u-modal.775e667c.js";import"./u-loading-icon.11ef83b8.js";import"./u-popup.e2790691.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-safe-bottom.987908cd.js";const I=B(e({__name:"bind",setup(e){let B={};A().setTopTabbarParam({title:"",topStatusBar:{bgColor:"#fff",textColor:"#333"}}),o((()=>Object.keys(B).length?_(Number(B.height))+_(B.top)+_(8)+"rpx":"auto"));const I=t(),M=o((()=>I.info)),U=o((()=>f().login)),D=a(!1),E=a(!1),N=l({mobile:"",mobile_code:"",mobile_key:""}),$=a(!0);a(null),r((()=>{setTimeout((()=>{$.value=!1}),800),uni.getStorageSync("openid")&&Object.assign(N,{openid:uni.getStorageSync("openid")}),uni.getStorageSync("pid")&&Object.assign(N,{pid:uni.getStorageSync("pid")}),uni.getStorageSync("unionid")&&Object.assign(N,{unionid:uni.getStorageSync("unionid")})}));const H={mobile:[{type:"string",required:!0,message:s("mobilePlaceholder"),trigger:["blur","change"]},{validator(e,o,t){/^1[3-9]\d{9}$/.test(o)?t():t(new Error("请输入正确的手机号"))},message:s("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:!0,message:s("codePlaceholder"),trigger:["blur","change"]}},J=()=>{E.value=!E.value},L=a(null),R=()=>{L.value.validate().then((()=>{if(D.value)return;D.value=!0;(M.value?h:v)(N).then((e=>{M.value?(I.getMemberInfo(),y({url:"/app/pages/member/personal",mode:"redirectTo"})):(I.setToken(e.data.token),k().handleLoginBack())})).catch((()=>{D.value=!1}))}))};return(e,o)=>{const t=j,a=S(C("u-input"),w),l=S(C("u-form-item"),F),r=S(C("sms-code"),O),_=S(C("u-form"),P),f=S(C("u-checkbox"),q),h=S(C("u-checkbox-group"),z),v=T,k=V;return i(),n(t,{class:"w-screen h-screen flex flex-col",style:x(e.themeColor())},{default:p((()=>[d(t,{class:"mx-[60rpx]"},{default:p((()=>[d(t,{class:"pt-[140rpx] text-[50rpx] text-[#333]"},{default:p((()=>[u(m(c(s)("bindMobile")),1)])),_:1}),d(t,{class:"text-[26rpx] leading-[39rpx] text-[var(--text-color-light6)] mt-[16rpx] mb-[80rpx]"},{default:p((()=>[u(m(c(s)("bindMobileTip")),1)])),_:1}),d(_,{labelPosition:"left",model:N,errorType:"toast",rules:H,ref_key:"formRef",ref:L},{default:p((()=>[d(t,{class:"h-[90rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:p((()=>[d(l,{label:"",prop:"mobile","border-bottom":!1},{default:p((()=>[d(a,{modelValue:N.mobile,"onUpdate:modelValue":o[0]||(o[0]=e=>N.mobile=e),type:"number",maxlength:"11",border:"none",placeholder:c(s)("mobilePlaceholder"),class:"!bg-transparent",disabled:$.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),d(t,{class:"h-[90rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:p((()=>[d(l,{label:"",prop:"mobile_code","border-bottom":!1},{default:p((()=>[d(a,{modelValue:N.mobile_code,"onUpdate:modelValue":o[2]||(o[2]=e=>N.mobile_code=e),type:"number",maxlength:"4",border:"none",placeholder:c(s)("codePlaceholder"),class:"!bg-transparent",disabled:$.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)]"},{suffix:p((()=>[d(r,{mobile:N.mobile,type:"bind_mobile",modelValue:N.mobile_key,"onUpdate:modelValue":o[1]||(o[1]=e=>N.mobile_key=e)},null,8,["mobile","modelValue"])])),_:1},8,["modelValue","placeholder","disabled"])])),_:1})])),_:1})])),_:1},8,["model"]),d(t,{class:"mt-[100rpx]"},{default:p((()=>[c(U).agreement_show?(i(),n(t,{key:0,class:"flex items-center mb-[20rpx] py-[10rpx]",onClick:b(J,["stop"])},{default:p((()=>[d(h,{onChange:J},{default:p((()=>[d(f,{activeColor:"var(--primary-color)",checked:E.value,shape:"circle",size:"24rpx",customStyle:{marginTop:"4rpx"}},null,8,["checked"])])),_:1}),d(t,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:p((()=>[d(v,null,{default:p((()=>[u(m(c(s)("agreeTips")),1)])),_:1}),d(v,{onClick:o[3]||(o[3]=b((e=>c(y)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:p((()=>[u("《"+m(c(s)("privacyAgreement"))+"》",1)])),_:1}),d(v,null,{default:p((()=>[u(m(c(s)("and")),1)])),_:1}),d(v,{onClick:o[4]||(o[4]=b((e=>c(y)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:p((()=>[u("《"+m(c(s)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):g("v-if",!0),d(k,{class:"w-full h-[80rpx] !bg-[var(--primary-color)] text-[26rpx] rounded-[40rpx] leading-[80rpx] font-500 !text-[#fff]",onClick:R},{default:p((()=>[u(m(c(s)("bind")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-34b5edf0"]]);export{I as default};

import{bK as e,bE as n,W as t,b8 as o}from"./index-dd56d0cc.js";function a(){const e=uni.getStorageSync("wapToken"),n=uni.getStorageSync("wap_member_info");return!(!e||!n)}function s(){const n=uni.getStorageSync("wapToken"),t=uni.getStorageSync("wap_member_info"),o=uni.getStorageSync("wap_member_id"),a={hasToken:!!n,tokenLength:n?n.length:0,hasMemberInfo:!!t,hasMemberId:!!o,memberInfo:t||null,memberId:o||null,allStorageKeys:[]};try{const n=e();a.allStorageKeys=n.keys}catch(s){a.allStorageKeys=["获取失败"]}return console.log("登录状态调试信息:",a),a}function c(e,s="请先登录后再进行此操作"){a()?e&&e():n({title:"登录提示",content:s,confirmText:"去登录",cancelText:"取消",success:e=>{if(e.confirm){const e=t(),n=e[e.length-1],a=n.route,s=n.options;let c="/"+a;if(s&&Object.keys(s).length>0){c+="?"+Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&")}const r=c.split("?"),i=r[0],l={};r[1]&&r[1].split("&").forEach((e=>{const[n,t]=e.split("=");n&&t&&(l[n]=decodeURIComponent(t))})),uni.setStorageSync("loginBack",{url:i,param:l}),o({url:"/app/pages/auth/login"})}}})}function r(e,n){return!!function(e){return!(!e.code||![401,"MUST_LOGIN","LOGIN_EXPIRE","TOKEN_INVALID"].includes(e.code))||!(!e.message||!e.message.includes("登录"))}(e)&&(c((()=>{n&&n()}),"登录已过期，请重新登录"),!0)}export{a as c,s as d,r as h,c as r};

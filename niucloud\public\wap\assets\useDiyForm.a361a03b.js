import{r as e,az as a,N as l,l as o,E as t,s as r,z as g,W as i,aW as n,L as d,al as u,am as p,ai as s}from"./index-4b8dc7db.js";import{b as m}from"./diy_form.03750fb6.js";function c(c={}){const v=e(!0),S=a(),b=e(c.form_id||0);e(c.name||""),e("");const f=e(""),y=l({});e(c.needLogin||!1);const h=l({pageMode:"diy",title:"",global:{},value:[],status:0}),B=o((()=>"decorate"==S.mode?S:h)),_=e(!1),x=(e=null)=>{let a=i();f.value=a[a.length-1]?a[a.length-1].route:"";let l=[];if(uni.getStorageSync("diyPageBlank")&&(l=uni.getStorageSync("diyPageBlank")),!l.length||l.length&&-1==l.indexOf(f.value)?S.topFixedStatus="home":l.length&&-1!=l.indexOf(f.value)&&(S.topFixedStatus="diy"),"decorate"==S.mode)S.init();else{if(!b.value)return;m({form_id:b.value}).then((a=>{if(Object.assign(y,a.data),h.status=a.data.status,y.value){h.pageMode=y.mode,h.title=y.title,S.id=y.form_id;let e=y.value,a=uni.getStorageSync("diyFormStorage_"+S.id);if(a){var l=new Date;let o=parseInt(l.getTime()/1e3);a.validTime>o?a.components&&a.components.forEach((a=>{for(let l=0;l<e.value.length;l++)if("diy_form"==e.value[l].componentType&&a.id==e.value[l].id){let o=n(a),t=n(o.field);delete o.field,delete t.required,delete t.unique,delete t.autofill,delete t.privacyProtection,Object.assign(e.value[l],o),Object.assign(e.value[l].field,t);break}})):uni.removeStorageSync("diyFormStorage_"+S.id)}S.value=e.value,h.global=e.global,h.value=e.value,h.value.forEach(((e,a)=>{e.isHidden?h.value.splice(a,1):(e.pageStyle="",("FormSubmit"!=e.componentName||"FormSubmit"==e.componentName&&"hover_screen_bottom"!=e.btnPosition)&&(e.pageStartBgColor&&(e.pageStartBgColor&&e.pageEndBgColor?e.pageStyle+=`background:linear-gradient(${e.pageGradientAngle},${e.pageStartBgColor},${e.pageEndBgColor});`:e.pageStyle+="background-color:"+e.pageStartBgColor+";"),e.margin&&(e.margin.top>0&&(e.pageStyle+="padding-top:"+2*e.margin.top+"rpx;"),e.pageStyle+="padding-bottom:"+2*e.margin.bottom+"rpx;",e.pageStyle+="padding-right:"+2*e.margin.both+"rpx;",e.pageStyle+="padding-left:"+2*e.margin.both+"rpx;")))})),_.value=h.value.some((e=>e&&e.position&&"top_fixed"==e.position)),d({title:h.title})}v.value=!1,e&&e(y)}))}};return{getLoading:()=>v.value,requestData:y,data:B.value,isShowTopTabbar:_,pageStyle:()=>{var e="";return B.value.global.pageStartBgColor&&(B.value.global.pageStartBgColor&&B.value.global.pageEndBgColor?e+=`background:linear-gradient(${B.value.global.pageGradientAngle},${B.value.global.pageStartBgColor},${B.value.global.pageEndBgColor});`:e+="background-color:"+B.value.global.pageStartBgColor+";"),B.value.global.bottomTabBarSwitch?e+="min-height:calc(100vh - 50px);":e+="min-height:calc(100vh);",B.value.global.bgUrl&&(e+=`background-image:url('${t(B.value.global.bgUrl)}');`),B.value.global.bgHeightScale&&(e+=`background-size: 100% ${B.value.global.bgHeightScale}%;`),e},onLoad:(e=null)=>{r((a=>{S.mode=a.mode||"","decorate"==S.mode&&(y.status=1,y.error=[],v.value=!1),a.form_record_id&&(y.form_record_id=a.form_record_id),b.value=a.form_id||"",x(e)}))},onShow:(e=null)=>{g((()=>{e&&e(y)}))},onHide:(e=null)=>{u((()=>{let a=[];uni.getStorageSync("diyPageBlank")&&(a=uni.getStorageSync("diyPageBlank")),a.length&&(a=Array.from(new Set(a)),a.forEach(((e,a,l)=>{e==f.value&&l.splice(a,1)}))),"diy"==S.topFixedStatus&&a.push(f.value),uni.setStorageSync("diyPageBlank",a),e&&e()}))},onUnload:()=>{p((()=>{}))},onPageScroll:()=>{s((e=>{e.scrollTop>0&&(S.scrollTop=e.scrollTop)}))},getData:x}}export{c as u};

import{x as a,J as e,y as t,o as s,c as o,w as l,k as n,g as r,b as i,A as u,T as d,B as c,I as m,H as _,ag as f,a0 as h}from"./index-dd56d0cc.js";import{g,s as p}from"./member-auth.e172a9b5.js";import{I as b}from"./image-upload.3ccee1ca.js";import{_ as k}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-upload.44346d61.js";import"./u-loading-icon.f15d7447.js";import"./common.eabc72c7.js";const x=k({name:"MemberAuth",components:{ImageUpload:b},data:()=>({loading:!1,showForm:!0,authStatus:{status:-1,status_text:"未认证",message:"",can_submit:!0,data:null},form:{real_name:"",id_card_no:"",id_card_front:"",id_card_back:"",bank_card_no:"",bank_name:"",bank_card_image:""},rules:{real_name:{rules:[{required:!0,errorMessage:"请输入真实姓名"},{maxLength:50,errorMessage:"真实姓名不能超过50个字符"}]},id_card_no:{rules:[{required:!0,errorMessage:"请输入身份证号"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,errorMessage:"身份证号格式不正确"}]},id_card_front:{rules:[{required:!0,errorMessage:"请上传身份证正面照片"}]},id_card_back:{rules:[{required:!0,errorMessage:"请上传身份证反面照片"}]},bank_card_no:{rules:[{required:!0,errorMessage:"请输入银行卡号"},{pattern:/^\d{16,19}$/,errorMessage:"银行卡号格式不正确"}]},bank_name:{rules:[{required:!0,errorMessage:"请输入银行名称"},{maxLength:100,errorMessage:"银行名称不能超过100个字符"}]},bank_card_image:{rules:[{required:!0,errorMessage:"请上传银行卡照片"}]}}}),onLoad(){this.loadAuthStatus()},onShow(){this.loadAuthStatus()},methods:{async loadAuthStatus(){const s=a();if(console.log("检查登录状态 - 当前token:",s),console.log("检查登录状态 - token长度:",s?s.length:0),!s)return console.log("用户未登录，跳转到登录页面"),void e().setLoginBack({url:"/addon/niucrowd/pages/member/auth"});try{console.log("开始获取认证状态...");const a=await g();console.log("获取认证状态结果:",a),1===a.code?(this.authStatus=a.data,a.data.data&&a.data.can_submit&&this.fillForm(a.data.data)):this.authStatus={status:-1,status_text:"未认证",message:"请上传身份证照片完成认证",can_submit:!0,data:null}}catch(o){if(console.error("获取认证状态失败:",o),"MEMBER_NOT_LOGIN"===o.msg||"MEMBER_NOT_LOGIN"===o.message)return console.log("API返回未登录，跳转到登录页面"),void e().setLoginBack({url:"/addon/niucrowd/pages/member/auth"});this.authStatus={status:-1,status_text:"未认证",message:"请上传身份证照片完成认证",can_submit:!0,data:null},t({title:o.msg||o.message||"获取认证状态失败",icon:"none"})}},fillForm(a){this.form={id_card_front:a.id_card_front||"",id_card_back:a.id_card_back||""}},validateForm(){if(!this.form.real_name)return t({title:"请输入真实姓名",icon:"none"}),!1;if(!this.form.id_card_no)return t({title:"请输入身份证号",icon:"none"}),!1;if(!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.form.id_card_no))return t({title:"身份证号格式不正确",icon:"none"}),!1;if(!this.form.id_card_front)return t({title:"请上传身份证正面照片",icon:"none"}),!1;if(!this.form.id_card_back)return t({title:"请上传身份证反面照片",icon:"none"}),!1;if(!this.form.bank_card_no)return t({title:"请输入银行卡号",icon:"none"}),!1;return/^\d{16,19}$/.test(this.form.bank_card_no)?this.form.bank_name?!!this.form.bank_card_image||(t({title:"请上传银行卡照片",icon:"none"}),!1):(t({title:"请输入银行名称",icon:"none"}),!1):(t({title:"银行卡号格式不正确",icon:"none"}),!1)},async handleSubmit(){try{if(!this.validateForm())return;this.loading=!0,console.log("提交的认证数据:",this.form),console.log("开始提交认证..."),console.log("submitAuth函数:",p),console.log("this.form:",this.form);try{const a=await p(this.form);return console.log("提交成功，结果:",a),void(1===a.code?(t({title:"提交成功，请等待审核",icon:"success"}),await this.loadAuthStatus(),this.showForm=!1):t({title:a.msg||"提交失败",icon:"none"}))}catch(a){throw console.error("API调用失败:",a),a}}catch(e){console.error("提交认证失败:",e),t({title:e.message||e.msg||"提交失败",icon:"none"})}finally{this.loading=!1}},getStatusClass:a=>({0:"status-pending",1:"status-approved",2:"status-rejected"}[a]||"status-default"),getStatusIcon:a=>({0:"icon-check-circle",1:"icon-check",2:"icon-close"}[a]||"icon-info")}},[["render",function(a,e,t,g,p,b){const k=n,x=m,S=_,y=f,V=h("uni-forms-item"),v=h("image-upload"),w=h("uni-forms");return s(),o(k,{class:"auth-container"},{default:l((()=>[r(" 页面标题 "),i(k,{class:"page-header"},{default:l((()=>[i(k,{class:"header-title"},{default:l((()=>[u("身份认证")])),_:1}),i(k,{class:"header-desc"},{default:l((()=>[u("完成身份认证后可发布众筹项目")])),_:1})])),_:1}),r(" 认证状态卡片 "),-1!==p.authStatus.status?(s(),o(k,{key:0,class:"status-card"},{default:l((()=>[i(k,{class:"status-header"},{default:l((()=>[i(k,{class:d(["status-icon",b.getStatusClass(p.authStatus.status)])},{default:l((()=>[i(x,{class:d(["iconfont",b.getStatusIcon(p.authStatus.status)])},null,8,["class"])])),_:1},8,["class"]),i(k,{class:"status-info"},{default:l((()=>[i(k,{class:"status-text"},{default:l((()=>[u(c(p.authStatus.status_text),1)])),_:1}),i(k,{class:"status-message"},{default:l((()=>[u(c(p.authStatus.message),1)])),_:1})])),_:1})])),_:1}),p.authStatus.can_submit?(s(),o(k,{key:0,class:"status-actions"},{default:l((()=>[i(S,{class:"btn-primary",onClick:e[0]||(e[0]=a=>p.showForm=!0)},{default:l((()=>[u("重新提交认证")])),_:1})])),_:1})):r("v-if",!0)])),_:1})):r("v-if",!0),r(" 认证表单 "),p.showForm||-1===p.authStatus.status?(s(),o(k,{key:1,class:"form-container"},{default:l((()=>[i(k,{class:"form-title"},{default:l((()=>[i(x,null,{default:l((()=>[u(c(-1===p.authStatus.status?"提交认证资料":"重新提交认证"),1)])),_:1})])),_:1}),i(w,{ref:"formRef",model:p.form,rules:p.rules,"label-width":"100"},{default:l((()=>[r(" 真实姓名 "),i(V,{label:"真实姓名",name:"real_name",required:""},{default:l((()=>[i(k,{class:"input-container"},{default:l((()=>[i(y,{modelValue:p.form.real_name,"onUpdate:modelValue":e[1]||(e[1]=a=>p.form.real_name=a),placeholder:"请输入真实姓名",class:"form-input",type:"text",maxlength:"50"},null,8,["modelValue"])])),_:1})])),_:1}),r(" 身份证号 "),i(V,{label:"身份证号",name:"id_card_no",required:""},{default:l((()=>[i(k,{class:"input-container"},{default:l((()=>[i(y,{modelValue:p.form.id_card_no,"onUpdate:modelValue":e[2]||(e[2]=a=>p.form.id_card_no=a),placeholder:"请输入18位身份证号",class:"form-input",type:"text",maxlength:"18"},null,8,["modelValue"])])),_:1})])),_:1}),r(" 身份证正面 "),i(V,{label:"身份证正面",name:"id_card_front",required:""},{default:l((()=>[i(k,{class:"upload-container"},{default:l((()=>[i(v,{modelValue:p.form.id_card_front,"onUpdate:modelValue":e[3]||(e[3]=a=>p.form.id_card_front=a),"max-count":1,"size-limit":5,"show-tips":!1},null,8,["modelValue"]),i(k,{class:"upload-tip"},{default:l((()=>[u("请上传清晰的身份证正面照片")])),_:1})])),_:1})])),_:1}),r(" 身份证反面 "),i(V,{label:"身份证反面",name:"id_card_back",required:""},{default:l((()=>[i(k,{class:"upload-container"},{default:l((()=>[i(v,{modelValue:p.form.id_card_back,"onUpdate:modelValue":e[4]||(e[4]=a=>p.form.id_card_back=a),"max-count":1,"size-limit":5,"show-tips":!1},null,8,["modelValue"]),i(k,{class:"upload-tip"},{default:l((()=>[u("请上传清晰的身份证反面照片")])),_:1})])),_:1})])),_:1}),r(" 银行卡号 "),i(V,{label:"银行卡号",name:"bank_card_no",required:""},{default:l((()=>[i(k,{class:"input-container"},{default:l((()=>[i(y,{modelValue:p.form.bank_card_no,"onUpdate:modelValue":e[5]||(e[5]=a=>p.form.bank_card_no=a),placeholder:"请输入银行卡号",class:"form-input",type:"number",maxlength:"19"},null,8,["modelValue"])])),_:1})])),_:1}),r(" 银行名称 "),i(V,{label:"银行名称",name:"bank_name",required:""},{default:l((()=>[i(k,{class:"input-container"},{default:l((()=>[i(y,{modelValue:p.form.bank_name,"onUpdate:modelValue":e[6]||(e[6]=a=>p.form.bank_name=a),placeholder:"请输入银行名称",class:"form-input",type:"text",maxlength:"100"},null,8,["modelValue"])])),_:1})])),_:1}),r(" 银行卡照片 "),i(V,{label:"银行卡照片",name:"bank_card_image",required:""},{default:l((()=>[i(k,{class:"upload-container"},{default:l((()=>[i(v,{modelValue:p.form.bank_card_image,"onUpdate:modelValue":e[7]||(e[7]=a=>p.form.bank_card_image=a),"max-count":1,"size-limit":5,"show-tips":!1},null,8,["modelValue"]),i(k,{class:"upload-tip"},{default:l((()=>[u("请上传清晰的银行卡照片")])),_:1})])),_:1})])),_:1}),r(" 提交按钮 "),i(k,{class:"form-actions"},{default:l((()=>[i(S,{class:"btn-primary btn-large",loading:p.loading,onClick:b.handleSubmit},{default:l((()=>[u(c(p.loading?"提交中...":"提交认证"),1)])),_:1},8,["loading","onClick"]),-1!==p.authStatus.status?(s(),o(S,{key:0,class:"btn-default btn-large",onClick:e[8]||(e[8]=a=>p.showForm=!1)},{default:l((()=>[u(" 取消 ")])),_:1})):r("v-if",!0)])),_:1})])),_:1},8,["model","rules"])])),_:1})):r("v-if",!0),r(" 认证须知 "),i(k,{class:"notice-card"},{default:l((()=>[i(k,{class:"notice-title"},{default:l((()=>[i(x,{class:"iconfont icon-info"}),i(x,null,{default:l((()=>[u("认证须知")])),_:1})])),_:1}),i(k,{class:"notice-content"},{default:l((()=>[i(k,{class:"notice-item"},{default:l((()=>[u("1. 请确保上传的身份证照片清晰可见，信息完整")])),_:1}),i(k,{class:"notice-item"},{default:l((()=>[u("2. 身份证必须为本人所有")])),_:1}),i(k,{class:"notice-item"},{default:l((()=>[u("3. 认证信息将严格保密，仅用于身份验证")])),_:1}),i(k,{class:"notice-item"},{default:l((()=>[u("4. 审核时间一般为1-3个工作日")])),_:1}),i(k,{class:"notice-item"},{default:l((()=>[u("5. 如有疑问请联系客服.1111111")])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-4d6a157a"]]);export{x as default};

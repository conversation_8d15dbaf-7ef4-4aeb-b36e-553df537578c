import{aB as e,aC as t,aD as l,aE as o,aF as a,aG as r,d as n,az as i,l as s,E as d,r as u,Q as c,a9 as f,an as p,aq as m,i as v,j as x,o as g,c as b,w as y,b as h,n as _,e as k,A as w,B as C,g as S,R,S as B,a3 as F,T,ar as z,k as $,I as D,G as E,ap as I,q as M,a1 as A,N as j,C as N,aA as W,L as V,aH as Y,aI as H,ai as U,F as P,ag as O,ay as L,a5 as G,a6 as q,a4 as X,a7 as Z,aJ as J,aK as Q,aL as K,aM as ee,aN as te,aO as le,aP as oe,a0 as ae,$ as re,aQ as ne,y as ie,aR as se,aS as de,ax as ue,aT as ce,aj as fe,H as pe,am as me,av as ve,aw as xe,aU as ge,W as be,aV as ye,aW as he,x as _e,J as ke,a as we,aX as Ce,p as Se,m as Re,aY as Be,t as Fe,aZ as Te,ab as ze,a_ as $e,a$ as De,b0 as Ee,f as Ie,v as Me,z as Ae,al as je}from"./index-4b8dc7db.js";import{_ as Ne}from"./u-icon.33002907.js";import{_ as We}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as Ve}from"./u-popup.e2790691.js";import{u as Ye,_ as He}from"./top-tabbar.59f1aa86.js";import{_ as Ue}from"./area-select.vue_vue_type_script_setup_true_lang.5d9e7fd9.js";import{_ as Pe}from"./u-checkbox.598bfa18.js";import{_ as Oe}from"./u-checkbox-group.0c3be417.js";import{_ as Le}from"./u-button.7aa0e948.js";import{_ as Ge}from"./u-input.7a7ec88f.js";import{_ as qe}from"./u-picker.0f983101.js";import{_ as Xe}from"./u-upload.899e5f6a.js";import{_ as Ze,a as Je}from"./u-radio-group.b3c6fbb7.js";import{a as Qe,e as Ke}from"./diy_form.03750fb6.js";import{_ as et}from"./u-action-sheet.8e2525cc.js";import{_ as tt}from"./u-avatar.db5bcfa1.js";import{_ as lt}from"./u-parse.1f53da94.js";import{_ as ot}from"./tabbar.805b8203.js";import{g as at}from"./category.146302da.js";import{i as rt}from"./common.eabc72c7.js";import{g as nt,a as it,b as st}from"./project.df03875d.js";import{_ as dt,d as ut}from"./index.3da4012a.js";import{d as ct,b as ft,e as pt}from"./coupon.d71b475a.js";import{g as mt,a as vt}from"./goods.f34d2594.js";import{_ as xt}from"./u--image.892273b2.js";import{g as gt,a as bt}from"./point.a8a5a12b.js";import{u as yt}from"./useGoods.0898f296.js";import{g as ht}from"./rank.1f5790d0.js";import{b as _t}from"./bind-mobile.259f3837.js";import{g as kt,_ as wt}from"./newcomer.2ec60692.js";import{a as Ct}from"./order.b56aabaa.js";var St="[object Symbol]";var Rt=/\s/;var Bt=/^\s+/;function Ft(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&Rt.test(e.charAt(t)););return t}(e)+1).replace(Bt,""):e}var Tt=NaN,zt=/^[-+]0x[0-9a-f]+$/i,$t=/^0b[01]+$/i,Dt=/^0o[0-7]+$/i,Et=parseInt;function It(o){if("number"==typeof o)return o;if(function(l){return"symbol"==typeof l||e(l)&&t(l)==St}(o))return Tt;if(l(o)){var a="function"==typeof o.valueOf?o.valueOf():o;o=l(a)?a+"":a}if("string"!=typeof o)return 0===o?o:+o;o=Ft(o);var r=$t.test(o);return r||Dt.test(o)?Et(o.slice(2),r?2:8):zt.test(o)?Tt:+o}var Mt=1/0,At=17976931348623157e292;function jt(e){return e?(e=It(e))===Mt||e===-Mt?(e<0?-1:1)*At:e==e?e:0:0===e?e:0}var Nt=Math.ceil,Wt=Math.max;var Vt,Yt=function(e,t,n){return n&&"number"!=typeof n&&function(e,t,n){if(!l(n))return!1;var i=typeof t;return!!("number"==i?o(n)&&a(t,n.length):"string"==i&&t in n)&&r(n[t],e)}(e,t,n)&&(t=n=void 0),e=jt(e),void 0===t?(t=e,e=0):t=jt(t),function(e,t,l,o){for(var a=-1,r=Wt(Nt((t-e)/(l||1)),0),n=Array(r);r--;)n[o?r:++a]=e,e+=l;return n}(e,t,n=void 0===n?e<t?1:-1:jt(n),Vt)};const Ht=Yt,Ut=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=s((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${W.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e})),n=u(""),M=u(""),A=e=>{var t="";return e.listFrame.startColor&&e.listFrame.endColor?t+=`background:linear-gradient(${e.listFrame.startColor},${e.listFrame.endColor});`:t+=`background:${e.listFrame.startColor||e.listFrame.endColor};`,o.value.topElementRounded&&(t+="border-top-left-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.topElementRounded&&(t+="border-top-right-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.bottomElementRounded&&(t+="border-bottom-left-radius:"+2*o.value.bottomElementRounded+"rpx;"),o.value.bottomElementRounded&&(t+="border-bottom-right-radius:"+2*o.value.bottomElementRounded+"rpx;"),t+="overflow: hidden;"},j=e=>{var t="";return t+=`background:linear-gradient(90deg,${e.startColor},${e.endColor});`};c((()=>{V(),"decorate"==l.mode?f((()=>o.value),((e,t)=>{e&&"ActiveCube"==e.componentName&&V()})):f((()=>o.value),((e,t)=>{V()}))}));const N=z(),W=u(0),V=()=>{p((()=>{m().in(N).select(".diy-active-cube").boundingClientRect((e=>{W.value=e.height})).exec(),"style-3"==o.value.blockStyle.value&&(n.value="margin-right:14rpx;"),"style-4"==o.value.blockStyle.value&&(M.value="margin-right:14rpx;")}))};return(e,t)=>{const i=$,s=D,u=E,c=v(x("u-icon"),Ne),f=I;return g(),b(i,{style:_(k(a))},{default:y((()=>[h(i,{style:_(k(r))},null,8,["style"]),h(i,{class:"diy-active-cube relative"},{default:y((()=>[h(i,{class:"active-cube-wrap pt-[28rpx] px-[20rpx] pb-[24rpx]"},{default:y((()=>["style-1"==k(o).titleStyle.value?(g(),b(i,{key:0,class:"flex items-center"},{default:y((()=>[h(i,{class:"mr-[10rpx] font-500 text-[30rpx]",style:_({color:k(o).titleColor}),onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).textLink))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),k(o).subTitle.text?(g(),b(i,{key:0,onClick:t[1]||(t[1]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-center text-[22rpx] rounded-[40rpx] rounded-tl-[10rpx] py-[6rpx] px-[14rpx]",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(o).titleStyle.value?(g(),b(i,{key:1,class:"flex items-center"},{default:y((()=>[h(i,{class:"mr-[10rpx] font-500 text-[30rpx]",style:_({color:k(o).titleColor}),onClick:t[2]||(t[2]=e=>k(l).toRedirect(k(o).textLink))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),k(o).subTitle.text?(g(),b(i,{key:0,onClick:t[3]||(t[3]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-center text-[22rpx] rounded-[6rpx] py-[6rpx] px-[14rpx]",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),"style-3"==k(o).titleStyle.value?(g(),b(i,{key:2,class:"flex items-center"},{default:y((()=>[h(i,{class:"mr-[10rpx] font-500 text-[30rpx]",onClick:t[4]||(t[4]=e=>k(l).toRedirect(k(o).textLink)),style:_({color:k(o).titleColor})},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),h(i,{class:"relative h-[36rpx]",onClick:t[5]||(t[5]=e=>k(l).toRedirect(k(o).subTitle.link))},{default:y((()=>[k(o).subTitle.text?(g(),b(i,{key:0,class:"flex items-center text-[22rpx] leading-0 min-w-[60rpx] h-[34rpx] pl-[10rpx] pr-[34rpx]",style:_({color:k(o).subTitle.textColor,"background-image":"url("+k(d)("static/resource/images/diy/active_cube/bg_2.png")+")","background-size":"100% 100%","background-repeat":"no-repeat"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0),S(' \t<image class="absolute left-0 top-0 bottom-0 !w-[16rpx] !h-[36rpx]" :src="img(\'static/resource/images/diy/active_cube/block_style2_1.png\')" mode="scaleToFill"/>\n                                    <image class="absolute right-0 top-0 bottom-0 !w-[28rpx] !h-[36rpx]" :src="img(\'static/resource/images/diy/active_cube/block_style2_2.png\')" mode="scaleToFill"/> ')])),_:1})])),_:1})):S("v-if",!0),"style-4"==k(o).titleStyle.value?(g(),b(i,{key:3,class:"flex items-center justify-between"},{default:y((()=>[h(i,{class:"font-500 text-[30rpx]",onClick:t[6]||(t[6]=e=>k(l).toRedirect(k(o).textLink)),style:_({color:k(o).titleColor})},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"]),k(o).subTitle.text?(g(),b(i,{key:0,onClick:t[7]||(t[7]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-[22rpx] rounded-[40rpx] pl-[16rpx] pr-[8rpx] h-[42rpx] flex-center",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[h(s,null,{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1}),h(s,{class:"nc-iconfont nc-icon-youV6xx !text-[26rpx]"})])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),"style-5"==k(o).titleStyle.value?(g(),b(i,{key:4,class:"flex items-center"},{default:y((()=>[k(o).textImg?(g(),b(i,{key:0,class:"h-[32rpx] flex items-center",onClick:t[8]||(t[8]=e=>k(l).toRedirect(k(o).textLink))},{default:y((()=>[h(u,{class:"h-[100%] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])])),_:1})):S("v-if",!0),k(o).subTitle.text&&k(o).textImg?(g(),b(i,{key:1,class:"mx-[16rpx] w-[2rpx] h-[24rpx]",style:_({background:k(o).subTitle.textColor})},null,8,["style"])):S("v-if",!0),k(o).subTitle.text?(g(),b(i,{key:2,onClick:t[9]||(t[9]=e=>k(l).toRedirect(k(o).subTitle.link)),class:"text-center text-[22rpx] py-[6rpx]",style:_({color:k(o).subTitle.textColor,background:"linear-gradient(90deg, "+k(o).subTitle.startColor+", "+k(o).subTitle.endColor+")"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(i,{class:"bd flex flex-wrap justify-between"},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,(e=>(g(),R(B,{key:e.id},["style-1"==k(o).blockStyle.value?(g(),b(i,{key:0,onClick:t=>k(l).toRedirect(e.link),class:"item flex justify-between px-[20rpx] py-[30rpx] bg-white mt-[20rpx]",style:_(A(e))},{default:y((()=>[h(i,{class:"flex-1 flex items-baseline flex-col"},{default:y((()=>[h(i,{class:"text-[28rpx] pb-[10rpx] text-[#333]",style:_({fontWeight:k(o).blockStyle.fontWeight})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"]),h(i,{class:"text-[22rpx] text-[#999] pb-[30rpx]"},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1024),e.moreTitle.text?(g(),b(i,{key:0,class:"link relative text-[22rpx] leading-[40rpx] flex items-center text-white rounded-r-[20rpx] h-[40rpx] pl-[26rpx] pr-[10rpx]",style:_(j(e.moreTitle))},{default:y((()=>[h(s,{class:"mr-[8rpx]"},{default:y((()=>[w(C(e.moreTitle.text),1)])),_:2},1024),h(s,{class:"iconfont iconjiantou-you-cuxiantiao-fill !text-[20rpx] text-[#fff]"}),h(u,{class:"absolute left-0 top-0 bottom-0 !w-[28rpx]",src:k(d)("static/resource/images/diy/active_cube/block_style1_1.png"),mode:"scaleToFill"},null,8,["src"])])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1024),e.imageUrl?(g(),b(i,{key:0,class:"img-box ml-[10rpx] w-[130rpx]"},{default:y((()=>[h(u,{src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(g(),b(i,{key:1,class:"img-box ml-[10rpx] flex items-center justify-center w-[130rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1}))])),_:2},1032,["onClick","style"])):S("v-if",!0),"style-2"==k(o).blockStyle.value?(g(),b(i,{key:1,onClick:t=>k(l).toRedirect(e.link),class:"item h-[150rpx] flex justify-between p-[20rpx] bg-white mt-[20rpx]",style:_(A(e))},{default:y((()=>[h(i,{class:"flex-1 flex items-baseline flex-col"},{default:y((()=>[h(i,{class:"text-[26rpx] mt-[10rpx] pb-[16rpx]",style:_({fontWeight:k(o).blockStyle.fontWeight})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"]),h(i,{class:"text-[22rpx] text-gray-500 pb-[26rpx]"},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1024),e.moreTitle.text?(g(),b(i,{key:0,class:"link relative text-[22rpx] leading-[40rpx] flex items-center text-white rounded-[20rpx] h-[40rpx] pl-[20rpx] pr-[10rpx]",style:_(j(e.moreTitle))},{default:y((()=>[h(s,{class:T(["mr-[8rpx]",{italic:"italics"==k(o).blockStyle.btnText}])},{default:y((()=>[w(C(e.moreTitle.text),1)])),_:2},1032,["class"]),h(s,{class:"iconfont iconjiantou-you-cuxiantiao-fill !text-[20rpx] text-[#fff]"})])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1024),e.imageUrl?(g(),b(i,{key:0,class:"img-box ml-[10rpx] w-[130rpx]"},{default:y((()=>[h(u,{src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(g(),b(i,{key:1,class:"img-box ml-[10rpx] flex items-center justify-center w-[130rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1}))])),_:2},1032,["onClick","style"])):S("v-if",!0)],64)))),128))])),_:1}),"style-3"==k(o).blockStyle.value?(g(),b(f,{key:5,"scroll-x":!0,class:"whitespace-nowrap",id:"warpStyle3-"+k(o).id},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),b(i,{key:e.id,class:"inline-flex"},{default:y((()=>[h(i,{id:"item"+t+k(o).id,onClick:t=>k(l).toRedirect(e.link),class:T(["flex flex-col items-center justify-between p-[10rpx] bg-white mt-[20rpx] w-[157rpx] h-[200rpx] box-border",{"!mr-[0rpx]":t+1===k(o).list.length}]),style:_(n.value+A(e))},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,class:"w-[141rpx] h-[141rpx] rounded-[var(--rounded-small)] overflow-hidden"},{default:y((()=>[h(u,{class:"w-[141rpx] h-[141rpx]",src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(g(),b(i,{key:1,class:"w-[141rpx] h-[141rpx] relative flex-shrink-0"},{default:y((()=>[h(i,{class:"absolute left-0 top-0 flex items-center justify-center w-[141rpx] h-[141rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1})])),_:1})),h(i,{class:"mt-[10rpx] mb-[2rpx] text-[26rpx]",style:_({color:e.title.textColor,fontWeight:k(o).blockStyle.fontWeight})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"])])),_:2},1032,["id","onClick","style","class"])])),_:2},1024)))),128))])),_:1},8,["id"])):S("v-if",!0),"style-4"==k(o).blockStyle.value?(g(),b(f,{key:6,"scroll-x":"true",class:"whitespace-nowrap",id:"warpStyle4-"+k(o).id},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),b(i,{key:e.id,class:"inline-flex"},{default:y((()=>[h(i,{id:"item"+t+k(o).id,onClick:t=>k(l).toRedirect(e.link),class:T(["flex flex-col items-center justify-between p-[4rpx] bg-[#F93D02] mt-[20rpx] box-border",{"!mr-[0rpx]":t+1===k(o).list.length}]),style:_(A(e)+M.value)},{default:y((()=>[h(i,{class:"w-[149rpx] h-[149rpx] box-border px-[18rpx] pt-[16rpx] pb-[6rpx] bg-[#fff] flex flex-col items-center rounded-[var(--rounded-small)]"},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,class:"w-[112rpx] h-[102rpx]"},{default:y((()=>[h(u,{class:"w-[112rpx] h-[102rpx]",src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["src"])])),_:2},1024)):(g(),b(i,{key:1,class:"w-[112rpx] h-[102rpx] relative flex-shrink-0"},{default:y((()=>[h(i,{class:"absolute left-0 top-0 flex items-center justify-center w-[112rpx] h-[102rpx] bg-[#f3f4f6]"},{default:y((()=>[h(c,{name:"photo",color:"#999",size:"50"})])),_:1})])),_:1})),h(i,{class:"relative -mt-[10rpx] text-[20rpx] bg-[#F3DAC5] text-[#ED6E00] rounded-[16rpx] px-[12rpx] h-[34rpx] flex-center",style:_({color:e.subTitle.textColor,background:"linear-gradient(to right,"+e.subTitle.startColor+","+e.subTitle.endColor+")"})},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1032,["style"])])),_:2},1024),h(i,{class:"mt-[12rpx] mb-[12rpx] text-[26rpx] text-[#fff]",style:_({fontWeight:"bold"==k(o).blockStyle.fontWeight?k(o).blockStyle.fontWeight:"500"})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"])])),_:2},1032,["id","onClick","class","style"])])),_:2},1024)))),128))])),_:1},8,["id"])):S("v-if",!0)])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-0ce90cab"]]),Pt=We(n({__name:"index",props:["component","index","global","scrollBool"],setup(e){const t=e,l=M(),o=A(),a=z(),r=i(),n=s((()=>"decorate"==r.mode?r.value[t.index]:t.component));let U=!1;n.value&&"style-2"==n.value.search.style&&"decorate"!=r.mode&&(U=!0);const P=Ye(U);P.onLoad(),P.init();const O=s((()=>{var e="";return n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e})),L=u(""),G=u(!1),q=s((()=>{var e="";if("style-3"==n.value.swiper.swiperStyle&&(e+="position: absolute;z-index: 99;left: 0;right: 0;"),t.global.topStatusBar.isShow&&"style-4"==t.global.topStatusBar.style&&(e+="top:"+r.topTabarHeight+"px;"),"style-3"==n.value.swiper.swiperStyle&&("ios"===o.platform?e+="top: 55px;":e+="top: 44.5px;"),"decorate"==r.mode)return e;if("fixed"==n.value.positionWay&&(null!=t.scrollBool&&-1!=t.scrollBool&&(e+="position: fixed;z-index: 99;top: 0;left: 0;right: 0;"),1!=t.scrollBool&&2!=t.scrollBool||t.global.topStatusBar.isShow&&"style-4"==t.global.topStatusBar.style&&(e+="top:"+r.topTabarHeight+"px;"),G.value=!1,1==t.scrollBool)){let t=(n.value.fixedBgColor||"").split(","),l=n.value.fixedBgColor?parseInt(t[t.length-1]):0;n.value.fixedBgColor&&0!=l?(G.value=!1,e+="background-color:"+n.value.fixedBgColor+";"):G.value=!0}return e})),X=()=>{let e="";return"style-3"==n.value.swiper.swiperStyle&&(e="ios"===o.platform?"margin-top: -55px;":"margin-top: -44.5px;"),e},Z=e=>{let l="";return e?(l=n.value.tab.selectColor,"fixed"==n.value.positionWay&&1==t.scrollBool&&(l=n.value.tab.fixedSelectColor)):(l=n.value.tab.noColor,"fixed"==n.value.positionWay&&1==t.scrollBool&&(l=n.value.tab.fixedNoColor)),l},J=s((()=>{let e=!0;for(let t=0;t<n.value.search.hotWord.list.length;t++){if(n.value.search.hotWord.list[t].text){e=!1;break}}return e})),Q=s((()=>{var e="";let l=t.global.pageStartBgColor?t.global.pageStartBgColor:"rgba(255,255,255,1)";if(l.indexOf("(")>-1){let t=l.split("(")[1].split(")")[0].split(",");1==n.value.bgGradient&&(e+=`background: linear-gradient(rgba(${t[0]}, ${t[1]}, ${t[2]}, 0) 65%, rgba(${t[0]}, ${t[1]}, ${t[2]}, 0.6) 70%, rgba(${t[0]}, ${t[1]}, ${t[2]}, 0.85) 80%, rgba(${t[0]}, ${t[1]}, ${t[2]}, 0.95) 90%,  rgb(${t[0]}, ${t[1]}, ${t[2]}, 1) 100%);`)}else e+=`background: (${l});`;return e})),K=s((()=>"style-2"==n.value.swiper.swiperStyle||"style-3"==n.value.swiper.swiperStyle)),ee=s((()=>2*n.value.swiper.imageHeight+"rpx")),te=u(0),le=e=>{te.value=e.detail.current},oe=s((()=>{var e="";return n.value.swiper.topRounded&&(e+="border-top-left-radius:"+2*n.value.swiper.topRounded+"rpx;"),n.value.swiper.topRounded&&(e+="border-top-right-radius:"+2*n.value.swiper.topRounded+"rpx;"),n.value.swiper.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.swiper.bottomRounded+"rpx;"),n.value.swiper.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.swiper.bottomRounded+"rpx;"),e})),ae=s((()=>{var e="";return n.value.search.subTitle.textColor&&(e+="color:"+n.value.search.subTitle.textColor+";"),n.value.search.subTitle.startColor&&n.value.search.subTitle.endColor?e+=`background:linear-gradient(${n.value.search.subTitle.startColor}, ${n.value.search.subTitle.endColor});`:e+="background-color:"+(n.value.search.subTitle.startColor||n.value.search.subTitle.endColor)+";",e})),re=u(-1),ne=u(""),ie=(e,t)=>{if("decorate"==r.mode)return!1;ne.value=e.source,re.value=t,"home"==e.source?r.topFixedStatus="home":"diy_page"==e.source&&(r.topFixedStatus="diy",pe(e.diy_id))},se=u(!1);let de={};const ue=u("");c((()=>{ce(),"decorate"==r.mode&&f((()=>n.value),((e,t)=>{e&&"CarouselSearch"==e.componentName&&ce()})),me=s((()=>{var e,t,l;return(null==(l=null==(t=null==(e=n.value)?void 0:e.swiper)?void 0:t.list)?void 0:l.length)>1}))}));const ce=()=>{p((()=>{setTimeout((()=>{"style-3"!=n.value.swiper.swiperStyle?m().in(a).select(".fixed-wrap").boundingClientRect((e=>{L.value=(e.height||0)+"px"})).exec():L.value=""}))})),P.refresh(),ie({source:"home"},-1),n.value.swiper.list.forEach((e=>{""==e.imageUrl&&(e.imgWidth=690,e.imgHeight=330)}))},fe=j({pageMode:"diy",title:"",global:{},value:[]}),pe=e=>{if(!e)return fe.pageMode="diy",fe.title="",fe.global={},void(fe.value=[]);W({id:e}).then((e=>{if(e.data.value){let t=e.data;fe.pageMode=t.mode,fe.title=t.title;let l=JSON.parse(t.value);fe.global=l.global,fe.global.topStatusBar.isShow=!1,fe.global.bottomTabBarSwitch=!1,fe.value=l.value,fe.value.forEach(((e,t)=>{e.pageStyle="",e.pageStartBgColor&&(e.pageStartBgColor&&e.pageEndBgColor?e.pageStyle+=`background:linear-gradient(${e.pageGradientAngle},${e.pageStartBgColor},${e.pageEndBgColor});`:e.pageStyle+="background-color:"+e.pageStartBgColor+";"),e.margin&&(e.margin.top>0&&(e.pageStyle+="padding-top:"+2*e.margin.top+"rpx;"),e.pageStyle+="padding-bottom:"+2*e.margin.bottom+"rpx;",e.pageStyle+="padding-right:"+2*e.margin.both+"rpx;",e.pageStyle+="padding-left:"+2*e.margin.both+"rpx;")})),V({title:fe.title})}}))};let me=u(!0);me.value=!0;let ve=uni.getStorageSync("componentsScrollValGroup");if(ve&&"object"==typeof ve)ve.CarouselSearch=20,uni.setStorageSync("componentsScrollValGroup",ve);else{let e={CarouselSearch:20};uni.setStorageSync("componentsScrollValGroup",e)}return(e,o)=>{const a=E,i=$,s=D,u=Y,c=H,f=I,p=v(x("u-popup"),Ve);return g(),b(i,{style:_(k(O)),class:"goods-carousel-search-wrap"},{default:y((()=>[h(i,{class:"relative pb-[20rpx]"},{default:y((()=>[h(i,{class:T(["bg-img",{"!-bottom-[200rpx]":1==k(n).bgGradient}])},{default:y((()=>[k(n).swiper.control&&k(n).swiper.list&&k(n).swiper.list[te.value].imageUrl?(g(),b(a,{key:0,src:k(d)(k(n).swiper.list[te.value].imageUrl),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(g(),b(i,{key:1,class:"w-full h-full bg-[#fff]"})),h(i,{class:"bg-img-box",style:_(k(Q))},null,8,["style"])])),_:1},8,["class"]),h(i,{class:"fixed-wrap",style:_(k(q))},{default:y((()=>["style-1"==k(n).search.style?(g(),b(i,{key:0,class:"diy-search-wrap relative z-10",onClick:o[1]||(o[1]=e=>k(r).toRedirect(k(n).search.link)),style:_(ue.value)},{default:y((()=>[k(n).search.logo?(g(),b(i,{key:0,class:"img-wrap"},{default:y((()=>[h(a,{src:k(d)(k(n).search.logo),mode:"aspectFit"},null,8,["src"])])),_:1})):S("v-if",!0),h(i,{class:"search-content",style:_({backgroundColor:k(n).search.bgColor}),onClick:o[0]||(o[0]=N((e=>k(r).toRedirect(k(n).search.link)),["stop"]))},{default:y((()=>[h(s,{class:"input-content text-[#fff] text-[24rpx] leading-[68rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(k(J)?k(n).search.text:""),1)])),_:1},8,["style"]),h(s,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 w-[80rpx] h-[52rpx] flex items-center justify-center rounded-[50rpx] text-[28rpx] text-[#fff]",style:_({backgroundColor:k(n).search.btnBgColor,color:k(n).search.btnColor})},null,8,["style"]),k(J)?S("v-if",!0):(g(),b(c,{key:0,class:"swiper-wrap",interval:1e3*k(n).search.hotWord.interval,autoplay:"true",vertical:"true",circular:"true"},{default:y((()=>[(g(!0),R(B,null,F(k(n).search.hotWord.list,(e=>(g(),b(u,{class:"swiper-item",key:e.id},{default:y((()=>[h(i,{class:"leading-[64rpx] text-[24rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["style"])])),_:2},1024)))),128))])),_:1},8,["interval"]))])),_:1},8,["style"])])),_:1},8,["style"])):S("v-if",!0),"style-2"==k(n).search.style?(g(),b(i,{key:1,class:"diy-search-wrap style-2 relative z-10",onClick:o[5]||(o[5]=e=>k(r).toRedirect(k(n).search.link))},{default:y((()=>[h(i,{class:"flex items-center",style:_(ue.value)},{default:y((()=>[k(n).search.logo?(g(),b(i,{key:0,class:"img-wrap"},{default:y((()=>[h(a,{src:k(d)(k(n).search.logo),mode:"aspectFit"},null,8,["src"])])),_:1})):S("v-if",!0),k(n).search.subTitle.text?(g(),b(i,{key:1,style:_(k(ae)),class:"max-w-[360rpx] text-[24rpx] h-[38rpx] rounded-r-[20rpx] rounded-t-[20rpx] rounded-bl-[2rpx]"},{default:y((()=>[h(i,{class:"truncate leading-[38rpx] h-[38rpx] px-[12rpx]"},{default:y((()=>[w(C(k(n).search.subTitle.text),1)])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"]),h(i,{class:"flex items-center w-full mt-[16rpx]"},{default:y((()=>[k(l).diyAddressInfo?(g(),b(i,{key:0,onClick:o[2]||(o[2]=N((e=>k(P).reposition()),["stop"])),style:_({color:k(n).search.positionColor}),class:"mr-[30rpx]"},{default:y((()=>[h(i,{class:"flex items-baseline font-500"},{default:y((()=>[h(s,{class:"text-[24rpx] mr-[2rpx]"},{default:y((()=>[w(C(k(l).diyAddressInfo.city),1)])),_:1}),h(s,{class:"iconfont iconxiaV6xx !text-[24rpx]"})])),_:1}),k(l).diyAddressInfo.community?(g(),b(i,{key:0,class:"text-[18rpx] mt-[10rpx] truncate max-w-[160rpx]"},{default:y((()=>[w(C(k(l).diyAddressInfo.community),1)])),_:1})):S("v-if",!0)])),_:1},8,["style"])):(g(),b(i,{key:1,onClick:o[3]||(o[3]=N((e=>k(P).reposition()),["stop"])),class:"text-[24rpx] mr-[30rpx] truncate max-w-[160rpx]",style:_({color:k(n).search.positionColor})},{default:y((()=>[w(C(k(l).defaultPositionAddress),1)])),_:1},8,["style"])),h(i,{class:"search-content",style:_({backgroundColor:k(n).search.bgColor}),onClick:o[4]||(o[4]=N((e=>k(r).toRedirect(k(n).search.link)),["stop"]))},{default:y((()=>[h(s,{class:"input-content text-[#fff] text-[24rpx] leading-[68rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(k(J)?k(n).search.text:""),1)])),_:1},8,["style"]),h(s,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 w-[80rpx] h-[52rpx] flex items-center justify-center rounded-[50rpx] text-[28rpx] text-[#fff]",style:_({backgroundColor:k(n).search.btnBgColor,color:k(n).search.btnColor})},null,8,["style"]),k(J)?S("v-if",!0):(g(),b(c,{key:0,class:"swiper-wrap",interval:1e3*k(n).search.hotWord.interval,autoplay:"true",vertical:"true",circular:"true"},{default:y((()=>[(g(!0),R(B,null,F(k(n).search.hotWord.list,(e=>(g(),b(u,{class:"swiper-item",key:e.id},{default:y((()=>[h(i,{class:"leading-[64rpx] text-[24rpx]",style:_({color:k(n).search.color})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["style"])])),_:2},1024)))),128))])),_:1},8,["interval"]))])),_:1},8,["style"])])),_:1})])),_:1})):S("v-if",!0),k(n).tab.control?(g(),b(i,{key:2,class:"tab-list-wrap relative z-10"},{default:y((()=>[h(f,{"scroll-x":"true",class:"scroll-wrap","scroll-into-view":"a"+re.value},{default:y((()=>[h(i,{onClick:o[6]||(o[6]=e=>ie({source:"home"},-1)),class:T(["scroll-item",[{active:-1==re.value}]])},{default:y((()=>[h(i,{class:"name",style:_({color:Z(-1==re.value)})},{default:y((()=>[w("首页")])),_:1},8,["style"]),S(' <view class="line" :style="{\'background-color\': getTabColor(currTabIndex == -1)}" v-if="currTabIndex == -1"></view> ')])),_:1},8,["class"]),(g(!0),R(B,null,F(k(n).tab.list,((e,t)=>(g(),b(i,{class:T(["scroll-item",[{active:t==re.value}]]),onClick:l=>ie(e,t),id:"a"+t,key:t},{default:y((()=>[h(i,{class:"name",style:_({color:Z(t==re.value)})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["style"]),S(' <view class="line" :style="{\'background-color\': getTabColor(index == currTabIndex)}" v-if="index == currTabIndex"></view> ')])),_:2},1032,["class","onClick","id"])))),128))])),_:1},8,["scroll-into-view"]),k(n).tab.list.length?(g(),b(i,{key:0,class:"absolute tab-btn iconfont icona-yingyongliebiaoV6xx-32",onClick:o[7]||(o[7]=e=>se.value=!0)})):S("v-if",!0)])),_:1})):S("v-if",!0),G.value?(g(),b(i,{key:3,class:"bg-img"},{default:y((()=>[k(n).swiper.control&&k(n).swiper.list&&k(n).swiper.list[te.value].imageUrl?(g(),b(a,{key:0,src:k(d)(k(n).swiper.list[te.value].imageUrl),mode:"widthFix",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(g(),b(i,{key:1,class:"w-full h-full bg-[#fff]"}))])),_:1})):S("v-if",!0)])),_:1},8,["style"]),S(" 解决fixed定位后导航栏塌陷的问题 "),"decorate"!=k(r).mode?(g(),R(B,{key:0},["fixed"==k(n).positionWay&&null!=t.scrollBool&&-1!=t.scrollBool?(g(),b(i,{key:0,class:"u-navbar-placeholder",style:_({width:"100%",paddingTop:L.value})},null,8,["style"])):S("v-if",!0)],64)):S("v-if",!0),S(" 轮播图 "),h(i,{class:T(["relative",{"mx-[20rpx]":k(K)&&"style-3"!=k(n).swiper.swiperStyle,"swiper-style-3":"style-3"==k(n).swiper.swiperStyle}]),style:_(X())},{default:y((()=>[k(n).swiper.control?(g(),b(c,{key:0,class:T(["swiper",{"swiper-left":"left"==k(n).swiper.indicatorAlign,"swiper-right":"right"==k(n).swiper.indicatorAlign,"ns-indicator-dots":"style-2"==k(n).swiper.indicatorStyle,"ns-indicator-dots-three":"style-3"==k(n).swiper.indicatorStyle}]),style:_({height:k(ee)}),autoplay:"true",circular:"true",onChange:le,"previous-margin":k(K)?0:"26rpx","next-margin":k(K)?0:"26rpx",interval:1e3*k(n).swiper.interval,"indicator-dots":k(me),"indicator-color":k(n).swiper.indicatorColor,"indicator-active-color":k(n).swiper.indicatorActiveColor},{default:y((()=>[(g(!0),R(B,null,F(k(n).swiper.list,((e,t)=>(g(),b(u,{class:"swiper-item",key:e.id,style:_(k(oe))},{default:y((()=>[h(i,{onClick:t=>k(r).toRedirect(e.link)},{default:y((()=>[h(i,{class:"item",style:_({height:k(ee)})},{default:y((()=>[e.imageUrl?(g(),b(a,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(k(oe)),class:T(["w-full h-full",{"swiper-animation":te.value!=t&&"style-3"!=k(n).swiper.indicatorStyle}]),"show-menu-by-longpress":!0},null,8,["src","style","class"])):(g(),b(a,{key:1,src:k(d)("static/resource/images/diy/figure.png"),style:_(k(oe)),mode:"scaleToFill",class:T(["w-full h-full",{"swiper-animation":te.value!=t&&"style-3"!=k(n).swiper.indicatorStyle}]),"show-menu-by-longpress":!0},null,8,["src","style","class"]))])),_:2},1032,["style"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["style","class","previous-margin","next-margin","interval","indicator-dots","indicator-color","indicator-active-color"])):S("v-if",!0)])),_:1},8,["class","style"]),S(" 分类展开 "),h(p,{safeAreaInsetTop:!0,show:se.value,mode:"top",onClose:o[9]||(o[9]=e=>se.value=!1)},{default:y((()=>[h(i,{class:"text-sm px-[30rpx] pt-3",style:_({"padding-top":k(de).top+"px"})},{default:y((()=>[w("全部分类")])),_:1},8,["style"]),h(i,{class:"flex flex-wrap pl-[30rpx] pt-[30rpx]"},{default:y((()=>[h(i,{onClick:o[8]||(o[8]=e=>ie({source:"home"},-1)),class:T(["px-[26rpx] border-[2rpx] border-solid border-transparent h-[60rpx] mr-[30rpx] mb-[30rpx] flex items-center justify-center bg-[#F4F4F4] rounded-[8rpx] text-xs",{"tab-select-popup":-1==re.value}])},{default:y((()=>[w("首页")])),_:1},8,["class"]),(g(!0),R(B,null,F(k(n).tab.list,((e,t)=>(g(),b(s,{onClick:l=>ie(e,t),key:t,class:T(["px-[26rpx] border-[2rpx] border-solid border-transparent h-[60rpx] mr-[30rpx] mb-[30rpx] flex items-center justify-center bg-[#F4F4F4] rounded-[8rpx] text-xs",{"tab-select-popup":t==re.value}])},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["onClick","class"])))),128))])),_:1})])),_:1},8,["show"])])),_:1}),S(" 展示微页面数据 "),"diy_page"==ne.value?(g(),b(i,{key:0,class:"child-diy-template-wrap bg-index"},{default:y((()=>[h(Fr,{data:fe},null,8,["data"])])),_:1})):S("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-3ffff1c9"]]),Ot=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=s((()=>{var e="";return e+="width:"+2*o.value.imageSize+"rpx;",e+="height:"+2*o.value.imageSize+"rpx;",e+="border-radius:"+2*o.value.aroundRadius+"rpx;"})),n=s((()=>{let e="";return o.value.offset&&("lowerRight"==o.value.bottomPosition||"lowerLeft"==o.value.bottomPosition?e+="translateY("+2*-o.value.offset+"rpx)":"upperRight"!=o.value.bottomPosition&&"upperLeft"!=o.value.bottomPosition||(e+="translateY("+2*o.value.offset+"rpx)")),o.value.lateralOffset&&("upperLeft"==o.value.bottomPosition||"lowerLeft"==o.value.bottomPosition?e+=" translateX("+2*o.value.lateralOffset+"rpx)":"upperRight"!=o.value.bottomPosition&&"lowerRight"!=o.value.bottomPosition||(e+=" translateX("+2*-o.value.lateralOffset+"rpx)")),e=`transform: ${e};`,e})),c=u(!0),f=u(null);return s((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e+="transition: right .25s;box-shadow:0px 32rpx 96rpx 32rpx rgba(0, 0, 0, .08), 0px 24rpx 64px rgba(0, 0, 0, .12), 0px 16rpx 32rpx -16rpx rgba(0, 0, 0, .16);",e+=c.value?"transition-delay: 0.25s;":"right:-"+(2*o.value.imageSize+24)+"rpx !important;"})),s((()=>{var e="transition: right .25s;background: rgba(0, 0, 0, 0.5);";return e+=c.value?"":"right:-32rpx !important;transition-delay: 0.25s;"})),U((()=>{"style-2"===o.value.style&&(f&&clearTimeout(f.value),c.value=!1,f.value=setTimeout((()=>{c.value=!0,clearTimeout(f.value)}),200))})),(e,t)=>{const i=E,s=$;return g(),b(s,{class:T(["float-btn fixed z-1000",[k(o).style,k(o).bottomPosition,"decorate"==k(l).mode?"float-btn-border":""]]),style:_(k(n))},{default:y((()=>["style-1"===k(o).style?(g(),b(s,{key:0,class:"flex flex-col items-center p-[24rpx]",style:_(k(a))},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),b(s,{key:t,onClick:t=>k(l).toRedirect(e.link),class:T({"flex items-center justify-center":!0,"mb-[20rpx]":k(o).list.length!=t+1}),style:_(k(r))},{default:y((()=>[e&&e.imageUrl?(g(),b(i,{key:0,style:_(k(r)),src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["style","src"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFit",style:_(k(r))},null,8,["src","style"]))])),_:2},1032,["onClick","class","style"])))),128))])),_:1},8,["style"])):S("v-if",!0),S(' <view v-if="diyComponent.style===\'style-2\'" class="relative w-[3rpx] h-[3rpx]">\n          <view class="py-[14rpx] overflow-hidden absolute right-[25rpx] top-[1rpx] transform -translate-y-1/2" :style="styleTwoWarpCss">\n            <swiper :style="{\'width\':diyComponent.imageSize * 2+24+\'rpx\',\'height\':diyComponent.imageSize * 2+44+\'rpx !important\',}" circular>\n              <swiper-item v-for="(item,index) in diyComponent.list" :key="index">\n                <view @click="diyStore.toRedirect(item.link)" class="px-[12rpx] flex flex-col items-center justify-center">\n                  <image v-if="item && item.imageUrl" :style="floatBtnItemCss" :src="img(item.imageUrl)" mode="aspectFit" />\n                  <image v-else :src="img(\'static/resource/images/diy/figure.png\')" mode="aspectFit" :style="floatBtnItemCss"/>\n                  <view class="text-[24rpx] text-[303133] text-center mt-[20rpx]">{{ item.link.title }}</view>\n                </view>\n              </swiper-item>\n            </swiper>\n          </view>\n          <view class="w-[60rpx] h-[60rpx] absolute right-[-64rpx] top-[1rpx] transform -translate-y-1/2 rounded-[30rpx] flex items-center" :style="styleTwoSphere">\n            <text class="!text-[60rpx] iconfont iconxiaolian-1 text-[var(--primary-color)] font-400  transform rotate-90 translate-x-[-13rpx]"></text>\n          </view>\n        </view> ')])),_:1},8,["class","style"])}}}),[["__scopeId","data-v-7dd4888c"]]),Lt=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(),r=u(null),n=u(!1),p=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),m=s((()=>l.global)),z=u({id:0,name:"",mobile:"",province_id:0,city_id:0,district_id:0,lat:"",lng:"",address:"",address_name:"",full_address:"",is_default:0,area:""}),E=s((()=>{let e="请选择";return"province/city/district/address"==p.value.addressFormat?e+="省/市/区/街道":"province/city/district/street"==p.value.addressFormat?e+="省/市/区/街道(镇)":"province/city/district"==p.value.addressFormat?e+="省/市/区(县)":"province/city"==p.value.addressFormat?e+="省/市":e+="省份",e})),I=s((()=>{var e="";return e+="position:relative;",p.value.componentStartBgColor&&(p.value.componentStartBgColor&&p.value.componentEndBgColor?e+=`background:linear-gradient(${p.value.componentGradientAngle},${p.value.componentStartBgColor},${p.value.componentEndBgColor});`:e+="background-color:"+p.value.componentStartBgColor+";"),p.value.componentBgUrl&&(e+=`background-image:url('${d(p.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),p.value.topRounded&&(e+="border-top-left-radius:"+2*p.value.topRounded+"rpx;"),p.value.topRounded&&(e+="border-top-right-radius:"+2*p.value.topRounded+"rpx;"),p.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*p.value.bottomRounded+"rpx;"),p.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*p.value.bottomRounded+"rpx;"),e})),M=e=>{!n.value||z.value.province_id!=e.province.id&&z.value.city_id==e.city.id&&z.value.district_id==e.district.id||(z.value.lat="",z.value.lng=""),z.value.province_id=e.province.id||0,z.value.city_id=e.city.id||0,z.value.district_id=e.district.id||0,z.value.area=`${e.province.name||""}${e.city.name||""}${e.district.name||""}`,p.value.field.value=z.value.area,n.value=!1};c((()=>{A(),"decorate"==o.mode&&f((()=>p.value),((e,t)=>{e&&"FormAddress"==e.componentName&&A()}))}));const A=()=>{},j=()=>{n.value=!0,a.value.open()},N=()=>{let e=[];if(p.value.autofill){let t={title:"已自动填充"};e.push(t)}if(p.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},W=()=>{p.value.field.value=""},V=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return p.value.field.required&&""==p.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message="111"),r.value=e,e},reset:W}),(e,t)=>{const l=D,n=$,i=O,s=L,d=v(x("area-select"),Ue);return g(),R(B,null,[k(p).viewFormDetail?(g(),b(n,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(g(),b(n,{key:0,class:"base-layout-one"},{default:y((()=>[h(n,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(p).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(p).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(g(),b(n,{key:1,class:"base-layout-two"},{default:y((()=>[h(n,{class:"detail-two-content"},{default:y((()=>[h(n,null,{default:y((()=>[w(C(k(p).field.name),1)])),_:1}),h(n,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(p).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(n,{key:1,style:_(k(I)),class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(g(),b(n,{key:0,class:"base-layout-one"},{default:y((()=>[h(n,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx","font-weight":k(p).fontWeight})},{default:y((()=>[w(C(k(p).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(p).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(p).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(p).field.remark.text?(g(),b(n,{key:0,class:"layout-one-remark",style:_({color:k(p).field.remark.color,fontSize:2*k(p).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(p).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(g(),b(n,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(n,{class:"flex layout-one-content justify-between items-center"},{default:y((()=>[h(i,{type:"text",class:"flex-1",placeholder:k(E),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(p).fontSize+"rpx"},style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx"}),modelValue:k(p).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(p).field.value=e),disabled:k(V),onClick:j},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),k(p).field.value?(g(),b(n,{key:0,class:"text-[var(--primary-color)]",onClick:W},{default:y((()=>[w(" 清除 ")])),_:1})):S("v-if",!0)])),_:1}),"province/city/district/address"==k(p).addressFormat?(g(),b(s,{key:2,type:"textarea",class:"layout-one-content mt-2 w-full",placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(p).fontSize+"rpx"},placeholder:"详细地址(如小区门牌号)",disabled:k(V)},null,8,["placeholder-style","disabled"])):S("v-if",!0),N().length?(g(),b(n,{key:3,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(N(),((t,l)=>(g(),b(n,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(g(),b(n,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(p).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(n,{class:T(["layout-two-wrap",{"no-border":!k(m).borderControl}])},{default:y((()=>[h(n,{class:T(["layout-two-label",{"justify-start":"left"==k(m).completeAlign,"justify-end":"right"==k(m).completeAlign}])},{default:y((()=>[k(p).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(p).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx","font-weight":k(p).fontWeight})},{default:y((()=>[w(C(k(p).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{type:"text",class:"layout-two-content no-flex",placeholder:k(E),placeholderClass:"layout-two-input-placeholder",onClick:j,"placeholder-style":{"font-size":2*k(p).fontSize+"rpx"},style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx"}),modelValue:k(p).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(p).field.value=e),disabled:k(V)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),"province/city/district/address"==k(p).addressFormat?(g(),b(s,{key:1,type:"textarea",class:"layout-one-content p-2 mt-2 w-full",placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(p).fontSize+"rpx"},placeholder:"详细地址(如小区门牌号)",disabled:k(V)},null,8,["placeholder-style","disabled"])):S("v-if",!0),r.value&&!r.value.code?(g(),b(n,{key:2,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(p).field.remark.text?(g(),b(n,{key:3,class:"layout-two-remark",style:_({color:k(p).field.remark.color,fontSize:2*k(p).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(p).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),N().length?(g(),b(n,{key:4,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(N(),((t,l)=>(g(),b(n,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(n,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"])),h(d,{ref_key:"areaRef",ref:a,onComplete:M,"area-id":z.value.district_id},null,8,["area-id"])],64)}}}),[["__scopeId","data-v-7ae9807f"]]),Gt=We(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=s((()=>l.data||"已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息")),a=u(!1),r=()=>{a.value=!1};return t({open:()=>{a.value=!0}}),(e,t)=>{const l=$,n=D,i=v(x("u-popup"),Ve);return g(),b(l,{onTouchmove:t[0]||(t[0]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(i,{show:a.value,onClose:r,zIndex:"500",mode:"center",round:8},{default:y((()=>[h(l,{class:"flex flex-col items-center w-[640rpx] pt-[50rpx]"},{default:y((()=>[h(l,{class:"text-[32rpx] font-bold"},{default:y((()=>[w(C(k(P)("diyForm.prompt")),1)])),_:1}),h(l,{class:"text-center px-[40rpx] py-[30rpx] leading-[1.5] min-h-[90rpx]"},{default:y((()=>[w(C(k(o)),1)])),_:1}),h(l,{class:"flex items-center justify-center border-solid border-[0] border-t-[2rpx] border-[#e6e6e6] w-[100%] h-[90rpx] text-[28rpx]"},{default:y((()=>[h(n,{onClick:r},{default:y((()=>[w(C(k(P)("diyForm.know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-4bdc5551"]]),qt=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u([]),r=u(!1),n=u([]),p=u(null),m=u(null),z=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),E=s((()=>l.global)),M=s((()=>`${z.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),A=()=>{let e=[];if(z.value.autofill){let t={title:"已自动填充"};e.push(t)}if(z.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},j=e=>{"privacy"==e&&m.value.open()},N=s((()=>{var e="";return e+="position:relative;",z.value.componentStartBgColor&&z.value.componentEndBgColor?e+=`background:linear-gradient(${z.value.componentGradientAngle},${z.value.componentStartBgColor},${z.value.componentEndBgColor});`:z.value.componentStartBgColor?e+="background-color:"+z.value.componentStartBgColor+";":z.value.componentEndBgColor&&(e+="background-color:"+z.value.componentEndBgColor+";"),z.value.componentBgUrl&&(e+=`background-image:url('${d(z.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),z.value.topRounded&&(e+="border-top-left-radius:"+2*z.value.topRounded+"rpx;"),z.value.topRounded&&(e+="border-top-right-radius:"+2*z.value.topRounded+"rpx;"),z.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*z.value.bottomRounded+"rpx;"),z.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*z.value.bottomRounded+"rpx;"),e}));c((()=>{W(),"decorate"==o.mode&&f((()=>z.value),((e,t)=>{e&&"FormCheckbox"==e.componentName&&W()})),"style-3"==z.value.style&&(n.value=z.value.field.value.map((e=>e.id))),z.value.field.value.length>0&&(a.value=z.value.field.value.map((e=>e.id)))}));const W=()=>{},V=s((()=>{let e="";return e+=`请选择${z.value.field.name}`,e})),Y=u([]),H=()=>{r.value=!1,n.value=Y.value.map((e=>e.id))},U=()=>{r.value=!1;const e=z.value.options.filter((e=>n.value.includes(e.id)));z.value.field.value=e.map((e=>({id:e.id,text:e.text})))},O=s((()=>"decorate"===o.mode)),L=()=>{O.value||(Y.value=[...z.value.field.value],r.value=!0)},G=e=>{const t=z.value.options.filter((t=>e.includes(t.id)));z.value.field.value=t.map((e=>({id:e.id,text:e.text})))},q=e=>{if(O.value)return;const t=e.id,l=a.value.indexOf(t);l>-1?a.value.splice(l,1):a.value.push(t);const o=z.value.options.filter((e=>a.value.includes(e.id)));z.value.field.value=o.map((e=>({id:e.id,text:e.text})))};return t({verify:()=>{const e={code:!0,message:""};return z.value.field.required&&""==z.value.field.value.length&&"decorate"!=o.mode&&(e.code=!1,e.message=V.value),p.value=e,e},reset:()=>{n.value=[],a.value=[],z.value.field.value=[]}}),(e,t)=>{const l=$,i=D,s=v(x("u-checkbox"),Pe),d=v(x("u-checkbox-group"),Oe),u=I,c=v(x("u-popup"),Ve);return k(z).viewFormDetail?(g(),b(l,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(E).completeLayout?(g(),b(l,{key:0,class:"base-layout-one"},{default:y((()=>[h(l,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(z).field.name),1)])),_:1}),h(l,{class:"flex detail-one-content-value"},{default:y((()=>[(g(!0),R(B,null,F(k(z).field.value,((e,t)=>(g(),b(l,{key:t},{default:y((()=>[w(C(e.text)+" ",1),t!==k(z).field.value.length-1?(g(),b(i,{key:0},{default:y((()=>[w("、")])),_:1})):S("v-if",!0)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(E).completeLayout?(g(),b(l,{key:1,class:"base-layout-two"},{default:y((()=>[h(l,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(z).field.name),1)])),_:1}),h(l,{class:"detail-two-content-value flex w-[80%] justify-end"},{default:y((()=>[(g(!0),R(B,null,F(k(z).field.value,((e,t)=>(g(),b(l,{key:t},{default:y((()=>[h(i,null,{default:y((()=>[w(C(e.text),1)])),_:2},1024),t!==k(z).field.value.length-1?(g(),b(i,{key:0},{default:y((()=>[w("、")])),_:1})):S("v-if",!0)])),_:2},1024)))),128)),k(z).field.value&&k(z).field.value.length?S("v-if",!0):(g(),b(i,{key:0},{default:y((()=>[w(C(k(P)("notHave")),1)])),_:1})),k(z).isShowArrow?(g(),b(i,{key:1,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(l,{key:1,style:_(k(N)),class:"form-item-frame"},{default:y((()=>["style-1"==k(E).completeLayout?(g(),b(l,{key:0,class:"base-layout-one"},{default:y((()=>[h(l,{class:"layout-one-label"},{default:y((()=>[h(i,{class:"name",style:_({color:k(z).textColor,"font-size":2*k(z).fontSize+"rpx","font-weight":k(z).fontWeight})},{default:y((()=>[w(C(k(z).field.name),1)])),_:1},8,["style"]),h(i,{class:"required"},{default:y((()=>[w(C(k(z).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(z).isHidden?(g(),b(i,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(z).field.remark.text?(g(),b(l,{key:0,class:"layout-one-remark",style:_({color:k(z).field.remark.color,fontSize:2*k(z).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(z).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),p.value&&!p.value.code?(g(),b(l,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(p.value.message),1)])),_:1})):S("v-if",!0),"style-1"==k(z).style?(g(),b(l,{key:2,class:"layout-one-content"},{default:y((()=>[h(d,{modelValue:a.value,"onUpdate:modelValue":t[0]||(t[0]=e=>a.value=e),onChange:G,iconPlacement:"left"},{default:y((()=>[(g(!0),R(B,null,F(k(z).options,((e,t)=>(g(),b(l,{key:t,class:"mr-[40rpx]"},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k(z).fontSize+"rpx",labelColor:k(z).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1})):S("v-if",!0),"style-2"==k(z).style?(g(),b(d,{key:3,modelValue:a.value,"onUpdate:modelValue":t[1]||(t[1]=e=>a.value=e),onChange:G,iconPlacement:"left",placement:"column"},{default:y((()=>[(g(!0),R(B,null,F(k(z).options,((e,t)=>(g(),b(l,{key:t,onClick:t=>q(e),class:T(["layout-one-content mb-[16rpx]",{"!mb-[0]":k(z).options.length-1==t}])},{default:y((()=>[h(s,{class:"!m-[0]",activeColor:"var(--primary-color)",labelSize:2*k(z).fontSize+"rpx",labelColor:k(z).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])):S("v-if",!0),"style-3"==k(z).style?(g(),b(l,{key:4,onClick:L,class:"layout-one-content justify-between"},{default:y((()=>[k(z).field.value&&k(z).field.value.length?(g(),b(l,{key:0},{default:y((()=>[(g(!0),R(B,null,F(k(z).field.value,((e,t)=>(g(),b(i,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k(z).textColor,"font-size":2*k(z).fontSize+"rpx"})},{default:y((()=>[w(C(e.text),1),t!==k(z).field.value.length-1?(g(),b(i,{key:0},{default:y((()=>[w(",")])),_:1})):S("v-if",!0)])),_:2},1032,["style"])))),256))])),_:1})):(g(),b(i,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k(z).fontSize+"rpx"})},{default:y((()=>[w(C(k(V)),1)])),_:1},8,["style"])),h(i,{class:T(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k(z).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})):S("v-if",!0),A().length?(g(),b(l,{key:5,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(A(),((e,t)=>(g(),b(l,{key:t,onClick:t=>j(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(E).completeLayout?(g(),b(l,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(z).isHidden?(g(),b(i,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(l,{class:T(["layout-two-wrap",{"!pb-[20rpx]":("style-2"==k(z).style||"style-3"==k(z).style)&&k(E).borderControl,"no-border":!k(E).borderControl}])},{default:y((()=>[h(l,{class:T(["layout-two-label",{"justify-start":"left"==k(E).completeAlign,"justify-end":"right"==k(E).completeAlign}])},{default:y((()=>[k(z).field.required?(g(),b(i,{key:0,class:"required"},{default:y((()=>[w(C(k(z).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(i,{class:"name",style:_({color:k(z).textColor,"font-size":2*k(z).fontSize+"rpx","font-weight":k(z).fontWeight})},{default:y((()=>[w(C(k(z).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),"style-1"==k(z).style?(g(),b(l,{key:0,class:"layout-two-content"},{default:y((()=>[h(d,{modelValue:a.value,"onUpdate:modelValue":t[2]||(t[2]=e=>a.value=e),onChange:G,iconPlacement:"left",class:"justify-end"},{default:y((()=>[(g(!0),R(B,null,F(k(z).options,((e,t)=>(g(),b(l,{class:"ml-[30rpx]"},{default:y((()=>[(g(),b(s,{activeColor:"var(--primary-color)",labelSize:2*k(z).fontSize+"rpx",labelColor:k(z).textColor,key:t,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"]))])),_:2},1024)))),256))])),_:1},8,["modelValue"])])),_:1})):S("v-if",!0),"style-2"==k(z).style?(g(),b(l,{key:1,class:"layout-two-content"},{default:y((()=>[h(l,{class:"justify-end w-full"},{default:y((()=>[h(d,{modelValue:a.value,"onUpdate:modelValue":t[3]||(t[3]=e=>a.value=e),placement:"column",onChange:G,iconPlacement:"left"},{default:y((()=>[(g(!0),R(B,null,F(k(z).options,((e,t)=>(g(),b(l,{key:t,onClick:t=>q(e),class:T(["border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] flex items-center h-[80rpx] mb-[16rpx] px-[16rpx] box-border",{"mb-[0]":k(z).options.length==t+1}])},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:2*k(z).fontSize+"rpx",labelColor:k(z).textColor,class:"!m-[0]",label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(z).style?(g(),b(l,{key:2,class:"layout-two-content"},{default:y((()=>[h(l,{onClick:L,class:"px-[16rpx] box-border h-[80rpx] flex items-center justify-between border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] w-[100%]"},{default:y((()=>[k(z).field.value&&k(z).field.value.length?(g(),b(l,{key:0},{default:y((()=>[(g(!0),R(B,null,F(k(z).field.value,((e,t)=>(g(),b(i,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k(z).textColor,"font-size":2*k(z).fontSize+"rpx"})},{default:y((()=>[w(C(e.text)+" ",1),t!==k(z).field.value.length-1?(g(),b(i,{key:0},{default:y((()=>[w(",")])),_:1})):S("v-if",!0)])),_:2},1032,["style"])))),256))])),_:1})):(g(),b(i,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k(z).fontSize+"rpx"})},{default:y((()=>[w(C(k(V)),1)])),_:1},8,["style"])),h(i,{class:T(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k(z).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),p.value&&!p.value.code?(g(),b(l,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(p.value.message),1)])),_:1})):S("v-if",!0),k(z).field.remark.text?(g(),b(l,{key:2,class:"layout-two-remark",style:_({color:k(z).field.remark.color,fontSize:2*k(z).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(z).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),A().length?(g(),b(l,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(A(),((e,t)=>(g(),b(l,{key:t,onClick:t=>j(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),S(" 样式三，下拉弹窗 "),h(c,{show:r.value,mode:"bottom",onClose:t[5]||(t[5]=e=>r.value=!1)},{default:y((()=>[h(l,{class:"p-[15rpx]"},{default:y((()=>[h(u,{"scroll-y":"true",class:"max-h-[450rpx] px-[14rpx] box-border"},{default:y((()=>[h(d,{modelValue:n.value,"onUpdate:modelValue":t[4]||(t[4]=e=>n.value=e),placement:"column",iconPlacement:"right"},{default:y((()=>[(g(!0),R(B,null,F(k(z).options,((e,t)=>(g(),b(l,{key:t,class:"border-solid border-[0] border-b-[2rpx] border-[#e6e6e6] py-[20rpx]"},{default:y((()=>[h(s,{activeColor:"var(--primary-color)",labelSize:"30rpx",labelColor:"#333",style:{width:"100%"},label:e.text,name:e.id},null,8,["label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1}),h(l,{class:"flex items-center pt-[20rpx]"},{default:y((()=>[h(l,{onClick:H,class:"flex-1 flex justify-center h-[70rpx] leading-[70rpx] text-[#333] bg-[#eee] text-[26rpx] border-[0] font-500 rounded-[10rpx] mr-[20rpx]"},{default:y((()=>[w(C(k(P)("cancel")),1)])),_:1}),h(l,{onClick:U,class:"flex-1 flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] rounded-[10rpx]"},{default:y((()=>[w(C(k(P)("confirm")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"]),"decorate"==k(o).mode?(g(),b(l,{key:2,class:"form-item-mask"})):S("v-if",!0),S(" 隐私弹窗 "),h(Gt,{ref_key:"formPrivacyRef",ref:m,data:k(M)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-d25a3fa8"]]);const Xt=We({name:"u-calendar-header",mixins:[G,q],props:{title:{type:String,default:""},subtitle:{type:String,default:""},showTitle:{type:Boolean,default:!0},showSubtitle:{type:Boolean,default:!0}},data:()=>({}),methods:{name(){}}},[["render",function(e,t,l,o,a,r){const n=D,i=$;return g(),b(i,{class:"u-calendar-header u-border-bottom"},{default:y((()=>[l.showTitle?(g(),b(n,{key:0,class:"u-calendar-header__title"},{default:y((()=>[w(C(l.title),1)])),_:1})):S("v-if",!0),l.showSubtitle?(g(),b(n,{key:1,class:"u-calendar-header__subtitle"},{default:y((()=>[w(C(l.subtitle),1)])),_:1})):S("v-if",!0),h(i,{class:"u-calendar-header__weekdays"},{default:y((()=>[h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("一")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("二")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("三")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("四")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("五")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("六")])),_:1}),h(n,{class:"u-calendar-header__weekdays__weekday"},{default:y((()=>[w("日")])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-583c0179"]]);var Zt=1e3,Jt=6e4,Qt=36e5,Kt="millisecond",el="second",tl="minute",ll="hour",ol="day",al="week",rl="month",nl="quarter",il="year",sl="date",dl="Invalid Date",ul=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,cl=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;const fl={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],l=e%100;return"["+e+(t[(l-20)%10]||t[l]||t[0])+"]"}};var pl=function(e,t,l){var o=String(e);return!o||o.length>=t?e:""+Array(t+1-o.length).join(l)+e};const ml={s:pl,z:function(e){var t=-e.utcOffset(),l=Math.abs(t),o=Math.floor(l/60),a=l%60;return(t<=0?"+":"-")+pl(o,2,"0")+":"+pl(a,2,"0")},m:function e(t,l){if(t.date()<l.date())return-e(l,t);var o=12*(l.year()-t.year())+(l.month()-t.month()),a=t.clone().add(o,rl),r=l-a<0,n=t.clone().add(o+(r?-1:1),rl);return+(-(o+(l-a)/(r?a-n:n-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:rl,y:il,w:al,d:ol,D:sl,h:ll,m:tl,s:el,ms:Kt,Q:nl}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};var vl="en",xl={};xl[vl]=fl;var gl="$isDayjsObject",bl=function(e){return e instanceof kl||!(!e||!e[gl])},yl=function e(t,l,o){var a;if(!t)return vl;if("string"==typeof t){var r=t.toLowerCase();xl[r]&&(a=r),l&&(xl[r]=l,a=r);var n=t.split("-");if(!a&&n.length>1)return e(n[0])}else{var i=t.name;xl[i]=t,a=i}return!o&&a&&(vl=a),a||!o&&vl},hl=function(e,t){if(bl(e))return e.clone();var l="object"==typeof t?t:{};return l.date=e,l.args=arguments,new kl(l)},_l=ml;_l.l=yl,_l.i=bl,_l.w=function(e,t){return hl(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var kl=function(){function e(e){this.$L=yl(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[gl]=!0}var t=e.prototype;return t.parse=function(e){this.$d=function(e){var t=e.date,l=e.utc;if(null===t)return new Date(NaN);if(_l.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var o=t.match(ul);if(o){var a=o[2]-1||0,r=(o[7]||"0").substring(0,3);return l?new Date(Date.UTC(o[1],a,o[3]||1,o[4]||0,o[5]||0,o[6]||0,r)):new Date(o[1],a,o[3]||1,o[4]||0,o[5]||0,o[6]||0,r)}}return new Date(t)}(e),this.init()},t.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},t.$utils=function(){return _l},t.isValid=function(){return!(this.$d.toString()===dl)},t.isSame=function(e,t){var l=hl(e);return this.startOf(t)<=l&&l<=this.endOf(t)},t.isAfter=function(e,t){return hl(e)<this.startOf(t)},t.isBefore=function(e,t){return this.endOf(t)<hl(e)},t.$g=function(e,t,l){return _l.u(e)?this[t]:this.set(l,e)},t.unix=function(){return Math.floor(this.valueOf()/1e3)},t.valueOf=function(){return this.$d.getTime()},t.startOf=function(e,t){var l=this,o=!!_l.u(t)||t,a=_l.p(e),r=function(e,t){var a=_l.w(l.$u?Date.UTC(l.$y,t,e):new Date(l.$y,t,e),l);return o?a:a.endOf(ol)},n=function(e,t){return _l.w(l.toDate()[e].apply(l.toDate("s"),(o?[0,0,0,0]:[23,59,59,999]).slice(t)),l)},i=this.$W,s=this.$M,d=this.$D,u="set"+(this.$u?"UTC":"");switch(a){case il:return o?r(1,0):r(31,11);case rl:return o?r(1,s):r(0,s+1);case al:var c=this.$locale().weekStart||0,f=(i<c?i+7:i)-c;return r(o?d-f:d+(6-f),s);case ol:case sl:return n(u+"Hours",0);case ll:return n(u+"Minutes",1);case tl:return n(u+"Seconds",2);case el:return n(u+"Milliseconds",3);default:return this.clone()}},t.endOf=function(e){return this.startOf(e,!1)},t.$set=function(e,t){var l,o=_l.p(e),a="set"+(this.$u?"UTC":""),r=(l={},l[ol]=a+"Date",l[sl]=a+"Date",l[rl]=a+"Month",l[il]=a+"FullYear",l[ll]=a+"Hours",l[tl]=a+"Minutes",l[el]=a+"Seconds",l[Kt]=a+"Milliseconds",l)[o],n=o===ol?this.$D+(t-this.$W):t;if(o===rl||o===il){var i=this.clone().set(sl,1);i.$d[r](n),i.init(),this.$d=i.set(sl,Math.min(this.$D,i.daysInMonth())).$d}else r&&this.$d[r](n);return this.init(),this},t.set=function(e,t){return this.clone().$set(e,t)},t.get=function(e){return this[_l.p(e)]()},t.add=function(e,t){var l,o=this;e=Number(e);var a=_l.p(t),r=function(t){var l=hl(o);return _l.w(l.date(l.date()+Math.round(t*e)),o)};if(a===rl)return this.set(rl,this.$M+e);if(a===il)return this.set(il,this.$y+e);if(a===ol)return r(1);if(a===al)return r(7);var n=(l={},l[tl]=Jt,l[ll]=Qt,l[el]=Zt,l)[a]||1,i=this.$d.getTime()+e*n;return _l.w(i,this)},t.subtract=function(e,t){return this.add(-1*e,t)},t.format=function(e){var t=this,l=this.$locale();if(!this.isValid())return l.invalidDate||dl;var o=e||"YYYY-MM-DDTHH:mm:ssZ",a=_l.z(this),r=this.$H,n=this.$m,i=this.$M,s=l.weekdays,d=l.months,u=l.meridiem,c=function(e,l,a,r){return e&&(e[l]||e(t,o))||a[l].slice(0,r)},f=function(e){return _l.s(r%12||12,e,"0")},p=u||function(e,t,l){var o=e<12?"AM":"PM";return l?o.toLowerCase():o};return o.replace(cl,(function(e,o){return o||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return _l.s(t.$y,4,"0");case"M":return i+1;case"MM":return _l.s(i+1,2,"0");case"MMM":return c(l.monthsShort,i,d,3);case"MMMM":return c(d,i);case"D":return t.$D;case"DD":return _l.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return c(l.weekdaysMin,t.$W,s,2);case"ddd":return c(l.weekdaysShort,t.$W,s,3);case"dddd":return s[t.$W];case"H":return String(r);case"HH":return _l.s(r,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return p(r,n,!0);case"A":return p(r,n,!1);case"m":return String(n);case"mm":return _l.s(n,2,"0");case"s":return String(t.$s);case"ss":return _l.s(t.$s,2,"0");case"SSS":return _l.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")}))},t.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},t.diff=function(e,t,l){var o,a=this,r=_l.p(t),n=hl(e),i=(n.utcOffset()-this.utcOffset())*Jt,s=this-n,d=function(){return _l.m(a,n)};switch(r){case il:o=d()/12;break;case rl:o=d();break;case nl:o=d()/3;break;case al:o=(s-i)/6048e5;break;case ol:o=(s-i)/864e5;break;case ll:o=s/Qt;break;case tl:o=s/Jt;break;case el:o=s/Zt;break;default:o=s}return l?o:_l.a(o)},t.daysInMonth=function(){return this.endOf(rl).$D},t.$locale=function(){return xl[this.$L]},t.locale=function(e,t){if(!e)return this.$L;var l=this.clone(),o=yl(e,t,!0);return o&&(l.$L=o),l},t.clone=function(){return _l.w(this.$d,this)},t.toDate=function(){return new Date(this.valueOf())},t.toJSON=function(){return this.isValid()?this.toISOString():null},t.toISOString=function(){return this.$d.toISOString()},t.toString=function(){return this.$d.toUTCString()},e}(),wl=kl.prototype;hl.prototype=wl,[["$ms",Kt],["$s",el],["$m",tl],["$H",ll],["$W",ol],["$M",rl],["$y",il],["$D",sl]].forEach((function(e){wl[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),hl.extend=function(e,t){return e.$i||(e(t,kl,hl),e.$i=!0),hl},hl.locale=yl,hl.isDayjs=bl,hl.unix=function(e){return hl(1e3*e)},hl.en=xl[vl],hl.Ls=xl,hl.p={};const Cl={name:"u-calendar-month",mixins:[G,q],props:{showMark:{type:Boolean,default:!0},color:{type:String,default:"#3c9cff"},months:{type:Array,default:()=>[]},mode:{type:String,default:"single"},rowHeight:{type:[String,Number],default:58},maxCount:{type:[String,Number],default:1/0},startText:{type:String,default:"开始"},endText:{type:String,default:"结束"},defaultDate:{type:[Array,String,Date],default:null},minDate:{type:[String,Number],default:0},maxDate:{type:[String,Number],default:0},maxMonth:{type:[String,Number],default:2},readonly:{type:Boolean,default:()=>X.calendar.readonly},maxRange:{type:[Number,String],default:1/0},rangePrompt:{type:String,default:""},showRangePrompt:{type:Boolean,default:!0},allowSameDay:{type:Boolean,default:!1}},data:()=>({width:0,item:{},selected:[]}),watch:{selectedChange:{immediate:!0,handler(e){this.setDefaultDate()}}},computed:{selectedChange(){return[this.minDate,this.maxDate,this.defaultDate]},dayStyle(e,t,l){return(e,t,l)=>{const o={};let a=l.week;const r=Number(parseFloat(this.width/7).toFixed(3).slice(0,-1));return o.height=Z(this.rowHeight),0===t&&(a=(0===a?7:a)-1,o.marginLeft=Z(a*r)),"range"===this.mode&&(o.paddingLeft=0,o.paddingRight=0,o.paddingBottom=0,o.paddingTop=0),o}},daySelectStyle(){return(e,t,l)=>{let o=hl(l.date).format("YYYY-MM-DD"),a={};if(this.selected.some((e=>this.dateSame(e,o)))&&(a.backgroundColor=this.color),"single"===this.mode)o===this.selected[0]&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px",a.borderTopRightRadius="3px",a.borderBottomRightRadius="3px");else if("range"===this.mode)if(this.selected.length>=2){const e=this.selected.length-1;this.dateSame(o,this.selected[0])&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px"),this.dateSame(o,this.selected[e])&&(a.borderTopRightRadius="3px",a.borderBottomRightRadius="3px"),hl(o).isAfter(hl(this.selected[0]))&&hl(o).isBefore(hl(this.selected[e]))&&(a.backgroundColor=J(this.color,"#ffffff",100)[90],a.opacity=.7)}else 1===this.selected.length&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px");else this.selected.some((e=>this.dateSame(e,o)))&&(a.borderTopLeftRadius="3px",a.borderBottomLeftRadius="3px",a.borderTopRightRadius="3px",a.borderBottomRightRadius="3px");return a}},textStyle(){return e=>{const t=hl(e.date).format("YYYY-MM-DD"),l={};if(this.selected.some((e=>this.dateSame(e,t)))&&(l.color="#ffffff"),"range"===this.mode){const e=this.selected.length-1;hl(t).isAfter(hl(this.selected[0]))&&hl(t).isBefore(hl(this.selected[e]))&&(l.color=this.color)}return l}},getBottomInfo(){return(e,t,l)=>{const o=hl(l.date).format("YYYY-MM-DD"),a=l.bottomInfo;if("range"===this.mode&&this.selected.length>0){if(1===this.selected.length)return this.dateSame(o,this.selected[0])?this.startText:a;{const e=this.selected.length-1;return this.dateSame(o,this.selected[0])&&this.dateSame(o,this.selected[1])&&1===e?`${this.startText}/${this.endText}`:this.dateSame(o,this.selected[0])?this.startText:this.dateSame(o,this.selected[e])?this.endText:a}}return a}}},mounted(){this.init()},methods:{init(){this.$emit("monthSelected",this.selected),this.$nextTick((()=>{Q(10).then((()=>{this.getWrapperWidth(),this.getMonthRect()}))}))},dateSame:(e,t)=>hl(e).isSame(hl(t)),getWrapperWidth(){this.$uGetRect(".u-calendar-month-wrapper").then((e=>{this.width=e.width}))},getMonthRect(){const e=this.months.map(((e,t)=>this.getMonthRectByPromise(`u-calendar-month-${t}`)));Promise.all(e).then((e=>{let t=1;const l=[];for(let o=0;o<this.months.length;o++)l[o]=t,t+=e[o].height;this.$emit("updateMonthTop",l)}))},getMonthRectByPromise(e){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))},clickHandler(e,t,l){if(this.readonly)return;this.item=l;const o=hl(l.date).format("YYYY-MM-DD");if(l.disabled)return;let a=K(this.selected);if("single"===this.mode)a=[o];else if("multiple"===this.mode)if(a.some((e=>this.dateSame(e,o)))){const e=a.findIndex((e=>e===o));a.splice(e,1)}else a.length<this.maxCount&&a.push(o);else if(0===a.length||a.length>=2)a=[o];else if(1===a.length){const e=a[0];if(hl(o).isBefore(e))a=[o];else if(hl(o).isAfter(e)){if(hl(hl(o).subtract(this.maxRange,"day")).isAfter(hl(a[0]))&&this.showRangePrompt)return void(this.rangePrompt?ee(this.rangePrompt):ee(`选择天数不能超过 ${this.maxRange} 天`));a.push(o);const e=a[0],t=a[1],l=[];let r=0;do{l.push(hl(e).add(r,"day").format("YYYY-MM-DD")),r++}while(hl(e).add(r,"day").isBefore(hl(t)));l.push(t),a=l}else{if(a[0]===o&&!this.allowSameDay)return;a.push(o)}}this.setSelected(a)},setDefaultDate(){if(!this.defaultDate){const e=[hl().format("YYYY-MM-DD")];return this.setSelected(e,!1)}let e=[];const t=this.minDate||hl().format("YYYY-MM-DD"),l=this.maxDate||hl(t).add(this.maxMonth-1,"month").format("YYYY-MM-DD");if("single"===this.mode)e=te.array(this.defaultDate)?[this.defaultDate[0]]:[hl(this.defaultDate).format("YYYY-MM-DD")];else{if(!te.array(this.defaultDate))return;e=this.defaultDate}e=e.filter((e=>hl(e).isAfter(hl(t).subtract(1,"day"))&&hl(e).isBefore(hl(l).add(1,"day")))),this.setSelected(e,!1)},setSelected(e,t=!0){this.selected=e,t&&this.$emit("monthSelected",this.selected,"tap")}}};const Sl={props:{title:{type:String,default:()=>X.calendar.title},showTitle:{type:Boolean,default:()=>X.calendar.showTitle},showSubtitle:{type:Boolean,default:()=>X.calendar.showSubtitle},mode:{type:String,default:()=>X.calendar.mode},startText:{type:String,default:()=>X.calendar.startText},endText:{type:String,default:()=>X.calendar.endText},customList:{type:Array,default:()=>X.calendar.customList},color:{type:String,default:()=>X.calendar.color},minDate:{type:[String,Number],default:()=>X.calendar.minDate},maxDate:{type:[String,Number],default:()=>X.calendar.maxDate},defaultDate:{type:[Array,String,Date,null],default:()=>X.calendar.defaultDate},maxCount:{type:[String,Number],default:()=>X.calendar.maxCount},rowHeight:{type:[String,Number],default:()=>X.calendar.rowHeight},formatter:{type:[Function,null],default:()=>X.calendar.formatter},showLunar:{type:Boolean,default:()=>X.calendar.showLunar},showMark:{type:Boolean,default:()=>X.calendar.showMark},confirmText:{type:String,default:()=>X.calendar.confirmText},confirmDisabledText:{type:String,default:()=>X.calendar.confirmDisabledText},show:{type:Boolean,default:()=>X.calendar.show},closeOnClickOverlay:{type:Boolean,default:()=>X.calendar.closeOnClickOverlay},readonly:{type:Boolean,default:()=>X.calendar.readonly},showConfirm:{type:Boolean,default:()=>X.calendar.showConfirm},maxRange:{type:[Number,String],default:()=>X.calendar.maxRange},rangePrompt:{type:String,default:()=>X.calendar.rangePrompt},showRangePrompt:{type:Boolean,default:()=>X.calendar.showRangePrompt},allowSameDay:{type:Boolean,default:()=>X.calendar.allowSameDay},round:{type:[Boolean,String,Number],default:()=>X.calendar.round},monthNum:{type:[Number,String],default:3}}};var Rl={lunarInfo:[19416,19168,42352,21717,53856,55632,91476,22176,39632,21970,19168,42422,42192,53840,119381,46400,54944,44450,38320,84343,18800,42160,46261,27216,27968,109396,11104,38256,21234,18800,25958,54432,59984,28309,23248,11104,100067,37600,116951,51536,54432,120998,46416,22176,107956,9680,37584,53938,43344,46423,27808,46416,86869,19872,42416,83315,21168,43432,59728,27296,44710,43856,19296,43748,42352,21088,62051,55632,23383,22176,38608,19925,19152,42192,54484,53840,54616,46400,46752,103846,38320,18864,43380,42160,45690,27216,27968,44870,43872,38256,19189,18800,25776,29859,59984,27480,23232,43872,38613,37600,51552,55636,54432,55888,30034,22176,43959,9680,37584,51893,43344,46240,47780,44368,21977,19360,42416,86390,21168,43312,31060,27296,44368,23378,19296,42726,42208,53856,60005,54576,23200,30371,38608,19195,19152,42192,118966,53840,54560,56645,46496,22224,21938,18864,42359,42160,43600,111189,27936,44448,84835,37744,18936,18800,25776,92326,59984,27424,108228,43744,41696,53987,51552,54615,54432,55888,23893,22176,42704,21972,21200,43448,43344,46240,46758,44368,21920,43940,42416,21168,45683,26928,29495,27296,44368,84821,19296,42352,21732,53600,59752,54560,55968,92838,22224,19168,43476,41680,53584,62034,54560],solarMonth:[31,28,31,30,31,30,31,31,30,31,30,31],Gan:["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"],Zhi:["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"],Animals:["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"],solarTerm:["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"],sTermInfo:["9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","9778397bd19801ec9210c965cc920e","97b6b97bd19801ec95f8c965cc920f","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd197c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bcf97c3598082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd19801ec9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bd07f1487f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b97bd197c36c9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b70c9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","977837f0e37f149b0723b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0723b06bd","7f07e7f0e37f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f595b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e37f14998083b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14898082b0723b02d5","7f07e7f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66aa89801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e26665b66a449801e9808297c35","665f67f0e37f1489801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722"],nStr1:["日","一","二","三","四","五","六","七","八","九","十"],nStr2:["初","十","廿","卅"],nStr3:["正","二","三","四","五","六","七","八","九","十","冬","腊"],lYearDays:function(e){var t,l=348;for(t=32768;t>8;t>>=1)l+=this.lunarInfo[e-1900]&t?1:0;return l+this.leapDays(e)},leapMonth:function(e){return 15&this.lunarInfo[e-1900]},leapDays:function(e){return this.leapMonth(e)?65536&this.lunarInfo[e-1900]?30:29:0},monthDays:function(e,t){return t>12||t<1?-1:this.lunarInfo[e-1900]&65536>>t?30:29},solarDays:function(e,t){if(t>12||t<1)return-1;var l=t-1;return 1==l?e%4==0&&e%100!=0||e%400==0?29:28:this.solarMonth[l]},toGanZhiYear:function(e){var t=(e-3)%10,l=(e-3)%12;return 0==t&&(t=10),0==l&&(l=12),this.Gan[t-1]+this.Zhi[l-1]},toAstro:function(e,t){return"魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯".substr(2*e-(t<[20,19,21,21,21,22,23,23,23,23,22,22][e-1]?2:0),2)+"座"},toGanZhi:function(e){return this.Gan[e%10]+this.Zhi[e%12]},getTerm:function(e,t){if(e<1900||e>2100)return-1;if(t<1||t>24)return-1;var l=this.sTermInfo[e-1900],o=[parseInt("0x"+l.substr(0,5)).toString(),parseInt("0x"+l.substr(5,5)).toString(),parseInt("0x"+l.substr(10,5)).toString(),parseInt("0x"+l.substr(15,5)).toString(),parseInt("0x"+l.substr(20,5)).toString(),parseInt("0x"+l.substr(25,5)).toString()],a=[o[0].substr(0,1),o[0].substr(1,2),o[0].substr(3,1),o[0].substr(4,2),o[1].substr(0,1),o[1].substr(1,2),o[1].substr(3,1),o[1].substr(4,2),o[2].substr(0,1),o[2].substr(1,2),o[2].substr(3,1),o[2].substr(4,2),o[3].substr(0,1),o[3].substr(1,2),o[3].substr(3,1),o[3].substr(4,2),o[4].substr(0,1),o[4].substr(1,2),o[4].substr(3,1),o[4].substr(4,2),o[5].substr(0,1),o[5].substr(1,2),o[5].substr(3,1),o[5].substr(4,2)];return parseInt(a[t-1])},toChinaMonth:function(e){if(e>12||e<1)return-1;var t=this.nStr3[e-1];return t+="月"},toChinaDay:function(e){var t;switch(e){case 10:t="初十";break;case 20:t="二十";break;case 30:t="三十";break;default:t=this.nStr2[Math.floor(e/10)],t+=this.nStr1[e%10]}return t},getAnimal:function(e){return this.Animals[(e-4)%12]},solar2lunar:function(e,t,l){if(e<1900||e>2100)return-1;if(1900==e&&1==t&&l<31)return-1;if(e)o=new Date(e,parseInt(t)-1,l);else var o=new Date;var a,r=0,n=(e=o.getFullYear(),t=o.getMonth()+1,l=o.getDate(),(Date.UTC(o.getFullYear(),o.getMonth(),o.getDate())-Date.UTC(1900,0,31))/864e5);for(a=1900;a<2101&&n>0;a++)n-=r=this.lYearDays(a);n<0&&(n+=r,a--);var i=new Date,s=!1;i.getFullYear()==e&&i.getMonth()+1==t&&i.getDate()==l&&(s=!0);var d=o.getDay(),u=this.nStr1[d];0==d&&(d=7);var c=a,f=this.leapMonth(a),p=!1;for(a=1;a<13&&n>0;a++)f>0&&a==f+1&&0==p?(--a,p=!0,r=this.leapDays(c)):r=this.monthDays(c,a),1==p&&a==f+1&&(p=!1),n-=r;0==n&&f>0&&a==f+1&&(p?p=!1:(p=!0,--a)),n<0&&(n+=r,--a);var m=a,v=n+1,x=t-1,g=this.toGanZhiYear(c),b=this.getTerm(e,2*t-1),y=this.getTerm(e,2*t),h=this.toGanZhi(12*(e-1900)+t+11);l>=b&&(h=this.toGanZhi(12*(e-1900)+t+12));var _=!1,k=null;b==l&&(_=!0,k=this.solarTerm[2*t-2]),y==l&&(_=!0,k=this.solarTerm[2*t-1]);var w=Date.UTC(e,x,1,0,0,0,0)/864e5+25567+10,C=this.toGanZhi(w+l-1),S=this.toAstro(t,l);return{lYear:c,lMonth:m,lDay:v,Animal:this.getAnimal(c),IMonthCn:(p?"闰":"")+this.toChinaMonth(m),IDayCn:this.toChinaDay(v),cYear:e,cMonth:t,cDay:l,gzYear:g,gzMonth:h,gzDay:C,isToday:s,isLeap:p,nWeek:d,ncWeek:"星期"+u,isTerm:_,Term:k,astro:S}},lunar2solar:function(e,t,l,o){o=!!o;var a=this.leapMonth(e);if(this.leapDays(e),o&&a!=t)return-1;if(2100==e&&12==t&&l>1||1900==e&&1==t&&l<31)return-1;var r=this.monthDays(e,t),n=r;if(o&&(n=this.leapDays(e,t)),e<1900||e>2100||l>n)return-1;for(var i=0,s=1900;s<e;s++)i+=this.lYearDays(s);var d=0,u=!1;for(s=1;s<t;s++)d=this.leapMonth(e),u||d<=s&&d>0&&(i+=this.leapDays(e),u=!0),i+=this.monthDays(e,s);o&&(i+=r);var c=Date.UTC(1900,1,30,0,0,0),f=new Date(864e5*(i+l-31)+c),p=f.getUTCFullYear(),m=f.getUTCMonth()+1,v=f.getUTCDate();return this.solar2lunar(p,m,v)}};const Bl=We({name:"u-calendar",mixins:[G,q,Sl],components:{uHeader:Xt,uMonth:We(Cl,[["render",function(e,t,l,o,a,r){const n=D,i=$;return g(),b(i,{class:"u-calendar-month-wrapper",ref:"u-calendar-month-wrapper"},{default:y((()=>[(g(!0),R(B,null,F(l.months,((e,t)=>(g(),b(i,{key:t,class:T([`u-calendar-month-${t}`]),ref_for:!0,ref:`u-calendar-month-${t}`,id:`month-${t}`},{default:y((()=>[0!==t?(g(),b(n,{key:0,class:"u-calendar-month__title"},{default:y((()=>[w(C(e.year)+"年"+C(e.month)+"月",1)])),_:2},1024)):S("v-if",!0),h(i,{class:"u-calendar-month__days"},{default:y((()=>[l.showMark?(g(),b(i,{key:0,class:"u-calendar-month__days__month-mark-wrapper"},{default:y((()=>[h(n,{class:"u-calendar-month__days__month-mark-wrapper__text"},{default:y((()=>[w(C(e.month),1)])),_:2},1024)])),_:2},1024)):S("v-if",!0),(g(!0),R(B,null,F(e.date,((e,l)=>(g(),b(i,{class:T(["u-calendar-month__days__day",[e.selected&&"u-calendar-month__days__day__select--selected"]]),key:l,style:_([r.dayStyle(t,l,e)]),onClick:o=>r.clickHandler(t,l,e)},{default:y((()=>[h(i,{class:"u-calendar-month__days__day__select",style:_([r.daySelectStyle(t,l,e)])},{default:y((()=>[h(n,{class:T(["u-calendar-month__days__day__select__info",[e.disabled&&"u-calendar-month__days__day__select__info--disabled"]]),style:_([r.textStyle(e)])},{default:y((()=>[w(C(e.day),1)])),_:2},1032,["class","style"]),r.getBottomInfo(t,l,e)?(g(),b(n,{key:0,class:T(["u-calendar-month__days__day__select__buttom-info",[e.disabled&&"u-calendar-month__days__day__select__buttom-info--disabled"]]),style:_([r.textStyle(e)])},{default:y((()=>[w(C(r.getBottomInfo(t,l,e)),1)])),_:2},1032,["class","style"])):S("v-if",!0),e.dot?(g(),b(n,{key:1,class:"u-calendar-month__days__day__select__dot"})):S("v-if",!0)])),_:2},1032,["style"])])),_:2},1032,["style","onClick","class"])))),128))])),_:2},1024)])),_:2},1032,["class","id"])))),128))])),_:1},512)}],["__scopeId","data-v-0ae350d7"]])},data:()=>({months:[],monthIndex:0,listHeight:0,selected:[],scrollIntoView:"",scrollIntoViewScroll:"",scrollTop:0,innerFormatter:e=>e}),watch:{scrollIntoView:{immediate:!0,handler(e){}},selectedChange:{immediate:!0,handler(e){this.setMonth()}},show:{immediate:!0,handler(e){e?this.setMonth():this.scrollIntoView=""}}},computed:{innerMaxDate(){return te.number(this.maxDate)?Number(this.maxDate):this.maxDate},innerMinDate(){return te.number(this.minDate)?Number(this.minDate):this.minDate},selectedChange(){return[this.innerMinDate,this.innerMaxDate,this.defaultDate]},subtitle(){return this.months.length?`${this.months[this.monthIndex].year}年${this.months[this.monthIndex].month}月`:""},buttonDisabled(){return"range"===this.mode&&this.selected.length<=1}},mounted(){this.start=Date.now(),this.init()},emits:["confirm","close"],methods:{addUnit:Z,setFormatter(e){this.innerFormatter=e},monthSelected(e,t="init"){if(this.selected=e,!this.showConfirm&&("multiple"===this.mode||"single"===this.mode||"range"===this.mode&&this.selected.length>=2)){if("init"===t)return;"tap"===t&&this.$emit("confirm",this.selected)}},init(){if(this.innerMaxDate&&this.innerMinDate&&new Date(this.innerMaxDate).getTime()<new Date(this.innerMinDate).getTime())return le();this.listHeight=5*this.rowHeight+30,this.setMonth()},close(){this.$emit("close")},confirm(){this.buttonDisabled||this.$emit("confirm",this.selected)},getMonths(e,t){const l=hl(e).year(),o=hl(e).month()+1;return 12*(hl(t).year()-l)+(hl(t).month()+1-o)+1},setMonth(){const e=this.innerMinDate||hl().valueOf(),t=this.innerMaxDate||hl(e).add(this.monthNum-1,"month").valueOf(),l=oe(1,this.monthNum,this.getMonths(e,t));this.months=[];for(let o=0;o<l;o++)this.months.push({date:new Array(hl(e).add(o,"month").daysInMonth()).fill(1).map(((l,a)=>{let r=a+1;const n=hl(e).add(o,"month").date(r).day(),i=hl(e).add(o,"month").date(r).format("YYYY-MM-DD");let s="";if(this.showLunar){s=Rl.solar2lunar(hl(i).year(),hl(i).month()+1,hl(i).date()).IDayCn}let d={day:r,week:n,disabled:hl(i).isBefore(hl(e).format("YYYY-MM-DD"))||hl(i).isAfter(hl(t).format("YYYY-MM-DD")),date:new Date(i),bottomInfo:s,dot:!1,month:hl(e).add(o,"month").month()+1};return(this.formatter||this.innerFormatter)(d)})),month:hl(e).add(o,"month").month()+1,year:hl(e).add(o,"month").year()})},scrollIntoDefaultMonth(e){const t=this.months.findIndex((({year:t,month:l})=>`${t}-${l=ne(l)}`===e));-1!==t&&this.$nextTick((()=>{this.scrollIntoView=`month-${t}`,this.scrollIntoViewScroll=this.scrollIntoView}))},onScroll(e){const t=Math.max(0,e.detail.scrollTop);for(let l=0;l<this.months.length;l++)t>=(this.months[l].top||this.listHeight)&&(this.monthIndex=l,this.scrollIntoViewScroll=`month-${l}`)},updateMonthTop(e=[]){if(e.map(((e,t)=>{this.months[t].top=e})),!this.defaultDate){const e=hl().format("YYYY-MM");return void this.scrollIntoDefaultMonth(e)}let t=hl().format("YYYY-MM");t=te.array(this.defaultDate)?hl(this.defaultDate[0]).format("YYYY-MM"):hl(this.defaultDate).format("YYYY-MM"),this.scrollIntoDefaultMonth(t)}}},[["render",function(e,t,l,o,a,r){const n=ae("uHeader"),i=ae("uMonth"),s=I,d=v(x("u-button"),Le),u=$,c=v(x("u-popup"),Ve);return g(),b(c,{show:e.show,mode:"bottom",closeable:"",onClose:r.close,round:e.round,closeOnClickOverlay:e.closeOnClickOverlay},{default:y((()=>[h(u,{class:"u-calendar"},{default:y((()=>[h(n,{title:e.title,subtitle:r.subtitle,showSubtitle:e.showSubtitle,showTitle:e.showTitle},null,8,["title","subtitle","showSubtitle","showTitle"]),h(s,{style:_({height:r.addUnit(a.listHeight)}),"scroll-y":"",onScroll:r.onScroll,"scroll-top":a.scrollTop,scrollIntoView:a.scrollIntoView},{default:y((()=>[h(i,{color:e.color,rowHeight:e.rowHeight,showMark:e.showMark,months:a.months,mode:e.mode,maxCount:e.maxCount,startText:e.startText,endText:e.endText,defaultDate:e.defaultDate,minDate:r.innerMinDate,maxDate:r.innerMaxDate,maxMonth:e.monthNum,readonly:e.readonly,maxRange:e.maxRange,rangePrompt:e.rangePrompt,showRangePrompt:e.showRangePrompt,allowSameDay:e.allowSameDay,ref:"month",onMonthSelected:r.monthSelected,onUpdateMonthTop:r.updateMonthTop},null,8,["color","rowHeight","showMark","months","mode","maxCount","startText","endText","defaultDate","minDate","maxDate","maxMonth","readonly","maxRange","rangePrompt","showRangePrompt","allowSameDay","onMonthSelected","onUpdateMonthTop"])])),_:1},8,["style","onScroll","scroll-top","scrollIntoView"]),e.showConfirm?re(e.$slots,"footer",{key:0},(()=>[h(u,{class:"u-calendar__confirm"},{default:y((()=>[h(d,{shape:"circle",text:r.buttonDisabled?e.confirmDisabledText:e.confirmText,color:e.color,onClick:r.confirm,disabled:r.buttonDisabled},null,8,["text","color","onClick","disabled"])])),_:1})]),!0):S("v-if",!0)])),_:3})])),_:3},8,["show","onClose","round","closeOnClickOverlay"])}],["__scopeId","data-v-43e34f6c"]]);const Fl=We({name:"datetime-picker",mixins:[G,q,{props:{hasInput:{type:Boolean,default:()=>!1},placeholder:{type:String,default:()=>"请选择"},format:{type:String,default:()=>""},show:{type:Boolean,default:()=>X.datetimePicker.show},popupMode:{type:String,default:()=>X.picker.popupMode},showToolbar:{type:Boolean,default:()=>X.datetimePicker.showToolbar},modelValue:{type:[String,Number],default:()=>X.datetimePicker.value},title:{type:String,default:()=>X.datetimePicker.title},mode:{type:String,default:()=>X.datetimePicker.mode},maxDate:{type:Number,default:()=>X.datetimePicker.maxDate},minDate:{type:Number,default:()=>X.datetimePicker.minDate},minHour:{type:Number,default:()=>X.datetimePicker.minHour},maxHour:{type:Number,default:()=>X.datetimePicker.maxHour},minMinute:{type:Number,default:()=>X.datetimePicker.minMinute},maxMinute:{type:Number,default:()=>X.datetimePicker.maxMinute},filter:{type:[Function,null],default:()=>X.datetimePicker.filter},formatter:{type:[Function,null],default:()=>X.datetimePicker.formatter},loading:{type:Boolean,default:()=>X.datetimePicker.loading},itemHeight:{type:[String,Number],default:()=>X.datetimePicker.itemHeight},cancelText:{type:String,default:()=>X.datetimePicker.cancelText},confirmText:{type:String,default:()=>X.datetimePicker.confirmText},cancelColor:{type:String,default:()=>X.datetimePicker.cancelColor},confirmColor:{type:String,default:()=>X.datetimePicker.confirmColor},visibleItemCount:{type:[String,Number],default:()=>X.datetimePicker.visibleItemCount},closeOnClickOverlay:{type:Boolean,default:()=>X.datetimePicker.closeOnClickOverlay},defaultIndex:{type:Array,default:()=>X.datetimePicker.defaultIndex}}}],data:()=>({inputValue:"",showByClickInput:!1,columns:[],innerDefaultIndex:[],innerFormatter:(e,t)=>t}),watch:{show(e,t){e&&this.updateColumnValue(this.innerValue)},modelValue(e){this.init()},propsChange(){this.init()}},computed:{propsChange(){return[this.mode,this.maxDate,this.minDate,this.minHour,this.maxHour,this.minMinute,this.maxMinute,this.filter]}},mounted(){this.init()},emits:["close","cancel","confirm","change","update:modelValue"],methods:{getInputValue(e){if(""!=e&&e&&null!=e)if("time"==this.mode)this.inputValue=e;else if(this.format)this.inputValue=hl(e).format(this.format);else{let t="";switch(this.mode){case"date":t="YYYY-MM-DD";break;case"year-month":t="YYYY-MM";break;case"datetime":t="YYYY-MM-DD HH:mm";break;case"time":t="HH:mm"}this.inputValue=hl(e).format(t)}else this.inputValue=""},init(){this.innerValue=this.correctValue(this.modelValue),this.updateColumnValue(this.innerValue),this.getInputValue(this.innerValue)},setFormatter(e){this.innerFormatter=e},close(){this.closeOnClickOverlay&&this.$emit("close")},cancel(){this.hasInput&&(this.showByClickInput=!1),this.$emit("cancel")},confirm(){this.$emit("confirm",{value:this.innerValue,mode:this.mode}),this.$emit("update:modelValue",this.innerValue),this.hasInput&&(this.getInputValue(this.innerValue),this.showByClickInput=!1)},intercept(e,t){let l=e.match(/\d+/g);return l.length>1?0:t&&4==l[0].length?l[0]:l[0].length>2?0:l[0]},change(e){const{indexs:t,values:l}=e;let o="";if("time"===this.mode)o=`${this.intercept(l[0][t[0]])}:${this.intercept(l[1][t[1]])}`;else{const e=parseInt(this.intercept(l[0][t[0]],"year")),a=parseInt(this.intercept(l[1][t[1]]));let r=parseInt(l[2]?this.intercept(l[2][t[2]]):1),n=0,i=0;const s=hl(`${e}-${a}`).daysInMonth();"year-month"===this.mode&&(r=1),r=Math.min(s,r),"datetime"===this.mode&&(n=parseInt(this.intercept(l[3][t[3]])),i=parseInt(this.intercept(l[4][t[4]]))),o=Number(new Date(e,a-1,r,n,i))}o=this.correctValue(o),this.innerValue=o,this.updateColumnValue(o),this.$emit("change",{value:o,mode:this.mode})},updateColumnValue(e){this.innerValue=e,this.updateColumns(),setTimeout((()=>{this.updateIndexs(e)}),0)},updateIndexs(e){let t=[];const l=this.formatter||this.innerFormatter;if("time"===this.mode){const o=e.split(":");t=[l("hour",o[0]),l("minute",o[1])]}else t=[l("year",`${hl(e).year()}`),l("month",ne(hl(e).month()+1))],"date"===this.mode&&t.push(l("day",ne(hl(e).date()))),"datetime"===this.mode&&t.push(l("day",ne(hl(e).date())),l("hour",ne(hl(e).hour())),l("minute",ne(hl(e).minute())));const o=this.columns.map(((e,l)=>Math.max(0,e.findIndex((e=>e===t[l])))));this.innerDefaultIndex=o},updateColumns(){const e=this.formatter||this.innerFormatter,t=this.getOriginColumns().map((t=>t.values.map((l=>e(t.type,l)))));this.columns=t},getOriginColumns(){return this.getRanges().map((({type:e,range:t})=>{let l=function(e,t){let l=-1;const o=Array(e<0?0:e);for(;++l<e;)o[l]=t(l);return o}(t[1]-t[0]+1,(l=>{let o=t[0]+l;return o="year"===e?`${o}`:ne(o),o}));return this.filter&&(l=this.filter(e,l),(!l||l&&0==l.length)&&ie({title:"日期filter结果不能为空",icon:"error",mask:!0})),{type:e,values:l}}))},generateArray:(e,t)=>Array.from(new Array(t+1).keys()).slice(e),correctValue(e){const t="time"!==this.mode;if(t&&!te.date(e)?e=this.minDate:t||e||(e=`${ne(this.minHour)}:${ne(this.minMinute)}`),t)return e=hl(e).isBefore(hl(this.minDate))?this.minDate:e,e=hl(e).isAfter(hl(this.maxDate))?this.maxDate:e;{if(-1===String(e).indexOf(":"))return le();let[t,l]=e.split(":");return t=ne(oe(this.minHour,this.maxHour,Number(t))),l=ne(oe(this.minMinute,this.maxMinute,Number(l))),`${t}:${l}`}},getRanges(){if("time"===this.mode)return[{type:"hour",range:[this.minHour,this.maxHour]},{type:"minute",range:[this.minMinute,this.maxMinute]}];const{maxYear:e,maxDate:t,maxMonth:l,maxHour:o,maxMinute:a}=this.getBoundary("max",this.innerValue),{minYear:r,minDate:n,minMonth:i,minHour:s,minMinute:d}=this.getBoundary("min",this.innerValue),u=[{type:"year",range:[r,e]},{type:"month",range:[i,l]},{type:"day",range:[n,t]},{type:"hour",range:[s,o]},{type:"minute",range:[d,a]}];return"date"===this.mode&&u.splice(3,2),"year-month"===this.mode&&u.splice(2,3),u},getBoundary(e,t){const l=new Date(t),o=new Date(this[`${e}Date`]),a=hl(o).year();let r=1,n=1,i=0,s=0;return"max"===e&&(r=12,n=hl(l).daysInMonth(),i=23,s=59),hl(l).year()===a&&(r=hl(o).month()+1,hl(l).month()+1===r&&(n=hl(o).date(),hl(l).date()===n&&(i=hl(o).hour(),hl(l).hour()===i&&(s=hl(o).minute())))),{[`${e}Year`]:a,[`${e}Month`]:r,[`${e}Date`]:n,[`${e}Hour`]:i,[`${e}Minute`]:s}}}},[["render",function(e,t,l,o,a,r){const n=v(x("u-input"),Ge),i=$,s=v(x("u-picker"),qe);return g(),R(B,null,[e.hasInput?(g(),b(i,{key:0,class:"u-datetime-picker"},{default:y((()=>[h(n,{placeholder:e.placeholder,border:"surround",modelValue:a.inputValue,"onUpdate:modelValue":t[0]||(t[0]=e=>a.inputValue=e),onClick:t[1]||(t[1]=e=>a.showByClickInput=!a.showByClickInput)},null,8,["placeholder","modelValue"])])),_:1})):S("v-if",!0),h(s,{ref:"picker",show:e.show||e.hasInput&&a.showByClickInput,popupMode:e.popupMode,closeOnClickOverlay:e.closeOnClickOverlay,columns:a.columns,title:e.title,itemHeight:e.itemHeight,showToolbar:e.showToolbar,visibleItemCount:e.visibleItemCount,defaultIndex:a.innerDefaultIndex,cancelText:e.cancelText,confirmText:e.confirmText,cancelColor:e.cancelColor,confirmColor:e.confirmColor,onClose:r.close,onCancel:r.cancel,onConfirm:r.confirm,onChange:r.change},null,8,["show","popupMode","closeOnClickOverlay","columns","title","itemHeight","showToolbar","visibleItemCount","defaultIndex","cancelText","confirmText","cancelColor","confirmColor","onClose","onCancel","onConfirm","onChange"])],64)}],["__scopeId","data-v-d603ed3a"]]),Tl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e;let o=new Date;o.setFullYear(o.getFullYear()+1);const a=u(o.getTime());let r=new Date;const n=u(r.getTime()),p=i(),m=u(!1),z=u(!1),E=u(null),I=s((()=>"decorate"==p.mode?p.value[l.index]:l.component)),M=s((()=>l.global)),A=()=>{let e=[];if(I.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},j=s((()=>{var e="";if(I.value.field.value.date){let t=I.value.field.value.timestamp;e=O(t),I.value.field.value.date=e,I.value.field.value.timestamp=se(e)}else if(I.value.defaultControl){if("current"==I.value.dateWay)e=O();else if("diy"==I.value.dateWay){let t=I.value.field.default.timestamp||"";e=O(t)}I.value.field.value.date=e,I.value.field.value.timestamp=se(e)}else e=I.value.placeholder;return e})),N=s((()=>{var e="";return e+="position:relative;",I.value.componentStartBgColor&&I.value.componentEndBgColor?e+=`background:linear-gradient(${I.value.componentGradientAngle},${I.value.componentStartBgColor},${I.value.componentEndBgColor});`:I.value.componentStartBgColor?e+="background-color:"+I.value.componentStartBgColor+";":I.value.componentEndBgColor&&(e+="background-color:"+I.value.componentEndBgColor+";"),I.value.componentBgUrl&&(e+=`background-image:url('${d(I.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),I.value.topRounded&&(e+="border-top-left-radius:"+2*I.value.topRounded+"rpx;"),I.value.topRounded&&(e+="border-top-right-radius:"+2*I.value.topRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*I.value.bottomRounded+"rpx;"),I.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*I.value.bottomRounded+"rpx;"),e}));c((()=>{W(),"decorate"==p.mode&&f((()=>I.value),((e,t)=>{e&&"FormDate"==e.componentName&&W()}))}));const W=()=>{},V=()=>{"decorate"!==p.mode&&("YYYY-MM-DD HH:mm"==I.value.dateFormat?z.value=!0:m.value=!0)},Y=e=>{I.value.field.value.date=e[0],I.value.field.value.timestamp=se(e[0]),m.value=!1},H=e=>{I.value.field.value.date=e.value,I.value.field.value.timestamp=se(e.value),z.value=!1},U=e=>(de(Date.parse(e.date)/1e3,"year_month_day"),e),O=(e="",t=I.value.dateFormat)=>{let l=e?new Date(1e3*e):new Date,o=l.getFullYear(),a=String(l.getMonth()+1).padStart(2,"0"),r=String(l.getDate()).padStart(2,"0");const n=String(l.getHours()).padStart(2,"0"),i=String(l.getMinutes()).padStart(2,"0");let s="";return"YYYY年M月D日"==t?s=`${o}年${a}月${r}日`:"YYYY-MM-DD"==t?s=`${o}-${a}-${r}`:"YYYY/MM/DD"==t?s=`${o}/${a}/${r}`:"YYYY-MM-DD HH:mm"==t&&(s=`${o}-${a}-${r} ${n}:${i}`),s};return t({verify:()=>{const e={code:!0,message:""};return!I.value.field.required||I.value.field.value&&I.value.field.value.timestamp||(e.code=!1,e.message=`请选择${I.value.placeholder}`),E.value=e,e},reset:()=>{I.value.field.value.date="",I.value.field.value.timestamp=""}}),(e,t)=>{const l=D,o=$,r=v(x("u-calendar"),Bl),i=v(x("u-datetime-picker"),Fl);return k(I).viewFormDetail?(g(),b(o,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(g(),b(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(I).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(I).field.value.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(g(),b(o,{key:1,class:"base-layout-two"},{default:y((()=>[h(o,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(I).field.name),1)])),_:1}),h(o,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(I).field.value.date||k(P)("notHave")),1)])),_:1}),k(I).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(o,{key:1,style:_(k(N)),class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(g(),b(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx","font-weight":k(I).fontWeight})},{default:y((()=>[w(C(k(I).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(I).field.required?"*":""),1)])),_:1}),"decorate"==k(p).mode&&k(I).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(I).field.remark.text?(g(),b(o,{key:0,class:"layout-one-remark",style:_({color:k(I).field.remark.color,fontSize:2*k(I).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(I).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),E.value&&!E.value.code?(g(),b(o,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(E.value.message),1)])),_:1})):S("v-if",!0),h(o,{class:"layout-one-content",onClick:V},{default:y((()=>[h(o,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(o,{class:T(["flex-1 text-overflow-ellipsis flex",{"!text-[#999]":!k(I).field.value.date&&!k(I).defaultControl}]),style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx"})},{default:y((()=>[w(C(k(j)),1)])),_:1},8,["class","style"])])),_:1}),A().length?(g(),b(o,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(A(),((e,t)=>(g(),b(o,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(g(),b(o,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(p).mode&&k(I).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(o,{class:T(["layout-two-wrap",{"no-border":!k(M).borderControl}])},{default:y((()=>[h(o,{class:T(["layout-two-label",{"justify-start":"left"==k(M).completeAlign,"justify-end":"right"==k(M).completeAlign}])},{default:y((()=>[k(I).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(I).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx","font-weight":k(I).fontWeight})},{default:y((()=>[w(C(k(I).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(o,{class:"layout-two-content",onClick:V},{default:y((()=>[h(o,{class:T(["flex-1 text-overflow-ellipsis flex justify-end",{"!text-[#999]":!k(I).field.value.date&&!k(I).defaultControl}]),style:_({color:k(I).textColor,"font-size":2*k(I).fontSize+"rpx"})},{default:y((()=>[w(C(k(j)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),E.value&&!E.value.code?(g(),b(o,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(E.value.message),1)])),_:1})):S("v-if",!0),k(I).field.remark.text?(g(),b(o,{key:2,class:"layout-two-remark",style:_({color:k(I).field.remark.color,fontSize:2*k(I).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(I).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),A().length?(g(),b(o,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(A(),((e,t)=>(g(),b(o,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(o,{class:"calendar-wrap"},{default:y((()=>[h(r,{show:m.value,mode:"single",onConfirm:Y,onClose:t[0]||(t[0]=e=>m.value=!1),closeOnClickOverlay:"true",formatter:U,confirmDisabledText:"禁止选择",color:"var(--primary-color)",ref:"calendar",maxDate:a.value},null,8,["show","maxDate"]),h(i,{show:z.value,modelValue:k(I).field.value.date,"onUpdate:modelValue":t[1]||(t[1]=e=>k(I).field.value.date=e),mode:"datetime",onCancel:t[2]||(t[2]=e=>z.value=!1),closeOnClickOverlay:"true",onConfirm:H,onClose:t[3]||(t[3]=e=>z.value=!1),minDate:n.value},null,8,["show","modelValue","minDate"])])),_:1}),S(" 遮罩层，装修使用 "),"decorate"==k(p).mode?(g(),b(o,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-eb8363a5"]]),zl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(!1),r=u(null),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),p=s((()=>l.global)),m=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},z=s((()=>{let e="",t="";if(n.value.field.value.start.date){let l=n.value.field.value.start.timestamp;e=W(l),t=e}else if(n.value.start.defaultControl){if("current"==n.value.start.dateWay)e=W();else if("diy"==n.value.start.dateWay){let t=n.value.field.default.start.timestamp?n.value.field.default.start.timestamp:"";e=W(t)}t=e}else e=n.value.start.placeholder,t="";return n.value.field.value.start.date=t,n.value.field.value.start.timestamp=t?se(t):0,e})),E=s((()=>{let e="",t="";if(n.value.field.value.end.date){let l=n.value.field.value.end.timestamp;e=W(l),t=e,n.value.field.value.end.date=t,n.value.field.value.end.timestamp=se(t)}else n.value.end.defaultControl?("current"==n.value.end.dateWay?e=W():"diy"==n.value.end.dateWay&&(e=W(n.value.field.default.end.timestamp)),t=e,n.value.field.value.end.date=t,n.value.field.value.end.timestamp=se(t)):e=n.value.end.placeholder;return e})),I=s((()=>{let e=[];return e[0]=W(se(z.value),"YYYY-MM-DD"),e[1]=W(se(E.value),"YYYY-MM-DD"),e})),M=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{A(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormDateScope"==e.componentName&&A()}))}));const A=()=>{},j=()=>{"decorate"!==o.mode&&(a.value=!0)},N=e=>{n.value.field.value.start.date=e[0],n.value.field.value.start.timestamp=se(e[0]),n.value.field.value.end.date=e[e.length-1],n.value.field.value.end.timestamp=se(e[e.length-1]),a.value=!1},W=(e="",t=n.value.dateFormat)=>{let l=e?new Date(1e3*e):new Date,o=l.getFullYear(),a=String(l.getMonth()+1).padStart(2,"0"),r=String(l.getDate()).padStart(2,"0"),i="";return"YYYY年M月D日"==t?i=`${o}年${a}月${r}日`:"YYYY-MM-DD"==t?i=`${o}-${a}-${r}`:"YYYY/MM/DD"==t&&(i=`${o}/${a}/${r}`),i};return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&(""!=n.value.field.value.start.date&&n.value.field.value.start.timestamp?""!=n.value.field.value.end.date&&n.value.field.value.end.timestamp||(e.code=!1,e.message=`请选择${n.value.end.placeholder}`):(e.code=!1,e.message=`请选择${n.value.start.placeholder}`)),r.value=e,e},reset:()=>{n.value.field.value.start.date="",n.value.field.value.start.timestamp=0,n.value.field.value.end.date="",n.value.field.value.end.timestamp=0}}),(e,t)=>{const l=D,i=$,s=v(x("u-calendar"),Bl);return k(n).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[k(n).field.value.start.date||k(n).field.value.end.date?(g(),b(i,{key:0,class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value.start.date),1)])),_:1}),k(n).field.value.start.date&&k(n).field.value.end.date?(g(),b(l,{key:0},{default:y((()=>[w("-")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(n).field.value.end.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[k(n).field.value.start.date||k(n).field.value.end.date?(g(),b(i,{key:0,class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value.start.date),1)])),_:1}),k(n).field.value.start.date&&k(n).field.value.end.date?(g(),b(l,{key:0},{default:y((()=>[w("-")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(n).field.value.end.date),1)])),_:1}),k(n).isShowArrow?(g(),b(l,{key:1,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,style:_(k(M)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(i,{class:"flex items-center"},{default:y((()=>[h(i,{class:"layout-one-content flex-1",onClick:j},{default:y((()=>[h(i,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(i,{class:T(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(n).field.value.start.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k(z)),1)])),_:1},8,["class","style"])])),_:1}),h(i,{class:"mx-[10rpx]",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(i,{class:"layout-one-content flex-1",onClick:j},{default:y((()=>[h(i,{class:"nc-iconfont nc-icon-a-riliV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(i,{class:T(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(n).field.value.end.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k(E)),1)])),_:1},8,["class","style"])])),_:1})])),_:1}),m().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(m(),((e,t)=>(g(),b(i,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{class:"layout-two-content",onClick:j},{default:y((()=>[h(i,{class:T(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(n).field.value.start.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k(z)),1)])),_:1},8,["class","style"]),h(i,{class:"mx-[10rpx]",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(i,{class:T(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(n).field.value.end.timestamp&&!k(n).defaultControl}]),style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"})},{default:y((()=>[w(C(k(E)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),r.value&&!r.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),m().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(m(),((e,t)=>(g(),b(i,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(i,{class:"calendar-wrap"},{default:y((()=>[h(s,{show:a.value,mode:"range",onConfirm:N,onClose:t[0]||(t[0]=e=>a.value=!1),closeOnClickOverlay:"true",defaultDate:k(I),startText:"开始",endText:"结束",confirmDisabledText:"禁止选择",color:"var(--primary-color)",ref:"calendar",monthNum:"2"},null,8,["show","defaultDate"])])),_:1}),S(" 遮罩层，装修使用 "),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-ce0c2832"]]),$l=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=u(null),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),p=s((()=>l.global)),m=s((()=>{let e="";return e+=n.value.placeholder,e})),v=s((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),x=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},z=e=>{"privacy"==e&&r.value.open()},E=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormEmail"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode?(e.code=!1,e.message=`${m.value}`):/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(n.value.field.value)||"decorate"==o.mode||(e.code=!1,e.message="邮箱格式不正确，请重新输入"),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,i=$,s=O;return k(n).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(i,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value||k(P)("notHave")),1)])),_:1}),k(n).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,style:_(k(E)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{type:"email",class:"layout-one-content",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),x().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{type:"email",class:"layout-two-content no-flex",placeholder:k(m),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),x().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Gt,{ref_key:"formPrivacyRef",ref:r,data:k(v)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-5f8683b0"]]),Dl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=u(null),a=i(),r=s((()=>"decorate"==a.mode?a.value[l.index]:l.component)),n=s((()=>l.global)),p=s((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e}));c((()=>{z(),"decorate"==a.mode&&f((()=>r.value),((e,t)=>{e&&"FormFile"==e.componentName&&z()}))}));const m=()=>{uni.chooseMessageFile({count:9,type:"all",success:e=>{console.log("选择的微信聊天文件:",e.tempFiles),e.tempFiles.forEach((e=>{ue({filePath:e.path,name:"file"}).then((e=>{r.value.field.value.length<Number(r.value.limit)&&r.value.field.value.push(e.data.url)})).catch((()=>{console.error("上传失败")}))}))},fail:e=>{console.error("选择文件失败",e)}})},T=e=>{e.file.forEach((e=>{ue({filePath:e.url,name:"file"}).then((e=>{r.value.field.value.length<Number(r.value.limit)&&r.value.field.value.push(e.data.url)})).catch((()=>{console.error("上传失败")}))}))},z=()=>{};return t({verify:()=>({code:!0,message:""}),reset:()=>{}}),(e,t)=>{const l=D,i=$,s=E,u=v(x("u-upload"),Xe);return g(),b(i,{style:_(k(p)),class:"form-item-frame"},{default:y((()=>["style-1"==k(n).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(r).textColor,"font-size":2*k(r).fontSize+"rpx","font-weight":k(r).fontWeight})},{default:y((()=>[w(C(k(r).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(r).field.required?"*":""),1)])),_:1}),"decorate"==k(a).mode&&k(r).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(r).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(r).field.remark.color,fontSize:2*k(r).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(r).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),o.value&&!o.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(o.value.message),1)])),_:1})):S("v-if",!0),h(i,{class:"flex flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(k(r).field.value,((e,t)=>(g(),b(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx] layout-one-content",key:t},{default:y((()=>[h(s,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(i,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>{return l=t,void r.value.field.value.splice(l.index,1);var l}},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),h(i,{class:"flex items-center flex-1"},{default:y((()=>[h(i,{class:"layout-one-content !p-[0] flex-1 !items-stretch !h-[100rpx]"},{default:y((()=>[h(i,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border",onClick:m},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-weixinV6mm"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("选择微信聊天文件")])),_:1})])),_:1})])),_:1}),h(i,{class:"layout-one-content !p-[0] ml-[20rpx] !items-stretch flex-1 !h-[100rpx]"},{default:y((()=>[h(u,{accept:"file ",onAfterRead:T,multiple:"",maxCount:9},{default:y((()=>[h(i,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border"},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-tupiandaohangpc"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("选择本地文件")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):S("v-if",!0),"decorate"==k(a).mode?(g(),b(i,{key:1,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-3511697c"]]),El=We(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=s((()=>l.data||"已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息")),a=u(!1),r=()=>{a.value=!1};return t({open:()=>{a.value=!0}}),(e,t)=>{const l=$,n=pe,i=v(x("u-popup"),Ve);return g(),b(l,{onTouchmove:t[1]||(t[1]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(i,{show:a.value,onClose:r,zIndex:"500",mode:"center",round:8},{default:y((()=>[h(l,{class:"w-[570rpx] popup-common center"},{default:y((()=>[h(l,{class:"text-center my-5"},{default:y((()=>[w(C(k(P)("diyForm.tips")),1),ce("br"),w(C(k(o)),1)])),_:1}),h(l,{class:"flex justify-between"},{default:y((()=>[h(n,{class:"w-[50%] h-[100rpx] rounded-[0rpx] leading-[100rpx] !bg-[transform] border-solid border-[0] border-t-[2rpx] border-[#e6e6e6] !text-[#333]",onClick:t[0]||(t[0]=e=>k(fe)(k(o)))},{default:y((()=>[w(C(k(P)("diyForm.copy")),1)])),_:1}),h(n,{class:"w-[50%] h-[100rpx] rounded-[0rpx] border-solid border-[0] border-t-[2rpx] border-l-[2rpx] bo border-[#e6e6e6] leading-[100rpx] !bg-[transform] !text-[var(--primary-color)]",onClick:r},{default:y((()=>[w(C(k(P)("diyForm.know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-bb1ac654"]]),Il=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=u(null),n=u(null),m=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),v=s((()=>l.global)),x=s((()=>{const e=String(m.value.field.value);return m.value.field.privacyProtection?e.replace(/(\d{3})\d*(\d{4})/,"$1****$2"):e})),z=s((()=>{let e="";return e+=m.value.placeholder,e})),E=s((()=>`${m.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),I=()=>{let e=[];if(m.value.autofill){let t={title:"已自动填充"};e.push(t)}if(m.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},M=e=>{"privacy"==e&&r.value.open()},A=()=>{p((()=>{n.value?n.value.open():console.warn("formDetailPrivacyRef is not defined")}))},j=s((()=>{var e="";return e+="position:relative;",m.value.componentStartBgColor&&m.value.componentEndBgColor?e+=`background:linear-gradient(${m.value.componentGradientAngle},${m.value.componentStartBgColor},${m.value.componentEndBgColor});`:m.value.componentStartBgColor?e+="background-color:"+m.value.componentStartBgColor+";":m.value.componentEndBgColor&&(e+="background-color:"+m.value.componentEndBgColor+";"),m.value.componentBgUrl&&(e+=`background-image:url('${d(m.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),m.value.topRounded&&(e+="border-top-left-radius:"+2*m.value.topRounded+"rpx;"),m.value.topRounded&&(e+="border-top-right-radius:"+2*m.value.topRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*m.value.bottomRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*m.value.bottomRounded+"rpx;"),e}));c((()=>{N(),"decorate"==o.mode&&f((()=>m.value),((e,t)=>{e&&"FormIdentity"==e.componentName&&N()}))}));const N=()=>{("decorate"==o.mode||""==m.value.field.value&&m.value.field.default)&&(m.value.field.value=m.value.field.default)},W=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return m.value.field.required&&""==m.value.field.value&&"decorate"!=o.mode?(e.code=!1,e.message=`${z.value}`):/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}[0-9Xx]$/.test(m.value.field.value)||"decorate"==o.mode||(e.code=!1,e.message="身份证格式不正确，请重新输入"),a.value=e,e},reset:()=>{m.value.field.value=""}}),(e,t)=>{const l=D,i=$,s=O;return k(m).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(v).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(i,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(x)),1)])),_:1}),k(m).field.privacyProtection?(g(),b(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(P)("diyForm.view")),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(v).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(x)||k(P)("notHave")),1)])),_:1}),k(m).field.privacyProtection&&k(x)?(g(),b(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(P)("diyForm.view")),1)])),_:1})):S("v-if",!0),k(m).isShowArrow?(g(),b(l,{key:1,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),h(El,{ref_key:"formDetailPrivacyRef",ref:n,data:k(m).field.value},null,8,["data"])])),_:1})):(g(),b(i,{key:1,style:_(k(j)),class:"form-item-frame"},{default:y((()=>["style-1"==k(v).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(m).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(m).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{type:"idcard",class:"layout-one-content",placeholder:k(z),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(m).field.value=e),disabled:k(W),maxlength:"18"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),I().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(I(),((e,t)=>(g(),b(i,{key:t,onClick:t=>M(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(v).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(m).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(v).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(v).completeAlign,"justify-end":"right"==k(v).completeAlign}])},{default:y((()=>[k(m).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{type:"idcard",class:"layout-two-content no-flex",placeholder:k(z),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(m).field.value=e),disabled:k(W),maxlength:"18"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(m).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),I().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(I(),((e,t)=>(g(),b(i,{key:t,onClick:t=>M(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Gt,{ref_key:"formPrivacyRef",ref:r,data:k(E)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-8c8b51b4"]]),Ml=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),n=s((()=>l.global)),p=s((()=>{let e=[];return r.value&&r.value.uploadMode&&r.value.uploadMode.forEach(((t,l)=>{"take_pictures"==t&&-1==r.value.uploadMode.indexOf("camera")?e.push("camera"):"select_from_album"==t&&-1==r.value.uploadMode.indexOf("album")&&e.push("album")})),e})),m=()=>{let e=[];if(r.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},z=(e,t)=>{let l=[];r.value.field.value&&(l=Object.values(r.value.field.value).map((e=>d(e)))),xe({current:t,urls:l,indicator:"number",loop:!0})};me((()=>{try{ve()}catch(e){}}));const I=s((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e}));c((()=>{W(),"decorate"==o.mode&&f((()=>r.value),((e,t)=>{e&&"FormImage"==e.componentName&&W()}))})),s((()=>"decorate"===o.mode)),s((()=>r.value.field.value.map((e=>({url:d(e)})))));const M=e=>{uni.setStorageSync("sku_form_refresh",!0),e.file.forEach((e=>{A(e)}))},A=e=>{if(r.value.field.value.length>Number(r.value.limit))return ie({title:`最多允许上传${r.value.limit}张图片`,icon:"none"}),!1;ue({filePath:e.url,name:"file"}).then((e=>{r.value.field.value.length<Number(r.value.limit)&&r.value.field.value.push(e.data.url)})).catch((()=>{}))},j=e=>{r.value.field.value.splice(e.index,1)},W=()=>{};return t({verify:()=>{const e={code:!0,message:""};return r.value.field.required&&(!r.value.field.value||r.value.field.value&&!r.value.field.value.length)?(e.code=!1,e.message="请上传图片"):r.value.field.value&&r.value.field.value.length>Number(r.value.limit)&&(e.code=!1,e.message="图片上传数量已超出限制数量"),a.value=e,e},reset:()=>{r.value.field.value=[]}}),(e,t)=>{const l=D,i=E,s=$,u=v(x("u-upload"),Xe);return k(r).viewFormDetail?(g(),b(s,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(n).completeLayout?(g(),b(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(r).field.name),1)])),_:1}),h(s,{class:"flex flex-wrap detail-one-content-value pt-[6rpx]"},{default:y((()=>[(g(!0),R(B,null,F(k(r).field.value,((e,t)=>(g(),b(s,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:t},{default:y((()=>[h(i,{class:"w-[100%] h-[100%]",src:k(d)(e),onClick:e=>z(0,t),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(n).completeLayout?(g(),b(s,{key:1,class:"base-layout-two"},{default:y((()=>[h(s,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(r).field.name),1)])),_:1}),h(s,{class:"flex items-center w-[80%] justify-end"},{default:y((()=>[ce("div",{class:"flex flex-wrap justify-end gap-[10rpx]"},[(g(!0),R(B,null,F(k(r).field.value,((e,t)=>(g(),b(s,{class:"relative image-item w-[180rpx] !h-[180rpx] gap-[16rpx]",key:t},{default:y((()=>[h(i,{class:"w-[100%] h-[100%]",src:k(d)(e),onClick:N((e=>z(0,t)),["stop"]),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))]),k(r).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(s,{key:1,style:_(k(I)),class:"form-item-frame"},{default:y((()=>["style-1"==k(n).completeLayout?(g(),b(s,{key:0,class:"base-layout-one"},{default:y((()=>[h(s,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(r).textColor,"font-size":2*k(r).fontSize+"rpx","font-weight":k(r).fontWeight})},{default:y((()=>[w(C(k(r).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(r).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(r).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(r).field.remark.text?(g(),b(s,{key:0,class:"layout-one-remark",style:_({color:k(r).field.remark.color,fontSize:2*k(r).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(r).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(s,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{class:"flex flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(k(r).field.value,((e,t)=>(g(),b(s,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx] layout-one-content",key:t},{default:y((()=>[h(i,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(s,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>j(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),k(r).uploadMode.length>1&&k(r).field.value.length<=0?(g(),b(s,{key:0,class:"flex items-center flex-1"},{default:y((()=>[h(s,{class:"layout-one-content !p-[0] flex-1 !items-stretch !h-[100rpx]"},{default:y((()=>[h(u,{accept:"image",onAfterRead:M,multiple:"",maxCount:9,capture:"camera"},{default:y((()=>[h(s,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border"},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-xiangjiV6xx"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("拍照上传")])),_:1})])),_:1})])),_:1})])),_:1}),h(s,{class:"layout-one-content !p-[0] ml-[20rpx] !items-stretch flex-1 !h-[100rpx]"},{default:y((()=>[h(u,{accept:"image",onAfterRead:M,multiple:"",maxCount:9,capture:"album"},{default:y((()=>[h(s,{class:"flex items-center h-[100%] w-[100%] pl-[30rpx] box-border"},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-tupiandaohangpc"}),h(l,{class:"text-[28rpx] ml-[10rpx]"},{default:y((()=>[w("从相册中选择")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):k(r).field.value.length<Number(k(r).limit)?(g(),b(s,{key:1,class:"layout-one-content h-[180rpx] w-[180rpx] !px-[0]"},{default:y((()=>[1==k(r).uploadMode.length?(g(),b(u,{key:0,accept:"image",onAfterRead:M,multiple:"",capture:k(p),maxCount:Number(k(r).limit)},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:T(["nc-iconfont !text-[36rpx] mb-[16rpx]",{"nc-icon-xiangjiV6xx":k(r).uploadMode.indexOf("take_pictures")>-1,"nc-icon-tupiandaohangpc":-1==k(r).uploadMode.indexOf("take_pictures")}])},null,8,["class"]),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w(C(k(r).uploadMode.indexOf("take_pictures")>-1?"拍照上传":"从相册选择"),1)])),_:1})])),_:1})])),_:1},8,["capture","maxCount"])):(g(),b(u,{key:1,accept:"image",onAfterRead:M,multiple:"",capture:k(p),maxCount:Number(k(r).limit)},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[40rpx] mb-[16rpx] nc-icon-jiahaoV6xx"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w(C(k(P)("diyForm.uploadTips")),1)])),_:1})])),_:1})])),_:1},8,["capture","maxCount"]))])),_:1})):S("v-if",!0)])),_:1}),m().length?(g(),b(s,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(m(),((e,t)=>(g(),b(s,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(n).completeLayout?(g(),b(s,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(r).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(s,{class:T(["layout-two-wrap",{"no-border":!k(n).borderControl}])},{default:y((()=>[h(s,{class:T(["layout-two-label",{"justify-start":"left"==k(n).completeAlign,"justify-end":"right"==k(n).completeAlign}])},{default:y((()=>[k(r).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(r).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(r).textColor,"font-size":2*k(r).fontSize+"rpx","font-weight":k(r).fontWeight})},{default:y((()=>[w(C(k(r).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{class:"layout-two-content flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(k(r).field.value,((e,t)=>(g(),b(s,{class:"relative border-box w-[180rpx] !h-[180rpx] ml-[16rpx] mb-[16rpx] border-box border-[2rpx] border-solid border-[#e6e6e6] rounded-[10rpx] flex items-center",key:t},{default:y((()=>[h(i,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(s,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>j(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),h(s,{class:"items-start border-box border-[2rpx] ml-[16rpx] mb-[16rpx] border-solid border-[#e6e6e6] rounded-[10rpx]"},{default:y((()=>[k(r).field.value.length<Number(k(r).limit)?(g(),b(u,{key:0,accept:"image",onAfterRead:M,multiple:"",capture:k(p),maxCount:Number(k(r).limit)},{default:y((()=>[h(s,{class:"flex flex-col items-center justify-center min-w-[180rpx] min-h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[40rpx] mb-[16rpx] nc-icon-jiahaoV6xx"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w(C(k(P)("diyForm.uploadTips")),1)])),_:1})])),_:1})])),_:1},8,["capture","maxCount"])):S("v-if",!0)])),_:1})])),_:1})])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(s,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(r).field.remark.text?(g(),b(s,{key:2,class:"layout-two-remark",style:_({color:k(r).field.remark.color,fontSize:2*k(r).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(r).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),m().length?(g(),b(s,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(m(),((e,t)=>(g(),b(s,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(s,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-a5bf88a9"]]),Al=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=u(null),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),p=s((()=>l.global)),m=s((()=>{let e="";return e+=n.value.placeholder,e})),v=s((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),x=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},z=e=>{"privacy"==e&&r.value.open()},E=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormInput"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message=`${m.value}`),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,i=$,s=O;return k(n).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(i,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value||k(P)("notHave")),1)])),_:1}),k(n).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,style:_(k(E)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{type:"text",class:"layout-one-content",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),x().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{type:"text",class:"layout-two-content no-flex",placeholder:k(m),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),x().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Gt,{ref_key:"formPrivacyRef",ref:r,data:k(v)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-1b8fce42"]]),jl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=u(null),a=M();A();const r=i(),n=s((()=>"decorate"==r.mode?r.value[l.index]:l.component)),p=s((()=>l.global));let m=!1;n.value&&"decorate"!=r.mode&&(m=!0);const v=Ye(m);v.onLoad(),v.init();const x=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{R(),"decorate"==r.mode&&f((()=>n.value),((e,t)=>{e&&"FormLocation"==e.componentName&&R()}))}));const R=()=>{};return t({verify:()=>({code:!0,message:""}),reset:()=>{}}),(e,t)=>{const l=D,i=$;return g(),b(i,{style:_(k(x)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(r).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(e.t("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),o.value&&!o.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(o.value.message),1)])),_:1})):S("v-if",!0),"authorized_wechat_location"==k(n).mode?(g(),b(i,{key:2},{default:y((()=>[k(a).diyAddressInfo?(g(),b(i,{key:0,onClick:t[0]||(t[0]=N((e=>k(v).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[S(' <view class="flex items-baseline font-500">\n\t\t\t\t\t\t<text class="text-[24rpx] mr-[2rpx]">{{ systemStore.diyAddressInfo.city }}</text>\n\t\t\t\t\t\t<text class="iconfont iconxiaV6xx !text-[24rpx]"></text>\n\t\t\t\t\t</view> '),h(l,{class:"iconfont iconzuobiaofill !text-[28rpx]"}),k(a).diyAddressInfo.community?(g(),b(i,{key:0},{default:y((()=>[w(C(k(a).diyAddressInfo.community),1)])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,onClick:t[1]||(t[1]=N((e=>k(v).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[h(l,{class:"iconfont iconzuobiaofill !text-[28rpx]"}),h(l,{class:"ml-1 text-[#999]"},{default:y((()=>[w("点击获取位置信息")])),_:1})])),_:1}))])),_:1})):(g(),b(i,{key:3},{default:y((()=>[k(a).diyAddressInfo?(g(),b(i,{key:0,onClick:t[2]||(t[2]=N((e=>k(v).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[h(i,{class:"flex items-baseline font-500"},{default:y((()=>[h(l,{class:"text-[24rpx] mr-[2rpx]"},{default:y((()=>[w(C(k(a).diyAddressInfo.city),1)])),_:1}),h(l,{class:"iconfont iconxiaV6xx !text-[24rpx]"})])),_:1}),k(a).diyAddressInfo.community?(g(),b(i,{key:0,class:"layout-one-content"},{default:y((()=>[w(C(k(a).diyAddressInfo.community),1)])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,onClick:t[3]||(t[3]=N((e=>k(v).reposition()),["stop"])),class:"layout-one-content"},{default:y((()=>[h(l,{class:"iconfont iconzuobiaofill !text-[28rpx]"}),h(l,{class:"ml-1 text-[#999]"},{default:y((()=>[w("选择位置")])),_:1})])),_:1})),h(i,{class:"text-[var(--primary-color)] mt-1"},{default:y((()=>[w(" 当前定位 ")])),_:1})])),_:1})),S(' <input type="text" class="layout-one-content" :placeholder=""\n\t\t\t\tplaceholderClass="layout-one-input-placeholder"\n\t\t\t\t:placeholder-style="{\'font-size\': (diyComponent.fontSize * 2) + \'rpx\' }"\n\t\t\t\t:style="{\'color\': diyComponent.textColor,\'font-size\': (diyComponent.fontSize * 2) + \'rpx\'}"\n\t\t\t\tv-model="diyComponent.field.value" :disabled="isDisabled" /> '),S(' <view class="layout-one-attribute-wrap" v-if="inputAttribute().length">\n\t\t\t\t<view v-for="(item,index) in inputAttribute()" :key="index" @click="eventFn(item.type)"\n\t\t\t\t\tclass="layout-one-attribute-item">{{ item.title }}</view>\n\t\t\t</view> ')])),_:1})):S("v-if",!0),S(" <view class=\"relative\">\n\t\t\t<view class=\"p-[10rpx]  flex items-center \">\n\t\t\t\t<view class=\"w-[27%] mr-[10rpx] flex items-center\">\n\t\t\t\t\t<text class=\"text-overflow-ellipsis\"\n\t\t\t\t\t\t:style=\"{'color': diyComponent.textColor,'font-size': (diyComponent.fontSize * 2) + 'rpx' ,'font-weight': diyComponent.fontWeight}\">{{ diyComponent.field.name}}</text>\n\t\t\t\t\t<text class=\"text-[#ec0003]\">{{ diyComponent.field.required ? '*' : '' }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</view>\n\t\t</view> ")])),_:1},8,["style"])}}}),[["__scopeId","data-v-fcb64919"]]),Nl=We(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=s((()=>l.data||"已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息")),a=u(!1),r=()=>{a.value=!1},n=()=>{ge({phoneNumber:l.data})};return t({open:()=>{a.value=!0}}),(e,t)=>{const l=$,i=pe,s=v(x("u-popup"),Ve);return g(),b(l,{onTouchmove:t[0]||(t[0]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(s,{show:a.value,onClose:r,zIndex:"500",mode:"center",round:8},{default:y((()=>[h(l,{class:"w-[570rpx] popup-common center"},{default:y((()=>[h(l,{class:"text-center my-5"},{default:y((()=>[w(C(k(P)("diyForm.tips")),1),ce("br"),w(C(k(o)),1)])),_:1}),h(l,{class:"flex justify-between"},{default:y((()=>[h(i,{class:"w-[50%] h-[100rpx] rounded-[0rpx] leading-[100rpx] !bg-[transform] border-solid border-[0] border-t-[2rpx] border-[#e6e6e6] !text-[#333]",onClick:n},{default:y((()=>[w(C(k(P)("diyForm.call")),1)])),_:1}),h(i,{class:"w-[50%] h-[100rpx] rounded-[0rpx] border-solid border-[0] border-t-[2rpx] border-l-[2rpx] bo border-[#e6e6e6] leading-[100rpx] !bg-[transform] !text-[var(--primary-color)]",onClick:r},{default:y((()=>[w(C(k(P)("diyForm.know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-33cb0694"]]),Wl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=u(null),n=u(null),m=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),v=s((()=>l.global)),x=s((()=>{const e=String(m.value.field.value);return m.value.field.privacyProtection?e.replace(/(\d{3})\d{4}(\d{3})/,"$1****$2"):e})),z=s((()=>{let e="";return e+=m.value.placeholder,e})),E=s((()=>`${m.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),I=()=>{let e=[];if(m.value.autofill){let t={title:"已自动填充"};e.push(t)}if(m.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},M=e=>{"privacy"==e&&r.value.open()},A=()=>{p((()=>{n.value?n.value.open():console.warn("formDetailPrivacyRef is not defined")}))},j=s((()=>{var e="";return e+="position:relative;",m.value.componentStartBgColor&&m.value.componentEndBgColor?e+=`background:linear-gradient(${m.value.componentGradientAngle},${m.value.componentStartBgColor},${m.value.componentEndBgColor});`:m.value.componentStartBgColor?e+="background-color:"+m.value.componentStartBgColor+";":m.value.componentEndBgColor&&(e+="background-color:"+m.value.componentEndBgColor+";"),m.value.componentBgUrl&&(e+=`background-image:url('${d(m.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),m.value.topRounded&&(e+="border-top-left-radius:"+2*m.value.topRounded+"rpx;"),m.value.topRounded&&(e+="border-top-right-radius:"+2*m.value.topRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*m.value.bottomRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*m.value.bottomRounded+"rpx;"),e}));c((()=>{N(),"decorate"==o.mode&&f((()=>m.value),((e,t)=>{e&&"FormMobile"==e.componentName&&N()}))}));const N=()=>{("decorate"==o.mode||""==m.value.field.value&&m.value.field.default)&&(m.value.field.value=m.value.field.default)},W=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return m.value.field.required&&""==m.value.field.value&&"decorate"!=o.mode?(e.code=!1,e.message=`${z.value}`):/^1[3-9]\d{9}$/.test(m.value.field.value)||"decorate"==o.mode||(e.code=!1,e.message="手机号格式不正确，请重新输入"),a.value=e,e},reset:()=>{m.value.field.value=""}}),(e,t)=>{const l=D,i=$,s=O;return k(m).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(v).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(i,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(x)),1)])),_:1}),k(m).field.privacyProtection?(g(),b(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(P)("diyForm.view")),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(v).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(x)||k(P)("notHave")),1)])),_:1}),k(m).field.privacyProtection&&k(x)?(g(),b(l,{key:0,class:"ml-[20rpx] text-[var(--primary-color)]",onClick:A},{default:y((()=>[w(C(k(P)("diyForm.view")),1)])),_:1})):S("v-if",!0),k(m).isShowArrow?(g(),b(l,{key:1,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0),h(Nl,{ref_key:"formDetailPrivacyRef",ref:n,data:k(m).field.value},null,8,["data"])])),_:1})):(g(),b(i,{key:1,style:_(k(j)),class:"form-item-frame"},{default:y((()=>["style-1"==k(v).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(m).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(m).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(s,{type:"number",class:"layout-one-content",placeholder:k(z),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(m).field.value=e),disabled:k(W),maxlength:"11"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),I().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(I(),((e,t)=>(g(),b(i,{key:t,onClick:t=>M(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(v).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(m).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(v).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(v).completeAlign,"justify-end":"right"==k(v).completeAlign}])},{default:y((()=>[k(m).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(s,{type:"number",class:"layout-two-content no-flex",placeholder:k(z),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(m).fontSize+"rpx"},style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),modelValue:k(m).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(m).field.value=e),disabled:k(W),maxlength:"11"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(m).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),I().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(I(),((e,t)=>(g(),b(i,{key:t,onClick:t=>M(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Gt,{ref_key:"formPrivacyRef",ref:r,data:k(E)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-d49999d7"]]),Vl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=u(null),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),p=s((()=>l.global)),m=s((()=>{let e="";return e+=n.value.placeholder,e})),v=s((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),x=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},z=e=>{"privacy"==e&&r.value.open()},E=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormNumber"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message=`${m.value}`),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,i=$,s=O;return k(n).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value||k(P)("notHave")),1)])),_:1}),k(n).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,style:_(k(E)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{class:"layout-one-content flex items-center"},{default:y((()=>[h(s,{type:"number",class:"flex-1",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),k(n).unit?(g(),b(l,{key:0,class:"text-[#999] text-[28rpx]"},{default:y((()=>[w(C(k(n).unit),1)])),_:1})):S("v-if",!0)])),_:1}),x().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{class:"layout-two-content"},{default:y((()=>[h(s,{type:"number",placeholder:k(m),placeholderClass:"layout-two-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(M)},null,8,["placeholder","placeholder-style","style","modelValue","disabled"]),k(n).unit?(g(),b(l,{key:0,class:"text-[#999] ml-[10rpx] pt-[2rpx] text-[28rpx]"},{default:y((()=>[w(C(k(n).unit),1)])),_:1})):S("v-if",!0)])),_:1})])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),x().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Gt,{ref_key:"formPrivacyRef",ref:r,data:k(v)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-3e60c551"]]),Yl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u([]),r=u(!1),n=u([]),p=u(""),m=u(null),z=u(null),E=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),M=s((()=>l.global)),A=s((()=>`${E.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),j=()=>{let e=[];if(E.value.autofill){let t={title:"已自动填充"};e.push(t)}if(E.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},W=e=>{"privacy"==e&&z.value.open()},V=s((()=>{var e="";return e+="position:relative;",E.value.componentStartBgColor&&E.value.componentEndBgColor?e+=`background:linear-gradient(${E.value.componentGradientAngle},${E.value.componentStartBgColor},${E.value.componentEndBgColor});`:E.value.componentStartBgColor?e+="background-color:"+E.value.componentStartBgColor+";":E.value.componentEndBgColor&&(e+="background-color:"+E.value.componentEndBgColor+";"),E.value.componentBgUrl&&(e+=`background-image:url('${d(E.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),E.value.topRounded&&(e+="border-top-left-radius:"+2*E.value.topRounded+"rpx;"),E.value.topRounded&&(e+="border-top-right-radius:"+2*E.value.topRounded+"rpx;"),E.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*E.value.bottomRounded+"rpx;"),E.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*E.value.bottomRounded+"rpx;"),e}));c((()=>{Y(),"decorate"==o.mode&&f((()=>E.value),((e,t)=>{e&&"FormRadio"==e.componentName&&Y()})),"style-3"==E.value.style&&E.value.field.value.length>0&&(n.value=E.value.field.value[0].id),E.value.field.value.length>0&&(p.value=E.value.field.value[0].id)}));const Y=()=>{},H=s((()=>{let e="";return e+=`请选择${E.value.field.name}`,e})),U=s((()=>{let e=[];return E.value.field.value.forEach((t=>{const l=E.value.options.find((e=>e.id===t.id));l&&e.push(l.text)})),e.join(", ")})),O=s((()=>"decorate"===o.mode)),L=()=>{O.value||(r.value=!0)},G=e=>{p.value=e;const t=E.value.options.find((t=>t.id===e));E.value.field.value=[{id:t.id,text:t.text}],r.value=!1},q=e=>{O.value||(p.value=e.id,E.value.field.value=[{id:e.id,text:e.text}])};return t({verify:()=>{const e={code:!0,message:""};return E.value.field.required&&""==E.value.field.value.length&&"decorate"!=o.mode&&(e.code=!1,e.message=H.value),m.value=e,e},reset:()=>{p.value="",n.value=[],a.value=[],E.value.field.value=[]}}),(e,t)=>{const l=D,a=$,i=v(x("u-radio"),Ze),s=v(x("u-radio-group"),Je),d=I,u=v(x("u-popup"),Ve);return k(E).viewFormDetail?(g(),b(a,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(g(),b(a,{key:0,class:"base-layout-one"},{default:y((()=>[h(a,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(E).field.name),1)])),_:1}),(g(!0),R(B,null,F(k(E).field.value,((e,t)=>(g(),b(l,{key:t,class:"detail-one-content-value"},{default:y((()=>[w(C(e.text),1)])),_:2},1024)))),128))])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(g(),b(a,{key:1,class:"base-layout-two"},{default:y((()=>[h(a,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(E).field.name),1)])),_:1}),h(a,{class:"flex items-center justify-end"},{default:y((()=>[(g(!0),R(B,null,F(k(E).field.value,((e,t)=>(g(),b(a,{class:"detail-two-content-value w-[80%]",key:t},{default:y((()=>[w(C(e.text),1)])),_:2},1024)))),128)),k(E).field.value&&k(E).field.value.length?S("v-if",!0):(g(),b(l,{key:0},{default:y((()=>[w(C(k(P)("notHave")),1)])),_:1})),k(E).isShowArrow?(g(),b(l,{key:1,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(a,{key:1,style:_(k(V)),class:"form-item-frame"},{default:y((()=>["style-1"==k(M).completeLayout?(g(),b(a,{key:0,class:"base-layout-one"},{default:y((()=>[h(a,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(E).textColor,"font-size":2*k(E).fontSize+"rpx","font-weight":k(E).fontWeight})},{default:y((()=>[w(C(k(E).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(E).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(E).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(E).field.remark.text?(g(),b(a,{key:0,class:"layout-one-remark",style:_({color:k(E).field.remark.color,fontSize:2*k(E).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(E).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),m.value&&!m.value.code?(g(),b(a,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(m.value.message),1)])),_:1})):S("v-if",!0),"style-1"==k(E).style?(g(),b(a,{key:2,class:"layout-one-content !flex-initial"},{default:y((()=>[h(s,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=e=>p.value=e),onChange:G,iconPlacement:"left"},{default:y((()=>[(g(!0),R(B,null,F(k(E).options,((e,t)=>(g(),b(a,{key:t,class:"mr-[40rpx]"},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(E).fontSize+"rpx",labelColor:k(E).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1})):S("v-if",!0),"style-2"==k(E).style?(g(),b(s,{key:3,modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=e=>p.value=e),onChange:G,iconPlacement:"left",placement:"column"},{default:y((()=>[(g(!0),R(B,null,F(k(E).options,((e,t)=>(g(),b(a,{key:t,onClick:t=>q(e),class:T(["layout-one-content mb-[16rpx]",{"!mb-[0]":k(E).options.length-1==t}])},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(E).fontSize+"rpx",labelColor:k(E).textColor,class:"mr-[20rpx]",label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])):S("v-if",!0),"style-3"==k(E).style?(g(),b(a,{key:4,onClick:L,class:"layout-one-content justify-between"},{default:y((()=>[k(E).field.value.length>0?(g(),b(a,{key:0},{default:y((()=>[h(l,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k(E).textColor,"font-size":2*k(E).fontSize+"rpx"})},{default:y((()=>[w(C(k(U)),1)])),_:1},8,["style"])])),_:1})):(g(),b(l,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k(E).fontSize+"rpx"})},{default:y((()=>[w(C(k(H)),1)])),_:1},8,["style"])),h(l,{class:T(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k(E).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})):S("v-if",!0),j().length?(g(),b(a,{key:5,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(j(),((e,t)=>(g(),b(a,{key:t,onClick:t=>W(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(M).completeLayout?(g(),b(a,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(E).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(a,{class:T(["layout-two-wrap",{"!pb-[20rpx]":("style-2"==k(E).style||"style-3"==k(E).style)&&k(M).borderControl,"no-border":!k(M).borderControl}])},{default:y((()=>[h(a,{class:T(["layout-two-label",{"justify-start":"left"==k(M).completeAlign,"justify-end":"right"==k(M).completeAlign}])},{default:y((()=>[h(l,{class:"required"},{default:y((()=>[w(C(k(E).field.required?"*":""),1)])),_:1}),h(l,{class:"name",style:_({color:k(E).textColor,"font-size":2*k(E).fontSize+"rpx","font-weight":k(E).fontWeight})},{default:y((()=>[w(C(k(E).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),"style-1"==k(E).style?(g(),b(a,{key:0,class:"layout-two-content"},{default:y((()=>[h(a,{class:"justify-end"},{default:y((()=>[h(s,{modelValue:p.value,"onUpdate:modelValue":t[2]||(t[2]=e=>p.value=e),onChange:G,iconPlacement:"left"},{default:y((()=>[(g(!0),R(B,null,F(k(E).options,((e,t)=>(g(),b(a,{key:t,class:"ml-[30rpx]"},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(E).fontSize+"rpx",labelColor:k(E).textColor,label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1024)))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(E).style?(g(),b(a,{key:1,class:"layout-two-content"},{default:y((()=>[h(a,{class:"justify-end w-full"},{default:y((()=>[h(s,{modelValue:p.value,"onUpdate:modelValue":t[3]||(t[3]=e=>p.value=e),onChange:G,placement:"column",iconPlacement:"left"},{default:y((()=>[(g(!0),R(B,null,F(k(E).options,((e,t)=>(g(),b(a,{key:t,onClick:t=>q(e),class:T(["border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] flex items-center h-[80rpx] mb-[16rpx] px-[16rpx] box-border",{"mb-[0]":k(E).options.length==t+1}])},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(E).fontSize+"rpx",labelColor:k(E).textColor,class:"!m-[0]",label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick","class"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(E).style?(g(),b(a,{key:2,class:"layout-two-content"},{default:y((()=>[h(a,{onClick:L,class:"px-[16rpx] box-border h-[80rpx] flex items-center justify-between border-solid border-[2rpx] border-[#e6e6e6] rounded-[10rpx] w-[100%]"},{default:y((()=>[k(E).field.value.length>0?(g(),b(a,{key:0},{default:y((()=>[h(l,{class:"mr-[10rpx] text-[28rpx]",style:_({color:k(E).textColor,"font-size":2*k(E).fontSize+"rpx"})},{default:y((()=>[w(C(k(U)),1)])),_:1},8,["style"])])),_:1})):(g(),b(l,{key:1,class:"text-[28rpx] text-[#999]",style:_({"font-size":2*k(E).fontSize+"rpx"})},{default:y((()=>[w(C(k(H)),1)])),_:1},8,["style"])),h(l,{class:T(["nc-iconfont nc-icon-xiaV6xx pull-down-arrow text-[#666]",{selected:r.value}]),style:_({"font-size":2*k(E).fontSize+2+"rpx !important"})},null,8,["class","style"])])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),m.value&&!m.value.code?(g(),b(a,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(m.value.message),1)])),_:1})):S("v-if",!0),k(E).field.remark.text?(g(),b(a,{key:2,class:"layout-two-remark",style:_({color:k(E).field.remark.color,fontSize:2*k(E).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(E).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),j().length?(g(),b(a,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(j(),((e,t)=>(g(),b(a,{key:t,onClick:t=>W(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),S(" 样式三，下拉弹窗 "),h(u,{show:r.value,mode:"bottom",onClose:t[5]||(t[5]=e=>r.value=!1)},{default:y((()=>[h(a,{class:"p-[15rpx]"},{default:y((()=>[h(d,{"scroll-y":"true",class:"max-h-[450rpx] px-[14rpx] box-border"},{default:y((()=>[h(s,{modelValue:n.value,"onUpdate:modelValue":t[4]||(t[4]=e=>n.value=e),placement:"column",onChange:G,iconPlacement:"right"},{default:y((()=>[(g(!0),R(B,null,F(k(E).options,((e,t)=>(g(),b(a,{class:"border-solid border-[0] border-b-[2rpx] border-[#e6e6e6] py-[20rpx]",onClick:N((t=>(e=>{r.value=!1,n.value=e.id,E.value.field.value=[{id:e.id,text:e.text}]})(e)),["stop"]),key:t},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(E).fontSize+"rpx",labelColor:k(E).textColor,style:{width:"100%"},label:e.text,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["show"]),"decorate"==k(o).mode?(g(),b(a,{key:2,class:"form-item-mask"})):S("v-if",!0),S(" 隐私弹窗 "),h(Gt,{ref_key:"formPrivacyRef",ref:z,data:k(A)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-5b2750c5"]]),Hl=We(n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=i(),o=u(),a=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),r=s((()=>t.global)),n=s((()=>{var e="";if(e+="position:relative;",a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:a.value.componentStartBgColor?e+="background-color:"+a.value.componentStartBgColor+";":a.value.componentEndBgColor&&(e+="background-color:"+a.value.componentEndBgColor+";"),a.value.componentBgUrl&&(e+=`background-image:url('${d(a.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),"decorate"!=l.mode&&"hover_screen_bottom"==a.value.btnPosition){e+="position: fixed !important;";var t=o.value?o.value.height:0;e+="left: 0;",e+="right: 0;",t&&r.value.bottomTabBarSwitch?e+=`bottom: ${t}px;`:e+="bottom: 0;",a.value.pageStartBgColor&&(a.value.pageStartBgColor&&a.value.pageEndBgColor?e+=`background:linear-gradient(${a.value.pageGradientAngle},${a.value.pageStartBgColor},${a.value.pageEndBgColor});`:a.value.pageStartBgColor?e+=`background: ${a.value.pageStartBgColor};`:a.value.pageEndBgColor&&(e+=`background: ${a.value.pageEndBgColor};`)),a.value.margin&&(a.value.margin.top>0&&(e+="padding-top:"+2*a.value.margin.top+"rpx;"),t&&r.value.bottomTabBarSwitch?e+="padding-bottom:"+2*a.value.margin.bottom+"rpx;":e+=`padding-bottom: ${2*(a.value.margin.bottom+Y.value)}rpx;`,e+="padding-right:"+2*a.value.margin.both+"rpx;",e+="padding-left:"+2*a.value.margin.both+"rpx;")}else"hover_screen_bottom"==a.value.btnPosition&&(e+="position: fixed !important;",e+="left: 0;",e+="right: 0;",e+="bottom: 0;");return e})),v=s((()=>{var e="";return"hover_screen_bottom"==a.value.btnPosition&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:a.value.componentStartBgColor?e+="background-color:"+a.value.componentStartBgColor+";":a.value.componentEndBgColor&&(e+="background-color:"+a.value.componentEndBgColor+";")),e})),x=s((()=>{var e="";return a.value.componentBgUrl&&(e+="position:absolute;top:0;right:0;left:0;bottom:0;",e+=`background: rgba(0,0,0,${a.value.componentBgAlpha/10});`,a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;")),e})),R=s((()=>{var e="";return e+=`color: ${a.value.resetBtn.color};`,e+=`background-color: ${a.value.resetBtn.bgColor};`,a.value.topElementRounded&&(e+="border-top-left-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.topElementRounded&&(e+="border-top-right-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomElementRounded+"rpx;"),e})),B=s((()=>{var e="";return e+=`color: ${a.value.submitBtn.color};`,e+=`background-color: ${a.value.submitBtn.bgColor};`,a.value.topElementRounded&&(e+="border-top-left-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.topElementRounded&&(e+="border-top-right-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomElementRounded+"rpx;"),a.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomElementRounded+"rpx;"),e})),F=s((()=>{let e="";return e+=`padding-bottom: ${W.value}px;`,e})),T=u([]);c((()=>{D(),"decorate"==l.mode?f((()=>a.value),((e,t)=>{e&&"FormSubmit"==e.componentName&&D()})):(E(),f((()=>l.value),((e,t)=>{if(e){let t=N(e);t.components.length&&uni.setStorageSync("diyFormStorage_"+l.id,t)}}),{deep:!0})),p((()=>{o.value=uni.getStorageSync("tabbarInfo")}))}));const D=()=>{},E=()=>{for(let e=0;e<l.value.length;e++){let t=l.value[e];"diy_form"==t.componentType&&"FormSubmit"!=t.componentName&&T.value.push(t)}},I=u(!1);let M=be()[be().length-1];const A=()=>{if("decorate"===l.mode)return;let e=!0;for(let n=0;n<T.value.length;n++){let t=T.value[n];if(t.field.required||t.field.value){let o=`diy${t.componentName}Ref`,a=!1;if(l.componentRefs[o]){for(let e=0;e<l.componentRefs[o].length;e++){let t=l.componentRefs[o][e].verify();if(t&&!t.code){a=!0,ie({title:t.message,icon:"none"});break}}if(a){e=!1;break}}}}if(!_e())return void ke().setLoginBack({url:"/app/pages/index/diy_form",param:{form_id:l.id}});if(!e)return;if(I.value)return;I.value=!0;let t=uni.getStorageSync("diyFormStorage_"+l.id),o={};o=t?t.components:N(T.value).components;const a={form_id:l.id,value:o,relate_id:""};let r=Qe;uni.getStorageSync("personalFormRecordId")?(r=Ke,a.record_id=uni.getStorageSync("personalFormRecordId")):a.record_id="",r(a).then((e=>{uni.removeStorageSync("diyFormStorage_"+l.id),uni.removeStorageSync("personalFormRecordId"),"app/pages/member/personal_form"==M.route?we({url:"/app/pages/member/personal",mode:"redirectTo"}):we({url:"/app/pages/index/diy_form_result",param:{record_id:e.data,form_id:l.id},mode:"redirectTo"}),I.value=!1})).catch((()=>{I.value=!1}))},j=()=>{for(let e=0;e<T.value.length;e++){let t=T.value[e],o=`diy${t.componentName}Ref`;l.componentRefs[o]&&l.componentRefs[o].forEach((e=>{e.reset&&e.reset(t)}))}},N=e=>{let t={validTime:ye(5),components:[]};return e.forEach((e=>{if("diy_form"==e.componentType&&"FormSubmit"!=e.componentName&&e.field.cache){let l=he(e.field);delete l.remark,delete l.detailComponent,delete l.default,t.components.push({id:e.id,componentName:e.componentName,componentType:e.componentType,componentTitle:e.componentTitle,isHidden:e.isHidden,field:l})}})),t};let W=u(0);const V=z();let Y=u(0);return p((()=>{const e=m().in(V);e.select(".iphone-secure").boundingClientRect((e=>{Y.value=e?e.height:0})).exec(),setTimeout((()=>{e.select(".submit-wrap").boundingClientRect((e=>{W.value=e?e.height:0})).exec()}),500)})),(e,t)=>{const o=$;return g(),b(o,null,{default:y((()=>[h(o,{class:"overflow-hidden"},{default:y((()=>[h(o,{style:_(k(x))},null,8,["style"]),h(o,{class:"relative submit-wrap z-10",style:_(k(n))},{default:y((()=>[h(o,{class:"flex flex-col items-center",style:_(k(v))},{default:y((()=>[h(o,{class:"w-[100%] h-[86rpx] text-[28rpx] flex items-center justify-center",onClick:A,style:_(k(B))},{default:y((()=>[w(C(k(a).submitBtn.text),1)])),_:1},8,["style"]),k(a).resetBtn.control?(g(),b(o,{key:0,class:"w-[100%] h-[86rpx] mt-[20rpx] text-[28rpx] flex items-center justify-center",onClick:j,style:_(k(R))},{default:y((()=>[w(C(k(a).resetBtn.text),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1}),"hover_screen_bottom"==k(a).btnPosition&&"decorate"!=k(l).mode?(g(),b(o,{key:0,class:"w-[100%]",style:_(k(F))},null,8,["style"])):S("v-if",!0),S(" 苹果安全距离，辅助计算 "),h(o,{class:"iphone-secure"}),S(" 遮罩层，装修使用 "),"decorate"==k(l).mode?(g(),b(o,{key:1,class:"form-item-mask"})):S("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-90e54893"]]),Ul=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u({id:0,name:"",mobile:"",province_id:0,city_id:0,district_id:0,lat:"",lng:"",address:"",address_name:"",full_address:"",is_default:0,area:""}),r=u(Array(10).fill(!1)),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component));s((()=>"decorate"===o.mode));const p=u(null),m=u(!1),T=s((()=>[{name:"男",value:1},{name:"女",value:2}])),z=e=>{p.value&&(p.value.value=e.value),m.value=!1};let E=new Date;E.setFullYear(E.getFullYear()+1);const M=u(E.getTime());let A=new Date;const j=u(A.getTime()),W=u(!1),V=u(!1);s((()=>p.value&&p.value.value?P(p.value.value,p.value.dateFormat):n.value.placeholder||"请选择日期"));const Y=e=>{p.value&&(p.value.value=e[0]),W.value=!1},H=e=>{p.value&&(p.value.value=e.value),V.value=!1},U=e=>(de(Date.parse(e.date)/1e3,"year_month_day"),e),P=(e="",t)=>{console.log(e,t);let l="YYYY-MM-DD HH:mm"==t?e:se(e),o=l>9999999999?new Date(l):new Date(1e3*l),a=o.getFullYear(),r=String(o.getMonth()+1).padStart(2,"0"),n=String(o.getDate()).padStart(2,"0");console.log(a);const i=String(o.getHours()).padStart(2,"0"),s=String(o.getMinutes()).padStart(2,"0");let d="";return"YYYY年M月D日"==t?d=`${a}年${r}月${n}日`:"YYYY-MM-DD"==t?d=`${a}-${r}-${n}`:"YYYY/MM/DD"==t?d=`${a}/${r}/${n}`:"YYYY-MM-DD HH:mm"==t&&(d=`${a}-${r}-${n} ${i}:${s}`),d},O=u(),L=u(!1),G=e=>{!L.value||a.value.province_id!=e.province.id&&a.value.city_id==e.city.id&&a.value.district_id==e.district.id||(a.value.lat="",a.value.lng=""),a.value.province_id=e.province.id||0,a.value.city_id=e.city.id||0,a.value.district_id=e.district.id||0,a.value.area=`${e.province.name||""}${e.city.name||""}${e.district.name||""}`,p.value.value=a.value.area,L.value=!1},q=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{X(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormTable"==e.componentName&&X()})),console.log(n.value)}));const X=()=>{};return t({verify:()=>({code:!0,message:""}),reset:()=>{}}),(e,t)=>{const l=D,o=$,i=v(x("u-radio"),Ze),s=v(x("u-radio-group"),Je),d=I,u=v(x("u-popup"),Ve),c=v(x("u-action-sheet"),et),f=v(x("area-select"),Ue),p=v(x("u-calendar"),Bl),E=v(x("u-datetime-picker"),Fl);return g(),b(o,{style:_(k(q)),class:"form-item-frame"},{default:y((()=>[h(o,{class:"relative base-layout-one"},{default:y((()=>[h(o,{class:"p-[10rpx] flex items-center"},{default:y((()=>[h(o,{class:"w-[27%] mr-[10rpx] flex items-center"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"text-[#ec0003]"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})])),_:1})])),_:1})])),_:1}),S(" 下拉弹窗 "),(g(!0),R(B,null,F(k(n).columnList,((e,t)=>(g(),b(u,{key:"popup-"+t,show:r.value[t],mode:"bottom",onClose:e=>r.value[t]=!1},{default:y((()=>[h(o,{class:"p-[15rpx]"},{default:y((()=>[h(d,{"scroll-y":"true",class:"max-h-[450rpx] px-[14rpx] box-border"},{default:y((()=>[h(s,{modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,placement:"column",onChange:e=>(e=>{const t=n.value.columnList[e];t&&t.value&&(r.value[e]=!1)})(t),iconPlacement:"right"},{default:y((()=>[(g(!0),R(B,null,F(e.options,((e,l)=>(g(),b(o,{class:"border-solid border-[0] border-b-[2rpx] border-[#e6e6e6] py-[20rpx]",key:l,onClick:N((l=>((e,t)=>{n.value.columnList[e].value=t.id,r.value[e]=!1})(t,e)),["stop"])},{default:y((()=>[h(i,{activeColor:"var(--primary-color)",labelSize:2*k(n).fontSize+"rpx",labelColor:k(n).textColor,style:{width:"100%"},label:e.label,name:e.id},null,8,["labelSize","labelColor","label","name"])])),_:2},1032,["onClick"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["show","onClose"])))),128)),S(" 修改性别 "),h(c,{actions:k(T),show:m.value,closeOnClickOverlay:!0,safeAreaInsetBottom:!0,onClose:t[0]||(t[0]=e=>m.value=!1),onSelect:z},null,8,["actions","show"]),h(f,{ref_key:"areaRef",ref:O,onComplete:G,"area-id":a.value.district_id},null,8,["area-id"]),h(o,{class:"calendar-wrap"},{default:y((()=>[h(p,{show:W.value,mode:"single",onConfirm:Y,onClose:t[1]||(t[1]=e=>W.value=!1),closeOnClickOverlay:"true",formatter:U,confirmDisabledText:"禁止选择",color:"var(--primary-color)",ref:"calendar",maxDate:M.value},null,8,["show","maxDate"]),h(E,{show:V.value,mode:"datetime",onCancel:t[2]||(t[2]=e=>V.value=!1),closeOnClickOverlay:"true",onConfirm:H,onClose:t[3]||(t[3]=e=>V.value=!1),minDate:j.value},null,8,["show","minDate"])])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-b45b944c"]]),Pl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(null),r=u(null),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),p=s((()=>l.global)),m=s((()=>{let e="";return e+=n.value.placeholder,e})),v=s((()=>`${n.value.field.name}已开启隐私保护，提交后会部分打码，只有你自己和管理员才能查看完整信息`)),x=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}if(n.value.field.privacyProtection){let t={title:"已开启隐私保护",type:"privacy"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},z=e=>{"privacy"==e&&r.value.open()},E=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:n.value.componentStartBgColor?e+="background-color:"+n.value.componentStartBgColor+";":n.value.componentEndBgColor&&(e+="background-color:"+n.value.componentEndBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{I(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormTextarea"==e.componentName&&I()}))}));const I=()=>{("decorate"==o.mode||""==n.value.field.value&&n.value.field.default)&&(n.value.field.value=n.value.field.default)},M=u("38rpx"),A=e=>{let t=e.detail.height/e.detail.lineCount;n.value.rowCount>e.detail.lineCount?M.value=e.detail.height?2*e.detail.height+"rpx":"38rpx":n.value.rowCount<=e.detail.lineCount&&(M.value=t?t*n.value.rowCount*2+"rpx":"38rpx")},j=s((()=>"decorate"==o.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=o.mode&&(e.code=!1,e.message=`${m.value}`),a.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,i=$,s=L,d=ae("viwe");return k(n).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(i,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(n).field.value||k(P)("notHave")),1)])),_:1}),k(n).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,style:_(k(E)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),h(i,{class:"layout-one-content !py-[20rpx] !h-[auto]"},{default:y((()=>[h(s,{class:"w-[100%]",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx",height:M.value}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(j),onLinechange:A,maxlength:"500"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1}),x().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(d,{class:"layout-two-content"},{default:y((()=>[h(s,{class:"w-[100%]",placeholder:k(m),placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx",height:M.value}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(j),onLinechange:A,maxlength:"500"},null,8,["placeholder","placeholder-style","style","modelValue","disabled"])])),_:1})])),_:1},8,["class"]),a.value&&!a.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(a.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),x().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(x(),((e,t)=>(g(),b(i,{key:t,onClick:t=>z(e.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),h(Gt,{ref_key:"formPrivacyRef",ref:r,data:k(v)},null,8,["data"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-a1011776"]]),Ol=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(!1);u(!1);const r=u(!1),n=u(null),p=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),m=s((()=>l.global)),z=()=>{let e=[];if(p.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},E=s((()=>{var e="";return p.value.field.value?(e=p.value.field.value,p.value.field.value=e):p.value.defaultControl?("current"==p.value.timeWay?e=N():"diy"==p.value.timeWay&&(e=p.value.field.default),p.value.field.value=e):e=p.value.placeholder,e})),I=s((()=>{var e="";return e+="position:relative;",p.value.componentStartBgColor&&p.value.componentEndBgColor?e+=`background:linear-gradient(${p.value.componentGradientAngle},${p.value.componentStartBgColor},${p.value.componentEndBgColor});`:p.value.componentStartBgColor?e+="background-color:"+p.value.componentStartBgColor+";":p.value.componentEndBgColor&&(e+="background-color:"+p.value.componentEndBgColor+";"),p.value.componentBgUrl&&(e+=`background-image:url('${d(p.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),p.value.topRounded&&(e+="border-top-left-radius:"+2*p.value.topRounded+"rpx;"),p.value.topRounded&&(e+="border-top-right-radius:"+2*p.value.topRounded+"rpx;"),p.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*p.value.bottomRounded+"rpx;"),p.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*p.value.bottomRounded+"rpx;"),e}));c((()=>{M(),"decorate"==o.mode&&f((()=>p.value),((e,t)=>{e&&"FormTime"==e.componentName&&M()}))}));const M=()=>{},A=()=>{"decorate"!==o.mode&&(r.value=!0)},j=e=>{p.value.field.value=e.value,a.value=!1},N=(e="")=>{let t=e?new Date(e):new Date,l="";return l=`${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`,l};return t({verify:()=>{const e={code:!0,message:""};return p.value.field.required&&""==p.value.field.value&&(e.code=!1,e.message=`请选择${p.value.placeholder}`),n.value=e,e},reset:()=>{p.value.field.value=""}}),(e,t)=>{const l=D,r=$,i=v(x("u-datetime-picker"),Fl);return k(p).viewFormDetail?(g(),b(r,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(g(),b(r,{key:0,class:"base-layout-one"},{default:y((()=>[h(r,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(p).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(p).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(g(),b(r,{key:1,class:"base-layout-two"},{default:y((()=>[h(r,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(p).field.name),1)])),_:1}),h(r,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(p).field.value||k(P)("notHave")),1)])),_:1}),k(p).isShowArrow?(g(),b(l,{key:0,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(r,{key:1,style:_(k(I)),class:"form-item-frame"},{default:y((()=>["style-1"==k(m).completeLayout?(g(),b(r,{key:0,class:"base-layout-one"},{default:y((()=>[h(r,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx","font-weight":k(p).fontWeight})},{default:y((()=>[w(C(k(p).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(p).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(p).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(p).field.remark.text?(g(),b(r,{key:0,class:"layout-one-remark",style:_({color:k(p).field.remark.color,fontSize:2*k(p).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(p).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),n.value&&!n.value.code?(g(),b(r,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(n.value.message),1)])),_:1})):S("v-if",!0),h(r,{class:"layout-one-content"},{default:y((()=>[h(r,{class:"nc-iconfont nc-icon-a-shijianV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(r,{class:T(["flex-1 text-overflow-ellipsis flex",{"!text-[#999]":!k(p).field.value&&!k(p).defaultControl}]),style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx"}),onClick:t[0]||(t[0]=e=>a.value=!0)},{default:y((()=>[w(C(k(E)),1)])),_:1},8,["class","style"])])),_:1}),z().length?(g(),b(r,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(z(),((e,t)=>(g(),b(r,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(m).completeLayout?(g(),b(r,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(p).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(r,{class:T(["layout-two-wrap",{"no-border":!k(m).borderControl}])},{default:y((()=>[h(r,{class:T(["layout-two-label",{"justify-start":"left"==k(m).completeAlign,"justify-end":"right"==k(m).completeAlign}])},{default:y((()=>[k(p).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(p).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx","font-weight":k(p).fontWeight})},{default:y((()=>[w(C(k(p).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(r,{class:"layout-two-content",onClick:A},{default:y((()=>[h(r,{class:T(["flex-1 text-overflow-ellipsis flex justify-end",{"!text-[#999]":!k(p).field.value&&!k(p).defaultControl}]),style:_({color:k(p).textColor,"font-size":2*k(p).fontSize+"rpx"}),onClick:t[1]||(t[1]=e=>a.value=!0)},{default:y((()=>[w(C(k(E)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),n.value&&!n.value.code?(g(),b(r,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(n.value.message),1)])),_:1})):S("v-if",!0),k(p).field.remark.text?(g(),b(r,{key:2,class:"layout-two-remark",style:_({color:k(p).field.remark.color,fontSize:2*k(p).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(p).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),z().length?(g(),b(r,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(z(),((e,t)=>(g(),b(r,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(i,{show:a.value,modelValue:k(p).field.value,"onUpdate:modelValue":t[2]||(t[2]=e=>k(p).field.value=e),mode:"time",onCancel:t[3]||(t[3]=e=>a.value=!1),onClose:t[4]||(t[4]=e=>a.value=!1),onConfirm:j,closeOnClickOverlay:"true"},null,8,["show","modelValue"]),S(" 遮罩层，装修使用 "),"decorate"==k(o).mode?(g(),b(r,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-b4a344e5"]]),Ll=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u(!1),r=u(!1),n=u(!1),p=u(null),m=s((()=>"decorate"==o.mode?o.value[l.index]:l.component)),z=s((()=>l.global)),E=()=>{let e=[];if(m.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},I=s((()=>{let e="",t="";return m.value.field.value.start.date?(e=m.value.field.value.start.date,t=m.value.field.value.start.date):m.value.start.defaultControl?"current"==m.value.start.timeWay?(e=G(),t=G()):"diy"==m.value.start.timeWay&&(e=m.value.field.default.start.date,t=m.value.field.default.start.date):(e=m.value.start.placeholder,t=""),m.value.field.value.start.date=t,m.value.field.value.start.timestamp=t?q(t):0,e})),M=s((()=>{let e="",t="";if(m.value.field.value.end.date)e=m.value.field.value.end.date,t=m.value.field.value.end.date;else if(m.value.end.defaultControl)if("current"==m.value.end.timeWay){let l=new Date,o=new Date(l.getTime()+6e5);e=G(o),t=G(o)}else"diy"==m.value.end.timeWay&&(e=m.value.field.default.end.date,t=m.value.field.default.end.date);else e=m.value.end.placeholder,t="";return m.value.field.value.end.date=t,m.value.field.value.end.timestamp=t?q(t):0,e})),A=s((()=>{var e="";return e+="position:relative;",m.value.componentStartBgColor&&m.value.componentEndBgColor?e+=`background:linear-gradient(${m.value.componentGradientAngle},${m.value.componentStartBgColor},${m.value.componentEndBgColor});`:m.value.componentStartBgColor?e+="background-color:"+m.value.componentStartBgColor+";":m.value.componentEndBgColor&&(e+="background-color:"+m.value.componentEndBgColor+";"),m.value.componentBgUrl&&(e+=`background-image:url('${d(m.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),m.value.topRounded&&(e+="border-top-left-radius:"+2*m.value.topRounded+"rpx;"),m.value.topRounded&&(e+="border-top-right-radius:"+2*m.value.topRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*m.value.bottomRounded+"rpx;"),m.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*m.value.bottomRounded+"rpx;"),e}));c((()=>{j(),"decorate"==o.mode&&f((()=>m.value),((e,t)=>{e&&"FormTimeScope"==e.componentName&&j()}))}));const j=()=>{},N=()=>{"decorate"!==o.mode&&(n.value=!0)},W=e=>{m.value.field.value.start.date=e.value,m.value.field.value.start.timestamp=q(e.value);let t=e.value,l=new Date(`1970-01-01T${e.value}:00`);l.setMinutes(l.getMinutes()+10),t=G(l),m.value.field.value.end.date=t,m.value.field.value.end.timestamp=q(t),a.value=!1},V=e=>{m.value.field.value.end.date=e.value,m.value.field.value.end.timestamp=q(e.value),r.value=!1},Y=e=>{O.value||(O.value=m.value.field.value.end.date),m.value.field.value.end.date=e.value},H=()=>{O.value&&(m.value.field.value.end.date=O.value),O.value="",r.value=!1},U=s((()=>{let e=m.value.field.value.start.date.split(":");return Number(e[0]?e[0]:"0")}));let O=u("");const L=s({get:()=>{let e=m.value.field.value.start.date.split(":"),t=m.value.field.value.end.date.split(":"),l=e[0]==t[0]?e[1]:0;return Number(l||"0")},set:e=>{}}),G=(e="")=>{let t=e?new Date(e):new Date;return`${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},q=e=>{let t=e.split(":"),l=0;return t[0]&&(l+=60*t[0]*60),t[1]&&(l+=60*t[1]),t[2]&&(l+=t[2]),l};return t({verify:()=>{const e={code:!0,message:""};return m.value.field.required&&""==m.value.field.value.start.date?(e.code=!1,e.message=`请选择${m.value.start.placeholder}`):m.value.field.required&&""==m.value.field.value.end.date?(e.code=!1,e.message=`请选择${m.value.end.placeholder}`):m.value.field.value.start.timestamp>=m.value.field.value.end.timestamp&&m.value.field.value.start.timestamp&&m.value.field.value.end.timestamp&&(e.code=!1,e.message="开始时间不能大于等于结束时间"),p.value=e,e},reset:()=>{m.value.field.value.start.date="",m.value.field.value.start.timestamp=0,m.value.field.value.end.date="",m.value.field.value.end.timestamp=0}}),(e,t)=>{const l=D,n=$,i=v(x("u-datetime-picker"),Fl);return k(m).viewFormDetail?(g(),b(n,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(z).completeLayout?(g(),b(n,{key:0,class:"base-layout-one"},{default:y((()=>[k(m).field.value.start.date||k(m).field.value.end.date?(g(),b(n,{key:0,class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(n,{class:"detail-one-content-value"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(m).field.value.start.date),1)])),_:1}),k(m).field.value.start.date&&k(m).field.value.end.date?(g(),b(l,{key:0},{default:y((()=>[w(" -")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(m).field.value.end.date),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(z).completeLayout?(g(),b(n,{key:1,class:"base-layout-two"},{default:y((()=>[k(m).field.value.start.date||k(m).field.value.end.date?(g(),b(n,{key:0,class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(m).field.name),1)])),_:1}),h(n,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[h(l,null,{default:y((()=>[w(C(k(m).field.value.start.date),1)])),_:1}),k(m).field.value.start.date&&k(m).field.value.end.date?(g(),b(l,{key:0},{default:y((()=>[w(" -")])),_:1})):S("v-if",!0),h(l,null,{default:y((()=>[w(C(k(m).field.value.end.date),1)])),_:1}),k(m).isShowArrow?(g(),b(l,{key:1,class:"iconfont iconfanhui1 text-[#888] !text-[20rpx] ml-[10rpx]"})):S("v-if",!0)])),_:1})])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0)])),_:1})):(g(),b(n,{key:1,style:_(k(A)),class:"form-item-frame"},{default:y((()=>["style-1"==k(z).completeLayout?(g(),b(n,{key:0,class:"base-layout-one"},{default:y((()=>[h(n,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(m).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(m).field.remark.text?(g(),b(n,{key:0,class:"layout-one-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),p.value&&!p.value.code?(g(),b(n,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(p.value.message),1)])),_:1})):S("v-if",!0),h(n,{class:"flex items-center"},{default:y((()=>[h(n,{class:"layout-one-content flex-1",onClick:t[0]||(t[0]=e=>a.value=!0)},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-a-shijianV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(n,{class:T(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(m).defaultControl&&!k(m).field.value.start.date}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w(C(k(I)),1)])),_:1},8,["class","style"])])),_:1}),h(n,{class:"mx-[10rpx]",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(n,{class:"layout-one-content flex-1",onClick:t[1]||(t[1]=e=>r.value=!0)},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-a-shijianV6xx-36 !text-[32rpx] text-[#999] mr-[16rpx]"}),h(n,{class:T(["flex-1 text-overflow-ellipsis",{"!text-[#999]":!k(m).defaultControl&&!k(m).field.value.end.date}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w(C(k(M)),1)])),_:1},8,["class","style"])])),_:1})])),_:1}),E().length?(g(),b(n,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(E(),((e,t)=>(g(),b(n,{key:t,onClick:t=>{e.type},class:"layout-one-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(z).completeLayout?(g(),b(n,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(m).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(n,{class:T(["layout-two-wrap",{"no-border":!k(z).borderControl}])},{default:y((()=>[h(n,{class:T(["layout-two-label",{"justify-start":"left"==k(z).completeAlign,"justify-end":"right"==k(z).completeAlign}])},{default:y((()=>[k(m).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(m).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx","font-weight":k(m).fontWeight})},{default:y((()=>[w(C(k(m).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(n,{class:"layout-two-content",onClick:N},{default:y((()=>[h(n,{class:T(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(m).field.value.start.date&&!k(m).defaultControl}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),onClick:t[2]||(t[2]=e=>a.value=!0)},{default:y((()=>[w(C(k(I)),1)])),_:1},8,["class","style"]),h(n,{class:"mx-[10rpx]",style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"})},{default:y((()=>[w("-")])),_:1},8,["style"]),h(n,{class:T(["text-overflow-ellipsis flex justify-center",{"!text-[#999]":!k(m).field.value.end.date&&!k(m).defaultControl}]),style:_({color:k(m).textColor,"font-size":2*k(m).fontSize+"rpx"}),onClick:t[3]||(t[3]=e=>r.value=!0)},{default:y((()=>[w(C(k(M)),1)])),_:1},8,["class","style"]),h(l,{class:"nc-iconfont !text-[#666] !text-[36rpx] nc-icon-youV6xx -mr-[8rpx]"})])),_:1})])),_:1},8,["class"]),p.value&&!p.value.code?(g(),b(n,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(p.value.message),1)])),_:1})):S("v-if",!0),k(m).field.remark.text?(g(),b(n,{key:2,class:"layout-two-remark",style:_({color:k(m).field.remark.color,fontSize:2*k(m).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(m).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),E().length?(g(),b(n,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(E(),((e,t)=>(g(),b(n,{key:t,onClick:t=>{e.type},class:"layout-two-attribute-item"},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),h(i,{show:a.value,modelValue:k(m).field.value.start.date,"onUpdate:modelValue":t[4]||(t[4]=e=>k(m).field.value.start.date=e),mode:"time",onCancel:t[5]||(t[5]=e=>a.value=!1),onConfirm:W,onClose:t[6]||(t[6]=e=>a.value=!1),closeOnClickOverlay:"true"},null,8,["show","modelValue"]),h(i,{show:r.value,minHour:k(U),minMinute:k(L),modelValue:k(m).field.value.end.date,"onUpdate:modelValue":t[7]||(t[7]=e=>k(m).field.value.end.date=e),mode:"time",onCancel:H,onConfirm:V,onChange:Y,onClose:t[8]||(t[8]=e=>a.value=!1),closeOnClickOverlay:"true"},null,8,["show","minHour","minMinute","modelValue"]),S(" 遮罩层，装修使用 "),"decorate"==k(o).mode?(g(),b(n,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-f4181d1c"]]),Gl=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=i(),a=u([]),r=u(null),n=s((()=>"decorate"==o.mode?o.value[l.index]:l.component));s((()=>{let e="";return n.value&&n.value.uploadMode&&("shoot_and_album"==n.value.uploadMode?e="album":"shoot_only"==n.value.uploadMode&&(e="camera")),e}));const p=s((()=>l.global)),m=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{j(),"decorate"==o.mode&&f((()=>n.value),((e,t)=>{e&&"FormVideo"==e.componentName&&j()}))})),s((()=>"decorate"===o.mode)),s((()=>a.value.map((e=>({url:d(e)})))));const z=e=>{e.file.forEach((e=>{I(e)}))},I=e=>{Ce({filePath:e.url,name:"file"}).then((e=>{n.value.field.value.push(e.data.url)})).catch((()=>{}))},M=e=>{n.value.field.value.splice(e.index,1)},A=()=>{let e=[];if(n.value.autofill){let t={title:"已自动填充"};e.push(t)}return e.forEach(((e,t,l)=>{if(t!=l.length-1){let e={title:"|"};l.push(e)}})),e},j=()=>{};return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&(!n.value.field.value||n.value.field.value&&!n.value.field.value.length)&&(e.code=!1,e.message="请上传视频"),r.value=e,e},reset:()=>{n.value.field.value=[]}}),(e,t)=>{const l=D,a=E,i=$,s=v(x("u-upload"),Xe);return k(n).viewFormDetail?(g(),b(i,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"flex flex-wrap detail-one-content-value pt-[6rpx]"},{default:y((()=>[(g(!0),R(B,null,F(k(n).field.value,((t,l)=>(g(),b(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:l},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(t),onClick:o=>e.handleImg(t,l),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>[h(i,{class:"detail-two-content"},{default:y((()=>[h(l,{class:"detail-two-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(i,{class:"flex flex-wrap w-[80%] justify-end"},{default:y((()=>[(g(!0),R(B,null,F(k(n).field.value,((t,l)=>(g(),b(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx]",key:l},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(t),onClick:o=>e.handleImg(t,l),mode:"aspectFill"},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(i,{key:1,style:_(k(m)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(i,{key:0,class:"base-layout-one"},{default:y((()=>[h(i,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(i,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(g(),b(i,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(i,{class:"flex flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(k(n).field.value,((e,t)=>(g(),b(i,{class:"relative w-[180rpx] !h-[180rpx] mr-[16rpx] mb-[16rpx] layout-one-content",key:t},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(i,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>M(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),S(' \t<view class="flex items-center flex-1"\n\t\t\t\t\tv-if="diyComponent.uploadMode.length > 1 && diyComponent.field.value.length <= 0">\n\t\t\t\t\t<view class="layout-one-content !p-[0] flex-1 !items-stretch !h-[100rpx]">\n\t\t\t\t\t\t<u-upload accept="image" @afterRead="afterRead" multiple :maxCount="9" capture="camera">\n\t\t\t\t\t\t\t<view class="flex items-center h-[100%] w-[100%] pl-[30rpx] box-border">\n\t\t\t\t\t\t\t\t<text class="nc-iconfont nc-icon-xiangjiV6xx"></text>\n\t\t\t\t\t\t\t\t<text class="text-[28rpx] ml-[10rpx]">拍照上传</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-upload>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class="layout-one-content !p-[0] ml-[20rpx] !items-stretch flex-1 !h-[100rpx]">\n\t\t\t\t\t\t<u-upload accept="image" @afterRead="afterRead" multiple :maxCount="9" capture="album">\n\t\t\t\t\t\t\t<view class="flex items-center h-[100%] w-[100%] pl-[30rpx] box-border">\n\t\t\t\t\t\t\t\t<text class="nc-iconfont nc-icon-tupiandaohangpc"></text>\n\t\t\t\t\t\t\t\t<text class="text-[28rpx] ml-[10rpx]">从相册中选择</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</u-upload>\n\t\t\t\t\t</view>\n\t\t\t\t</view> '),h(i,{class:"layout-one-content h-[180rpx] w-[180rpx] !px-[0]"},{default:y((()=>["shoot_and_album"==k(n).uploadMode?(g(),b(s,{key:0,accept:"image",onAfterRead:z,capture:"album",maxCount:1},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("添加视频")])),_:1})])),_:1})])),_:1})):(g(),b(s,{key:1,accept:"image",onAfterRead:z,maxCount:1,maxDuration:60,capture:"camera"},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("拍摄上传")])),_:1})])),_:1})])),_:1}))])),_:1})])),_:1}),A().length?(g(),b(i,{key:2,class:"layout-one-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(A(),((t,l)=>(g(),b(i,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-one-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(i,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(o).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(i,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(i,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{class:"layout-two-content flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(k(n).field.value,((e,t)=>(g(),b(i,{class:"relative border-box w-[180rpx] !h-[180rpx] ml-[16rpx] mb-[16rpx] border-box border-[2rpx] border-solid border-[#e6e6e6] rounded-[10rpx] flex items-center",key:t},{default:y((()=>[h(a,{class:"w-[100%] h-[100%]",src:k(d)(e),mode:"aspectFill"},null,8,["src"]),h(i,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:e=>M(t)},{default:y((()=>[h(l,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),h(i,{class:"items-start border-box border-[2rpx] ml-[16rpx] mb-[16rpx] border-solid border-[#e6e6e6] rounded-[10rpx]"},{default:y((()=>["shoot_and_album"==k(n).uploadMode?(g(),b(s,{key:0,accept:"video",onAfterRead:z,maxCount:1,capture:"album"},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("添加视频")])),_:1})])),_:1})])),_:1})):(g(),b(s,{key:1,accept:"video",onAfterRead:z,maxCount:1,maxDuration:60,capture:"camera"},{default:y((()=>[h(i,{class:"flex flex-col items-center justify-center w-[180rpx] h-[180rpx]"},{default:y((()=>[h(l,{class:"nc-iconfont !text-[36rpx] nc-icon-jiahaoV6xx mb-[16rpx]"}),h(l,{class:"text-[28rpx] ml-[10rpx] text-[24rpx]"},{default:y((()=>[w("拍摄上传")])),_:1})])),_:1})])),_:1}))])),_:1})])),_:1})])),_:1},8,["class"]),r.value&&!r.value.code?(g(),b(i,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(i,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),A().length?(g(),b(i,{key:3,class:"layout-two-attribute-wrap"},{default:y((()=>[(g(!0),R(B,null,F(A(),((t,l)=>(g(),b(i,{key:l,onClick:l=>e.eventFn(t.type),class:"layout-two-attribute-item"},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0)])),_:1})):S("v-if",!0),"decorate"==k(o).mode?(g(),b(i,{key:2,class:"form-item-mask"})):S("v-if",!0),S(' <view class="relative">\n\t\t\t<view class="p-[10rpx] flex items-center ">\n\t\t\t\t<view class="w-[25%] flex items-center">\n\t\t\t\t\t<text class="text-overflow-ellipsis"\n\t\t\t\t\t\t:style="{\'color\': diyComponent.textColor,\'font-size\': (diyComponent.fontSize * 2) + \'rpx\' ,\'font-weight\': diyComponent.fontWeight}">{{ diyComponent.field.name }}</text>\n\t\t\t\t\t<text class="text-[#ec0003]">{{ diyComponent.field.required ? \'*\' : \'\' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class="w-[75%] flex justify-center items-center">\n\t\t\t\t\t<u-upload :fileList="imgListPreview" :disabled="isDisabled" @afterRead="afterRead"\n\t\t\t\t\t\t@delete="deletePic" multiple :maxCount="diyComponent.limit" />\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view> ')])),_:1},8,["style"]))}}}),[["__scopeId","data-v-d2b6b77b"]]),ql=We(n({__name:"index",props:["component","index","global"],setup(e,{expose:t}){const l=e,o=Se(),a=i();u(!1);const r=u(null),n=s((()=>"decorate"==a.mode?a.value[l.index]:l.component)),p=s((()=>l.global)),m=()=>{v()},v=async()=>{o&&(console.log(o),n.value.field.value=o.info.nickname)},x=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e}));c((()=>{R(),"decorate"==a.mode&&f((()=>n.value),((e,t)=>{e&&"FormWechatName"==e.componentName&&R()}))}));const R=()=>{},B=s((()=>"decorate"==a.mode));return t({verify:()=>{const e={code:!0,message:""};return n.value.field.required&&""==n.value.field.value&&"decorate"!=a.mode&&(e.code=!1,e.message="111"),r.value=e,e},reset:()=>{n.value.field.value=""}}),(e,t)=>{const l=D,o=$,i=O;return k(n).viewFormDetail?(g(),b(o,{key:0,class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"detail-one-content"},{default:y((()=>[h(l,{class:"detail-one-content-label"},{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(l,{class:"detail-one-content-value"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(o,{key:1,class:"base-layout-two"},{default:y((()=>[h(o,{class:"detail-two-content"},{default:y((()=>[h(o,null,{default:y((()=>[w(C(k(n).field.name),1)])),_:1}),h(o,{class:"detail-two-content-value w-[80%]"},{default:y((()=>[w(C(k(n).field.value),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1})):(g(),b(o,{key:1,style:_(k(x)),class:"form-item-frame"},{default:y((()=>["style-1"==k(p).completeLayout?(g(),b(o,{key:0,class:"base-layout-one"},{default:y((()=>[h(o,{class:"layout-one-label"},{default:y((()=>[h(l,{class:"text-overflow-ellipsis",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"]),h(l,{class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1}),"decorate"==k(a).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0)])),_:1}),k(n).field.remark.text?(g(),b(o,{key:0,class:"layout-one-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),r.value&&!r.value.code?(g(),b(o,{key:1,class:"layout-one-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),h(i,{type:"nickname",class:"layout-one-content",placeholder:"获取填表人的微信名",placeholderClass:"layout-one-input-placeholder","placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[0]||(t[0]=e=>k(n).field.value=e),disabled:k(B),onClick:m},null,8,["placeholder-style","style","modelValue","disabled"]),S(' <view class="layout-one-attribute-wrap" v-if="inputAttribute().length">\n\t\t        <view v-for="(item,index) in inputAttribute()" :key="index" @click="eventFn(item.type)" class="layout-one-attribute-item">{{ item.title }}</view>\n\t\t    </view> ')])),_:1})):S("v-if",!0),"style-2"==k(p).completeLayout?(g(),b(o,{key:1,class:"base-layout-two"},{default:y((()=>["decorate"==k(a).mode&&k(n).isHidden?(g(),b(l,{key:0,class:"layout-two-is-hidden"},{default:y((()=>[w(C(k(P)("diyForm.hidden")),1)])),_:1})):S("v-if",!0),h(o,{class:T(["layout-two-wrap",{"no-border":!k(p).borderControl}])},{default:y((()=>[h(o,{class:T(["layout-two-label",{"justify-start":"left"==k(p).completeAlign,"justify-end":"right"==k(p).completeAlign}])},{default:y((()=>[k(n).field.required?(g(),b(l,{key:0,class:"required"},{default:y((()=>[w(C(k(n).field.required?"*":""),1)])),_:1})):S("v-if",!0),h(l,{class:"name",style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx","font-weight":k(n).fontWeight})},{default:y((()=>[w(C(k(n).field.name),1)])),_:1},8,["style"])])),_:1},8,["class"]),h(i,{type:"nickname",class:"layout-two-content no-flex",placeholder:"获取填表人的微信名",placeholderClass:"layout-two-input-placeholder",onClick:m,"placeholder-style":{"font-size":2*k(n).fontSize+"rpx"},style:_({color:k(n).textColor,"font-size":2*k(n).fontSize+"rpx"}),modelValue:k(n).field.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k(n).field.value=e),disabled:k(B)},null,8,["placeholder-style","style","modelValue","disabled"])])),_:1},8,["class"]),r.value&&!r.value.code?(g(),b(o,{key:1,class:"layout-two-error-message"},{default:y((()=>[w(C(r.value.message),1)])),_:1})):S("v-if",!0),k(n).field.remark.text?(g(),b(o,{key:2,class:"layout-two-remark",style:_({color:k(n).field.remark.color,fontSize:2*k(n).field.remark.fontSize+"rpx"})},{default:y((()=>[w(C(k(n).field.remark.text),1)])),_:1},8,["style"])):S("v-if",!0),S(' <view class="layout-two-attribute-wrap" v-if="inputAttribute().length">\n\t\t        <view v-for="(item,index) in inputAttribute()" :key="index" @click="eventFn(item.type)" class="layout-two-attribute-item">{{ item.title }}</view>\n\t\t    </view> ')])),_:1})):S("v-if",!0),"decorate"==k(a).mode?(g(),b(o,{key:2,class:"form-item-mask"})):S("v-if",!0)])),_:1},8,["style"]))}}}),[["__scopeId","data-v-beba8b3d"]]),Xl=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>o.value.list.length>o.value.pageCount*o.value.rowCount)),r=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),n=s((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${P.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e})),M=e=>{let t={width:""};return t.width=100/o.value.rowCount+"%",t},A=u(0),j=e=>{A.value=e.detail.current},N=(e,t)=>{let l=o.value.pageCount*o.value.rowCount;return e>=[(t-1)*l]&&e<[t*l]},W="graphic_nav_horizontal_page_slide_swiperheight_"+t.index+"_"+o.value.list.length,V=u(uni.getStorageSync(W)||"");c((()=>{O(),"decorate"==l.mode&&f((()=>o.value),((e,t)=>{e&&"GraphicNav"==e.componentName&&O()}))}));const U=z(),P=u(0),O=()=>{p((()=>{(()=>{if("horizontal"==o.value.layout&&"pageSlide"==o.value.showStyle){var e=0;m().in(U).select(".graphic-nav-item").boundingClientRect((t=>{let l=1;2==o.value.pageCount&&(l=o.value.list.length/o.value.rowCount>1?2:1),e=t.height*l,V.value=e+"px",uni.setStorageSync(W,V.value)})).exec()}})();m().in(U).select(".diy-graphic-nav").boundingClientRect((e=>{e&&(P.value=e.height)})).exec()}))},L=()=>{let e=1;return 2==o.value.pageCount&&(e=o.value.list.length>o.value.rowCount?2:1),e},G=(e,t)=>{let l=e+1;if(2==o.value.pageCount){let e=Math.ceil(o.value.list.length/o.value.rowCount);for(let a=1;a<=e;a++){if(1==t&&a%2!=0&&l>(a-1)*o.value.rowCount&&l<=a*o.value.rowCount)return!0;if(2==t&&a%2==0&&l>(a-1)*o.value.rowCount&&l<=a*o.value.rowCount)return!0}}return!1};return(e,t)=>{const i=$,s=E,u=D,c=v(x("u-icon"),Ne),f=Y,p=H,m=I;return g(),b(i,{style:_(k(r))},{default:y((()=>[h(i,{style:_(k(n))},null,8,["style"]),h(i,{class:"diy-graphic-nav relative"},{default:y((()=>["vertical"==k(o).layout?(g(),b(i,{key:0,class:"graphic-nav"},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),b(i,{class:"graphic-nav-item",key:e.id},{default:y((()=>[h(i,{onClick:t=>k(l).toRedirect(e.link),class:T(["flex items-center justify-between py-3 px-4",0==t?"border-t-0":"border-t"])},{default:y((()=>["text"!=k(o).mode?(g(),b(i,{key:0,class:"graphic-img relative flex items-center w-10 h-10 mr-[20rpx]",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[e.imageUrl?(g(),b(s,{key:0,src:k(d)(e.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(g(),b(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),e.label.control?(g(),b(u,{key:2,class:"tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs",style:_({color:e.label.textColor,backgroundImage:"linear-gradient("+e.label.bgColorStart+","+e.label.bgColorEnd+")"})},{default:y((()=>[w(C(e.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(g(),b(u,{key:1,class:"graphic-text w-full truncate leading-normal",style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["style"])):S("v-if",!0),h(c,{name:"arrow-right",color:"#999999",size:"12"})])),_:2},1032,["onClick","class"])])),_:2},1024)))),128))])),_:1})):"horizontal"==k(o).layout&&"pageSlide"==k(o).showStyle?(g(),b(i,{key:1,class:"pt-[10rpx]"},{default:y((()=>[h(p,{class:"graphic-nav swiper relative",style:_({height:V.value,width:"95%",margin:"0 auto",opacity:V.value?1:0}),circular:"",onChange:j},{default:y((()=>[(g(!0),R(B,null,F(Math.ceil(k(o).list.length/(k(o).pageCount*k(o).rowCount)),((e,t)=>(g(),b(f,{class:"graphic-nav-wrap flex flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((t,a)=>(g(),R(B,null,[N(a,e)?(g(),b(i,{class:T([k(o).mode]),key:t.id,style:_({width:100/k(o).rowCount+"%"})},{default:y((()=>[h(i,{onClick:e=>k(l).toRedirect(t.link),class:"graphic-nav-item flex flex-col items-center py-2"},{default:y((()=>["text"!=k(o).mode?(g(),b(i,{key:0,class:"graphic-img relative flex items-center justify-center w-10 h-10",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[t.imageUrl?(g(),b(s,{key:0,src:k(d)(t.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(g(),b(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),t.label.control?(g(),b(u,{key:2,class:"tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs",style:_({color:t.label.textColor,backgroundImage:"linear-gradient("+t.label.bgColorStart+","+t.label.bgColorEnd+")"})},{default:y((()=>[w(C(t.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(g(),b(u,{key:1,class:T(["graphic-text w-full text-center truncate leading-normal",{"pt-[16rpx]":"text"!=k(o).mode}]),style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["class","style"])):S("v-if",!0)])),_:2},1032,["onClick"])])),_:2},1032,["class","style"])):S("v-if",!0)],64)))),256))])),_:2},1024)))),256))])),_:1},8,["style"]),k(a)&&V.value?(g(),b(i,{key:0,class:"graphic-nav-indicator-dot"},{default:y((()=>[h(i,{class:T(["dots-wrap",[k(o).swiper.indicatorAlign]])},{default:y((()=>[(g(!0),R(B,null,F(Math.ceil(k(o).list.length/(k(o).pageCount*k(o).rowCount)),((e,t)=>(g(),b(i,{class:T(["dot",t==A.value?"dot-active":"",k(o).swiper.indicatorStyle]),style:_({background:t==A.value?k(o).swiper.indicatorActiveColor:k(o).swiper.indicatorColor})},null,8,["class","style"])))),256))])),_:1},8,["class"])])),_:1})):S("v-if",!0)])),_:1})):"horizontal"==k(o).layout&&2==k(o).pageCount&&"singleSlide"==k(o).showStyle?(g(),b(i,{key:2,style:{width:"98%",margin:"0 auto"},class:T([["graphic-nav multiple-lines","graphic-nav-"+k(o).showStyle],"py-[10rpx]"])},{default:y((()=>[(g(!0),R(B,null,F(L(),((e,t)=>(g(),b(m,{class:"graphic-nav-wrap whitespace-nowrap","scroll-x":"singleSlide"==k(o).showStyle},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((t,a)=>(g(),R(B,null,[G(a,e)?(g(),b(i,{key:0,onClick:e=>k(l).toRedirect(t.link),style:_(M()),class:"graphic-nav-item inline-flex flex-col items-center box-border py-2"},{default:y((()=>["text"!=k(o).mode?(g(),b(i,{key:0,class:"graphic-img relative flex items-center justify-center w-10 h-10",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[t.imageUrl?(g(),b(s,{key:0,src:k(d)(t.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(g(),b(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),t.label.control?(g(),b(u,{key:2,class:T(["tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs"]),style:_({color:t.label.textColor,backgroundImage:"linear-gradient("+t.label.bgColorStart+","+t.label.bgColorEnd+")"})},{default:y((()=>[w(C(t.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(g(),b(u,{key:1,class:T(["graphic-text w-full text-center truncate leading-normal",{"pt-[16rpx]":"text"!=k(o).mode}]),style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(t.title),1)])),_:2},1032,["class","style"])):S("v-if",!0)])),_:2},1032,["onClick","style"])):S("v-if",!0)],64)))),256))])),_:2},1032,["scroll-x"])))),256))])),_:1},8,["class"])):(g(),b(m,{key:3,"scroll-x":"singleSlide"==k(o).showStyle,class:T([["graphic-nav","graphic-nav-"+k(o).showStyle],"py-[10rpx]"])},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),b(i,{class:T(["graphic-nav-item",{"flex-shrink-0":"singleSlide"==k(o).showStyle}]),key:e.id,style:_({width:100/k(o).rowCount+"%"})},{default:y((()=>[h(i,{onClick:t=>k(l).toRedirect(e.link),class:"flex flex-col items-center box-border py-2"},{default:y((()=>["text"!=k(o).mode?(g(),b(i,{key:0,class:"graphic-img relative flex items-center justify-center w-10 h-10",style:_({width:2*k(o).imageSize+"rpx",height:2*k(o).imageSize+"rpx"})},{default:y((()=>[e.imageUrl?(g(),b(s,{key:0,src:k(d)(e.imageUrl),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])):(g(),b(s,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"aspectFill",style:_({maxWidth:2*k(o).imageSize+"rpx",maxHeight:2*k(o).imageSize+"rpx",borderRadius:2*k(o).aroundRadius+"rpx"})},null,8,["src","style"])),e.label.control?(g(),b(u,{key:2,class:T(["tag absolute -top-[10rpx] -right-[24rpx] text-white rounded-[24rpx] rounded-bl-none transform scale-80 py-1 px-2 text-xs"]),style:_({color:e.label.textColor,backgroundImage:"linear-gradient("+e.label.bgColorStart+","+e.label.bgColorEnd+")"})},{default:y((()=>[w(C(e.label.text),1)])),_:2},1032,["style"])):S("v-if",!0)])),_:2},1032,["style"])):S("v-if",!0),"img"!=k(o).mode?(g(),b(u,{key:1,class:T(["graphic-text w-full text-center truncate leading-normal",{"pt-[16rpx]":"text"!=k(o).mode}]),style:_({fontSize:2*k(o).font.size+"rpx",fontWeight:k(o).font.weight,color:k(o).font.color})},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["class","style"])):S("v-if",!0)])),_:2},1032,["onClick"])])),_:2},1032,["class","style"])))),128))])),_:1},8,["scroll-x","class"]))])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-1c87432a"]]),Zl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return e+="height:"+2*o.value.height+"rpx;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e}));return(e,t)=>{const l=$;return g(),b(l,{style:_(k(a))},null,8,["style"])}}}),Jl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return e+="border-top:"+2*o.value.borderWidth+"rpx "+o.value.borderStyle+" "+o.value.borderColor+";"}));return(e,t)=>{const o=$;return g(),b(o,{class:"horz-line-wrap"},{default:y((()=>["decorate"==k(l).mode?(g(),b(o,{key:0,class:"h-[30rpx]"})):S("v-if",!0),h(o,{style:_(k(a))},null,8,["style"]),"decorate"==k(l).mode?(g(),b(o,{key:1,class:"h-[30rpx]"})):S("v-if",!0)])),_:1})}}}),Ql=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),e})),r=s((()=>{var e="";return e+="height:"+o.value.imgHeight+";",o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e}));c((()=>{n(),f((()=>o.value),((e,t)=>{e&&"HotArea"==e.componentName&&n()}))}));const n=()=>{"decorate"==l.mode&&""==o.value.imageUrl&&(o.value.imgWidth=690,o.value.imgHeight=330)};return(e,t)=>{const n=E,i=$;return g(),b(i,{style:_(k(a))},{default:y((()=>[h(i,{class:"simple-graph-wrap overflow-hidden relative leading-0"},{default:y((()=>[k(o).imageUrl?(g(),b(n,{key:0,style:_(k(r)),src:k(d)(k(o).imageUrl),mode:"widthFix","show-menu-by-longpress":!0,class:"w-full"},null,8,["style","src"])):(g(),b(n,{key:1,style:_(k(r)),src:k(d)("static/resource/images/diy/figure.png"),mode:"widthFix","show-menu-by-longpress":!0,class:"w-full"},null,8,["style","src"])),"decorate"!=k(l).mode?(g(),R(B,{key:2},[S(" 热区功能 "),(g(!0),R(B,null,F(k(o).heatMapData,((e,t)=>(g(),b(i,{onClick:t=>k(l).toRedirect(e.link),class:"absolute",key:t,style:_({width:e.width+"%",height:e.height+"%",left:e.left+"%",top:e.top+"%"})},null,8,["onClick","style"])))),128))],64)):S("v-if",!0)])),_:1})])),_:1},8,["style"])}}}),Kl=n({__name:"index",props:["component","index"],setup(e){const t=e,l=A(),o=i(),a=s((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=()=>{let e="";return a.value.isSameScreen&&0==t.index&&(e="ios"===l.platform?"margin-top: -55px;":"margin-top: -44.5px;"),e},n=s((()=>{var e="";return e+="position:relative;",a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.componentBgUrl&&(e+=`background-image:url('${d(a.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),e})),p=s((()=>{var e="";return a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),m=s((()=>2*a.value.imageHeight+"rpx")),v=u(0),x=e=>{v.value=e.detail.current};c((()=>{w(),"decorate"==o.mode&&f((()=>a.value),((e,t)=>{e&&"ImageAds"==e.componentName&&w()}))}));const w=()=>{"decorate"==o.mode?a.value.list.forEach((e=>{""==e.imageUrl&&(e.imgWidth=690,e.imgHeight=330)})):uni.removeStorageSync("imageAdsSameScreen")};return(e,t)=>{const l=E,i=$,s=Y,u=H;return g(),b(i,{style:_(k(n))},{default:y((()=>[h(i,{class:"diy-image-ads",style:_(r())},{default:y((()=>[1==k(a).list.length?(g(),b(i,{key:0,class:"leading-0 overflow-hidden",style:_(k(p))},{default:y((()=>[h(i,{onClick:t[0]||(t[0]=e=>k(o).toRedirect(k(a).list[0].link))},{default:y((()=>[k(a).list[0].imageUrl?(g(),b(l,{key:0,src:k(d)(k(a).list[0].imageUrl),style:_({height:k(m)}),mode:"heightFix",class:"!w-full","show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(l,{key:1,src:k(d)("static/resource/images/diy/figure.png"),style:_({height:k(m)}),mode:"heightFix",class:"!w-full","show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1})])),_:1},8,["style"])):(g(),b(u,{key:1,class:"swiper",style:_({height:k(m)}),autoplay:"true",circular:"true",onChange:x},{default:y((()=>[(g(!0),R(B,null,F(k(a).list,(e=>(g(),b(s,{class:"swiper-item",key:e.id,style:_(k(p))},{default:y((()=>[h(i,{onClick:t=>k(o).toRedirect(e.link)},{default:y((()=>[h(i,{class:"item",style:_({height:k(m)})},{default:y((()=>[e.imageUrl?(g(),b(l,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(g(),b(l,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"]))])),_:2},1032,["style"])])),_:2},1032,["onClick"])])),_:2},1032,["style"])))),128))])),_:1},8,["style"]))])),_:1},8,["style"])])),_:1},8,["style"])}}}),eo=n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=Re(),o=i(),a=s((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=s((()=>{var e="";return a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=Se(),{query:c}=Be(location.href);c.code&&Fe()&&_e()&&Te({code:c.code}).then((e=>{n.getMemberInfo()}));const f=s((()=>"decorate"==o.mode?{headimg:"",nickname:"昵称",balance:0,point:0,money:0,member_no:"NIU0000021"}:n.info)),p=s((()=>{if(f.value){let e=parseFloat(f.value.balance)+parseFloat(f.value.money);return ze(e.toString())}return 0})),m=()=>{let e=!l.login.is_username&&!l.login.is_mobile&&!l.login.is_bind_mobile,t=!l.login.is_auth_register;Fe()?e&&t?ie({title:"商家未开启登录注册",icon:"none"}):l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile?ke().setLoginBack({url:"/app/pages/member/index"}):e&&l.login.is_auth_register&&l.login.is_force_access_user_info?ke().getAuthCode({scopes:"snsapi_userinfo"}):e&&l.login.is_auth_register&&!l.login.is_force_access_user_info&&ke().getAuthCode({scopes:"snsapi_base"}):e?ie({title:"商家未开启登录注册",icon:"none"}):(l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile)&&ke().setLoginBack({url:"/app/pages/member/index"})};u(!1);const R=()=>{Fe()?ke().getAuthCode({scopes:"snsapi_userinfo"}):we({url:"/app/pages/member/personal"})};return(e,t)=>{const l=v(x("u-avatar"),tt),o=$,n=D;return g(),b(o,{style:_(k(r))},{default:y((()=>[h(o,{class:"pt-[34rpx] member-info"},{default:y((()=>[k(f)?(g(),b(o,{key:0,class:"flex ml-[32rpx] mr-[52rpx] items-center relative"},{default:y((()=>[S(" 唤起获取微信 "),h(l,{src:k(d)(k(f).headimg),size:"55",leftIcon:"none","default-url":k(d)("static/resource/images/default_headimg.png"),onClick:R},null,8,["src","default-url"]),h(o,{class:"ml-[22rpx]"},{default:y((()=>[h(o,{class:"text-[#222222] flex pr-[50rpx] flex-wrap items-center"},{default:y((()=>[h(o,{class:"text-[#222222] truncate max-w-[320rpx] font-bold text-lg mr-[16rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(f).nickname),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[#696B70] text-[24rpx] mt-[10rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w("UID："+C(k(f).member_no),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"set-icon flex items-center absolute right-0 top-2"},{default:y((()=>[h(o,{onClick:t[0]||(t[0]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx-1 text-[40rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})])),_:1})):(g(),b(o,{key:1,class:"flex ml-[32rpx] mr-[52rpx] items-center relative"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/default_headimg.png"),size:"55",onClick:m},null,8,["src"]),h(o,{class:"ml-[22rpx]",onClick:m},{default:y((()=>[h(o,{class:"text-[#222222] font-bold text-lg",style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(P)("login"))+"/"+C(k(P)("register")),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"set-icon flex items-center absolute right-0 top-2",onClick:t[1]||(t[1]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(o,null,{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx-1 text-[40rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})])),_:1})),h(o,{class:"flex m-[30rpx] mb-0 py-[30rpx] items-center"},{default:y((()=>[h(o,{class:"flex-1 text-center"},{default:y((()=>[h(o,{class:"font-bold"},{default:y((()=>[h(o,{onClick:t[2]||(t[2]=e=>k(we)({url:k(f)?"/app/pages/member/balance":""})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(p)),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-sm mt-[10rpx]"},{default:y((()=>[h(o,{onClick:t[3]||(t[3]=e=>k(we)({url:k(f)?"/app/pages/member/balance":""})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(P)("balance")),1)])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"border-solid border-white border-l border-b-0 border-t-0 border-r-0 h-[60rpx]"}),h(o,{class:"flex-1 text-center"},{default:y((()=>[h(o,{class:"font-bold"},{default:y((()=>[h(o,{onClick:t[4]||(t[4]=e=>k(we)({url:k(f)?"/app/pages/member/point":""})),style:_({color:k(a).textColor})},{default:y((()=>{var e;return[w(C(parseInt(null==(e=k(f))?void 0:e.point)||0),1)]})),_:1},8,["style"])])),_:1}),h(o,{class:"text-sm mt-[10rpx]"},{default:y((()=>[h(o,{onClick:t[5]||(t[5]=e=>k(we)({url:k(f)?"/app/pages/member/point":""})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(P)("point")),1)])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["style"])}}}),to=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=Se(),a=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),r=s((()=>{var e="";return a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=u(0),c=u(0),f=u(-1),p=u([]);u(uni.getStorageSync("wap_member_info"));const m=s((()=>"decorate"==l.mode?(n.value=0,p.value=[{title:"商品包邮"}],c.value=1,{member_level_name:"会员等级",growth:5}):o.info||{})),v=s((()=>"decorate"==l.mode?[{}]:(x(o.levelList),o.levelList))),x=e=>{if(!e||!e.length)return!1;let t=!1;m.value&&m.value.member_level&&e&&e.length&&e.forEach(((e,l)=>{e.level_id==m.value.member_level&&(c.value=l+1,e.level_benefits&&Object.values(e.level_benefits).forEach((e=>{e.content&&p.value.push(e.content)}))),e.growth>m.value.growth&&e.level_id!=m.value.member_level&&!t&&(f.value=l,t=!0)})),m.value.member_level?(-1==f.value&&(f.value=e.length-1),e[f.value]&&e[f.value].growth&&(n.value=e[f.value].growth-m.value.growth)):(m.value.member_level_name=e[0].level_name,n.value=e[0].growth-(m.value.growth||0),f.value=0,c.value=1)};let R=()=>{let e=100;return v.value[f.value]&&v.value[f.value].growth&&(e=m.value.growth?m.value.growth/v.value[f.value].growth*100:0),e};const B=e=>{if("decorate"==l.mode)return!1;we({url:e})};return(e,t)=>{const l=E,o=D,i=$,s=$e;return k(m)&&k(v)&&k(v).length?(g(),b(i,{key:0,style:_(k(r)),class:"overflow-hidden"},{default:y((()=>["style-1"==k(a).style?(g(),b(i,{key:0,class:"flex items-center justify-between style-bg-1 py-[22rpx] px-[30rpx]"},{default:y((()=>[h(i,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/VIP_02.png"),mode:"aspectFit",class:"w-[50rpx] h-[36rpx]"},null,8,["src"]),h(o,{class:"text-[30rpx] text-[#FFDAA8] ml-[10rpx] font-500 max-w-[440rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),h(i,{class:"flex items-center justify-center rounded-[30rpx] box-border style-btn w-[140rpx] h-[56rpx]",onClick:t[0]||(t[0]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[24rpx] text-[#333]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(o,{class:"iconfont iconxiayibu1 ml-[4rpx] -mb-[2rpx] !text-[14rpx] text-[#333]"})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(a).style?(g(),b(i,{key:1,class:"flex items-center justify-between style-bg-2 p-[30rpx]"},{default:y((()=>[h(i,{class:"flex flex-col"},{default:y((()=>[h(i,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/VIP_01.png"),mode:"aspectFit",class:"w-[74rpx] h-[30rpx]"},null,8,["src"]),h(o,{class:"text-[32rpx] text-[#FFE3B1] leading-[normal] ml-[14rpx] font-500 max-w-[420rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),p.value&&p.value.length?(g(),b(o,{key:0,class:"text-[#FFE3B1] opacity-80 text-[24rpx] mt-[10rpx] leading-[32rpx]"},{default:y((()=>[w(C(k(m).member_level_name)+"购物享"+C(p.value[0].title),1)])),_:1})):S("v-if",!0)])),_:1}),h(i,{class:"flex items-center justify-center rounded-[30rpx] box-border style-btn w-[140rpx] h-[56rpx]",onClick:t[1]||(t[1]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[24rpx] text-[#333]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(o,{class:"iconfont iconxiayibu1 ml-[4rpx] -mb-[2rpx] !text-[14rpx] text-[#333]"})])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(a).style?(g(),b(i,{key:2,class:"style-bg-3 py-[var(--pad-top-m)] px-[var(--pad-sidebar-m)]"},{default:y((()=>[h(i,{class:"flex items-center justify-between style-border-3 mb-[30rpx] pb-[40rpx]"},{default:y((()=>[h(i,{class:"flex flex-col flex-1"},{default:y((()=>[h(i,{class:"flex items-center justify-between"},{default:y((()=>[h(i,{class:"flex items-center"},{default:y((()=>[h(i,{class:"flex font-500 leading-[30rpx] box-border text-[#fff] pl-[50rpx] text-[24rpx] w-[120rpx] h-[30rpx] bg-contain bg-no-repeat",style:_({backgroundImage:"url("+k(d)("static/resource/images/diy/member/VIP.png")+")"})},{default:y((()=>[w("VIP."+C(c.value),1)])),_:1},8,["style"]),h(o,{class:"text-[#733F02] ml-[8rpx] text-[30rpx] font-500 max-w-[380rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),h(i,{class:"flex items-center",onClick:t[2]||(t[2]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(i,{class:"inline-block"},{default:y((()=>[h(o,{class:"nc-iconfont nc-icon-a-bangzhuV6xx-36 !text-[22rpx] text-[#733F02]"}),h(o,{class:"text-[22rpx] text-[#733F02] ml-[6rpx] leading-[24rpx]"},{default:y((()=>[w("规则")])),_:1})])),_:1}),h(i,{class:"ml-[2rpx] -mb-[4rpx] text-[#733F02] !text-[24rpx] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1}),h(o,{class:"text-[24rpx] text-[#794200] mt-[10rpx]"},{default:y((()=>[w("购物或邀请好友可以提升等级")])),_:1})])),_:1})])),_:1}),h(i,{class:"flex items-center justify-between"},{default:y((()=>[h(i,{class:"flex flex-col flex-1 mt-[2rpx]"},{default:y((()=>[h(i,{class:"overflow-hidden rounded-[20rpx]"},{default:y((()=>[h(s,{percent:k(R)(),activeColor:"#fff",backgroundColor:"rgba(255,5,5,0.1)","stroke-width":"4"},null,8,["percent"])])),_:1}),n.value>0?(g(),b(o,{key:0,class:"text-[22rpx] ml-[2rpx] leading-[1.4] text-[#794200] mt-[16rpx]"},{default:y((()=>[w("还差"+C(n.value)+"成长值即可升级为"+C(k(v)[f.value].level_name),1)])),_:1})):(g(),b(o,{key:1,class:"text-[22rpx] ml-[2rpx] text-[#794200] mt-[16rpx]"},{default:y((()=>[w("恭喜您升级为最高等级")])),_:1}))])),_:1}),h(i,{class:"flex items-center rounded-[30rpx] bg-[rgb(245,230,185)] px-[24rpx] text-[22rpx] text-[#733F02] h-[56rpx] ml-[40rpx] leading-normal",onClick:t[3]||(t[3]=e=>B("/app/pages/member/level"))},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-4"==k(a).style?(g(),b(i,{key:3,class:"flex items-center justify-between style-4 px-[24rpx] py-[20rpx]",style:_({backgroundImage:"url("+k(d)("static/resource/images/diy/member/style4_bg.jpg")+")"})},{default:y((()=>[h(i,{class:"flex flex-col"},{default:y((()=>[h(i,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/style4_vip.png"),mode:"aspectFit",class:"w-[70rpx] h-[32rpx] pt-[1rpx]"},null,8,["src"]),h(o,{class:"text-[30rpx] text-[#FFEFB0] leading-[normal] ml-[8rpx] font-500 max-w-[420rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),p.value&&p.value.length?(g(),b(i,{key:0,class:"text-[#B0B0B0] text-[24rpx] mt-[10rpx] leading-[32rpx]"},{default:y((()=>[h(o,null,{default:y((()=>[w(C(k(m).member_level_name)+"购物享",1)])),_:1}),h(o,{class:"text-[#FFEFB0]"},{default:y((()=>[w(C(p.value[0].title),1)])),_:1})])),_:1})):S("v-if",!0)])),_:1}),h(i,{class:"flex items-center justify-center rounded-[30rpx] box-border style-btn w-[150rpx] h-[50rpx]",onClick:t[4]||(t[4]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[22rpx] text-[#333] mr-[8rpx]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(l,{src:k(d)("static/resource/images/diy/member/style4_arrow.png"),mode:"aspectFit",class:"w-[26rpx] h-[26rpx] pt-[2rpx]"},null,8,["src"])])),_:1})])),_:1},8,["style"])):S("v-if",!0),"style-5"==k(a).style?(g(),b(i,{key:4,class:"style-5",style:_({backgroundImage:"url("+k(d)("static/resource/images/diy/member/style5_bg.jpg")+")"})},{default:y((()=>[h(i,{class:"content-head pt-[16rpx] pb-[10rpx] px-[24rpx] flex items-center justify-between"},{default:y((()=>[h(i,{class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/diy/member/style5_vip.png"),mode:"aspectFit",class:"w-[40rpx] h-[40rpx]"},null,8,["src"]),h(o,{class:"text-[#FFFBE2] ml-[10rpx] text-[30rpx] font-500 max-w-[470rpx] truncate"},{default:y((()=>[w(C(k(m).member_level_name),1)])),_:1})])),_:1}),h(i,{class:"flex items-center rounded-[30rpx] pl-[16rpx] pr-[12rpx] h-[44rpx] leading-normal style-btn",onClick:t[5]||(t[5]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"text-[22rpx] text-[#333] font-500 pb-[2rpx]"},{default:y((()=>[w(C(k(m).member_level?n.value>0?"做任务":"点击查看":"去解锁"),1)])),_:1}),h(l,{src:k(d)("static/resource/images/diy/member/style5_arrow_01.png"),mode:"aspectFit",class:"w-[22rpx] h-[22rpx] pb-[1rpx]"},null,8,["src"])])),_:1})])),_:1}),h(i,{class:"flex flex-col pt-[28rpx] pb-[30rpx] px-[24rpx]"},{default:y((()=>[h(i,{class:"flex items-center justify-between pb-[16rpx]"},{default:y((()=>[n.value>0?(g(),b(o,{key:0,class:"text-[22rpx] ml-[2rpx] leading-[1.4] text-[#FFFBE2]"},{default:y((()=>[w("还差"+C(n.value)+"成长值即可升级为"+C(k(v)[f.value].level_name),1)])),_:1})):(g(),b(o,{key:1,class:"text-[22rpx] ml-[2rpx] text-[#FFFBE2]"},{default:y((()=>[w("恭喜您升级为最高等级")])),_:1})),h(i,{class:"flex items-center",onClick:t[6]||(t[6]=e=>B("/app/pages/member/level"))},{default:y((()=>[h(o,{class:"nc-iconfont nc-icon-a-bangzhuV6xx-36 !text-[22rpx] text-[#FFFBE2]"}),h(o,{class:"text-[22rpx] text-[#FFFBE2] ml-[6rpx] leading-[24rpx]"},{default:y((()=>[w("规则")])),_:1}),h(i,{class:"ml-[2rpx] -mb-[4rpx] text-[#FFFBE2] !text-[24rpx] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1}),h(i,{class:"overflow-hidden rounded-[20rpx]"},{default:y((()=>[h(s,{percent:k(R)(),activeColor:"#fff",backgroundColor:"rgba(255,255,255,0.4)","stroke-width":"4"},null,8,["percent"])])),_:1})])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])):S("v-if",!0)}}}),[["__scopeId","data-v-fb31e6f0"]]),lo=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=u(!1),a=u(""),r=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),n=s((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.componentBgUrl&&(e+=`background-image:url('${d(r.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e})),M=s((()=>{var e="";return r.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${r.value.componentBgAlpha/10});`,e+=`height:${L.value}px;`,r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;")),e})),A=u(0),j=u(0),W=u(""),V=u(0),U=u(800),P=()=>{"horizontal"==r.value.scrollWay&&setTimeout((()=>{p((()=>{let e=window.document.getElementById("horizontal-body-"+r.value.id),t=window.document.getElementById("marquee-one");e&&t&&(A.value=e.offsetWidth,j.value=t.offsetWidth,V.value=Math.ceil(14*j.value),A.value>j.value-30?W.value="animation: none;":W.value=`animation-duration: ${V.value}ms;animation-delay: ${U.value}ms;`)}))}))};c((()=>{P(),G(),"decorate"==l.mode&&f((()=>r.value),((e,t)=>{e&&"Notice"==e.componentName&&(P(),G())}))}));const O=z(),L=u(0),G=()=>{p((()=>{m().in(O).select(".diy-notice").boundingClientRect((e=>{L.value=e.height})).exec()}))},q=e=>{if("decorate"==l.mode)return!1;"popup"==r.value.showType?(o.value=!0,a.value=e.text):l.toRedirect(e.link)};return(e,t)=>{const l=$,i=E,s=D,u=Y,c=H,f=I,p=pe,m=v(x("u-popup"),Ve);return g(),b(l,{style:_(k(n))},{default:y((()=>[h(l,{style:_(k(M))},null,8,["style"]),h(l,{class:"diy-notice relative overflow-hidden"},{default:y((()=>[h(l,{class:"flex items-center pl-[28rpx] p-[22rpx]"},{default:y((()=>["img"==k(r).noticeType?(g(),b(l,{key:0,class:"min-w-[60rpx] flex items-center"},{default:y((()=>["system"==k(r).imgType?(g(),R(B,{key:0},["style_1"==k(r).systemUrl?(g(),b(i,{key:0,src:k(d)(`static/resource/images/diy/notice/${k(r).systemUrl}.png`),class:"h-[40rpx] w-[auto] mr-[20rpx] flex-shrink-0",mode:"heightFix"},null,8,["src"])):"style_2"==k(r).systemUrl?(g(),b(i,{key:1,src:k(d)(`static/resource/images/diy/notice/${k(r).systemUrl}.png`),class:"w-[200rpx] mr-[20rpx] h-[30rpx] flex-shrink-0",mode:"heightFix"},null,8,["src"])):S("v-if",!0)],64)):"diy"==k(r).imgType?(g(),b(i,{key:1,src:k(d)(k(r).imageUrl||""),class:"w-[200rpx] h-[30rpx] mr-[20rpx] flex-shrink-0",mode:"heightFix"},null,8,["src"])):S("v-if",!0)])),_:1})):S("v-if",!0),"text"==k(r).noticeType&&k(r).noticeTitle?(g(),b(l,{key:1,class:"max-w-[128rpx] px-[12rpx] text-[26rpx] h-[40rpx] leading-[40rpx] text-[var(--primary-color)] bg-[var(--primary-color-light)] truncate rounded-[8rpx] mr-[20rpx] flex-shrink-0"},{default:y((()=>[w(C(k(r).noticeTitle),1)])),_:1})):S("v-if",!0),h(l,{class:T(["flex-1 flex overflow-hidden horizontal-body",{"items-center":"upDown"==k(r).scrollWay}]),id:"horizontal-body-"+k(r).id},{default:y((()=>[S(" 横向滚动 "),"horizontal"==k(r).scrollWay?(g(),b(l,{key:0,class:"horizontal-wrap",style:_(W.value)},{default:y((()=>[h(l,{class:"marquee marquee-one",id:"marquee-one"},{default:y((()=>[(g(!0),R(B,null,F(k(r).list,((e,t)=>(g(),b(l,{class:T(["item flex-shrink-0 !leading-[40rpx] h-[40rpx]",{"ml-[80rpx]":t}]),key:t,onClick:t=>q(e),style:_({color:k(r).textColor,fontSize:2*k(r).fontSize+"rpx",fontWeight:k(r).fontWeight})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["class","onClick","style"])))),128))])),_:1}),A.value<j.value-30?(g(),b(l,{key:0,class:"marquee"},{default:y((()=>[(g(!0),R(B,null,F(k(r).list,((e,t)=>(g(),b(l,{class:T(["item flex-shrink-0 !leading-[40rpx] h-[40rpx]",{"ml-[80rpx]":t}]),key:t,onClick:t=>q(e),style:_({color:k(r).textColor,fontSize:2*k(r).fontSize+"rpx",fontWeight:k(r).fontWeight})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["class","onClick","style"])))),128))])),_:1})):S("v-if",!0)])),_:1},8,["style"])):S("v-if",!0),S(" 上下滚动 "),"upDown"==k(r).scrollWay?(g(),R(B,{key:1},[h(c,{vertical:!0,duration:500,autoplay:"true",circular:"true",class:"flex-1"},{default:y((()=>[(g(!0),R(B,null,F(k(r).list,((e,l)=>(g(),b(u,{key:l,onTouchmove:t[0]||(t[0]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(s,{onClick:t=>q(e),class:"beyond-hiding truncate",style:_({color:k(r).textColor,fontSize:2*k(r).fontSize+"rpx",fontWeight:k(r).fontWeight})},{default:y((()=>[w(C(e.text),1)])),_:2},1032,["onClick","style"])])),_:2},1024)))),128))])),_:1}),h(s,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] -ml-[8rpx] pl-[30rpx]",style:_({color:"#999",fontWeight:k(r).fontWeight})},null,8,["style"])],64)):S("v-if",!0)])),_:1},8,["id","class"])])),_:1}),h(l,{onTouchmove:t[3]||(t[3]=N((()=>{}),["prevent","stop"]))},{default:y((()=>[h(m,{show:o.value,onClose:t[2]||(t[2]=e=>o.value=!1),mode:"center",round:"var(--rounded-big)",safeAreaInsetBottom:!1},{default:y((()=>[h(l,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:y((()=>[h(l,{class:"title"},{default:y((()=>[w("公告")])),_:1}),h(f,{"scroll-y":!0,class:"px-[30rpx] box-border h-[260rpx]"},{default:y((()=>[(g(!0),R(B,null,F(a.value.split("\n"),(e=>(g(),b(l,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:y((()=>[w(C(e),1)])),_:2},1024)))),256))])),_:1}),h(l,{class:"btn-wrap !pt-[40rpx]"},{default:y((()=>[h(p,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:t[1]||(t[1]=e=>o.value=!1)},{default:y((()=>[w("我知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-646198ff"]]),oo=n({__name:"index",props:["component","index","global","scrollBool"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=s((()=>{var e="";return o.value.moduleOne.listFrame&&o.value.moduleOne.listFrame.startColor&&o.value.moduleOne.listFrame.endColor&&(e+=`background:linear-gradient(${o.value.moduleOne.listFrame.startColor},${o.value.moduleOne.listFrame.endColor});`),o.value.moduleRounded.topRounded&&(e+="border-top-left-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.topRounded&&(e+="border-top-right-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.margin&&o.value.margin.both?e+="width: calc((100vw - "+4*o.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e})),n=s((()=>{var e="";return o.value.moduleTwo.listFrame&&o.value.moduleTwo.listFrame.startColor&&o.value.moduleTwo.listFrame.endColor&&(e+=`background:linear-gradient(${o.value.moduleTwo.listFrame.startColor},${o.value.moduleTwo.listFrame.endColor});`),o.value.moduleRounded.topRounded&&(e+="border-top-left-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.topRounded&&(e+="border-top-right-radius:"+2*o.value.moduleRounded.topRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.moduleRounded.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.moduleRounded.bottomRounded+"rpx;"),o.value.margin&&o.value.margin.both?e+="width: calc((100vw - "+4*o.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e})),u=e=>{var t="";return e.btnTitle.color&&(t+="color:"+e.btnTitle.color+";"),o.value.moduleTwo.listFrame.startColor&&o.value.moduleTwo.listFrame.endColor&&(t+=`background:linear-gradient(${e.btnTitle.startColor},${e.btnTitle.endColor});`),t};c((()=>{p(),"decorate"==l.mode&&f((()=>o.value),((e,t)=>{e&&"PictureShow"==e.componentName&&p()}))}));const p=()=>{};return(e,t)=>{const i=E,s=D,c=$,f=v(x("u-icon"),Ne);return g(),b(c,{style:_(k(a)),class:"flex justify-between overflow-hidden"},{default:y((()=>[h(c,{class:"p-[20rpx] box-border overflow-hidden",style:_(k(r))},{default:y((()=>[k(o).moduleOne.head.textImg||k(o).moduleOne.head.subText?(g(),b(c,{key:0,class:"flex items-center pb-[30rpx] pt-[6rpx]"},{default:y((()=>[k(o).moduleOne.head.textImg?(g(),b(i,{key:0,class:"h-[28rpx]",src:k(d)(k(o).moduleOne.head.textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),k(o).moduleOne.head.textImg&&k(o).moduleOne.head.subText?(g(),b(s,{key:1,class:"w-[2rpx] mx-[10rpx] h-[22rpx]",style:_({backgroundColor:k(o).moduleOne.head.subTextColor})},null,8,["style"])):S("v-if",!0),k(o).moduleOne.head.subText?(g(),b(s,{key:2,class:"text-[22rpx] truncate max-w-[164rpx]",style:_({color:k(o).moduleOne.head.subTextColor})},{default:y((()=>[w(C(k(o).moduleOne.head.subText),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(c,{class:"flex items-center"},{default:y((()=>[(g(!0),R(B,null,F(k(o).moduleOne.list,((e,t)=>(g(),b(c,{key:t,class:T(["flex flex-col items-center",{"mr-[10rpx]":0==t}]),onClick:t=>k(l).toRedirect(e.link)},{default:y((()=>[h(c,{class:"bg-[#fff] flex items-center justify-center w-[148rpx] h-[148rpx] rounded-[12rpx] mb-[16rpx]"},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,class:"w-[102rpx] h-[102rpx]",src:k(d)(e.imageUrl),mode:"aspectFill"},null,8,["src"])):(g(),b(f,{key:1,name:"photo",color:"#999",size:"50"}))])),_:2},1024),h(c,{class:"w-[132rpx] h-[44rpx] rounded-[30rpx] flex items-center justify-center text-[22rpx]",style:_(u(e))},{default:y((()=>[w(C(e.btnTitle.text),1)])),_:2},1032,["style"])])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["style"]),h(c,{class:"p-[20rpx] box-border overflow-hidden",style:_(k(n))},{default:y((()=>[k(o).moduleTwo.head.textImg||k(o).moduleTwo.head.subText?(g(),b(c,{key:0,class:"flex items-center pb-[30rpx] pt-[6rpx]"},{default:y((()=>[k(o).moduleTwo.head.textImg?(g(),b(i,{key:0,class:"h-[28rpx] w-[auto]",src:k(d)(k(o).moduleTwo.head.textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),k(o).moduleTwo.head.textImg&&k(o).moduleTwo.head.subText?(g(),b(s,{key:1,class:"w-[2rpx] mx-[10rpx] h-[22rpx]",style:_({backgroundColor:k(o).moduleTwo.head.subTextColor})},null,8,["style"])):S("v-if",!0),k(o).moduleTwo.head.subText?(g(),b(s,{key:2,class:"text-[22rpx] truncate max-w-[164rpx]",style:_({color:k(o).moduleTwo.head.subTextColor})},{default:y((()=>[w(C(k(o).moduleTwo.head.subText),1)])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(c,{class:"flex items-center"},{default:y((()=>[(g(!0),R(B,null,F(k(o).moduleTwo.list,((e,t)=>(g(),b(c,{key:t,class:T(["flex flex-col items-center",{"mr-[10rpx]":0==t}]),onClick:t=>k(l).toRedirect(e.link)},{default:y((()=>[h(c,{class:"bg-[#fff] flex items-center justify-center w-[148rpx] h-[148rpx] rounded-[12rpx] mb-[16rpx]"},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,class:"w-[102rpx] h-[102rpx]",src:k(d)(e.imageUrl),mode:"aspectFill"},null,8,["src"])):(g(),b(f,{key:1,name:"photo",color:"#999",size:"50"}))])),_:2},1024),h(c,{class:"w-[132rpx] h-[44rpx] rounded-[30rpx] flex items-center justify-center text-[22rpx]",style:_(u(e))},{default:y((()=>[w(C(e.btnTitle.text),1)])),_:2},1032,["style"])])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["style"])])),_:1},8,["style"])}}}),ao=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=s((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${C.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{S(),"decorate"==l.mode&&f((()=>o.value),((e,t)=>{e&&"RichText"==e.componentName&&S()}))}));const n=z(),C=u(0),S=()=>{p((()=>{m().in(n).select(".diy-rich-text").boundingClientRect((e=>{C.value=e.height})).exec()}))};return(e,t)=>{const l=$,n=v(x("u-parse"),lt),i=D;return g(),b(l,{style:_(k(a))},{default:y((()=>[h(l,{style:_(k(r))},null,8,["style"]),h(l,{class:"diy-rich-text relative"},{default:y((()=>[k(o).html&&"<p><br></p>"!=k(o).html?(g(),b(l,{key:0},{default:y((()=>[h(n,{content:k(o).html,tagStyle:{img:"vertical-align: top;"}},null,8,["content"])])),_:1})):(g(),R(B,{key:1},[h(l,null,{default:y((()=>[w("点此编辑『富文本』内容 ——>")])),_:1}),h(l,null,{default:y((()=>[h(i,null,{default:y((()=>[w("你可以对文字进行")])),_:1}),h(i,null,{default:y((()=>[w("、")])),_:1}),h(i,{class:"font-bold"},{default:y((()=>[w("加粗")])),_:1}),h(i,null,{default:y((()=>[w("、")])),_:1}),h(i,{class:"italic"},{default:y((()=>[w("斜体")])),_:1}),h(i,null,{default:y((()=>[w("、")])),_:1}),h(i,{class:"underline"},{default:y((()=>[w("下划线")])),_:1}),h(i,null,{default:y((()=>[w("、")])),_:1}),h(i,{class:"line-through"},{default:y((()=>[w("删除线")])),_:1}),h(i,null,{default:y((()=>[w("、文字")])),_:1}),h(i,{style:{color:"rgb(0, 176, 240)"}},{default:y((()=>[w("颜色")])),_:1}),h(i,null,{default:y((()=>[w("、")])),_:1}),h(i,{style:{"background-color":"rgb(255, 192, 0)",color:"rgb(255, 255, 255)"}},{default:y((()=>[w("背景色")])),_:1}),h(i,null,{default:y((()=>[w("、以及字号")])),_:1}),h(i,{class:"text-lg"},{default:y((()=>[w("大")])),_:1}),h(i,{class:"text-sm"},{default:y((()=>[w("小")])),_:1}),h(i,{class:"pl-[10rpx]"},{default:y((()=>[w("等简单排版操作。")])),_:1})])),_:1}),h(l,null,{default:y((()=>[w("也可在这里插入图片、并对图片加上超级链接，方便用户点击。")])),_:1})],64))])),_:1})])),_:1},8,["style"])}}}),ro=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=e=>Ee(e)+1,r=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),n=s((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${x.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{w(),"decorate"==l.mode?f((()=>o.value),((e,t)=>{e&&"RubikCube"==e.componentName&&w()})):f((()=>o.value),((e,t)=>{w()}))}));const v=z(),x=u(0),w=()=>{"decorate"==l.mode&&o.value.list.forEach((e=>{""==e.imageUrl&&(e.imgWidth=690,e.imgHeight=330)})),C(),p((()=>{m().in(v).select(".rubik-cube").boundingClientRect((e=>{x.value=e.height})).exec()}))},C=()=>{var e={"row1-of2":{ratio:2,width:"calc((100% - "+a(2*o.value.imageGap)+"px) / 2)"},"row1-of3":{ratio:3,width:"calc((100% - "+a(4*o.value.imageGap)+"px) / 3)"},"row1-of4":{ratio:4,width:"calc((100% - "+a(6*o.value.imageGap)+"px) / 4)"}};o.value.list.forEach(((e,t)=>{e.pageItemStyle=((e,t)=>{var l="";return"right"==o.value.elementAngle||{"row1-lt-of2-rt":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-lt-of1-tp-of2-bm":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-tp-of2-bm":[["border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-right-radius","border-top-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row2-lt-of2-rt":[["border-top-right-radius","border-bottom-left-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-right-radius","border-bottom-left-radius"],["border-top-left-radius","border-bottom-right-radius","border-top-right-radius"],["border-top-left-radius","border-bottom-left-radius","border-top-right-radius"]],"row1-of4":[["border-top-right-radius","border-bottom-right-radius"],["border-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius"]],"row1-of3":[["border-top-right-radius","border-bottom-right-radius"],["border-radius"],["border-top-left-radius","border-bottom-left-radius"]],"row1-of2":[["border-top-right-radius","border-bottom-right-radius"],["border-top-left-radius","border-bottom-left-radius"]]}[e][t].forEach(((e,t)=>{l+="border-top-left-radius:"+2*o.value.topElementRounded+"rpx;",l+="border-top-right-radius:"+2*o.value.topElementRounded+"rpx;",l+="border-bottom-left-radius:"+2*o.value.bottomElementRounded+"rpx;",l+="border-bottom-right-radius:"+2*o.value.bottomElementRounded+"rpx;"})),l})(o.value.mode,t)})),e[o.value.mode]?D(e[o.value.mode]):"row2-lt-of2-rt"==o.value.mode?I():"row1-lt-of2-rt"==o.value.mode?M():"row1-tp-of2-bm"==o.value.mode?A():"row1-lt-of1-tp-of2-bm"==o.value.mode&&j()},D=e=>{De({success:t=>{let l=0;o.value.list.forEach(((r,n)=>{var i=r.imgHeight/r.imgWidth;let s=t.windowWidth-a(2*o.value.margin.both);o.value.imageGap>0&&(s-=a(e.ratio*o.value.imageGap*2)),r.imgWidth=s/e.ratio,r.imgHeight=r.imgWidth*i,(0==l||l<r.imgHeight)&&(l=r.imgHeight)})),o.value.list.forEach(((t,o)=>{t.widthStyle=e.width,t.imgHeight=l}))}})},I=()=>{De({success:e=>{let t=0,l=0;o.value.list.forEach(((r,n)=>{var i=r.imgHeight/r.imgWidth;r.imgWidth=e.windowWidth,r.imgWidth-=a(4*o.value.margin.both),o.value.imageGap>0&&(r.imgWidth-=a(2*o.value.imageGap)),r.imgWidth=r.imgWidth/2,r.imgHeight=r.imgWidth*i,n<=1?(0==t||t<r.imgHeight)&&(t=r.imgHeight):n>1&&(0==l||l<r.imgHeight)&&(l=r.imgHeight)})),o.value.list.forEach(((e,r)=>{e.imgWidth="calc((100% - "+a(2*o.value.imageGap)+"px) / 2)",e.widthStyle=e.imgWidth,r<=1?e.imgHeight=t:r>1&&(e.imgHeight=l)}))}})},M=()=>{let e=0;o.value.list[1].imgWidth,o.value.list[2].imgWidth,De({success:t=>{o.value.list.forEach(((l,r)=>{if(0==r){var n=l.imgHeight/l.imgWidth;l.imgWidth=t.windowWidth-a(4*o.value.margin.both)-a(2*o.value.imageGap),l.imgWidth=l.imgWidth/2,l.imgHeight=l.imgWidth*n,e=(l.imgHeight-a(2*o.value.imageGap))/2,l.imgWidth+="px"}else l.imgWidth=o.value.list[0].imgWidth,l.imgHeight=e}))}})},A=()=>{var e=0;De({success:t=>{o.value.list.forEach(((l,r)=>{var n=l.imgHeight/l.imgWidth;0==r?l.imgWidth=t.windowWidth-a(4*o.value.margin.both):r>0&&(l.imgWidth=t.windowWidth-a(4*o.value.margin.both)-a(2*o.value.imageGap),l.imgWidth=l.imgWidth/2),l.imgHeight=l.imgWidth*n,r>0&&(0==e||e<l.imgHeight)&&(e=l.imgHeight)})),o.value.list.forEach(((t,l)=>{t.imgWidth+="px",t.widthStyle=t.imgWidth,l>0&&(t.imgHeight=e)}))}})},j=()=>{De({success:e=>{o.value.list.forEach(((t,l)=>{if(0==l){var r=t.imgHeight/t.imgWidth;t.imgWidth=e.windowWidth-a(4*o.value.margin.both)-a(2*o.value.imageGap),t.imgWidth=t.imgWidth/2,t.imgHeight=t.imgWidth*r}else 1==l?(t.imgWidth=o.value.list[0].imgWidth,t.imgHeight=(o.value.list[0].imgHeight-a(2*o.value.imageGap))/2):l>1&&(t.imgWidth=(o.value.list[0].imgWidth-a(2*o.value.imageGap))/2,t.imgHeight=o.value.list[1].imgHeight)})),o.value.list.forEach(((e,t)=>{e.imgWidth+="px"}))}})};return(e,t)=>{const a=$,i=E;return g(),b(a,{style:_(k(r))},{default:y((()=>[h(a,{style:_(k(n))},null,8,["style"]),h(a,{class:T(["rubik-cube relative",k(l).mode])},{default:y((()=>[S(" 1左2右 "),"row1-lt-of2-rt"==k(o).mode?(g(),R(B,{key:0},[h(a,{class:"template-left"},{default:y((()=>[h(a,{onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).list[0].link)),class:T(["item",k(o).mode]),style:_({marginRight:2*k(o).imageGap+"rpx",width:k(o).list[0].imgWidth,height:k(o).list[0].imgHeight+"px"})},{default:y((()=>[k(o).list[0].imageUrl?(g(),b(i,{key:0,src:k(d)(k(o).list[0].imageUrl),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1},8,["class","style"])])),_:1}),h(a,{class:"template-right"},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),R(B,{key:t},[t>0?(g(),b(a,{key:0,onClick:t=>k(l).toRedirect(e.link),class:T(["item",k(o).mode]),style:_({marginBottom:2*k(o).imageGap+"rpx",width:e.imgWidth,height:e.imgHeight+"px"})},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:2},1032,["onClick","class","style"])):S("v-if",!0)],64)))),128))])),_:1})],64)):"row1-lt-of1-tp-of2-bm"==k(o).mode?(g(),R(B,{key:1},[S(" 1左3右 "),h(a,{class:"template-left"},{default:y((()=>[h(a,{onClick:t[1]||(t[1]=e=>k(l).toRedirect(k(o).list[0].link)),class:T(["item",k(o).mode]),style:_({marginRight:2*k(o).imageGap+"rpx",width:k(o).list[0].imgWidth,height:k(o).list[0].imgHeight+"px"})},{default:y((()=>[k(o).list[0].imageUrl?(g(),b(i,{key:0,src:k(d)(k(o).list[0].imageUrl),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(k(o).list[0].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1},8,["class","style"])])),_:1}),h(a,{class:"template-right"},{default:y((()=>[h(a,{onClick:t[2]||(t[2]=e=>k(l).toRedirect(k(o).list[1].link)),class:T(["item",k(o).mode]),style:_({marginBottom:2*k(o).imageGap+"rpx",width:k(o).list[1].imgWidth,height:k(o).list[1].imgHeight+"px"})},{default:y((()=>[k(o).list[1].imageUrl?(g(),b(i,{key:0,src:k(d)(k(o).list[1].imageUrl),mode:"scaleToFill",style:_(k(o).list[1].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(k(o).list[1].pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:1},8,["class","style"]),h(a,{class:"template-bottom"},{default:y((()=>[(g(!0),R(B,null,F(k(o).list,((e,t)=>(g(),R(B,{key:t},[t>1?(g(),b(a,{key:0,onClick:t=>k(l).toRedirect(e.link),class:T(["item",k(o).mode]),style:_({marginRight:2*k(o).imageGap+"rpx",width:e.imgWidth,height:e.imgHeight+"px"})},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:2},1032,["onClick","class","style"])):S("v-if",!0)],64)))),128))])),_:1})])),_:1})],64)):(g(!0),R(B,{key:2},F(k(o).list,((e,t)=>(g(),b(a,{class:T(["item",k(o).mode]),key:t,onClick:t=>k(l).toRedirect(e.link),style:_({marginRight:2*k(o).imageGap+"rpx",marginBottom:2*k(o).imageGap+"rpx",width:e.widthStyle,height:e.imgHeight+"px"})},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",style:_(e.pageItemStyle),"show-menu-by-longpress":!0},null,8,["src","style"]))])),_:2},1032,["class","onClick","style"])))),128))])),_:1},8,["class"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-23f821fa"]]),no=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>({fontSize:2*o.value.fontSize+"rpx",color:o.value.textColor,fontWeight:"normal"===o.value.fontWeight?500:o.value.fontWeight,textAlign:o.value.textAlign}))),r=s((()=>({fontSize:2*o.value.fontSize+"rpx",color:o.value.textColor,fontWeight:"normal"===o.value.fontWeight?500:o.value.fontWeight}))),n=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),v=s((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${R.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{B(),"decorate"==l.mode&&f((()=>o.value),((e,t)=>{e&&"Text"==e.componentName&&B()}))}));const x=z(),R=u(0),B=()=>{p((()=>{m().in(x).select(".diy-text").boundingClientRect((e=>{R.value=e.height})).exec()}))};return(e,t)=>{const i=$,s=D;return g(),b(i,{style:_(k(n))},{default:y((()=>[h(i,{style:_(k(v))},null,8,["style"]),h(i,{class:"diy-text relative"},{default:y((()=>["style-1"==k(o).style?(g(),b(i,{key:0,class:"px-[var(--pad-sidebar-m)]"},{default:y((()=>[h(i,{onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).link))},{default:y((()=>[h(i,{class:"leading-[1]",style:_(k(a))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"])])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(o).style?(g(),b(i,{key:1,class:"px-[20rpx] flex items-center"},{default:y((()=>[h(i,{onClick:t[1]||(t[1]=e=>k(l).toRedirect(k(o).link))},{default:y((()=>[h(i,{class:"max-w-[200rpx] truncate leading-[1]",style:_(k(r))},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"])])),_:1}),k(o).subTitle.text?(g(),b(s,{key:0,style:_({background:k(o).subTitle.color}),class:"mx-[10rpx] w-[2rpx] h-[24rpx] opacity-70"},null,8,["style"])):S("v-if",!0),h(s,{class:"max-w-[300rpx] truncate",style:_({color:k(o).subTitle.color,fontSize:2*k(o).subTitle.fontSize+"rpx"})},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1},8,["style"]),k(o).more.isShow?(g(),b(i,{key:1,class:"ml-auto text-right",style:_({color:k(o).more.color})},{default:y((()=>[h(i,{onClick:t[2]||(t[2]=e=>k(l).toRedirect(k(o).more.link)),class:"flex items-center"},{default:y((()=>[h(s,{class:"max-w-[200rpx] truncate text-[26rpx]"},{default:y((()=>[w(C(k(o).more.text),1)])),_:1}),h(s,{class:"nc-iconfont nc-icon-youV6xx text-[24rpx]",style:_({color:k(o).more.color})},null,8,["style"])])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0)])),_:1})])),_:1},8,["style"])}}}),io=We({__name:"index",props:["component","index","value"],setup(e){const t=e,l=i(),o=s((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),a={list:[{image:"",title:"轮播图1",link:{name:""}}],height:300,border_radius:12,indicator_dots:!0,autoplay:!0,interval:3e3,margin:[20,20,20,20],componentStartBgColor:"#ffffff",componentBgColor:"#ffffff",componentEndBgColor:"#ffffff",topRounded:0,bottomRounded:0,elementBgColor:"",topElementRounded:0,bottomElementRounded:0,padding:[0,0,0,0],columns:1,show:!0},r=s((()=>{const e=o.value||{};return{...a,...e}})),n=u([]),d=u("/static/resource/images/diy/banner_placeholder.png"),f=s((()=>{const e={};return t.data.componentStartBgColor&&(e.background=`linear-gradient(${t.data.componentGradientAngle||"to bottom"}, ${t.data.componentStartBgColor}, ${t.data.componentEndBgColor||t.data.componentStartBgColor})`),t.data.topRounded&&(e.borderTopLeftRadius=t.data.topRounded+"rpx",e.borderTopRightRadius=t.data.topRounded+"rpx"),t.data.bottomRounded&&(e.borderBottomLeftRadius=t.data.bottomRounded+"rpx",e.borderBottomRightRadius=t.data.bottomRounded+"rpx"),t.data.margin&&(e.marginTop=t.data.margin.top+"rpx",e.marginBottom=t.data.margin.bottom+"rpx",e.marginLeft=t.data.margin.both+"rpx",e.marginRight=t.data.margin.both+"rpx"),e})),p=s((()=>{const e={height:(t.data.height||300)+"rpx"};return t.data.border_radius&&(e.borderRadius=t.data.border_radius+"rpx",e.overflow="hidden"),e}));c((()=>{m()}));const m=()=>{t.data.list&&t.data.list.length>0?n.value=t.data.list:n.value=[{image:"",title:"众筹项目展示",link:{name:"NIUCROWD_PROJECT_LIST",url:"/addon/niucrowd/pages/project/list"}}]};return(e,t)=>{const l=E,o=D,a=$,i=Y,s=H;return g(),b(a,{class:"niucrowd-banner-wrap",style:_(k(f))},{default:y((()=>[h(s,{class:"banner-swiper",style:_(k(p)),"indicator-dots":k(r).indicator_dots,autoplay:k(r).autoplay,interval:k(r).interval||3e3,duration:500,"indicator-color":"rgba(255, 255, 255, 0.5)","indicator-active-color":"#FF6B6B"},{default:y((()=>[(g(!0),R(B,null,F(n.value,((e,t)=>(g(),b(i,{key:t,onClick:t=>(e=>{e.link&&e.link.url&&we({url:e.link.url})})(e)},{default:y((()=>[h(a,{class:"banner-item"},{default:y((()=>[h(l,{src:e.image||d.value,mode:"aspectFill",class:"banner-img"},null,8,["src"]),e.title?(g(),b(a,{key:0,class:"banner-title"},{default:y((()=>[h(o,null,{default:y((()=>[w(C(e.title),1)])),_:2},1024)])),_:2},1024)):S("v-if",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1},8,["style","indicator-dots","autoplay","interval"])])),_:1},8,["style"])}}},[["__scopeId","data-v-b9d2f2ae"]]),so=We({__name:"index",props:["component","index","value"],setup(e){const t=e,l=i(),o=s((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),a={style:"style1",source:"all",category_ids:[],columns:4,show_all:!0,title:"项目分类",title_color:"#333333",show_title:!0,componentStartBgColor:"#ffffff",componentBgColor:"#ffffff",componentEndBgColor:"#ffffff",topRounded:0,bottomRounded:0,elementBgColor:"",topElementRounded:0,bottomElementRounded:0,margin:[0,0,0,0],padding:[0,0,0,0],height:"auto",list:[],show:!0},r=s((()=>{const e=o.value||{},t={...a,...e};return t.componentStartBgColor||(t.componentStartBgColor=a.componentStartBgColor),t.columns||(t.columns=a.columns),t})),n=u([]);u("/static/resource/images/diy/category_placeholder.png");const d=s((()=>{const e={};return t.data&&t.data.componentStartBgColor&&(e.background=`linear-gradient(${t.data.componentGradientAngle||"to bottom"}, ${t.data.componentStartBgColor}, ${t.data.componentEndBgColor||t.data.componentStartBgColor})`),t.data&&t.data.topRounded&&(e.borderTopLeftRadius=t.data.topRounded+"rpx",e.borderTopRightRadius=t.data.topRounded+"rpx"),t.data&&t.data.bottomRounded&&(e.borderBottomLeftRadius=t.data.bottomRounded+"rpx",e.borderBottomRightRadius=t.data.bottomRounded+"rpx"),t.data&&t.data.margin&&(e.marginTop=t.data.margin.top+"rpx",e.marginBottom=t.data.margin.bottom+"rpx",e.marginLeft=t.data.margin.both+"rpx",e.marginRight=t.data.margin.both+"rpx"),e})),f=s((()=>({gridTemplateColumns:`repeat(${t.data&&t.data.columns?t.data.columns:4}, 1fr)`})));c((()=>{p()}));const p=async()=>{try{if("decorate"==l.mode){const e=[{category_id:1,category_name:"科技产品",category_image:"",project_count:25},{category_id:2,category_name:"创意设计",category_image:"",project_count:18},{category_id:3,category_name:"公益慈善",category_image:"",project_count:12},{category_id:4,category_name:"文化艺术",category_image:"",project_count:8},{category_id:5,category_name:"智能硬件",category_image:"",project_count:15},{category_id:6,category_name:"生活用品",category_image:"",project_count:20}];n.value=e}else{const e=await at({status:1});if(e&&e.data){const t=Array.isArray(e.data)?e.data:e.data.data||[];n.value=t.map((e=>({category_id:e.category_id,category_name:e.category_name,category_image:e.image||"",project_count:e.project_count||0}))),console.log("分类API响应数据:",e.data),console.log("处理后的分类列表:",n.value)}else n.value=[]}}catch(e){console.error("加载分类列表失败:",e),n.value=[]}},m=()=>{we({url:"/addon/niucrowd/pages/project/list"})},v=e=>{console.warn("分类图片加载失败:",e)},x=e=>{const t=["linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)","linear-gradient(135deg, #34C759 0%, #30D158 100%)","linear-gradient(135deg, #FF9500 0%, #FFCC02 100%)","linear-gradient(135deg, #AF52DE 0%, #BF5AF2 100%)","linear-gradient(135deg, #FF2D92 0%, #FF375F 100%)","linear-gradient(135deg, #FF3B30 0%, #FF6961 100%)"];return{background:t[e%t.length]}};return(e,t)=>{const l=D,o=$,a=E;return g(),b(o,{class:"niucrowd-category-wrap",style:_(k(d))},{default:y((()=>[k(r).show_title?(g(),b(o,{key:0,class:"title-wrap"},{default:y((()=>[h(l,{class:"title",style:_({color:k(r).title_color})},{default:y((()=>[w(C(k(r).title),1)])),_:1},8,["style"])])),_:1})):S("v-if",!0),h(o,{class:T(["category-list",k(r).style]),style:_(k(f))},{default:y((()=>[S(" 全部分类按钮 - 放在最前面 "),k(r).show_all?(g(),b(o,{key:0,class:"category-item all-category",onClick:m},{default:y((()=>[h(o,{class:"category-icon"},{default:y((()=>[h(o,{class:"all-icon"},{default:y((()=>[h(l,{class:"iconfont iconquanbu"},{default:y((()=>[w("全")])),_:1})])),_:1})])),_:1}),h(l,{class:"category-name"},{default:y((()=>[w("全部")])),_:1})])),_:1})):S("v-if",!0),S(" 分类列表 "),(g(!0),R(B,null,F(n.value,((e,t)=>(g(),b(o,{key:t,class:"category-item",onClick:t=>(e=>{we({url:`/addon/niucrowd/pages/project/list?category_id=${e.category_id}`})})(e)},{default:y((()=>[h(o,{class:"category-icon"},{default:y((()=>[e.category_image&&""!==e.category_image?(g(),b(a,{key:0,src:k(rt)(e.category_image),mode:"aspectFill",class:"icon-img",onError:v},null,8,["src"])):(g(),b(o,{key:1,class:"default-category-icon",style:_(x(t))},{default:y((()=>[h(l,null,{default:y((()=>[w(C(e.category_name.charAt(0)),1)])),_:2},1024)])),_:2},1032,["style"]))])),_:2},1024),h(l,{class:"category-name"},{default:y((()=>[w(C(e.category_name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1},8,["class","style"])])),_:1},8,["style"])}}},[["__scopeId","data-v-1de7bfeb"]]),uo=We({__name:"index",props:["component","index","value"],setup(e){const t=e,l=i(),o=s((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),a={style:"style1",source:"all",num:6,category_id:0,project_ids:[],order:"create_time",show_more:!0,more_text:"查看更多",title:"热门项目",title_color:"#333333",show_title:!0,componentStartBgColor:"#ffffff",componentBgColor:"#ffffff",componentEndBgColor:"#ffffff",topRounded:0,bottomRounded:0,elementBgColor:"",topElementRounded:0,bottomElementRounded:0,margin:[0,0,0,0],padding:[0,0,0,0],height:"auto",columns:2,list:[],show:!0},r=s((()=>{const e=o.value||{},t={...a,...e};return t.componentStartBgColor||(t.componentStartBgColor=a.componentStartBgColor),t.columns||(t.columns=a.columns),t})),n=u([]),d=u("/static/resource/images/diy/block_placeholder.png"),f=s((()=>{const e={},t=r.value;return t&&t.componentStartBgColor&&(e.background=`linear-gradient(${t.componentGradientAngle||"to bottom"}, ${t.componentStartBgColor}, ${t.componentEndBgColor||t.componentStartBgColor})`),t&&t.topRounded&&(e.borderTopLeftRadius=t.topRounded+"rpx",e.borderTopRightRadius=t.topRounded+"rpx"),t&&t.bottomRounded&&(e.borderBottomLeftRadius=t.bottomRounded+"rpx",e.borderBottomRightRadius=t.bottomRounded+"rpx"),t.margin&&("object"==typeof t.margin?(e.marginTop=t.margin.top+"rpx",e.marginBottom=t.margin.bottom+"rpx",e.marginLeft=t.margin.both+"rpx",e.marginRight=t.margin.both+"rpx"):Array.isArray(t.margin)&&(e.marginTop=t.margin[0]+"rpx",e.marginRight=t.margin[1]+"rpx",e.marginBottom=t.margin[2]+"rpx",e.marginLeft=t.margin[3]+"rpx")),e}));c((()=>{p()}));const p=async()=>{try{if("decorate"==l.mode){const e=[{project_id:1,project_name:"智能手表众筹项目",description:"全新智能手表，健康监测新体验",cover_image:"",target_amount:1e5,current_amount:5e4,support_count:120,status:1,status_text:"进行中",end_time:Date.now()/1e3+2592e3},{project_id:2,project_name:"环保背包设计",description:"可持续发展的环保材料制作",cover_image:"",target_amount:5e4,current_amount:3e4,support_count:85,status:1,status_text:"进行中",end_time:Date.now()/1e3+1296e3}];n.value=e.slice(0,r.value.num||6)}else{const e=r.value||{},t=e.source||"all",l=e.num||6;let o,a={limit:l,page:1};if("manual"===t&&e.project_ids&&e.project_ids.length>0?(a.project_ids=e.project_ids.join(","),o=await nt(a)):"category"===t&&e.category_id?(a.category_id=e.category_id,a.order=e.order||"create_time",o=await nt(a)):"recommend"===t?o=await it(a):"hot"===t?(a.type=e.order||"amount",o=await st(a)):(a.order=e.order||"create_time",o=await nt(a)),o&&o.data){const e=Array.isArray(o.data)?o.data:o.data.data||[];n.value=e.slice(0,l)}else n.value=[]}}catch(e){console.error("加载项目列表失败:",e),n.value=[]}},m=e=>!e.target_amount||e.target_amount<=0?0:Math.min(Math.round(e.current_amount/e.target_amount*100),100),v=e=>{if(!e.end_time)return"时间待定";const t=Date.now()/1e3,l=e.end_time-t;if(l<=0)return"已结束";const o=Math.floor(l/86400);if(o>0)return`${o}天`;const a=Math.floor(l/3600);if(a>0)return`${a}小时`;return`${Math.floor(l/60)}分钟`},x=()=>{we({url:"/addon/niucrowd/pages/project/list"})};return(e,t)=>{const l=D,o=$,a=E;return g(),b(o,{class:"niucrowd-project-wrap",style:_(k(f))},{default:y((()=>[k(r).show_title?(g(),b(o,{key:0,class:"title-wrap"},{default:y((()=>[h(l,{class:"title",style:_({color:k(r).title_color})},{default:y((()=>[w(C(k(r).title),1)])),_:1},8,["style"])])),_:1})):S("v-if",!0),h(o,{class:T(["project-list",k(r).style])},{default:y((()=>[(g(!0),R(B,null,F(n.value,((e,t)=>(g(),b(o,{key:t,class:"project-item",onClick:t=>(e=>{we({url:`/addon/niucrowd/pages/project/detail?id=${e.project_id}`})})(e)},{default:y((()=>[h(o,{class:"project-image"},{default:y((()=>[h(a,{src:e.cover_image||d.value,mode:"aspectFill",class:"cover-img"},null,8,["src"]),e.status_text?(g(),b(o,{key:0,class:"status-tag"},{default:y((()=>[w(C(e.status_text),1)])),_:2},1024)):S("v-if",!0)])),_:2},1024),h(o,{class:"project-info"},{default:y((()=>[h(o,{class:"project-name"},{default:y((()=>[w(C(e.project_name),1)])),_:2},1024),h(o,{class:"project-desc"},{default:y((()=>[w(C(e.description),1)])),_:2},1024),h(o,{class:"project-stats"},{default:y((()=>[h(o,{class:"stat-item"},{default:y((()=>[h(l,{class:"stat-label"},{default:y((()=>[w("目标：")])),_:1}),h(l,{class:"stat-value"},{default:y((()=>[w("¥"+C(e.target_amount),1)])),_:2},1024)])),_:2},1024),h(o,{class:"stat-item"},{default:y((()=>[h(l,{class:"stat-label"},{default:y((()=>[w("已筹：")])),_:1}),h(l,{class:"stat-value primary"},{default:y((()=>[w("¥"+C(e.current_amount),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),h(o,{class:"progress-wrap"},{default:y((()=>[h(o,{class:"progress-bar"},{default:y((()=>[h(o,{class:"progress-fill",style:_({width:m(e)+"%"})},null,8,["style"])])),_:2},1024),h(l,{class:"progress-text"},{default:y((()=>[w(C(m(e))+"%",1)])),_:2},1024)])),_:2},1024),h(o,{class:"project-footer"},{default:y((()=>[h(l,{class:"support-count"},{default:y((()=>[w(C(e.support_count)+"人支持",1)])),_:2},1024),h(l,{class:"time-left"},{default:y((()=>[w(C(v(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1},8,["class"]),k(r).show_more&&n.value.length>0?(g(),b(o,{key:1,class:"more-wrap"},{default:y((()=>[h(o,{class:"more-btn",onClick:x},{default:y((()=>[w(C(k(r).more_text||"查看更多"),1)])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["style"])}}},[["__scopeId","data-v-cbfc4bf2"]]),co=We({__name:"index",props:["component","index","value"],setup(e){const t=e,l=i(),o=s((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),a={placeholder:"搜索项目名称",bg_color:"#f5f5f5",text_color:"#999999",border_radius:25,height:70,margin:[20,20,20,20],componentStartBgColor:"#ffffff",componentBgColor:"#ffffff",componentEndBgColor:"#ffffff",topRounded:0,bottomRounded:0,elementBgColor:"",topElementRounded:0,bottomElementRounded:0,padding:[0,0,0,0],columns:1,list:[],show:!0},r=s((()=>{const e=o.value||{};return{...a,...e}})),n=s((()=>{const e={};if(r.value.componentStartBgColor&&(e.background=`linear-gradient(${r.value.componentGradientAngle||"to bottom"}, ${r.value.componentStartBgColor}, ${r.value.componentEndBgColor||r.value.componentStartBgColor})`),r.value.topRounded&&(e.borderTopLeftRadius=r.value.topRounded+"rpx",e.borderTopRightRadius=r.value.topRounded+"rpx"),r.value.bottomRounded&&(e.borderBottomLeftRadius=r.value.bottomRounded+"rpx",e.borderBottomRightRadius=r.value.bottomRounded+"rpx"),r.value.margin){const t=Array.isArray(r.value.margin)?r.value.margin:[0,0,0,0];e.marginTop=t[0]+"rpx",e.marginRight=t[1]+"rpx",e.marginBottom=t[2]+"rpx",e.marginLeft=t[3]+"rpx"}return e})),d=s((()=>({backgroundColor:r.value.bg_color||"#f5f5f5",height:(r.value.height||70)+"rpx",borderRadius:(r.value.border_radius||25)+"rpx"}))),u=()=>{we({url:"/addon/niucrowd/pages/project/search"})};return(e,t)=>{const l=D,o=$;return g(),b(o,{class:"niucrowd-search-wrap",style:_(k(n))},{default:y((()=>[h(o,{class:"search-box",style:_(k(d)),onClick:u},{default:y((()=>[h(l,{class:"search-icon iconfont iconsousuo"}),h(l,{class:"search-placeholder",style:_({color:k(r).text_color})},{default:y((()=>[w(C(k(r).placeholder||"搜索项目名称"),1)])),_:1},8,["style"])])),_:1},8,["style"])])),_:1},8,["style"])}}},[["__scopeId","data-v-aa4a9782"]]),fo=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=Se(),a=s((()=>o.info)),r=j({type:"banner",loading:!1,config:{gridRows:1,gridRowsGap:"0rpx",headHeight:"170rpx"}}),n=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),f=s((()=>{var e="";return n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e})),p=s((()=>{var e="";return n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+(n.value.componentStartBgColor||n.value.componentEndBgColor)+";",e})),m=e=>{"decorate"!=l.mode&&we({url:e})},z=u([]),E=e=>{A(e.id)};c((()=>{M()}));const M=()=>{if("decorate"==l.mode){let e={title:"满减券",type_name:"通用券",price:100,min_condition_money:0};for(let t=0;t<4;t++)z.value.push(e)}else(()=>{let e={num:"all"==n.value.source?n.value.num:"",coupon_ids:"custom"==n.value.source?n.value.couponIds:""};ct(e).then((e=>{z.value=e.data,r.loading=!1}))})()},A=e=>{if("decorate"!=l.mode)return a.value?void ft({coupon_id:e,number:1}).then((e=>{})):(ke().setLoginBack({url:"/addon/shop/pages/coupon/list"}),!1)};return(e,t)=>{const l=D,o=$,a=I,i=v(x("x-skeleton"),dt);return z.value&&Object.keys(z.value).length>0?(g(),b(i,{key:0,type:r.type,loading:r.loading,config:r.config},{default:y((()=>[h(o,{style:_(k(f)),class:"overflow-hidden"},{default:y((()=>["style-1"==k(n).style?(g(),b(o,{key:0,class:"coupon-wrap style-1 relative"},{default:y((()=>[h(a,{"scroll-x":"true",class:"coupon-list",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style1_bg2.png")+")","background-size":"100%","background-repeat":"no-repeat"})},{default:y((()=>[h(o,{class:"coupon-class"},{default:y((()=>[z.value.length>1?(g(!0),R(B,{key:0},F(z.value,((e,t)=>(g(),b(o,{key:t,class:T(["rounded-[16rpx] box-border pt-[14rpx] inline-flex flex-col items-center relative w-[150rpx] h-[130rpx]",{"mr-[20rpx]":t!=z.value.length-1}]),style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/coupon_item_bg.png")+")","background-size":"100%","background-repeat":"no-repeat"}),onClick:t=>E(e)},{default:y((()=>[h(o,{class:"truncate w-full flex items-baseline justify-center price-font text-[var(--price-text-color)]"},{default:y((()=>[h(l,{class:"text-[26rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[36rpx] truncate font-500"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#303133] text-[20rpx] mt-[12rpx]"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024),h(o,{class:"mt-[auto] rounded-b-[12rpx] text-[#f2333c] text-[20rpx] w-[100%] h-[36rpx] flex items-center justify-center bg-[#fff5f2]"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128)):(g(!0),R(B,{key:1},F(z.value,((e,t)=>(g(),b(o,{key:t,class:"rounded-[16rpx] box-border pt-[14rpx] pl-[44rpx] pr-[44rpx] inline-flex items-center justify-between relative w-[100%] h-[130rpx]",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style1_bg4.png")+")","background-size":"100%","background-repeat":"no-repeat"}),onClick:t=>E(e)},{default:y((()=>[h(o,{class:"flex price-font text-[var(--price-text-color)] items-baseline"},{default:y((()=>[h(l,{class:"text-[36rpx] mt-[16rpx] mr-[4rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[85rpx] font-500 max-w-[170rpx] truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024),h(o,{class:"border-0 border-dashed border-r-[2rpx] border-[#FF323C] absolute left-[40%] top-[46rpx] bottom-0 w-[2rpx]"}),h(o,{class:"w-[270rpx]"},{default:y((()=>[h(o,{class:"flex items-center mt-[auto]"},{default:y((()=>[h(l,{class:"rounded-[4rpx] bg-[#fff3f0] text-[#f2333c] border-[2rpx] border-solid border-[#f2333c] text-[22rpx] px-[6rpx] pb-[4rpx] pt-[6rpx] flex items-center justify-center whitespace-nowrap"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024),h(l,{class:"ml-[4rpx] text-[#f2333c] max-w-[184rpx] truncate"},{default:y((()=>[w(C(e.title),1)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#f2333c] text-[30rpx] font-500 mt-[10rpx] w-[270rpx] truncate"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"消费满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1},8,["style"]),h(o,{class:"w-[100%] h-[130rpx] pt-[24rpx] px-[26rpx] box-border flex items-center justify-between absolute left-0 right-0 bottom-0",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style1_bg.png")+")","background-size":"100% 130rpx","background-repeat":"no-repeat"})},{default:y((()=>[h(o,{class:"flex flex-col"},{default:y((()=>[h(l,{class:"text-[30rpx] text-[#fff] font-400"},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1}),h(l,{class:"text-[20rpx] text-[rgba(255,255,255,.8)] mt-[10rpx]"},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1})])),_:1}),k(n).btnText?(g(),b(l,{key:0,onClick:t[0]||(t[0]=e=>m("/addon/shop/pages/coupon/list")),class:"bg-[#fff] flex items-center justify-center text-[#FF4142] text-[22rpx] min-w-[100rpx] px-[24rpx] box-border h-[50rpx] coupon-buy-btn"},{default:y((()=>[w(C(k(n).btnText),1)])),_:1})):S("v-if",!0)])),_:1},8,["style"])])),_:1})):"style-2"==k(n).style?(g(),b(o,{key:1,class:"coupon-wrap style-2 relative"},{default:y((()=>[h(a,{"scroll-x":"true",class:"coupon-list"},{default:y((()=>[(g(!0),R(B,null,F(z.value,((e,t)=>(g(),b(o,{key:t,class:T(["box-border pt-[14rpx] inline-flex flex-col items-center relative w-[140rpx] h-[130rpx] rounded-[10rpx]",{"mr-[20rpx]":t!=z.value.length-1,"mr-[290rpx]":t==z.value.length-1}]),style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/coupon_item_bg.png")+")","background-size":"100%","background-repeat":"no-repeat"}),onClick:t=>E(e)},{default:y((()=>[h(o,{class:"flex items-baseline justify-center w-full truncate price-font text-[var(--price-text-color)]"},{default:y((()=>[h(l,{class:"text-[24rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[38rpx] font-bold truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#303133] text-[20rpx] truncate max-w-[120rpx] mt-[12rpx]"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024),h(o,{class:"mt-[auto] rounded-b-[12rpx] text-[#f2333c] text-[20rpx] w-[100%] h-[36rpx] flex items-center justify-center bg-[#fff5f2]"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1}),h(o,{class:"w-[290rpx] h-[170rpx] py-[20rpx] pl-[30rpx] box-border flex flex-col items-center justify-between absolute right-0 bottom-0",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style2_bg.png")+")","background-size":"290rpx 170rpx","background-repeat":"no-repeat"})},{default:y((()=>[h(l,{class:"text-[30rpx] text-[#fff] font-500"},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1}),h(l,{class:"text-[20rpx] text-[rgba(255,255,255,.8)] mt-[14rpx]"},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1}),k(n).btnText?(g(),b(l,{key:0,onClick:t[1]||(t[1]=e=>m("/addon/shop/pages/coupon/list")),class:"bg-[#fff] text-[#FF4142] text-[22rpx] min-w-[100rpx] px-[24rpx] box-border h-[50rpx] leading-[50rpx] text-center coupon-buy-btn mt-auto"},{default:y((()=>[w(C(k(n).btnText),1)])),_:1})):S("v-if",!0)])),_:1},8,["style"])])),_:1})):"style-3"==k(n).style?(g(),b(o,{key:2,class:"coupon-wrap style-3 relative",style:_({"background-image":"url("+k(d)("addon/shop/diy/goods_coupon/style3_bg.jpg")+")","background-size":"100% 204rpx","background-repeat":"no-repeat"})},{default:y((()=>[h(o,{class:"desc flex flex-col"},{default:y((()=>[h(l,{class:"text-[30rpx] text-[#fff] font-500"},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1}),h(l,{class:"text-[22rpx] text-[rgba(255,255,255,.8)] mt-[10rpx]"},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1}),k(n).btnText?(g(),b(l,{key:0,onClick:t[2]||(t[2]=e=>m("/addon/shop/pages/coupon/list")),class:"bg-[#fff] text-[#FF4142] text-[24rpx] w-[140rpx] box-border h-[50rpx] leading-[50rpx] text-center coupon-buy-btn mt-auto"},{default:y((()=>[w(C(k(n).btnText),1)])),_:1})):S("v-if",!0)])),_:1}),z.value.length>1?(g(),b(a,{key:0,"scroll-x":"true",class:"coupon-list"},{default:y((()=>[(g(!0),R(B,null,F(z.value,((e,t)=>(g(),b(o,{key:t,class:"bg-[#fff] box-border p-[8rpx] pb-[12rpx] inline-flex flex-col items-center relative rounded-[20rpx] ml-[12rpx]",onClick:t=>E(e)},{default:y((()=>[h(o,{class:"coupon-item-content"},{default:y((()=>[h(o,{class:"text-[20rpx] text-[#fff]"},{default:y((()=>[w(C(e.type_name),1)])),_:2},1024),h(o,{class:"mt-[auto] flex items-baseline justify-center w-full truncate price-font text-[#fff]"},{default:y((()=>[h(l,{class:"text-[24rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[44rpx] font-bold truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),h(o,{class:"text-[#303133] text-[22rpx] truncate max-w-[120rpx] mt-[12rpx]"},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})):S("v-if",!0),1==z.value.length?(g(),b(o,{key:1,class:"bg-[#fff] box-border p-[10rpx] relative rounded-[20rpx] single-coupon flex-1",onClick:t[3]||(t[3]=e=>E(z.value[0]))},{default:y((()=>[h(o,{class:"flex items-center coupon-item-content"},{default:y((()=>[h(o,{class:"coupon-left flex items-center justify-center text-[#fff] w-[156rpx] mr-[30rpx]"},{default:y((()=>[h(l,{class:"text-[24rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[44rpx] font-[500]"},{default:y((()=>[w(C(parseFloat(z.value[0].price)),1)])),_:1})])),_:1}),h(o,{class:"flex flex-col"},{default:y((()=>[h(o,{class:"text-[#fff] text-[28rpx] mb-[14rpx]"},{default:y((()=>[w(C("0.00"==z.value[0].min_condition_money?"无门槛":"满"+parseFloat(z.value[0].min_condition_money)+"元可用"),1)])),_:1}),h(o,{class:"flex items-center"},{default:y((()=>[h(l,{class:"bg-[#fff] mr-[10rpx] text-[red] text-[20rpx] px-[10rpx] py-[8rpx] rounded-[20rpx]"},{default:y((()=>[w(C(z.value[0].type_name),1)])),_:1}),h(l,{class:"text-[#fff] text-[24rpx]"},{default:y((()=>[w("店铺优惠券")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["style"])):"style-4"==k(n).style?(g(),b(o,{key:3,class:"coupon-wrap style-4 relative",style:_(k(p))},{default:y((()=>[h(o,{class:"desc flex items-center pt-[6rpx] pb-[26rpx]",onClick:t[4]||(t[4]=e=>m("/addon/shop/pages/coupon/list"))},{default:y((()=>[h(l,{class:"text-[32rpx] text-[#fff] font-500",style:_({color:k(n).titleColor})},{default:y((()=>[w(C(k(n).couponTitle),1)])),_:1},8,["style"]),h(l,{class:"text-[22rpx] text-[rgba(255,255,255,.8)] ml-[10rpx]",style:_({color:k(n).subTitleColor})},{default:y((()=>[w(C(k(n).couponSubTitle),1)])),_:1},8,["style"])])),_:1}),h(a,{"scroll-x":"true",class:"coupon-list"},{default:y((()=>[(g(!0),R(B,null,F(z.value,((e,t)=>(g(),b(o,{key:t,class:"px-[10rpx] h-[120rpx] inline-flex items-center relative mr-[12rpx] coupon-item box-border min-w-[310rpx]",style:_({"background-color":k(n).couponItem.bgColor,"border-radius":2*k(n).couponItem.aroundRadius+"rpx"}),onClick:t=>E(e)},{default:y((()=>[h(o,{class:"flex min-w-[110rpx] max-w-[120rpx] items-baseline justify-center truncate price-font mr-[10rpx]",style:_({color:k(n).couponItem.moneyColor})},{default:y((()=>[h(l,{class:"text-[26rpx]"},{default:y((()=>[w("￥")])),_:1}),h(l,{class:"text-[46rpx] font-bold truncate"},{default:y((()=>[w(C(parseFloat(e.price)),1)])),_:2},1024)])),_:2},1032,["style"]),h(o,{class:"flex flex-col"},{default:y((()=>[h(o,{class:"text-[28rpx] font-500",style:_({color:k(n).couponItem.textColor})},{default:y((()=>[w(C(e.type_name),1)])),_:2},1032,["style"]),h(o,{class:"text-[#666] text-[24rpx] truncate max-w-[180rpx] mt-[12rpx]",style:_({color:k(n).couponItem.subTextColor})},{default:y((()=>[w(C("0.00"==e.min_condition_money?"无门槛":"满"+parseFloat(e.min_condition_money)+"元可用"),1)])),_:2},1032,["style"])])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])])),_:1},8,["type","loading","config"])):S("v-if",!0)}}}),[["__scopeId","data-v-e4b27359"]]),po=We(n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e}));c((()=>{r(),"decorate"==l.mode&&f((()=>o.value),((e,t)=>{e&&"ManyGoodsList"==e.componentName&&r()}))}));const r=()=>{"style-3"==o.value.headStyle&&"goods_category"==o.value.source&&o.value.goods_category?z(o.value.goods_category):m(o.value.list[0],0,!0)},n=u(0),p=u(null),m=(e,t,o=!1)=>{("decorate"!=l.mode||o)&&(n.value=t,A({source:e.source,goods_category:e.goods_category,goods_ids:e.goods_ids}))},v=u([]),x=u(0),z=e=>{mt({pid:e}).then((e=>{1==e.code&&(v.value=e.data,v.value&&A({source:"category",goods_category:""}))}))},M=e=>{"decorate"!=l.mode&&(x.value=e.category_id,A({source:"category",goods_category:x.value}))},A=e=>{p.value={style:o.value.style,margin:o.value.margin,source:e.source,num:o.value.num,sortWay:o.value.sortWay,goodsNameStyle:o.value.goodsNameStyle,priceStyle:o.value.priceStyle,saleStyle:o.value.saleStyle,btnStyle:o.value.btnStyle,labelStyle:o.value.labelStyle,imgElementRounded:o.value.imgElementRounded,elementBgColor:o.value.elementBgColor,topElementRounded:o.value.topElementRounded,bottomElementRounded:o.value.bottomElementRounded},e.goods_category&&(p.value.goods_category=e.goods_category),e.goods_ids&&e.goods_ids.length&&(p.value.goods_ids=e.goods_ids)};return(e,t)=>{const l=E,r=D,i=$,s=I;return g(),b(i,{class:"overflow-hidden"},{default:y((()=>[h(s,{"scroll-x":"true",class:T(["many-goods-list-head",k(o).headStyle]),"scroll-into-view":"a"+n.value,style:_(k(a))},{default:y((()=>["style-3"==k(o).headStyle?(g(),R(B,{key:0},["custom"==k(o).source?(g(!0),R(B,{key:0},F(k(o).list,((e,t)=>(g(),b(i,{key:t,class:T(["flex-col inline-flex items-center justify-center",{"pr-[40rpx]":t!=k(o).list.length-1}]),onClick:l=>m(e,t)},{default:y((()=>[e.imageUrl?(g(),b(l,{key:0,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:T(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":t==n.value}]),src:k(d)(e.imageUrl),mode:"aspectFit"},null,8,["style","class","src"])):(g(),b(l,{key:1,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:T(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":t==n.value}]),src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill"},null,8,["style","class","src"])),h(r,{class:T(["text-[28rpx] mt-[16rpx]",{"font-500 text-[var(--primary-color)]":t==n.value}])},{default:y((()=>[w(C(e.title),1)])),_:2},1032,["class"])])),_:2},1032,["class","onClick"])))),128)):"goods_category"==k(o).source?(g(),R(B,{key:1},[h(i,{class:"pr-[40rpx] inline-flex flex-col items-center justify-center",onClick:t[0]||(t[0]=e=>M({category_id:0}))},{default:y((()=>[h(l,{style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:T(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":0==x.value}]),src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill"},null,8,["style","class","src"]),h(r,{class:T(["text-[28rpx] mt-[16rpx]",{"font-500 text-[var(--primary-color)]":0==x.value}])},{default:y((()=>[w("全部")])),_:1},8,["class"])])),_:1}),(g(!0),R(B,null,F(v.value,((e,t)=>(g(),b(i,{key:t,class:T(["flex-col inline-flex items-center justify-center",{"pr-[40rpx]":t!=v.value.length-1}]),onClick:t=>M(e)},{default:y((()=>[e.image?(g(),b(l,{key:0,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:T(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":x.value==e.category_id}]),src:k(d)(e.image),mode:"aspectFit"},null,8,["style","class","src"])):(g(),b(l,{key:1,style:_({borderRadius:2*k(o).aroundRadius+"rpx"}),class:T(["w-[90rpx] h-[90rpx] overflow-hidden border-[2rpx] border-solid border-transparent",{"border-[var(--primary-color)]":x.value==e.category_id}]),src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill"},null,8,["style","class","src"])),h(r,{class:T(["text-[28rpx] mt-[16rpx]",{"font-500 text-[var(--primary-color)]":x.value==e.category_id}])},{default:y((()=>[w(C(e.category_name),1)])),_:2},1032,["class"])])),_:2},1032,["class","onClick"])))),128))],64)):S("v-if",!0)],64)):(g(!0),R(B,{key:1},F(k(o).list,((e,t)=>(g(),b(i,{class:T(["scroll-item",[k(o).headStyle,{active:t==n.value}]]),id:"a"+t,key:t,onClick:l=>m(e,t)},{default:y((()=>["style-1"==k(o).headStyle?(g(),b(i,{key:0,class:"cate"},{default:y((()=>[h(i,{class:"name"},{default:y((()=>[w(C(e.title),1)])),_:2},1024),h(i,{class:"desc","v-if":e.desc},{default:y((()=>[w(C(e.desc),1)])),_:2},1032,["v-if"])])),_:2},1024)):S("v-if",!0),"style-2"==k(o).headStyle?(g(),b(i,{key:1,class:"cate"},{default:y((()=>[h(i,{class:"name"},{default:y((()=>[w(C(e.title),1)])),_:2},1024),t==n.value?(g(),b(r,{key:0,class:"nc-iconfont nc-icon-xiaolian-2 !text-[40rpx] text-[var(--primary-color)] transform"})):S("v-if",!0)])),_:2},1024)):S("v-if",!0),"style-4"==k(o).headStyle?(g(),b(i,{key:2,class:"cate"},{default:y((()=>[h(i,{class:"name"},{default:y((()=>[w(C(e.title),1)])),_:2},1024)])),_:2},1024)):S("v-if",!0)])),_:2},1032,["class","id","onClick"])))),128))])),_:1},8,["class","scroll-into-view","style"]),p.value?(g(),b(ut,{key:0,class:"many-goods-list-body",value:p.value},null,8,["value"])):S("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-b439954f"]]),mo=We(n({__name:"index",props:["component","index","value"],setup(e){const t=e,l=i(),o=yt(),a=j({type:"",loading:"decorate"!=l.mode,config:{}}),r=u([]),n=s((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),I=s((()=>{var e="";return e+="position:relative;",n.value.componentStartBgColor&&(n.value.componentStartBgColor&&n.value.componentEndBgColor?e+=`background:linear-gradient(${n.value.componentGradientAngle},${n.value.componentStartBgColor},${n.value.componentEndBgColor});`:e+="background-color:"+n.value.componentStartBgColor+";"),n.value.componentBgUrl&&(e+=`background-image:url('${d(n.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;"),e})),M=s((()=>{var e={val:"",style:""};return n.value.imgElementRounded&&(e.val=2*n.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*n.value.imgElementRounded+"rpx;"),e})),A=s((()=>{var e="";return n.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${n.value.componentBgAlpha/10});`,e+=`height:${Y.value}px;`,n.value.topRounded&&(e+="border-top-left-radius:"+2*n.value.topRounded+"rpx;"),n.value.topRounded&&(e+="border-top-right-radius:"+2*n.value.topRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomRounded+"rpx;"),n.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomRounded+"rpx;")),e})),N=s((()=>{var e="";return n.value.topElementRounded&&(e+="border-top-left-radius:"+2*n.value.topElementRounded+"rpx;"),n.value.topElementRounded&&(e+="border-top-right-radius:"+2*n.value.topElementRounded+"rpx;"),n.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*n.value.bottomElementRounded+"rpx;"),n.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*n.value.bottomElementRounded+"rpx;"),e})),W=s((()=>{var e="";return n.value.margin&&n.value.margin.both?e+="calc((100vw - "+4*n.value.margin.both+"rpx - 20rpx) / 2)":e+="calc((100vw - 20rpx) / 2 )",e})),V=z(),Y=u(0);c((()=>{H(),"decorate"==l.mode?f((()=>n.value),((e,t)=>{e&&"ShopExchangeGoods"==e.componentName&&p((()=>{m().in(V).select(".diy-shop-exchange-goods-list").boundingClientRect((e=>{Y.value=e.height})).exec()}))})):f((()=>n.value),((e,t)=>{H()}),{deep:!0})}));const H=()=>{if("decorate"==l.mode){let e={image:"",names:"商品名称",total_exchange_num:100,point:100,price:100};r.value.push(e),r.value.push(e)}else"style-1"==n.value.style?(a.type="list",a.type="list",a.config={textRows:2}):"style-2"==n.value.style?(a.type="waterfall",a.config={headHeight:"320rpx",gridRows:1,textRows:2,textWidth:["100%","80%"]}):"style-3"==n.value.style&&(a.type="waterfall",a.config={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]}),(()=>{let e={order:n.value.sortWay,num:"all"==n.value.source?n.value.num:"",ids:"custom"==n.value.source?n.value.goods_ids.join(","):""};gt(e).then((e=>{r.value=e.data,a.loading=!1,n.value.componentBgUrl&&setTimeout((()=>{m().in(V).select(".diy-shop-exchange-goods-list").boundingClientRect((e=>{Y.value=e.height})).exec()}),1e3)}))})()};return(e,t)=>{const l=$,i=E,s=v(x("u--image"),xt),u=D,c=v(x("x-skeleton"),dt);return g(),b(c,{type:a.type,loading:a.loading,config:a.config},{default:y((()=>[h(l,{class:"overflow-hidden",style:_(k(I))},{default:y((()=>[h(l,{style:_(k(A))},null,8,["style"]),r.value.length?(g(),b(l,{key:0,class:"diy-shop-exchange-goods-list relative flex flex-wrap justify-between"},{default:y((()=>[(g(!0),R(B,null,F(r.value,((e,t)=>(g(),b(l,{class:T(["overflow-hidden bg-[#fff] flex flex-col box-border w-[calc(50%-10rpx)]",{"mt-[20rpx]":t>1}]),style:_(k(N)),key:e.goods_id,onClick:t=>{we({url:"/addon/shop/pages/point/detail",param:{id:e.id}})}},{default:y((()=>[h(s,{width:k(W),height:k(W),radius:k(M).val,src:k(d)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"overflow-hidden",style:_({width:k(W),height:k(W),"border-radius":k(M).val}),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["width","height","radius","src"]),h(l,{class:"flex-1 pt-[10rpx] pb-[20rpx] px-[16rpx] flex flex-col justify-between"},{default:y((()=>[h(l,{class:"text-[28rpx] leading-[40rpx] text-[#303133] multi-hidden mb-[10rpx]",style:_({color:k(n).goodsNameStyle.color,fontWeight:k(n).goodsNameStyle.fontWeight})},{default:y((()=>[e.goods_brand?(g(),b(l,{key:0,class:"brand-tag",style:_(k(o).baseTagStyle(e.goods_brand))},{default:y((()=>[w(C(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):S("v-if",!0),w(" "+C(e.names),1)])),_:2},1032,["style"]),h(l,{class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden my-[5rpx]"},{default:y((()=>[w(C(e.title),1)])),_:2},1024),e.goods_label&&e.goods_label.length?(g(),b(l,{key:0,class:"flex flex-wrap"},{default:y((()=>[(g(!0),R(B,null,F(e.goods_label,((e,t)=>(g(),R(B,null,["icon"==e.style_type&&e.icon?(g(),b(i,{key:0,class:"img-tag",src:k(d)(e.icon),mode:"heightFix",onError:t=>k(o).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?S("v-if",!0):(g(),b(l,{key:1,class:"base-tag",style:_(k(o).baseTagStyle(e))},{default:y((()=>[w(C(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):S("v-if",!0),h(l,{class:"text-[22rpx] leading-[28rpx] mt-[10rpx] text-[var(--text-color-light9)]",style:_({color:k(n).saleStyle.color})},{default:y((()=>[w("已兑"+C(e.total_exchange_num)+"人",1)])),_:2},1032,["style"]),h(l,{class:"flex justify-between flex-wrap items-center mt-[16rpx]"},{default:y((()=>[h(l,{class:"flex flex-col"},{default:y((()=>[h(l,{style:_({color:k(n).priceStyle.mainColor}),class:"text-[var(--price-text-color)] price-font ml-[2rpx] flex items-baseline"},{default:y((()=>[h(u,{class:"text-[32rpx]"},{default:y((()=>[w(C(e.point),1)])),_:2},1024),h(u,{class:"text-[24rpx] ml-[4rpx]"},{default:y((()=>[w("积分")])),_:1})])),_:2},1032,["style"]),e.price&&e.price>0?(g(),b(l,{key:0,class:"flex items-center mt-[6rpx] price-font"},{default:y((()=>[h(u,{style:_({color:k(n).priceStyle.mainColor}),class:"text-[#333] font-400 text-[32rpx]"},{default:y((()=>[w("+"+C(parseFloat(e.price).toFixed(2)),1)])),_:2},1032,["style"]),h(u,{style:_({color:k(n).priceStyle.mainColor}),class:"text-[var(--price-text-color)] font-400 ml-[4rpx] text-[24rpx]"},{default:y((()=>[w("元")])),_:1},8,["style"])])),_:2},1024)):S("v-if",!0)])),_:2},1024),h(l,{class:"w-[120rpx] h-[54rpx] text-[22rpx] flex-center !text-[#fff] m-0 rounded-full primary-btn-bg remove-border text-center",shape:"circle"},{default:y((()=>[w("去兑换")])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1})):r.value.length?S("v-if",!0):(g(),b(l,{key:1,class:"empty-page"},{default:y((()=>[h(i,{class:"img",src:k(d)("static/resource/images/system/empty.png"),model:"aspectFit"},null,8,["src"]),h(l,{class:"desc"},{default:y((()=>[w("暂无商品")])),_:1})])),_:1}))])),_:1},8,["style"])])),_:1},8,["type","loading","config"])}}}),[["__scopeId","data-v-69918a03"]]),vo=We(n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=i(),o=u(!0),a=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),r=s((()=>{var e="";return a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="backgroundPosition: bottom;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e}));c((()=>{m()})),f((()=>a.value),((e,t)=>{m()}),{deep:!0});const n=u({}),p=u(_e()),m=()=>{if("decorate"==l.mode)n.value={point:10500,today_point:500};else{if(!p)return;v()}},v=async()=>{const e=await bt();n.value=e.data,o.value=!1};return s((()=>"")),(e,t)=>{const l=$;return g(),b(l,{style:_(k(r)),class:"shop-exchange-info-wrap"},{default:y((()=>[h(l,{class:"pl-[60rpx] pt-[71rpx] pb-[133rpx] min-h-[382rpx] box-border text-[#fff]"},{default:y((()=>[p.value?(g(),R(B,{key:0},[h(l,{class:"text-[34rpx] leading-[48rpx]"},{default:y((()=>[w("我的积分")])),_:1}),h(l,{class:"text-[80rpx] font-500 price-font leading-[112rpx]"},{default:y((()=>[w(C(n.value.point||0),1)])),_:1}),h(l,{class:"text-[24rpx] font-400 leading-[34rpx]"},{default:y((()=>[w("今日获得："+C(n.value.today_point||0),1)])),_:1})],64)):(g(),R(B,{key:1},[h(l,{class:"pt-[42rpx] title"},{default:y((()=>[w("积分当钱花")])),_:1}),h(l,{class:"text-[26rpx] leading-[36rpx] text-[#FEF2C0] mt-[10rpx]"},{default:y((()=>[w("做任务可领积分")])),_:1})],64))])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-721ac61f"]]),xo=n({__name:"index",props:["component","index"],setup(e){const t=e,l=yt(),o=i(),a=s((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=s((()=>{var e="";return e+="position:relative;",a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e}));f((()=>a.value),((e,t)=>{n()}),{deep:!0});const n=()=>{if("decorate"==o.mode){if(a.value&&"ShopGoodsRanking"==a.value.componentName){const e={goods_name:"商品名称",goods_cover_thumb_mid:"",goodsSku:{show_price:10},rank_num:0};a.value.list.forEach((t=>{if(t.goodsList||(t.goodsList=[]),0===t.goodsList.length){const l=[];for(let t=0;t<3;t++){const o={...e,rank_num:t+1};l.push(o)}u[t.rankIds[0]]=l}}))}}else m()},u=j({}),p=j({}),m=()=>{a.value.list.forEach((e=>{const t=Array.isArray(e.rankIds)?e.rankIds[0]:0,l={rank_id:"custom"===e.source?t:0};ht(l).then((t=>{t.data&&t.data.goods_list&&t.data.goods_list.length>0&&(u[e.rankIds[0]]=t.data.goods_list,p[e.rankIds[0]]=t.data.name)})).catch((e=>{console.error("获取商品数据失败:",e)}))}))};function z(e){switch(e){case 1:return d("addon/shop/rank/rank_first.png");case 2:return d("addon/shop/rank/rank_second.png");case 3:return d("addon/shop/rank/rank_third.png");default:return d("addon/shop/rank/rank.png")}}return c((()=>{n()})),(e,t)=>{const n=E,i=D,s=$,c=v(x("u--image"),xt),f=I;return g(),b(s,{style:_(k(r)),class:"overflow-hidden"},{default:y((()=>[h(f,{"scroll-x":"true",class:"w-[100%] whitespace-nowrap"},{default:y((()=>[h(s,{class:"flex items-start"},{default:y((()=>[(g(!0),R(B,null,F(k(a).list,((e,t)=>(g(),b(s,{class:"inline-block",key:t},{default:y((()=>{return[u[e.rankIds[0]]&&u[e.rankIds[0]].length>0?(g(),b(s,{key:0,class:T(["w-[460rpx] px-[20rpx] pb-[20rpx] pt-[20rpx] flex flex-col items-start",{"mr-[20rpx]":k(a).list.length-1!=t}]),style:_((r=e,f="",r.listFrame.startColor&&(r.listFrame.startColor&&r.listFrame.endColor?f+="background: linear-gradient(90deg, "+r.listFrame.startColor+", "+r.listFrame.endColor+");":f="background-color:"+r.listFrame.startColor+";"),r.bgUrl&&(f+="background-image:url("+d(r.bgUrl)+");",f+="background-size: 100%;",f+="background-repeat: no-repeat;"),a.value.topElementRounded&&(f+="border-top-left-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.topElementRounded&&(f+="border-top-right-radius:"+2*a.value.topElementRounded+"rpx;"),a.value.bottomElementRounded&&(f+="border-bottom-left-radius:"+2*a.value.bottomElementRounded+"rpx;"),a.value.bottomElementRounded&&(f+="border-bottom-right-radius:"+2*a.value.bottomElementRounded+"rpx;"),f))},{default:y((()=>[h(s,{class:"flex items-center h-[50rpx]"},{default:y((()=>[e.imgUrl?(g(),b(n,{key:0,class:"w-[30rpx] h-[30rpx] mr-[10rpx]",src:k(d)(e.imgUrl),mode:"aspectFill"},null,8,["src"])):S("v-if",!0),h(s,{style:_({color:e.textColor})},{default:y((()=>[e.text?(g(),b(i,{key:0,class:"text-[30rpx] font-bold"},{default:y((()=>[w(C(e.text),1)])),_:2},1024)):(g(),b(i,{key:1,class:"text-[30rpx] font-bold"},{default:y((()=>[w(C(p[e.rankIds[0]]),1)])),_:2},1024))])),_:2},1032,["style"])])),_:2},1024),h(s,{class:"flex items-center mt-[6rpx]",style:_({color:e.subTitle.textColor}),onClick:t=>k(o).toRedirect(e.subTitle.link)},{default:y((()=>[e.subTitle.text?(g(),b(i,{key:0,class:"text-[24rpx] font-500"},{default:y((()=>[w(C(e.subTitle.text),1)])),_:2},1024)):S("v-if",!0),e.subTitle.text?(g(),b(i,{key:1,class:"iconfont iconyouV6xx !text-[24rpx]"})):S("v-if",!0)])),_:2},1032,["style","onClick"]),h(s,{class:"mt-[24rpx]"},{default:y((()=>[(g(!0),R(B,null,F(u[e.rankIds[0]],((t,o)=>(g(),b(s,{class:T(["flex bg-[rgba(255,255,255,0.94)] p-[10rpx] rounded-[16rpx] mb-[16rpx]",{"mb-0":o===u[e.rankIds[0]].length-1}]),onClick:e=>{return l=t.goods_id,void we({url:"/addon/shop/pages/goods/detail",param:{goods_id:l},mode:"navigateTo"});var l}},{default:y((()=>[h(s,{class:"relative w-[130rpx] h-[130rpx] mr-[16rpx]"},{default:y((()=>[h(n,{class:"absolute top-[6rpx] left-[8rpx] w-[30rpx] h-[36rpx]",style:{zIndex:2},src:z(t.rank_num),mode:"aspectFill"},null,8,["src"]),h(s,{class:"absolute top-[2rpx] left-[-3rpx] flex items-center justify-center w-[50rpx] h-[50rpx]",style:{zIndex:3}},{default:y((()=>[h(i,{class:"text-[20rpx] font-bold text-[#fff]"},{default:y((()=>[w(C(t.rank_num),1)])),_:2},1024)])),_:2},1024),h(c,{radius:"var(--goods-rounded-big)",width:"130rpx",height:"130rpx",src:k(d)(t.goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(n,{class:"w-[130rpx] h-[130rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),h(s,{class:"flex flex-col"},{default:y((()=>[h(s,{class:"leading-[1.3] multi-hidden w-[290rpx] text-[28rpx] whitespace-normal"},{default:y((()=>[w(C(t.goods_name),1)])),_:2},1024),h(s,{class:"flex items-center justify-between mt-[auto]"},{default:y((()=>[h(s,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:y((()=>[h(i,{class:"text-[24rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(i,{class:"text-[40rpx] font-500"},{default:y((()=>[w(C(k(l).goodsPrice(t).toFixed(2).split(".")[0]),1)])),_:2},1024),h(i,{class:"text-[24rpx] font-500"},{default:y((()=>[w("."+C(k(l).goodsPrice(t).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),h(s,null,{default:y((()=>[h(i,{class:"iconfont icongouwuche3 text-[var(--primary-color)] border-[2rpx] border-solid border-[var(--primary-color)] rounded-[50%] text-[22rpx] p-[6rpx]"})])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),256))])),_:2},1024)])),_:2},1032,["class","style"])):S("v-if",!0)];var r,f})),_:2},1024)))),128))])),_:1})])),_:1})])),_:1},8,["style"])}}}),go=n({__name:"index",props:["component","index","value"],emits:["loadingFn"],setup(e,{emit:t}){const l=e,o=yt(),a=i(),r=u(0),n=j({type:"",loading:!1,config:{}}),m=u([]),z=s((()=>l.value?l.value:"decorate"==a.mode?a.value[l.index]:l.component)),M=s((()=>{var e="";return e+="position:relative;",z.value.componentStartBgColor&&(z.value.componentStartBgColor&&z.value.componentEndBgColor?e+=`background:linear-gradient(${z.value.componentGradientAngle},${z.value.componentStartBgColor},${z.value.componentEndBgColor});`:e+="background-color:"+z.value.componentStartBgColor+";"),z.value.topRounded&&(e+="border-top-left-radius:"+2*z.value.topRounded+"rpx;"),z.value.topRounded&&(e+="border-top-right-radius:"+2*z.value.topRounded+"rpx;"),z.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*z.value.bottomRounded+"rpx;"),z.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*z.value.bottomRounded+"rpx;"),e})),A=s((()=>{var e={val:"",style:""};return z.value.imgElementRounded&&(e.val=2*z.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*z.value.imgElementRounded+"rpx;"),e})),N=s((()=>{var e="";return z.value.elementBgColor&&(e+="background-color:"+z.value.elementBgColor+";"),z.value.topElementRounded&&(e+="border-top-left-radius:"+2*z.value.topElementRounded+"rpx;"),z.value.topElementRounded&&(e+="border-top-right-radius:"+2*z.value.topElementRounded+"rpx;"),z.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*z.value.bottomElementRounded+"rpx;"),z.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*z.value.bottomElementRounded+"rpx;"),e})),W=e=>{var t="";return e.listFrame.startColor&&(t=e.listFrame.startColor&&e.listFrame.endColor?`background:linear-gradient( 110deg, ${e.listFrame.startColor} 0%, ${e.listFrame.endColor} 100%);`:"background-color:"+e.listFrame.startColor+";"),t},V=e=>{var t="";return e.moreTitle.startColor&&(t=e.moreTitle.startColor&&e.moreTitle.endColor?`background:linear-gradient( 0deg, ${e.moreTitle.startColor} 0%, ${e.moreTitle.endColor} 100%);`:"background-color:"+e.moreTitle.startColor+";"),t},Y=u(""),H=()=>{z.value.margin&&z.value.margin.both?Y.value="width: calc((100vw - "+4*z.value.margin.both+"rpx - 40rpx) / 3);":Y.value="width: calc((100vw - 40rpx) / 3 );"};H();c((()=>{U()})),f((()=>z.value),((e,t)=>{U()}));const U=()=>{"decorate"==a.mode?p((()=>{z.value&&z.value.list&&(m.value=z.value.list.map((e=>{let t=he(e);return t.info={goods_cover_thumb_mid:"",goodsSku:{show_price:"10.00"}},t})),r.value=3,H())})):(()=>{let e={};"all"==z.value.source?e.num=z.value.list.length:"custom"==z.value.source&&(e.goods_ids=z.value.goods_ids),vt(e).then((e=>{let t=e.data;r.value=t.length||0,z.value.list.filter(((e,l)=>{e.info=he(t[l])})),m.value=he(z.value.list),n.loading=!1}))})()};return(e,t)=>{const l=$,a=E,i=v(x("u--image"),xt),s=D,u=I,c=v(x("x-skeleton"),dt);return g(),b(c,{type:n.type,loading:n.loading,config:n.config},{default:y((()=>[r.value?(g(),b(l,{key:0,style:_(k(M))},{default:y((()=>[h(l,{class:"w-full"},{default:y((()=>[h(u,{id:"warpStyle-"+k(z).id,class:"whitespace-nowrap h-[341rpx] w-full","scroll-x":!0},{default:y((()=>[(g(!0),R(B,null,F(m.value,((e,t)=>(g(),R(B,{key:t},[e.info?(g(),b(l,{key:0,id:"item"+t+k(z).id,class:T(["w-[224rpx] h-[341rpx] mr-[20rpx] inline-block bg-[#fff] box-border overflow-hidden",{"!mr-[0rpx]":t==m.value.length-1}]),style:_(k(N)+Y.value),onClick:t=>{we({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.info.goods_id}})}},{default:y((()=>[h(l,{class:"w-full h-[134rpx]",style:_(W(e))},{default:y((()=>[h(l,{class:"flex pl-[16rpx] pr-[20rpx] justify-between h-[63rpx] items-center"},{default:y((()=>[h(l,{class:"text-[28rpx] leading-[34rpx] flex-1 mr-[8rpx]",style:_({color:e.title.textColor})},{default:y((()=>[w(C(e.title.text),1)])),_:2},1032,["style"]),h(l,{class:"w-[68rpx] h-[34rpx] text-[22rpx] text-center leading-[34rpx] text-[#fff] rounded-[17rpx]",style:_(V(e))},{default:y((()=>[w(C(e.moreTitle.text),1)])),_:2},1032,["style"])])),_:2},1024)])),_:2},1032,["style"]),h(l,{class:"mt-[-71rpx] h-[278rpx] w-full px-[20rpx] pt-[18rpx] box-border bg-white",style:_(k(N))},{default:y((()=>[h(l,{class:"flex items-center justify-center w-[184rpx] h-[184rpx]"},{default:y((()=>[h(i,{width:"184rpx",height:"184rpx",radius:k(A).val,src:k(d)(e.info.goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(a,{class:"w-[184rpx] h-[184rpx]",style:_(k(A).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1024),h(l,{class:"pt-[12rpx]"},{default:y((()=>[h(l,{class:"h-[44rpx] pl-[4rpx] min-w-[168rpx] box-border flex justify-between items-center mx-auto border-[2rpx] border-solid border-[var(--primary-color)] rounded-[20rpx]",style:_({"border-color":e.button.color})},{default:y((()=>[h(l,{class:"text-[var(--price-text-color)] font-bold price-font flex items-baseline leading-[40rpx] flex-1 justify-center"},{default:y((()=>[h(l,{class:"leading-[1] max-w-[105rpx] truncate",style:_({color:k(z).priceStyle.mainColor})},{default:y((()=>[h(s,{class:"text-[18rpx] font-400 mr-[2rpx]"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[28rpx] font-500"},{default:y((()=>[w(C(parseFloat(k(o).goodsPrice(e.info)).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"])])),_:2},1024),h(l,{class:"w-[70rpx] box-border text-right text-[#fff] pr-[8rpx] text-[22rpx] font-500 leading-[44rpx] rounded-tr-[20rpx] rounded-br-[20rpx] rounded-tl-[24rpx] relative",style:_({"background-color":e.button.color})},{default:y((()=>[h(s,null,{default:y((()=>[w(C(e.button.text),1)])),_:2},1024),h(a,{class:"w-[24rpx] h-[44rpx] absolute top-[-2rpx] left-0",src:k(d)("/addon/shop/Union.png")},null,8,["src"])])),_:2},1032,["style"])])),_:2},1032,["style"])])),_:2},1024)])),_:2},1032,["style"])])),_:2},1032,["id","class","style","onClick"])):S("v-if",!0)],64)))),128))])),_:1},8,["id"])])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["type","loading","config"])}}}),bo=n({__name:"index",props:["component","index","global"],setup(e){const t=e,l=Re(),o=i(),a=s((()=>"decorate"==o.mode?o.value[t.index]:t.component)),r=s((()=>{var e="";return a.value.componentStartBgColor&&(a.value.componentStartBgColor&&a.value.componentEndBgColor?e+=`background:linear-gradient(${a.value.componentGradientAngle},${a.value.componentStartBgColor},${a.value.componentEndBgColor});`:e+="background-color:"+a.value.componentStartBgColor+";"),a.value.bgUrl&&(e+="background-image:url("+d(a.value.bgUrl)+");",e+="background-size: 100%;",e+="background-repeat: no-repeat;"),a.value.topRounded&&(e+="border-top-left-radius:"+2*a.value.topRounded+"rpx;"),a.value.topRounded&&(e+="border-top-right-radius:"+2*a.value.topRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*a.value.bottomRounded+"rpx;"),a.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*a.value.bottomRounded+"rpx;"),e})),n=Se(),{query:c}=Be(location.href);c.code&&Fe()&&setTimeout((()=>{Te({code:c.code}).then((e=>{n.getMemberInfo()}))}),1500);const f=s((()=>{if("decorate"==o.mode)return{headimg:"",nickname:"昵称",member_level_name:"普通会员",balance:0,point:0,money:0,mobile:"155****0549",member_no:"NIU0000021"};F();return n?n.info:null})),p=s((()=>{if(f.value){let e=parseFloat(f.value.balance)+parseFloat(f.value.money);return ze(e.toString())}return 0})),m=()=>{let e=!l.login.is_username&&!l.login.is_mobile&&!l.login.is_bind_mobile,t=!l.login.is_auth_register;Fe()?e&&t?ie({title:"商家未开启登录注册",icon:"none"}):l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile?ke().setLoginBack({url:"/addon/shop/pages/member/index"}):e&&l.login.is_auth_register&&l.login.is_force_access_user_info?ke().getAuthCode({scopes:"snsapi_userinfo"}):e&&l.login.is_auth_register&&!l.login.is_force_access_user_info&&ke().getAuthCode({scopes:"snsapi_base"}):e?ie({title:"商家未开启登录注册",icon:"none"}):(l.login.is_username||l.login.is_mobile||l.login.is_bind_mobile)&&ke().setLoginBack({url:"/addon/shop/pages/member/index"})};u(!1);const R=()=>{Fe()?ke().getAuthCode({scopes:"snsapi_userinfo"}):we({url:"/app/pages/member/personal"})},B=u(0),F=async()=>{try{const e=await pt({status:1});B.value=e.data}catch(e){B.value=0}},z=u(null),E=()=>{z.value.open()};return s((()=>"")),(e,t)=>{const l=v(x("u-avatar"),tt),o=$,n=D;return g(),b(o,{style:_(k(r))},{default:y((()=>[h(o,{class:T(["px-[30rpx] pt-[30rpx] box-border pb-[30rpx]",{"!pb-[120rpx]":k(a).isShowAccount}])},{default:y((()=>[k(f)?(g(),b(o,{key:0,class:"flex items-center"},{default:y((()=>[S(" 唤起获取微信 "),h(l,{src:k(d)(k(f).headimg),size:"110rpx",leftIcon:"none","default-url":k(d)("static/resource/images/default_headimg.png"),onClick:R},null,8,["src","default-url"]),h(o,{class:"ml-[20rpx] flex-1"},{default:y((()=>[h(o,{class:"text-[#ffffff] flex items-baseline flex-wrap",style:_({color:k(a).textColor})},{default:y((()=>[h(o,{class:"text-[32rpx] truncate max-w-[320rpx] font-500 leading-[38rpx]"},{default:y((()=>[w(C(k(f).nickname),1)])),_:1}),k(f).mobile?(g(),b(o,{key:0,class:"text-[26rpx] font-400 leading-[28rpx] ml-[10rpx]"},{default:y((()=>[w(C(k(f).mobile.replace(k(f).mobile.substring(3,7),"****")),1)])),_:1})):k(f).mobile?S("v-if",!0):(g(),b(o,{key:1,onClick:E,class:"text-[22rpx] ml-[10rpx] px-[6rpx] border-[1rpx] border-solid border-[#E3E4E9] rounded-[8rpx] h-[34rpx] flex-center",style:_(k(a).textColor?{boxShadow:"0 0 0 1rpx "+k(a).textColor,border:"none"}:{})},{default:y((()=>[w(C(k(P)("bindMobile")),1)])),_:1},8,["style"]))])),_:1},8,["style"]),h(o,{class:"text-[#666] text-[24rpx] font-400 leading-[28rpx] mt-[14rpx]",style:_({color:k(a).uidTextColor})},{default:y((()=>[w("UID："+C(k(f).member_no),1)])),_:1},8,["style"])])),_:1}),h(n,{onClick:t[0]||(t[0]=e=>k(we)({url:"/app/pages/setting/index"})),class:"nc-iconfont nc-icon-shezhiV6xx1 text-[38rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})):(g(),b(o,{key:1,class:"flex items-center"},{default:y((()=>[h(l,{src:k(d)("static/resource/images/default_headimg.png"),size:"100rpx",onClick:m},null,8,["src"]),h(o,{class:"ml-[20rpx] flex-1",onClick:m},{default:y((()=>[h(o,{class:"text-[32rpx] font-500 leading-[38rpx]",style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(P)("login"))+"/"+C(k(P)("register")),1)])),_:1},8,["style"])])),_:1}),h(o,{onClick:t[1]||(t[1]=e=>k(we)({url:"/app/pages/setting/index"}))},{default:y((()=>[h(n,{class:"nc-iconfont nc-icon-shezhiV6xx1 text-[38rpx] ml-[10rpx]",style:_({color:k(a).textColor})},null,8,["style"])])),_:1})])),_:1})),k(a).isShowAccount?(g(),b(o,{key:2,class:"flex mt-[40rpx] items-center"},{default:y((()=>[h(o,{class:"text-center w-[33.333%] flex-shrink-0"},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{onClick:t[2]||(t[2]=e=>k(we)({url:"/app/pages/member/balance"})),style:_({color:k(a).textColor})},{default:y((()=>[w(C(k(p)),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{onClick:t[3]||(t[3]=e=>k(we)({url:"/app/pages/member/balance"})),style:_({color:k(a).accountTextColor})},{default:y((()=>[w(C(k(P)("balance")),1)])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"text-center w-[33.333%] flex-shrink-0"},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{onClick:t[4]||(t[4]=e=>k(we)({url:"/app/pages/member/point"})),style:_({color:k(a).textColor})},{default:y((()=>{var e;return[w(C(parseInt(null==(e=k(f))?void 0:e.point)||0),1)]})),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{onClick:t[5]||(t[5]=e=>k(we)({url:"/app/pages/member/point"})),style:_({color:k(a).accountTextColor})},{default:y((()=>[w(C(k(P)("point")),1)])),_:1},8,["style"])])),_:1})])),_:1}),h(o,{class:"text-center w-[33.333%] flex-shrink-0",onClick:t[6]||(t[6]=e=>k(we)({url:"/addon/shop/pages/member/my_coupon"}))},{default:y((()=>[h(o,{class:"text-[36rpx] mb-[20rpx] font-500 price-font"},{default:y((()=>[h(o,{style:_({color:k(a).textColor})},{default:y((()=>[w(C(B.value),1)])),_:1},8,["style"])])),_:1}),h(o,{class:"text-[22rpx] font-400"},{default:y((()=>[h(o,{style:_({color:k(a).accountTextColor})},{default:y((()=>[w(C(k(P)("coupon")),1)])),_:1},8,["style"])])),_:1})])),_:1})])),_:1})):S("v-if",!0)])),_:1},8,["class"]),S(" 强制绑定手机号 "),h(_t,{ref_key:"bindMobileRef",ref:z},null,512)])),_:1},8,["style"])}}}),yo=We(n({__name:"index",props:["component","index","value"],setup(e){const t=e,l=i(),o=s((()=>t.value?t.value:"decorate"==l.mode?l.value[t.index]:t.component)),a=u({}),r=e=>{a.value=e},n=u(""),f=e=>{let t=0;return e&&e.newcomer_price&&(t=Number(e.newcomer_price).toFixed(2)),t},p=()=>{let e=24*a.value.days+a.value.hours;return e=e||0,e=e>=10?e:"0"+e,e},m=()=>{let e=!0;return"decorate"!=l.mode&&a.value.days<=0&&a.value.hours<=0&&a.value.minutes<=0&&a.value.seconds<=0&&a.value.milliseconds<=0&&(e=!1),e},z=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),M=s((()=>{var e={val:"",style:""};return o.value.imgElementRounded&&(e.val=2*o.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*o.value.imgElementRounded+"rpx;"),e})),A=s((()=>{var e="";return o.value.countDown&&o.value.countDown.numberBg&&(o.value.countDown.numberBg.startColor&&o.value.countDown.numberBg.endColor?e+=`background:linear-gradient(${o.value.countDown.numberBg.startColor},${o.value.countDown.numberBg.endColor});`:e+="background-color:"+(o.value.countDown.numberBg.startColor||o.value.countDown.numberBg.endColor)+";"),o.value.countDown.numberColor&&(e+="color:"+o.value.countDown.numberColor+";"),e})),j=()=>{var e="";return o.value.topElementRounded&&(e+="border-top-left-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.topElementRounded&&(e+="border-top-right-radius:"+2*o.value.topElementRounded+"rpx;"),o.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomElementRounded+"rpx;"),o.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomElementRounded+"rpx;"),e},W=s((()=>{var e="";return o.value.subTitle&&(o.value.subTitle.startColor&&o.value.subTitle.endColor?e+=`background:linear-gradient(to right, ${o.value.subTitle.startColor},${o.value.subTitle.endColor});`:e+="background-color:"+(o.value.subTitle.startColor||o.value.subTitle.endColor)+";"),o.value.subTitle.textColor&&(e+="color:"+o.value.subTitle.textColor+";"),e})),V=u([]);c((()=>{if("decorate"==l.mode){let e={goods:{goods_name:"商品名称"},sku_image:"",newcomer_price:.01};V.value.push(e),V.value.push(e),V.value.push(e)}else(()=>{let e={limit:"all"==o.value.source?o.value.num:"",sku_ids:"custom"==o.value.source?o.value.goods_ids:""};kt(e).then((e=>{n.value=e.data.validity_time;let t=(new Date).getTime();n.value=1e3*Number(n.value)-t,V.value=e.data.goods_list}))})()}));const Y=()=>{we({url:"/addon/shop/pages/newcomer/list"})},H=e=>{we({url:"/addon/shop/pages/goods/detail",param:{sku_id:e.sku_id,type:"newcomer_discount"}})};return(e,t)=>{const i=E,s=D,u=$,c=v(x("up-count-down"),wt),U=v(x("u--image"),xt),P=I;return V.value&&Object.keys(V.value).length?(g(),b(u,{key:0,class:"shop-newcomer overflow-hidden",style:_(k(z))},{default:y((()=>["style-1"==k(o).style.value?(g(),b(u,{key:0,class:"style-1 p-[20rpx]"},{default:y((()=>[h(u,{class:"head flex justify-between items-center mb-[16rpx]",onClick:t[0]||(t[0]=e=>Y())},{default:y((()=>[k(o).textImg?(g(),b(i,{key:0,class:"h-[34rpx] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),Ie(h(u,{class:"time-wrap flex items-center ml-[auto]"},{default:y((()=>[k(_e)()||"decorate"==k(l).mode?m()?(g(),R(B,{key:1},[h(s,{style:_({color:k(o).countDown.otherColor}),class:"mr-[10rpx] text-[24rpx]"},{default:y((()=>[w("距结束还有")])),_:1},8,["style"]),h(c,{class:"text-[#fff] text-[28rpx]",time:n.value,format:"HH:mm:ss",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[p()?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(p()),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.minutes?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.seconds?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.seconds<10?"0"+a.value.seconds:a.value.seconds),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"]))])),_:1})])),_:1})])),_:1},8,["time"])],64)):(g(),b(s,{key:2,class:"text-[28rpx]",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动已结束 ")])),_:1},8,["style"])):(g(),b(s,{key:0,class:"text-[24rpx] font-500",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Me,a.value&&Object.keys(a.value).length]])])),_:1}),h(P,{"scroll-x":"true",class:"content"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"inline-flex bg-[#fff] p-[16rpx] box-border",style:_(j()),onClick:t[1]||(t[1]=e=>H(V.value[0]))},{default:y((()=>[h(u,{class:"w-[150rpx] h-[150rpx] flex items-center justify-center"},{default:y((()=>[h(U,{radius:k(M).val,width:"150rpx",height:"150rpx",src:k(d)(V.value[0].sku_image||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"w-[150rpx] h-[150rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:1},8,["radius","src"])])),_:1}),h(u,{class:"flex flex-col ml-[20rpx] py-[4rpx] flex-1"},{default:y((()=>[V.value[0].goods?(g(),b(u,{key:0,class:"text-[26rpx] w-[200rpx] whitespace-pre-wrap leading-[1.4] multi-hidden"},{default:y((()=>[w(C(V.value[0].goods.goods_name),1)])),_:1})):S("v-if",!0),h(u,{class:"flex items-center justify-between mt-[auto]"},{default:y((()=>[h(u,{class:"flex flex-1 items-center"},{default:y((()=>[h(s,{class:"text-[20rpx] text-[#FF0000]"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[28rpx] font-500 text-[#FF0000] max-w[120rpx] truncate"},{default:y((()=>[w(C(f(V.value[0])),1)])),_:1})])),_:1}),h(s,{class:"italic flex items-center justify-center rounded-[40rpx] w-[60rpx] h-[40rpx] leading-1 text-[#fff] font-bold first-btn-bg"},{default:y((()=>[w("抢")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]),(g(!0),R(B,null,F(V.value,((e,t)=>(g(),R(B,{key:t},[t>0?(g(),b(u,{key:0,class:"ml-[10rpx] inline-flex flex-col items-center p-[16rpx] bg-[#fff] box-border",style:_(j()),onClick:t=>H(e)},{default:y((()=>[h(u,{class:"w-[110rpx] h-[110rpx] flex items-center justify-center"},{default:y((()=>[h(U,{radius:k(M).val,width:"110rpx",height:"110rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"w-[110rpx] h-[110rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1024),h(u,{class:"flex items-center mt-[auto]"},{default:y((()=>[h(s,{class:"text-[24rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[24rpx] font-500 truncate"},{default:y((()=>[w(C(f(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style","onClick"])):S("v-if",!0)],64)))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-2"==k(o).style.value?(g(),b(u,{key:1,class:"style-2 p-[20rpx]"},{default:y((()=>[h(u,{class:"head flex justify-between items-center mb-[16rpx]",onClick:t[2]||(t[2]=e=>Y())},{default:y((()=>[k(o).textImg?(g(),b(i,{key:0,class:"h-[34rpx] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),Ie(h(u,{class:"time-wrap flex items-center ml-[auto]"},{default:y((()=>[k(_e)()||"decorate"==k(l).mode?m()?(g(),R(B,{key:1},[h(s,{style:_({color:k(o).countDown.otherColor}),class:"mr-[10rpx] text-[24rpx]"},{default:y((()=>[w("倒计时")])),_:1},8,["style"]),h(c,{class:"text-[#fff] text-[28rpx]",time:n.value,format:"DD:HH:mm",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.days?(g(),b(s,{key:0,class:"time-num",style:_(k(A))},{default:y((()=>[w(C(a.value.days),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num",style:_(k(A))},{default:y((()=>[w("0")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[22rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w("天")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.hours?(g(),b(s,{key:0,class:"time-num",style:_(k(A))},{default:y((()=>[w(C(a.value.hours>=10?a.value.hours:"0"+a.value.hours),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[22rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w("时")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.minutes?(g(),b(s,{key:0,class:"time-num",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[22rpx] ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w("分")])),_:1},8,["style"])])),_:1})])),_:1})])),_:1},8,["time"])],64)):(g(),b(s,{key:2,class:"text-[28rpx]",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动已结束 ")])),_:1},8,["style"])):(g(),b(s,{key:0,class:"text-[24rpx] font-500",style:_({color:k(o).countDown.otherColor})},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Me,a.value&&Object.keys(a.value).length]])])),_:1}),h(P,{"scroll-x":"true",class:"content"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[(g(!0),R(B,null,F(V.value,((e,t)=>(g(),b(u,{key:t,class:"item-bg mr-[10rpx] inline-flex flex-col items-center p-[6rpx] bg-[#fff] box-border",style:_(j()),onClick:t=>H(e)},{default:y((()=>[h(u,{class:"flex items-center justify-center w-[146rpx] h-[146rpx]"},{default:y((()=>[h(U,{radius:k(M).val,width:"146rpx",height:"146rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"w-[146rpx] h-[146rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1024),h(i,{class:"h-[32rpx] w-[auto] mt-[12rpx] mb-[8rpx]",src:k(d)("addon/shop/diy/newcomer/style_2_img.png"),mode:"heightFix"},null,8,["src"]),h(u,{class:"flex items-center text-[#fff] pb-[4rpx]"},{default:y((()=>[h(s,{class:"text-[20rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[30rpx] max-w-[120rpx] font-500 truncate"},{default:y((()=>[w(C(f(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["style","onClick"])))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-3"==k(o).style.value?(g(),b(u,{key:2,class:"style-3 pt-[20rpx] pb-[10rpx] px-[10rpx]"},{default:y((()=>[h(u,{class:"head flex mx-[10rpx] items-center mb-[12rpx]",onClick:t[4]||(t[4]=e=>Y())},{default:y((()=>[k(o).textImg?(g(),b(i,{key:0,class:"h-[34rpx] w-[auto] mr-[16rpx]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),Ie(h(u,{class:"time-wrap flex items-center"},{default:y((()=>[k(_e)()||"decorate"==k(l).mode?m()?(g(),b(c,{key:1,class:"text-[#fff] text-[28rpx]",time:n.value,format:"HH:mm:ss",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[p()?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(p()),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] font-bold ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.minutes?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] font-bold ml-[6rpx] mr-[7rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[24rpx] flex items-center"},{default:y((()=>[a.value.seconds?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.seconds<10?"0"+a.value.seconds:a.value.seconds),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"]))])),_:1})])),_:1})])),_:1},8,["time"])):(g(),b(s,{key:2,style:_({color:k(o).countDown.otherColor}),class:"text-[26rpx]"},{default:y((()=>[w("活动已结束 ")])),_:1},8,["style"])):(g(),b(s,{key:0,style:_({color:k(o).countDown.otherColor}),class:"text-[24rpx] font-500"},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Me,a.value&&Object.keys(a.value).length]]),h(u,{class:"ml-[auto] rounded-[20rpx] flex items-baseline pl-[16rpx] pr-[10rpx] pt-[10rpx] pb-[10rpx]",style:_(k(W)),onClick:t[3]||(t[3]=N((e=>k(l).toRedirect(k(o).subTitle.link)),["stop"]))},{default:y((()=>[h(s,{class:"text-[22rpx]"},{default:y((()=>[w(C(k(o).subTitle.text),1)])),_:1}),h(s,{class:"iconfont iconarrow-right !text-[18rpx] font-bold"})])),_:1},8,["style"])])),_:1}),h(P,{"scroll-x":"true",class:"content bg-[#fff] box-border p-[16rpx] rounded-[var(--rounded-small)]"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[(g(!0),R(B,null,F(V.value,((e,t)=>(g(),b(u,{key:t,class:T(["item-bg inline-flex flex-col items-center box-border",{"mr-[16rpx]":t!=V.value.length-1}]),style:_(j()),onClick:t=>H(e)},{default:y((()=>[h(u,{class:"bg-[#f8f8f8] flex items-center justify-center w-[152rpx] h-[152rpx] overflow-hidden",style:_(k(M).style)},{default:y((()=>[h(U,{radius:k(M).val,width:"152rpx",height:"152rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"w-[152rpx] h-[152rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"])])),_:2},1032,["style"]),h(i,{class:"h-[32rpx] w-[auto] mt-[12rpx] mb-[10rpx]",src:k(d)("addon/shop/diy/newcomer/style_3_img.png"),mode:"heightFix"},null,8,["src"]),h(u,{class:"flex items-center text-[#FF0E00] pb-[2rpx]"},{default:y((()=>[h(s,{class:"text-[20rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[30rpx] max-w-[120rpx] font-500 truncate"},{default:y((()=>[w(C(f(e)),1)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1})])),_:1})])),_:1})):S("v-if",!0),"style-4"==k(o).style.value?(g(),b(u,{key:3,class:"style-4 p-[20rpx] pt-[24rpx]",style:_({background:"url("+k(d)("addon/shop/diy/newcomer/style_4_head.png")+") no-repeat"})},{default:y((()=>[h(u,{class:"head flex mx-[10rpx] items-center justify-between mb-[24rpx]"},{default:y((()=>[k(o).textImg?(g(),b(i,{key:0,class:"h-[34rpx] w-[auto]",src:k(d)(k(o).textImg),mode:"heightFix"},null,8,["src"])):S("v-if",!0),Ie(h(u,{class:"time-wrap ml-[auto] flex items-center -mt-[8rpx]"},{default:y((()=>[k(_e)()||"decorate"==k(l).mode?m()?(g(),R(B,{key:1},[h(s,{style:_({color:k(o).countDown.otherColor}),class:"mr-[8rpx] text-[24rpx]"},{default:y((()=>[w("本场仅剩")])),_:1},8,["style"]),h(c,{class:"text-[#fff] text-[28rpx]",time:n.value,format:"HH:mm:ss",onChange:r},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[h(u,{class:"text-[28rpx] flex items-center"},{default:y((()=>[p()?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(p()),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[4rpx] font-bold mr-[5rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[28rpx] flex items-center"},{default:y((()=>[a.value.minutes?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.minutes>=10?a.value.minutes:"0"+a.value.minutes),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"])),h(s,{style:_({color:k(o).countDown.otherColor}),class:"text-[20rpx] ml-[4rpx] font-bold mr-[5rpx]"},{default:y((()=>[w(":")])),_:1},8,["style"])])),_:1}),h(u,{class:"text-[28rpx] flex items-center"},{default:y((()=>[a.value.seconds?(g(),b(s,{key:0,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w(C(a.value.seconds<10?"0"+a.value.seconds:a.value.seconds),1)])),_:1},8,["style"])):(g(),b(s,{key:1,class:"time-num font-500",style:_(k(A))},{default:y((()=>[w("00")])),_:1},8,["style"]))])),_:1})])),_:1})])),_:1},8,["time"])],64)):(g(),b(s,{key:2,style:_({color:k(o).countDown.otherColor}),class:"w-[200rpx] text-center text-[24rpx] pb-[4rpx]"},{default:y((()=>[w("活动已结束")])),_:1},8,["style"])):(g(),b(s,{key:0,style:_({color:k(o).countDown.otherColor}),class:"w-[200rpx] text-center text-[24rpx] font-500 pb-[4rpx]"},{default:y((()=>[w("活动未开始")])),_:1},8,["style"]))])),_:1},512),[[Me,a.value&&Object.keys(a.value).length]])])),_:1}),h(P,{"scroll-x":"true",class:"content"},{default:y((()=>[h(u,{class:"flex"},{default:y((()=>[(g(!0),R(B,null,F(V.value,((e,t)=>(g(),b(u,{key:t,class:T(["item-bg inline-flex flex-col items-center box-border",{"mr-[20rpx]":t!=V.value.length-1}]),style:_(j()),onClick:t=>H(e)},{default:y((()=>[h(u,{class:"relative flex items-center justify-center w-[100%] h-[130rpx] pt-[40rpx] mb-[10rpx]"},{default:y((()=>[h(U,{radius:k(M).val,width:"130rpx",height:"130rpx",src:k(d)(e.sku_image||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"w-[130rpx] h-[130rpx] overflow-hidden",style:_(k(M).style),src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"]),h(u,{class:"content-sign text-[20rpx] text-[#fff]"},{default:y((()=>[w("新人价")])),_:1})])),_:2},1024),h(u,{class:"w-[210rpx] relative -right-[2rpx] -bottom-[2rpx] flex items-center text-[#FF0E00] pb-[2rpx]"},{default:y((()=>[h(u,{class:"flex items-center justify-center flex-1"},{default:y((()=>[h(s,{class:"text-[20rpx] font-500"},{default:y((()=>[w("￥")])),_:1}),h(s,{class:"text-[36rpx] max-w-[140rpx] font-500 truncate"},{default:y((()=>[w(C(f(e)),1)])),_:2},1024)])),_:2},1024),h(s,{class:"btn-bg ml-auto"},{default:y((()=>[w("抢")])),_:1})])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["style"])):S("v-if",!0)])),_:1},8,["style"])):S("v-if",!0)}}}),[["__scopeId","data-v-bdf86967"]]),ho=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component));c((()=>{a()})),f((()=>o.value),((e,t)=>{a()}),{deep:!0});const a=()=>{"decorate"==l.mode?r.value={}:p()},r=u({}),n=s((()=>{var e="";return e+="position:relative;",o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),p=()=>{Ct().then((e=>{r.value=e.data}))},m=e=>{we({url:"/addon/shop/pages/order/list",param:{status:e}})};return(e,t)=>{const a=$,i=D,s=E;return g(),b(a,{style:_(k(n))},{default:y((()=>[h(a,{class:"diy-text relative"},{default:y((()=>[h(a,{class:"px-[var(--pad-sidebar-m)] pt-[var(--pad-top-m)] pb-[40rpx] flex items-center justify-between"},{default:y((()=>[h(a,{onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).link))},{default:y((()=>[h(a,{class:"max-w-[200rpx] truncate leading-[1] text-[30rpx]",style:_({fontSize:2*k(o).fontSize+"rpx",color:k(o).textColor,fontWeight:"normal"==k(o).fontWeight?500:k(o).fontWeight})},{default:y((()=>[w(C(k(o).text),1)])),_:1},8,["style"])])),_:1}),h(a,{class:"flex items-center"},{default:y((()=>[h(a,{onClick:t[1]||(t[1]=e=>k(we)({url:"/addon/shop/pages/order/list"})),class:"flex items-center"},{default:y((()=>[h(i,{class:"max-w-[200rpx] truncate text-[24rpx]",style:_({color:k(o).more.color})},{default:y((()=>[w(C(k(o).more.text),1)])),_:1},8,["style"]),h(i,{class:"nc-iconfont nc-icon-youV6xx text-[24rpx]",style:_({color:k(o).more.color})},null,8,["style"])])),_:1})])),_:1})])),_:1})])),_:1}),h(a,{class:"pb-[var(--pad-top-m)] px-[var(--pad-sidebar-m)] flex items-center justify-between text-center"},{default:y((()=>[h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[2]||(t[2]=e=>m(1))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(s,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order1.png")},null,8,["src"]),r.value.wait_pay?(g(),b(a,{key:0,class:T(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.wait_pay>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.wait_pay>99?"99+":r.value.wait_pay),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待付款")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[3]||(t[3]=e=>m(2))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(s,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order2.png")},null,8,["src"]),r.value.wait_shipping?(g(),b(a,{key:0,class:T(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.wait_shipping>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.wait_shipping>99?"99+":r.value.wait_shipping),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待发货")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[4]||(t[4]=e=>m(3))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(s,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order3.png")},null,8,["src"]),r.value.wait_take?(g(),b(a,{key:0,class:T(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.wait_take>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.wait_take>99?"99+":r.value.wait_take),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待收货")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[5]||(t[5]=e=>m(5))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(s,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order4.png")},null,8,["src"]),r.value.evaluate?(g(),b(a,{key:0,class:T(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.evaluate>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.evaluate>99?"99+":r.value.evaluate),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("待评价")])),_:1},8,["style"])])),_:1}),h(a,{class:"flex flex-col items-center w-[20%] flex-shrink-0",onClick:t[6]||(t[6]=e=>k(we)({url:"/addon/shop/pages/refund/list"}))},{default:y((()=>[h(a,{class:"relative w-[44rpx] h-[44rpx]"},{default:y((()=>[h(s,{class:"w-[44rpx] h-[44rpx]",src:k(d)("addon/shop/diy/member/order5.png")},null,8,["src"]),r.value.refund?(g(),b(a,{key:0,class:T(["absolute left-[35rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[30rpx] bg-[#FF4646] text-[#fff] text-[20rpx] font-500 box-border",r.value.refund>9?"px-[10rpx]":""])},{default:y((()=>[w(C(r.value.refund>99?"99+":r.value.refund),1)])),_:1},8,["class"])):S("v-if",!0)])),_:1}),h(a,{class:"mt-[20rpx] leading-[1]",style:_({fontSize:2*k(o).item.fontSize+"rpx",color:k(o).item.color,fontWeight:k(o).item.fontWeight})},{default:y((()=>[w("售后/退款")])),_:1},8,["style"])])),_:1})])),_:1})])),_:1},8,["style"])}}}),_o=n({__name:"index",props:["component","index"],setup(e){const t=e,l=i(),o=s((()=>"decorate"==l.mode?l.value[t.index]:t.component)),a=s((()=>{var e="";return o.value.componentStartBgColor&&(o.value.componentStartBgColor&&o.value.componentEndBgColor?e+=`background:linear-gradient(${o.value.componentGradientAngle},${o.value.componentStartBgColor},${o.value.componentEndBgColor});`:e+="background-color:"+o.value.componentStartBgColor+";"),o.value.componentBgUrl&&(e+=`background-image:url('${d(o.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;"),e})),r=s((()=>{var e="";return o.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${o.value.componentBgAlpha/10});`,e+=`height:${v.value}px;`,o.value.topRounded&&(e+="border-top-left-radius:"+2*o.value.topRounded+"rpx;"),o.value.topRounded&&(e+="border-top-right-radius:"+2*o.value.topRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*o.value.bottomRounded+"rpx;"),o.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*o.value.bottomRounded+"rpx;")),e}));c((()=>{x(),"decorate"==l.mode&&f((()=>o.value),((e,t)=>{e&&"ShopSearch"==e.componentName&&x()}))}));const n=z(),v=u(0),x=()=>{p((()=>{m().in(n).select(".diy-shop-search").boundingClientRect((e=>{v.value=e.height})).exec()}))},S=e=>{if("decorate"==l.mode)return!1;we({url:e})};return(e,t)=>{const l=$,n=E,i=D;return g(),b(l,{style:_(k(a))},{default:y((()=>[h(l,{style:_(k(r))},null,8,["style"]),h(l,{class:"diy-shop-search relative overflow-hidden flex items-center"},{default:y((()=>[h(n,{src:k(d)("addon/shop/diy/search_01.png"),class:"w-[40rpx] h-[40rpx]",mode:"widthFix",onClick:t[0]||(t[0]=e=>S("/addon/shop/pages/goods/category"))},null,8,["src"]),h(l,{class:"flex-1 ml-[24rpx] rounded-[32rpx] flex items-center bg-[var(--temp-bg)] opacity-90 py-[10rpx] pl-[38rpx] pr-[32rpx] justify-between h-[60rpx] box-border",onClick:t[1]||(t[1]=e=>S("/addon/shop/pages/goods/search"))},{default:y((()=>[h(i,{class:"text-[var(--text-color-light9)] text-[26rpx]"},{default:y((()=>[w(C(k(o).text),1)])),_:1}),h(i,{class:"nc-iconfont nc-icon-sousuo-duanV6xx1 text-[24rpx]"})])),_:1})])),_:1})])),_:1},8,["style"])}}}),ko=We(n({__name:"index",props:["component","index","value"],setup(e){const t=e,l=yt(),o=i(),a=u([]),r=s((()=>t.value?t.value:"decorate"==o.mode?o.value[t.index]:t.component));let n=u(!0);n.value=!0;const p=s((()=>{var e="";return e+="position:relative;",r.value.componentStartBgColor&&(r.value.componentStartBgColor&&r.value.componentEndBgColor?e+=`background:linear-gradient(${r.value.componentGradientAngle},${r.value.componentStartBgColor},${r.value.componentEndBgColor});`:e+="background-color:"+r.value.componentStartBgColor+";"),r.value.topRounded&&(e+="border-top-left-radius:"+2*r.value.topRounded+"rpx;"),r.value.topRounded&&(e+="border-top-right-radius:"+2*r.value.topRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomRounded+"rpx;"),r.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomRounded+"rpx;"),e})),m=s((()=>{var e="";return r.value.elementBgColor&&(e+="background-color:"+r.value.elementBgColor+";"),r.value.topElementRounded&&(e+="border-top-left-radius:"+2*r.value.topElementRounded+"rpx;"),r.value.topElementRounded&&(e+="border-top-right-radius:"+2*r.value.topElementRounded+"rpx;"),r.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomElementRounded+"rpx;"),r.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomElementRounded+"rpx;"),r.value.margin&&r.value.margin.both?e+="width: calc((100vw - "+4*r.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e})),T=s((()=>{var e="";return r.value.topElementRounded&&(e+="border-radius:"+2*r.value.topElementRounded+"rpx;"),e})),z=s((()=>{var e="";return r.value.topCarouselRounded&&(e+="border-top-left-radius:"+2*r.value.topCarouselRounded+"rpx;"),r.value.topCarouselRounded&&(e+="border-top-right-radius:"+2*r.value.topCarouselRounded+"rpx;"),r.value.bottomCarouselRounded&&(e+="border-bottom-left-radius:"+2*r.value.bottomCarouselRounded+"rpx;"),r.value.bottomCarouselRounded&&(e+="border-bottom-right-radius:"+2*r.value.bottomCarouselRounded+"rpx;"),r.value.margin&&r.value.margin.both?e+="width: calc((100vw - "+4*r.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );",e}));c((()=>{I(),"decorate"!=o.mode&&f((()=>r.value),((e,t)=>{I()}),{deep:!0})}));const I=()=>{if("decorate"==o.mode){let e={goods_cover_thumb_mid:"",goods_name:"商品名称",sale_num:"100",unit:"件",goodsSku:{show_price:100}};a.value.push(e)}else(()=>{let e={num:1,goods_ids:"custom"==r.value.source?r.value.goods_ids:""};vt(e).then((e=>{a.value=e.data}))})()},M=u(0),A=e=>{M.value=e.detail.current};return(e,t)=>{const i=E,s=$,u=D,c=Y,f=H,I=v(x("u--image"),xt);return a.value&&a.value[0]?(g(),b(s,{key:0,style:_(k(p)),class:"overflow-hidden"},{default:y((()=>[k(r).textImg||k(r).subTitle.text?(g(),b(s,{key:0,class:"flex justify-between items-center mb-[20rpx]"},{default:y((()=>[k(r).textImg?(g(),b(s,{key:0,class:"h-[34rpx] flex items-center",onClick:t[0]||(t[0]=e=>k(o).toRedirect(k(r).textLink))},{default:y((()=>[h(i,{class:"h-[100%] w-[auto]",src:k(d)(k(r).textImg),mode:"heightFix"},null,8,["src"])])),_:1})):S("v-if",!0),k(r).subTitle.text?(g(),b(s,{key:1,class:"flex items-center ml-[auto]",onClick:t[1]||(t[1]=e=>k(o).toRedirect(k(r).subTitle.link)),style:_({color:k(r).subTitle.textColor})},{default:y((()=>[h(u,{class:"text-[24rpx]"},{default:y((()=>[w(C(k(r).subTitle.text),1)])),_:1}),h(u,{class:"text-[22rpx] iconfont iconxiangyoujiantou"})])),_:1},8,["style"])):S("v-if",!0)])),_:1})):S("v-if",!0),h(s,{class:"flex justify-between"},{default:y((()=>[S(" 轮播图 "),h(s,{class:"relative w-[340rpx] overflow-hidden",style:_(k(z))},{default:y((()=>[1==k(r).list.length?(g(),b(s,{key:0,class:"leading-0 overflow-hidden"},{default:y((()=>[h(s,{onClick:t[2]||(t[2]=e=>k(o).toRedirect(k(r).list[0].link))},{default:y((()=>[k(r).list[0].imageUrl?(g(),b(i,{key:0,src:k(d)(k(r).list[0].imageUrl),mode:"heightFix",class:"h-[504rpx] !w-full","show-menu-by-longpress":!0},null,8,["src"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"heightFix",class:"h-[504rpx] !w-full","show-menu-by-longpress":!0},null,8,["src"]))])),_:1})])),_:1})):(g(),b(f,{key:1,class:"swiper ns-indicator-dots-three h-[504rpx]",autoplay:"true",circular:"true","indicator-dots":k(n),onChange:A,"indicator-color":k(r).indicatorColor,"indicator-active-color":k(r).indicatorActiveColor},{default:y((()=>[(g(!0),R(B,null,F(k(r).list,(e=>(g(),b(c,{class:"swiper-item",key:e.id},{default:y((()=>[h(s,{onClick:t=>k(o).toRedirect(e.link)},{default:y((()=>[h(s,{class:"item h-[504rpx]"},{default:y((()=>[e.imageUrl?(g(),b(i,{key:0,src:k(d)(e.imageUrl),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"])):(g(),b(i,{key:1,src:k(d)("static/resource/images/diy/figure.png"),mode:"scaleToFill",class:"w-full h-full","show-menu-by-longpress":!0},null,8,["src"]))])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1},8,["indicator-dots","indicator-color","indicator-active-color"]))])),_:1},8,["style"]),h(s,{class:"w-[340rpx] h-[504rpx] flex flex-col bg-[#fff] box-border overflow-hidden",style:_(k(m)),onClick:t[3]||(t[3]=e=>{return t=a.value[0],void we({url:"/addon/shop/pages/goods/detail",param:{goods_id:t.goods_id}});var t})},{default:y((()=>[h(s,{style:_(k(T)),class:"w-[346rpx] h-[350rpx] overflow-hidden"},{default:y((()=>[h(I,{width:"346rpx",height:"350rpx",src:k(d)(a.value[0].goods_cover_thumb_mid||""),model:"aspectFill"},{error:y((()=>[h(i,{class:"w-[346rpx] h-[350rpx]",src:k(d)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1},8,["style"]),h(s,{class:"px-[16rpx] flex-1 pt-[16rpx] pb-[20rpx] flex flex-col justify-between"},{default:y((()=>[h(s,{class:"text-[#303133] leading-[40rpx] text-[28rpx] truncate",style:_({color:k(r).goodsNameStyle.color,fontWeight:k(r).goodsNameStyle.fontWeight})},{default:y((()=>[w(C(a.value[0].goods_name),1)])),_:1},8,["style"]),h(s,{class:"flex justify-between flex-wrap items-baseline mt-[28rpx]"},{default:y((()=>[h(s,{class:"flex items-center"},{default:y((()=>[h(s,{class:"text-[var(--price-text-color)] price-font truncate max-w-[200rpx]",style:_({color:k(r).priceStyle.mainColor})},{default:y((()=>[h(u,{class:"text-[24rpx] font-400"},{default:y((()=>[w("￥")])),_:1}),h(u,{class:"text-[40rpx] font-500"},{default:y((()=>[w(C(parseFloat(k(l).goodsPrice(a.value[0])).toFixed(2).split(".")[0]),1)])),_:1}),h(u,{class:"text-[24rpx] font-500"},{default:y((()=>[w("."+C(parseFloat(k(l).goodsPrice(a.value[0])).toFixed(2).split(".")[1]),1)])),_:1})])),_:1},8,["style"]),"member_price"==k(l).priceType(a.value[0])?(g(),b(i,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:k(d)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==k(l).priceType(a.value[0])?(g(),b(i,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:k(d)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==k(l).priceType(a.value[0])?(g(),b(i,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:k(d)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):S("v-if",!0)])),_:1}),h(s,{class:"w-[44rpx] h-[44rpx] bg-[red] flex items-center justify-center rounded-[50%]",style:_({backgroundColor:k(r).saleStyle.color})},{default:y((()=>[h(u,{class:"iconfont iconjia font-500 text-[32rpx] text-[#fff]"})])),_:1},8,["style"])])),_:1})])),_:1})])),_:1},8,["style"])])),_:1})])),_:1},8,["style"])):S("v-if",!0)}}}),[["__scopeId","data-v-0cd3db00"]]),wo=n({__name:"pop-ads",props:{data:{type:Object,default:{}}},setup(e){const t=e,l=i(),o=s((()=>t.data.popWindow)),a=u(!1);c((()=>{p((()=>{"always"==o.value.count&&uni.removeStorageSync(o.value.id+"_pop_window_count"),uni.getStorageSync("isOnLoad")&&(n(),uni.removeStorageSync("isOnLoad"))}))}));const r=()=>{const e=o.value.imgHeight/o.value.imgWidth;let t,l;return e<=1?(t=290,l=t*e):(l=410,t=l/e),`height:${2*l}rpx;width:${2*t}rpx;`},n=()=>{if("decorate"==l.mode||!o.value.show)return!1;const e=uni.getStorageSync(o.value.id+"_pop_window_count");"always"!==e&&""!=e||(a.value=!0)},f=()=>{a.value=!1,uni.setStorageSync(o.value.id+"_pop_window_count",o.value.count)};return(e,t)=>{const n=E,i=D,s=$,u=v(x("u-popup"),Ve);return g(),b(s,{onTouchmove:t[2]||(t[2]=N((()=>{}),["prevent","stop"])),class:"share-popup"},{default:y((()=>[h(u,{show:a.value,mode:"center",onClose:f,overlayOpacity:"0.3",bgColor:"transparent"},{default:y((()=>[k(o)&&k(o).imgUrl?(g(),b(s,{key:0,onTouchmove:t[1]||(t[1]=N((()=>{}),["prevent","stop"])),class:"flex flex-col items-center"},{default:y((()=>[h(n,{src:k(d)(k(o).imgUrl),style:_(r()),onClick:t[0]||(t[0]=e=>k(l).toRedirect(k(o).link))},null,8,["src","style"]),h(i,{onClick:f,class:"mt-[50rpx] nc-iconfont nc-icon-cuohaoV6xx1 !text-[50rpx] text-[#fff]"})])),_:1})):S("v-if",!0)])),_:1},8,["show"])])),_:1})}}});
/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */
function Co(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,o)}return l}function So(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?Co(Object(l),!0).forEach((function(t){Bo(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):Co(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}function Ro(e){return(Ro="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Bo(e,t,l){return t in e?Object.defineProperty(e,t,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[t]=l,e}function Fo(){return Fo=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var o in l)Object.prototype.hasOwnProperty.call(l,o)&&(e[o]=l[o])}return e},Fo.apply(this,arguments)}function To(e,t){if(null==e)return{};var l,o,a=function(e,t){if(null==e)return{};var l,o,a={},r=Object.keys(e);for(o=0;o<r.length;o++)l=r[o],t.indexOf(l)>=0||(a[l]=e[l]);return a}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)l=r[o],t.indexOf(l)>=0||Object.prototype.propertyIsEnumerable.call(e,l)&&(a[l]=e[l])}return a}function zo(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var $o=zo(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Do=zo(/Edge/i),Eo=zo(/firefox/i),Io=zo(/safari/i)&&!zo(/chrome/i)&&!zo(/android/i),Mo=zo(/iP(ad|od|hone)/i),Ao=zo(/chrome/i)&&zo(/android/i),jo={capture:!1,passive:!1};function No(e,t,l){e.addEventListener(t,l,!$o&&jo)}function Wo(e,t,l){e.removeEventListener(t,l,!$o&&jo)}function Vo(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(l){return!1}return!1}}function Yo(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function Ho(e,t,l,o){if(e){l=l||document;do{if(null!=t&&(">"===t[0]?e.parentNode===l&&Vo(e,t):Vo(e,t))||o&&e===l)return e;if(e===l)break}while(e=Yo(e))}return null}var Uo,Po=/\s+/g;function Oo(e,t,l){if(e&&t)if(e.classList)e.classList[l?"add":"remove"](t);else{var o=(" "+e.className+" ").replace(Po," ").replace(" "+t+" "," ");e.className=(o+(l?" "+t:"")).replace(Po," ")}}function Lo(e,t,l){var o=e&&e.style;if(o){if(void 0===l)return document.defaultView&&document.defaultView.getComputedStyle?l=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(l=e.currentStyle),void 0===t?l:l[t];t in o||-1!==t.indexOf("webkit")||(t="-webkit-"+t),o[t]=l+("string"==typeof l?"":"px")}}function Go(e,t){var l="";if("string"==typeof e)l=e;else do{var o=Lo(e,"transform");o&&"none"!==o&&(l=o+" "+l)}while(!t&&(e=e.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(l)}function qo(e,t,l){if(e){var o=e.getElementsByTagName(t),a=0,r=o.length;if(l)for(;a<r;a++)l(o[a],a);return o}return[]}function Xo(){var e=document.scrollingElement;return e||document.documentElement}function Zo(e,t,l,o,a){if(e.getBoundingClientRect||e===window){var r,n,i,s,d,u,c;if(e!==window&&e.parentNode&&e!==Xo()?(n=(r=e.getBoundingClientRect()).top,i=r.left,s=r.bottom,d=r.right,u=r.height,c=r.width):(n=0,i=0,s=window.innerHeight,d=window.innerWidth,u=window.innerHeight,c=window.innerWidth),(t||l)&&e!==window&&(a=a||e.parentNode,!$o))do{if(a&&a.getBoundingClientRect&&("none"!==Lo(a,"transform")||l&&"static"!==Lo(a,"position"))){var f=a.getBoundingClientRect();n-=f.top+parseInt(Lo(a,"border-top-width")),i-=f.left+parseInt(Lo(a,"border-left-width")),s=n+r.height,d=i+r.width;break}}while(a=a.parentNode);if(o&&e!==window){var p=Go(a||e),m=p&&p.a,v=p&&p.d;p&&(s=(n/=v)+(u/=v),d=(i/=m)+(c/=m))}return{top:n,left:i,bottom:s,right:d,width:c,height:u}}}function Jo(e,t,l){for(var o=la(e,!0),a=Zo(e)[t];o;){var r=Zo(o)[l];if(!("top"===l||"left"===l?a>=r:a<=r))return o;if(o===Xo())break;o=la(o,!1)}return!1}function Qo(e,t,l,o){for(var a=0,r=0,n=e.children;r<n.length;){if("none"!==n[r].style.display&&n[r]!==nr.ghost&&(o||n[r]!==nr.dragged)&&Ho(n[r],l.draggable,e,!1)){if(a===t)return n[r];a++}r++}return null}function Ko(e,t){for(var l=e.lastElementChild;l&&(l===nr.ghost||"none"===Lo(l,"display")||t&&!Vo(l,t));)l=l.previousElementSibling;return l||null}function ea(e,t){var l=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===nr.clone||t&&!Vo(e,t)||l++;return l}function ta(e){var t=0,l=0,o=Xo();if(e)do{var a=Go(e),r=a.a,n=a.d;t+=e.scrollLeft*r,l+=e.scrollTop*n}while(e!==o&&(e=e.parentNode));return[t,l]}function la(e,t){if(!e||!e.getBoundingClientRect)return Xo();var l=e,o=!1;do{if(l.clientWidth<l.scrollWidth||l.clientHeight<l.scrollHeight){var a=Lo(l);if(l.clientWidth<l.scrollWidth&&("auto"==a.overflowX||"scroll"==a.overflowX)||l.clientHeight<l.scrollHeight&&("auto"==a.overflowY||"scroll"==a.overflowY)){if(!l.getBoundingClientRect||l===document.body)return Xo();if(o||t)return l;o=!0}}}while(l=l.parentNode);return Xo()}function oa(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function aa(e,t){return function(){if(!Uo){var l=arguments;1===l.length?e.call(this,l[0]):e.apply(this,l),Uo=setTimeout((function(){Uo=void 0}),t)}}}function ra(e,t,l){e.scrollLeft+=t,e.scrollTop+=l}function na(e){var t=window.Polymer,l=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):l?l(e).clone(!0)[0]:e.cloneNode(!0)}function ia(e,t,l){var o={};return Array.from(e.children).forEach((function(a){var r,n,i,s;if(Ho(a,t.draggable,e,!1)&&!a.animated&&a!==l){var d=Zo(a);o.left=Math.min(null!==(r=o.left)&&void 0!==r?r:1/0,d.left),o.top=Math.min(null!==(n=o.top)&&void 0!==n?n:1/0,d.top),o.right=Math.max(null!==(i=o.right)&&void 0!==i?i:-1/0,d.right),o.bottom=Math.max(null!==(s=o.bottom)&&void 0!==s?s:-1/0,d.bottom)}})),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var sa="Sortable"+(new Date).getTime();function da(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach((function(e){if("none"!==Lo(e,"display")&&e!==nr.ghost){t.push({target:e,rect:Zo(e)});var l=So({},t[t.length-1].rect);if(e.thisAnimationDuration){var o=Go(e,!0);o&&(l.top-=o.f,l.left-=o.e)}e.fromRect=l}}))},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var l in e)if(e.hasOwnProperty(l))for(var o in t)if(t.hasOwnProperty(o)&&t[o]===e[l][o])return Number(l);return-1}(t,{target:e}),1)},animateAll:function(l){var o=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof l&&l());var a=!1,r=0;t.forEach((function(e){var t=0,l=e.target,n=l.fromRect,i=Zo(l),s=l.prevFromRect,d=l.prevToRect,u=e.rect,c=Go(l,!0);c&&(i.top-=c.f,i.left-=c.e),l.toRect=i,l.thisAnimationDuration&&oa(s,i)&&!oa(n,i)&&(u.top-i.top)/(u.left-i.left)==(n.top-i.top)/(n.left-i.left)&&(t=function(e,t,l,o){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-l.top,2)+Math.pow(t.left-l.left,2))*o.animation}(u,s,d,o.options)),oa(i,n)||(l.prevFromRect=n,l.prevToRect=i,t||(t=o.options.animation),o.animate(l,u,i,t)),t&&(a=!0,r=Math.max(r,t),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout((function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null}),t),l.thisAnimationDuration=t)})),clearTimeout(e),a?e=setTimeout((function(){"function"==typeof l&&l()}),r):"function"==typeof l&&l(),t=[]},animate:function(e,t,l,o){if(o){Lo(e,"transition",""),Lo(e,"transform","");var a=Go(this.el),r=a&&a.a,n=a&&a.d,i=(t.left-l.left)/(r||1),s=(t.top-l.top)/(n||1);e.animatingX=!!i,e.animatingY=!!s,Lo(e,"transform","translate3d("+i+"px,"+s+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),Lo(e,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),Lo(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){Lo(e,"transition",""),Lo(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),o)}}}}var ua=[],ca={initializeByDefault:!0},fa={mount:function(e){for(var t in ca)ca.hasOwnProperty(t)&&!(t in e)&&(e[t]=ca[t]);ua.forEach((function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")})),ua.push(e)},pluginEvent:function(e,t,l){var o=this;this.eventCanceled=!1,l.cancel=function(){o.eventCanceled=!0};var a=e+"Global";ua.forEach((function(o){t[o.pluginName]&&(t[o.pluginName][a]&&t[o.pluginName][a](So({sortable:t},l)),t.options[o.pluginName]&&t[o.pluginName][e]&&t[o.pluginName][e](So({sortable:t},l)))}))},initializePlugins:function(e,t,l,o){for(var a in ua.forEach((function(o){var a=o.pluginName;if(e.options[a]||o.initializeByDefault){var r=new o(e,t,e.options);r.sortable=e,r.options=e.options,e[a]=r,Fo(l,r.defaults)}})),e.options)if(e.options.hasOwnProperty(a)){var r=this.modifyOption(e,a,e.options[a]);void 0!==r&&(e.options[a]=r)}},getEventProperties:function(e,t){var l={};return ua.forEach((function(o){"function"==typeof o.eventProperties&&Fo(l,o.eventProperties.call(t[o.pluginName],e))})),l},modifyOption:function(e,t,l){var o;return ua.forEach((function(a){e[a.pluginName]&&a.optionListeners&&"function"==typeof a.optionListeners[t]&&(o=a.optionListeners[t].call(e[a.pluginName],l))})),o}};var pa=["evt"],ma=function(e,t){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=l.evt,a=To(l,pa);fa.pluginEvent.bind(nr)(e,t,So({dragEl:xa,parentEl:ga,ghostEl:ba,rootEl:ya,nextEl:ha,lastDownEl:_a,cloneEl:ka,cloneHidden:wa,dragStarted:Aa,putSortable:Ta,activeSortable:nr.active,originalEvent:o,oldIndex:Ca,oldDraggableIndex:Ra,newIndex:Sa,newDraggableIndex:Ba,hideGhostForTarget:lr,unhideGhostForTarget:or,cloneNowHidden:function(){wa=!0},cloneNowShown:function(){wa=!1},dispatchSortableEvent:function(e){va({sortable:t,name:e,originalEvent:o})}},a))};function va(e){!function(e){var t=e.sortable,l=e.rootEl,o=e.name,a=e.targetEl,r=e.cloneEl,n=e.toEl,i=e.fromEl,s=e.oldIndex,d=e.newIndex,u=e.oldDraggableIndex,c=e.newDraggableIndex,f=e.originalEvent,p=e.putSortable,m=e.extraEventProperties;if(t=t||l&&l[sa]){var v,x=t.options,g="on"+o.charAt(0).toUpperCase()+o.substr(1);!window.CustomEvent||$o||Do?(v=document.createEvent("Event")).initEvent(o,!0,!0):v=new CustomEvent(o,{bubbles:!0,cancelable:!0}),v.to=n||l,v.from=i||l,v.item=a||l,v.clone=r,v.oldIndex=s,v.newIndex=d,v.oldDraggableIndex=u,v.newDraggableIndex=c,v.originalEvent=f,v.pullMode=p?p.lastPutMode:void 0;var b=So(So({},m),fa.getEventProperties(o,t));for(var y in b)v[y]=b[y];l&&l.dispatchEvent(v),x[g]&&x[g].call(t,v)}}(So({putSortable:Ta,cloneEl:ka,targetEl:xa,rootEl:ya,oldIndex:Ca,oldDraggableIndex:Ra,newIndex:Sa,newDraggableIndex:Ba},e))}var xa,ga,ba,ya,ha,_a,ka,wa,Ca,Sa,Ra,Ba,Fa,Ta,za,$a,Da,Ea,Ia,Ma,Aa,ja,Na,Wa,Va,Ya=!1,Ha=!1,Ua=[],Pa=!1,Oa=!1,La=[],Ga=!1,qa=[],Xa="undefined"!=typeof document,Za=Mo,Ja=Do||$o?"cssFloat":"float",Qa=Xa&&!Ao&&!Mo&&"draggable"in document.createElement("div"),Ka=function(){if(Xa){if($o)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),er=function(e,t){var l=Lo(e),o=parseInt(l.width)-parseInt(l.paddingLeft)-parseInt(l.paddingRight)-parseInt(l.borderLeftWidth)-parseInt(l.borderRightWidth),a=Qo(e,0,t),r=Qo(e,1,t),n=a&&Lo(a),i=r&&Lo(r),s=n&&parseInt(n.marginLeft)+parseInt(n.marginRight)+Zo(a).width,d=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+Zo(r).width;if("flex"===l.display)return"column"===l.flexDirection||"column-reverse"===l.flexDirection?"vertical":"horizontal";if("grid"===l.display)return l.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(a&&n.float&&"none"!==n.float){var u="left"===n.float?"left":"right";return!r||"both"!==i.clear&&i.clear!==u?"horizontal":"vertical"}return a&&("block"===n.display||"flex"===n.display||"table"===n.display||"grid"===n.display||s>=o&&"none"===l[Ja]||r&&"none"===l[Ja]&&s+d>o)?"vertical":"horizontal"},tr=function(e){function t(e,l){return function(o,a,r,n){var i=o.options.group.name&&a.options.group.name&&o.options.group.name===a.options.group.name;if(null==e&&(l||i))return!0;if(null==e||!1===e)return!1;if(l&&"clone"===e)return e;if("function"==typeof e)return t(e(o,a,r,n),l)(o,a,r,n);var s=(l?o:a).options.group.name;return!0===e||"string"==typeof e&&e===s||e.join&&e.indexOf(s)>-1}}var l={},o=e.group;o&&"object"==Ro(o)||(o={name:o}),l.name=o.name,l.checkPull=t(o.pull,!0),l.checkPut=t(o.put),l.revertClone=o.revertClone,e.group=l},lr=function(){!Ka&&ba&&Lo(ba,"display","none")},or=function(){!Ka&&ba&&Lo(ba,"display","")};Xa&&!Ao&&document.addEventListener("click",(function(e){if(Ha)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Ha=!1,!1}),!0);var ar=function(e){if(xa){e=e.touches?e.touches[0]:e;var t=(a=e.clientX,r=e.clientY,Ua.some((function(e){var t=e[sa].options.emptyInsertThreshold;if(t&&!Ko(e)){var l=Zo(e),o=a>=l.left-t&&a<=l.right+t,i=r>=l.top-t&&r<=l.bottom+t;return o&&i?n=e:void 0}})),n);if(t){var l={};for(var o in e)e.hasOwnProperty(o)&&(l[o]=e[o]);l.target=l.rootEl=t,l.preventDefault=void 0,l.stopPropagation=void 0,t[sa]._onDragOver(l)}}var a,r,n},rr=function(e){xa&&xa.parentNode[sa]._isOutsideThisEl(e.target)};function nr(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=Fo({},t),e[sa]=this;var l={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return er(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==nr.supportPointer&&"PointerEvent"in window&&!Io,emptyInsertThreshold:5};for(var o in fa.initializePlugins(this,e,l),l)!(o in t)&&(t[o]=l[o]);for(var a in tr(t),this)"_"===a.charAt(0)&&"function"==typeof this[a]&&(this[a]=this[a].bind(this));this.nativeDraggable=!t.forceFallback&&Qa,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?No(e,"pointerdown",this._onTapStart):(No(e,"mousedown",this._onTapStart),No(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(No(e,"dragover",this),No(e,"dragenter",this)),Ua.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Fo(this,da())}function ir(e,t,l,o,a,r,n,i){var s,d,u=e[sa],c=u.options.onMove;return!window.CustomEvent||$o||Do?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=t,s.from=e,s.dragged=l,s.draggedRect=o,s.related=a||t,s.relatedRect=r||Zo(t),s.willInsertAfter=i,s.originalEvent=n,e.dispatchEvent(s),c&&(d=c.call(u,s,n)),d}function sr(e){e.draggable=!1}function dr(){Ga=!1}function ur(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,l=t.length,o=0;l--;)o+=t.charCodeAt(l);return o.toString(36)}function cr(e){return setTimeout(e,0)}function fr(e){return clearTimeout(e)}nr.prototype={constructor:nr,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(ja=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,xa):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,l=this.el,o=this.options,a=o.preventOnFilter,r=e.type,n=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,i=(n||e).target,s=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||i,d=o.filter;if(function(e){qa.length=0;var t=e.getElementsByTagName("input"),l=t.length;for(;l--;){var o=t[l];o.checked&&qa.push(o)}}(l),!xa&&!(/mousedown|pointerdown/.test(r)&&0!==e.button||o.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!Io||!i||"SELECT"!==i.tagName.toUpperCase())&&!((i=Ho(i,o.draggable,l,!1))&&i.animated||_a===i)){if(Ca=ea(i),Ra=ea(i,o.draggable),"function"==typeof d){if(d.call(this,e,i,this))return va({sortable:t,rootEl:s,name:"filter",targetEl:i,toEl:l,fromEl:l}),ma("filter",t,{evt:e}),void(a&&e.cancelable&&e.preventDefault())}else if(d&&(d=d.split(",").some((function(o){if(o=Ho(s,o.trim(),l,!1))return va({sortable:t,rootEl:o,name:"filter",targetEl:i,fromEl:l,toEl:l}),ma("filter",t,{evt:e}),!0}))))return void(a&&e.cancelable&&e.preventDefault());o.handle&&!Ho(s,o.handle,l,!1)||this._prepareDragStart(e,n,i)}}},_prepareDragStart:function(e,t,l){var o,a=this,r=a.el,n=a.options,i=r.ownerDocument;if(l&&!xa&&l.parentNode===r){var s=Zo(l);if(ya=r,ga=(xa=l).parentNode,ha=xa.nextSibling,_a=l,Fa=n.group,nr.dragged=xa,za={target:xa,clientX:(t||e).clientX,clientY:(t||e).clientY},Ia=za.clientX-s.left,Ma=za.clientY-s.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,xa.style["will-change"]="all",o=function(){ma("delayEnded",a,{evt:e}),nr.eventCanceled?a._onDrop():(a._disableDelayedDragEvents(),!Eo&&a.nativeDraggable&&(xa.draggable=!0),a._triggerDragStart(e,t),va({sortable:a,name:"choose",originalEvent:e}),Oo(xa,n.chosenClass,!0))},n.ignore.split(",").forEach((function(e){qo(xa,e.trim(),sr)})),No(i,"dragover",ar),No(i,"mousemove",ar),No(i,"touchmove",ar),No(i,"mouseup",a._onDrop),No(i,"touchend",a._onDrop),No(i,"touchcancel",a._onDrop),Eo&&this.nativeDraggable&&(this.options.touchStartThreshold=4,xa.draggable=!0),ma("delayStart",this,{evt:e}),!n.delay||n.delayOnTouchOnly&&!t||this.nativeDraggable&&(Do||$o))o();else{if(nr.eventCanceled)return void this._onDrop();No(i,"mouseup",a._disableDelayedDrag),No(i,"touchend",a._disableDelayedDrag),No(i,"touchcancel",a._disableDelayedDrag),No(i,"mousemove",a._delayedDragTouchMoveHandler),No(i,"touchmove",a._delayedDragTouchMoveHandler),n.supportPointer&&No(i,"pointermove",a._delayedDragTouchMoveHandler),a._dragStartTimer=setTimeout(o,n.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){xa&&sr(xa),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;Wo(e,"mouseup",this._disableDelayedDrag),Wo(e,"touchend",this._disableDelayedDrag),Wo(e,"touchcancel",this._disableDelayedDrag),Wo(e,"mousemove",this._delayedDragTouchMoveHandler),Wo(e,"touchmove",this._delayedDragTouchMoveHandler),Wo(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?No(document,"pointermove",this._onTouchMove):No(document,t?"touchmove":"mousemove",this._onTouchMove):(No(xa,"dragend",this),No(ya,"dragstart",this._onDragStart));try{document.selection?cr((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(l){}},_dragStarted:function(e,t){if(Ya=!1,ya&&xa){ma("dragStarted",this,{evt:t}),this.nativeDraggable&&No(document,"dragover",rr);var l=this.options;!e&&Oo(xa,l.dragClass,!1),Oo(xa,l.ghostClass,!0),nr.active=this,e&&this._appendGhost(),va({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if($a){this._lastX=$a.clientX,this._lastY=$a.clientY,lr();for(var e=document.elementFromPoint($a.clientX,$a.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint($a.clientX,$a.clientY))!==t;)t=e;if(xa.parentNode[sa]._isOutsideThisEl(e),t)do{if(t[sa]){if(t[sa]._onDragOver({clientX:$a.clientX,clientY:$a.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);or()}},_onTouchMove:function(e){if(za){var t=this.options,l=t.fallbackTolerance,o=t.fallbackOffset,a=e.touches?e.touches[0]:e,r=ba&&Go(ba,!0),n=ba&&r&&r.a,i=ba&&r&&r.d,s=Za&&Va&&ta(Va),d=(a.clientX-za.clientX+o.x)/(n||1)+(s?s[0]-La[0]:0)/(n||1),u=(a.clientY-za.clientY+o.y)/(i||1)+(s?s[1]-La[1]:0)/(i||1);if(!nr.active&&!Ya){if(l&&Math.max(Math.abs(a.clientX-this._lastX),Math.abs(a.clientY-this._lastY))<l)return;this._onDragStart(e,!0)}if(ba){r?(r.e+=d-(Da||0),r.f+=u-(Ea||0)):r={a:1,b:0,c:0,d:1,e:d,f:u};var c="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");Lo(ba,"webkitTransform",c),Lo(ba,"mozTransform",c),Lo(ba,"msTransform",c),Lo(ba,"transform",c),Da=d,Ea=u,$a=a}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!ba){var e=this.options.fallbackOnBody?document.body:ya,t=Zo(xa,!0,Za,!0,e),l=this.options;if(Za){for(Va=e;"static"===Lo(Va,"position")&&"none"===Lo(Va,"transform")&&Va!==document;)Va=Va.parentNode;Va!==document.body&&Va!==document.documentElement?(Va===document&&(Va=Xo()),t.top+=Va.scrollTop,t.left+=Va.scrollLeft):Va=Xo(),La=ta(Va)}Oo(ba=xa.cloneNode(!0),l.ghostClass,!1),Oo(ba,l.fallbackClass,!0),Oo(ba,l.dragClass,!0),Lo(ba,"transition",""),Lo(ba,"transform",""),Lo(ba,"box-sizing","border-box"),Lo(ba,"margin",0),Lo(ba,"top",t.top),Lo(ba,"left",t.left),Lo(ba,"width",t.width),Lo(ba,"height",t.height),Lo(ba,"opacity","0.8"),Lo(ba,"position",Za?"absolute":"fixed"),Lo(ba,"zIndex","100000"),Lo(ba,"pointerEvents","none"),nr.ghost=ba,e.appendChild(ba),Lo(ba,"transform-origin",Ia/parseInt(ba.style.width)*100+"% "+Ma/parseInt(ba.style.height)*100+"%")}},_onDragStart:function(e,t){var l=this,o=e.dataTransfer,a=l.options;ma("dragStart",this,{evt:e}),nr.eventCanceled?this._onDrop():(ma("setupClone",this),nr.eventCanceled||((ka=na(xa)).removeAttribute("id"),ka.draggable=!1,ka.style["will-change"]="",this._hideClone(),Oo(ka,this.options.chosenClass,!1),nr.clone=ka),l.cloneId=cr((function(){ma("clone",l),nr.eventCanceled||(l.options.removeCloneOnHide||ya.insertBefore(ka,xa),l._hideClone(),va({sortable:l,name:"clone"}))})),!t&&Oo(xa,a.dragClass,!0),t?(Ha=!0,l._loopId=setInterval(l._emulateDragOver,50)):(Wo(document,"mouseup",l._onDrop),Wo(document,"touchend",l._onDrop),Wo(document,"touchcancel",l._onDrop),o&&(o.effectAllowed="move",a.setData&&a.setData.call(l,o,xa)),No(document,"drop",l),Lo(xa,"transform","translateZ(0)")),Ya=!0,l._dragStartId=cr(l._dragStarted.bind(l,t,e)),No(document,"selectstart",l),Aa=!0,Io&&Lo(document.body,"user-select","none"))},_onDragOver:function(e){var t,l,o,a,r=this.el,n=e.target,i=this.options,s=i.group,d=nr.active,u=Fa===s,c=i.sort,f=Ta||d,p=this,m=!1;if(!Ga){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),n=Ho(n,i.draggable,r,!0),T("dragOver"),nr.eventCanceled)return m;if(xa.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||p._ignoreWhileAnimating===n)return $(!1);if(Ha=!1,d&&!i.disabled&&(u?c||(o=ga!==ya):Ta===this||(this.lastPutMode=Fa.checkPull(this,d,xa,e))&&s.checkPut(this,d,xa,e))){if(a="vertical"===this._getDirection(e,n),t=Zo(xa),T("dragOverValid"),nr.eventCanceled)return m;if(o)return ga=ya,z(),this._hideClone(),T("revert"),nr.eventCanceled||(ha?ya.insertBefore(xa,ha):ya.appendChild(xa)),$(!0);var v=Ko(r,i.draggable);if(!v||function(e,t,l){var o=Zo(Ko(l.el,l.options.draggable)),a=ia(l.el,l.options,ba),r=10;return t?e.clientX>a.right+r||e.clientY>o.bottom&&e.clientX>o.left:e.clientY>a.bottom+r||e.clientX>o.right&&e.clientY>o.top}(e,a,this)&&!v.animated){if(v===xa)return $(!1);if(v&&r===e.target&&(n=v),n&&(l=Zo(n)),!1!==ir(ya,r,xa,t,n,l,e,!!n))return z(),v&&v.nextSibling?r.insertBefore(xa,v.nextSibling):r.appendChild(xa),ga=r,D(),$(!0)}else if(v&&function(e,t,l){var o=Zo(Qo(l.el,0,l.options,!0)),a=ia(l.el,l.options,ba),r=10;return t?e.clientX<a.left-r||e.clientY<o.top&&e.clientX<o.right:e.clientY<a.top-r||e.clientY<o.bottom&&e.clientX<o.left}(e,a,this)){var x=Qo(r,0,i,!0);if(x===xa)return $(!1);if(l=Zo(n=x),!1!==ir(ya,r,xa,t,n,l,e,!1))return z(),r.insertBefore(xa,x),ga=r,D(),$(!0)}else if(n.parentNode===r){l=Zo(n);var g,b,y,h=xa.parentNode!==r,_=!function(e,t,l){var o=l?e.left:e.top,a=l?e.right:e.bottom,r=l?e.width:e.height,n=l?t.left:t.top,i=l?t.right:t.bottom,s=l?t.width:t.height;return o===n||a===i||o+r/2===n+s/2}(xa.animated&&xa.toRect||t,n.animated&&n.toRect||l,a),k=a?"top":"left",w=Jo(n,"top","top")||Jo(xa,"top","top"),C=w?w.scrollTop:void 0;if(ja!==n&&(b=l[k],Pa=!1,Oa=!_&&i.invertSwap||h),g=function(e,t,l,o,a,r,n,i){var s=o?e.clientY:e.clientX,d=o?l.height:l.width,u=o?l.top:l.left,c=o?l.bottom:l.right,f=!1;if(!n)if(i&&Wa<d*a){if(!Pa&&(1===Na?s>u+d*r/2:s<c-d*r/2)&&(Pa=!0),Pa)f=!0;else if(1===Na?s<u+Wa:s>c-Wa)return-Na}else if(s>u+d*(1-a)/2&&s<c-d*(1-a)/2)return function(e){return ea(xa)<ea(e)?1:-1}(t);if((f=f||n)&&(s<u+d*r/2||s>c-d*r/2))return s>u+d/2?1:-1;return 0}(e,n,l,a,_?1:i.swapThreshold,null==i.invertedSwapThreshold?i.swapThreshold:i.invertedSwapThreshold,Oa,ja===n),0!==g){var S=ea(xa);do{S-=g,y=ga.children[S]}while(y&&("none"===Lo(y,"display")||y===ba))}if(0===g||y===n)return $(!1);ja=n,Na=g;var R=n.nextElementSibling,B=!1,F=ir(ya,r,xa,t,n,l,e,B=1===g);if(!1!==F)return 1!==F&&-1!==F||(B=1===F),Ga=!0,setTimeout(dr,30),z(),B&&!R?r.appendChild(xa):n.parentNode.insertBefore(xa,B?R:n),w&&ra(w,0,C-w.scrollTop),ga=xa.parentNode,void 0===b||Oa||(Wa=Math.abs(b-Zo(n)[k])),D(),$(!0)}if(r.contains(xa))return $(!1)}return!1}function T(i,s){ma(i,p,So({evt:e,isOwner:u,axis:a?"vertical":"horizontal",revert:o,dragRect:t,targetRect:l,canSort:c,fromSortable:f,target:n,completed:$,onMove:function(l,o){return ir(ya,r,xa,t,l,Zo(l),e,o)},changed:D},s))}function z(){T("dragOverAnimationCapture"),p.captureAnimationState(),p!==f&&f.captureAnimationState()}function $(t){return T("dragOverCompleted",{insertion:t}),t&&(u?d._hideClone():d._showClone(p),p!==f&&(Oo(xa,Ta?Ta.options.ghostClass:d.options.ghostClass,!1),Oo(xa,i.ghostClass,!0)),Ta!==p&&p!==nr.active?Ta=p:p===nr.active&&Ta&&(Ta=null),f===p&&(p._ignoreWhileAnimating=n),p.animateAll((function(){T("dragOverAnimationComplete"),p._ignoreWhileAnimating=null})),p!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(n===xa&&!xa.animated||n===r&&!n.animated)&&(ja=null),i.dragoverBubble||e.rootEl||n===document||(xa.parentNode[sa]._isOutsideThisEl(e.target),!t&&ar(e)),!i.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),m=!0}function D(){Sa=ea(xa),Ba=ea(xa,i.draggable),va({sortable:p,name:"change",toEl:r,newIndex:Sa,newDraggableIndex:Ba,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){Wo(document,"mousemove",this._onTouchMove),Wo(document,"touchmove",this._onTouchMove),Wo(document,"pointermove",this._onTouchMove),Wo(document,"dragover",ar),Wo(document,"mousemove",ar),Wo(document,"touchmove",ar)},_offUpEvents:function(){var e=this.el.ownerDocument;Wo(e,"mouseup",this._onDrop),Wo(e,"touchend",this._onDrop),Wo(e,"pointerup",this._onDrop),Wo(e,"touchcancel",this._onDrop),Wo(document,"selectstart",this)},_onDrop:function(e){var t=this.el,l=this.options;Sa=ea(xa),Ba=ea(xa,l.draggable),ma("drop",this,{evt:e}),ga=xa&&xa.parentNode,Sa=ea(xa),Ba=ea(xa,l.draggable),nr.eventCanceled||(Ya=!1,Oa=!1,Pa=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),fr(this.cloneId),fr(this._dragStartId),this.nativeDraggable&&(Wo(document,"drop",this),Wo(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Io&&Lo(document.body,"user-select",""),Lo(xa,"transform",""),e&&(Aa&&(e.cancelable&&e.preventDefault(),!l.dropBubble&&e.stopPropagation()),ba&&ba.parentNode&&ba.parentNode.removeChild(ba),(ya===ga||Ta&&"clone"!==Ta.lastPutMode)&&ka&&ka.parentNode&&ka.parentNode.removeChild(ka),xa&&(this.nativeDraggable&&Wo(xa,"dragend",this),sr(xa),xa.style["will-change"]="",Aa&&!Ya&&Oo(xa,Ta?Ta.options.ghostClass:this.options.ghostClass,!1),Oo(xa,this.options.chosenClass,!1),va({sortable:this,name:"unchoose",toEl:ga,newIndex:null,newDraggableIndex:null,originalEvent:e}),ya!==ga?(Sa>=0&&(va({rootEl:ga,name:"add",toEl:ga,fromEl:ya,originalEvent:e}),va({sortable:this,name:"remove",toEl:ga,originalEvent:e}),va({rootEl:ga,name:"sort",toEl:ga,fromEl:ya,originalEvent:e}),va({sortable:this,name:"sort",toEl:ga,originalEvent:e})),Ta&&Ta.save()):Sa!==Ca&&Sa>=0&&(va({sortable:this,name:"update",toEl:ga,originalEvent:e}),va({sortable:this,name:"sort",toEl:ga,originalEvent:e})),nr.active&&(null!=Sa&&-1!==Sa||(Sa=Ca,Ba=Ra),va({sortable:this,name:"end",toEl:ga,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){ma("nulling",this),ya=xa=ga=ba=ha=ka=_a=wa=za=$a=Aa=Sa=Ba=Ca=Ra=ja=Na=Ta=Fa=nr.dragged=nr.ghost=nr.clone=nr.active=null,qa.forEach((function(e){e.checked=!0})),qa.length=Da=Ea=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":xa&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move");e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],l=this.el.children,o=0,a=l.length,r=this.options;o<a;o++)Ho(e=l[o],r.draggable,this.el,!1)&&t.push(e.getAttribute(r.dataIdAttr)||ur(e));return t},sort:function(e,t){var l={},o=this.el;this.toArray().forEach((function(e,t){var a=o.children[t];Ho(a,this.options.draggable,o,!1)&&(l[e]=a)}),this),t&&this.captureAnimationState(),e.forEach((function(e){l[e]&&(o.removeChild(l[e]),o.appendChild(l[e]))})),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return Ho(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var l=this.options;if(void 0===t)return l[e];var o=fa.modifyOption(this,e,t);l[e]=void 0!==o?o:t,"group"===e&&tr(l)},destroy:function(){ma("destroy",this);var e=this.el;e[sa]=null,Wo(e,"mousedown",this._onTapStart),Wo(e,"touchstart",this._onTapStart),Wo(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(Wo(e,"dragover",this),Wo(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),Ua.splice(Ua.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!wa){if(ma("hideClone",this),nr.eventCanceled)return;Lo(ka,"display","none"),this.options.removeCloneOnHide&&ka.parentNode&&ka.parentNode.removeChild(ka),wa=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(wa){if(ma("showClone",this),nr.eventCanceled)return;xa.parentNode!=ya||this.options.group.revertClone?ha?ya.insertBefore(ka,ha):ya.appendChild(ka):ya.insertBefore(ka,xa),this.options.group.revertClone&&this.animate(xa,ka),Lo(ka,"display",""),wa=!1}}else this._hideClone()}},Xa&&No(document,"touchmove",(function(e){(nr.active||Ya)&&e.cancelable&&e.preventDefault()})),nr.utils={on:No,off:Wo,css:Lo,find:qo,is:function(e,t){return!!Ho(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var l in t)t.hasOwnProperty(l)&&(e[l]=t[l]);return e},throttle:aa,closest:Ho,toggleClass:Oo,clone:na,index:ea,nextTick:cr,cancelNextTick:fr,detectDirection:er,getChild:Qo},nr.get=function(e){return e[sa]},nr.mount=function(){for(var e=arguments.length,t=new Array(e),l=0;l<e;l++)t[l]=arguments[l];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(nr.utils=So(So({},nr.utils),e.utils)),fa.mount(e)}))},nr.create=function(e,t){return new nr(e,t)},nr.version="1.15.2";var pr,mr,vr,xr,gr,br,yr=[],hr=!1;function _r(){yr.forEach((function(e){clearInterval(e.pid)})),yr=[]}function kr(){clearInterval(br)}var wr=aa((function(e,t,l,o){if(t.scroll){var a,r=(e.touches?e.touches[0]:e).clientX,n=(e.touches?e.touches[0]:e).clientY,i=t.scrollSensitivity,s=t.scrollSpeed,d=Xo(),u=!1;mr!==l&&(mr=l,_r(),pr=t.scroll,a=t.scrollFn,!0===pr&&(pr=la(l,!0)));var c=0,f=pr;do{var p=f,m=Zo(p),v=m.top,x=m.bottom,g=m.left,b=m.right,y=m.width,h=m.height,_=void 0,k=void 0,w=p.scrollWidth,C=p.scrollHeight,S=Lo(p),R=p.scrollLeft,B=p.scrollTop;p===d?(_=y<w&&("auto"===S.overflowX||"scroll"===S.overflowX||"visible"===S.overflowX),k=h<C&&("auto"===S.overflowY||"scroll"===S.overflowY||"visible"===S.overflowY)):(_=y<w&&("auto"===S.overflowX||"scroll"===S.overflowX),k=h<C&&("auto"===S.overflowY||"scroll"===S.overflowY));var F=_&&(Math.abs(b-r)<=i&&R+y<w)-(Math.abs(g-r)<=i&&!!R),T=k&&(Math.abs(x-n)<=i&&B+h<C)-(Math.abs(v-n)<=i&&!!B);if(!yr[c])for(var z=0;z<=c;z++)yr[z]||(yr[z]={});yr[c].vx==F&&yr[c].vy==T&&yr[c].el===p||(yr[c].el=p,yr[c].vx=F,yr[c].vy=T,clearInterval(yr[c].pid),0==F&&0==T||(u=!0,yr[c].pid=setInterval(function(){o&&0===this.layer&&nr.active._onTouchMove(gr);var t=yr[this.layer].vy?yr[this.layer].vy*s:0,l=yr[this.layer].vx?yr[this.layer].vx*s:0;"function"==typeof a&&"continue"!==a.call(nr.dragged.parentNode[sa],l,t,e,gr,yr[this.layer].el)||ra(yr[this.layer].el,l,t)}.bind({layer:c}),24))),c++}while(t.bubbleScroll&&f!==d&&(f=la(f,!1)));hr=u}}),30),Cr=function(e){var t=e.originalEvent,l=e.putSortable,o=e.dragEl,a=e.activeSortable,r=e.dispatchSortableEvent,n=e.hideGhostForTarget,i=e.unhideGhostForTarget;if(t){var s=l||a;n();var d=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(d.clientX,d.clientY);i(),s&&!s.el.contains(u)&&(r("spill"),this.onSpill({dragEl:o,putSortable:l}))}};function Sr(){}function Rr(){}function Br(e={}){let t="";const l=u({}),o=i(),a=u(["fixed","top_fixed","right_fixed","bottom_fixed","left_fixed"]),r=s((()=>"decorate"==o.mode?o:e.data)),n=u(!1);Ae((()=>{n.value=!1})),je((()=>{n.value=!0}));return{scrollV:u().value,data:r.value,componentsScrollBool:l.value,placeholderEvent:()=>{},refresh:()=>{p((()=>{let t=null,l=()=>{let l=!1;try{e.getFormRef(),l=!0}catch(n){l=!1}var a;return l&&(o.componentRefs=e.getFormRef().componentRefs,r.value.componentRefs=e.getFormRef().componentRefs,null==(a=e.getFormRef().componentRefs.topTabbarRef)||a.refresh(),t&&clearInterval(t)),l};l()||(t=setInterval((()=>{l()}),100))}))},isShowPlaceHolder:(e,t)=>{if("decorate"==o.mode){let l=document.getElementById("componentList");if(l&&l.children.length&&l.children[e]){let o=l.children[e].offsetHeight,a=0;if(t.margin.top<0&&(a=2*t.margin.top*-1,a>o))return!1}return!0}return!1},getComponentClass:(e,t)=>{let l={relative:!0,selected:o.currentIndex==e,decorate:"decorate"==o.mode};return l["top-fixed-"+o.topFixedStatus]=!0,t.position&&-1!=a.value.indexOf(t.position)?l["ignore-draggable-element"]=!0:l["draggable-element"]=!0,"ImageAds"==t.componentName&&(l["overflow-hidden"]=!0),l},onPageScroll:()=>{U((e=>{if(t&&!n.value)for(let o in t)e.scrollTop<=0?l.value[o]=-1:e.scrollTop>t[o]?l.value[o]=1:l.value[o]=2}))},onMounted:()=>{c((()=>{if("decorate"==o.mode){var e=document.getElementById("componentList");const t=nr.create(e,{draggable:".draggable-element",animation:200,onEnd:e=>{let l=o.value[e.oldIndex];o.value.splice(e.oldIndex,1),o.value.splice(e.newIndex,0,l),p((()=>{t.sort(Ht(o.value.length).map((e=>e.toString()))),o.postMessage(e.newIndex,o.value[e.newIndex])}))}})}p((()=>{setTimeout((()=>{if(t=uni.getStorageSync("componentsScrollValGroup"),t)for(let e in t)l.value[e]=-1}),500)}))}))}}}Sr.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,l=e.putSortable;this.sortable.captureAnimationState(),l&&l.captureAnimationState();var o=Qo(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(t,o):this.sortable.el.appendChild(t),this.sortable.animateAll(),l&&l.animateAll()},drop:Cr},Fo(Sr,{pluginName:"revertOnSpill"}),Rr.prototype={onSpill:function(e){var t=e.dragEl,l=e.putSortable||this.sortable;l.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),l.animateAll()},drop:Cr},Fo(Rr,{pluginName:"removeOnSpill"}),nr.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?No(document,"dragover",this._handleAutoScroll):this.options.supportPointer?No(document,"pointermove",this._handleFallbackAutoScroll):t.touches?No(document,"touchmove",this._handleFallbackAutoScroll):No(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?Wo(document,"dragover",this._handleAutoScroll):(Wo(document,"pointermove",this._handleFallbackAutoScroll),Wo(document,"touchmove",this._handleFallbackAutoScroll),Wo(document,"mousemove",this._handleFallbackAutoScroll)),kr(),_r(),clearTimeout(Uo),Uo=void 0},nulling:function(){gr=mr=pr=hr=br=vr=xr=null,yr.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var l=this,o=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,r=document.elementFromPoint(o,a);if(gr=e,t||this.options.forceAutoScrollFallback||Do||$o||Io){wr(e,this.options,r,t);var n=la(r,!0);!hr||br&&o===vr&&a===xr||(br&&kr(),br=setInterval((function(){var r=la(document.elementFromPoint(o,a),!0);r!==n&&(n=r,_r()),wr(e,l.options,r,t)}),10),vr=o,xr=a)}else{if(!this.options.bubbleScroll||la(r,!0)===Xo())return void _r();wr(e,this.options,la(r,!1),!1)}}},Fo(e,{pluginName:"scroll",initializeByDefault:!0})}),nr.mount(Rr,Sr);const Fr=We(n({__name:"index",props:["data"],setup(e,{expose:t}){const l=e,o=z(),a=()=>({componentRefs:o.refs}),r=i(),n=Br({...l,getFormRef:a}),s=u(n.data);return n.onMounted(),n.onPageScroll(),t({refresh:n.refresh,getFormRef:a}),(e,t)=>{const l=$,o=v(x("diy-active-cube"),Ut),a=v(x("diy-carousel-search"),Pt),i=v(x("diy-float-btn"),Ot),d=v(x("diy-form-address"),Lt),u=v(x("diy-form-checkbox"),qt),c=v(x("diy-form-date"),Tl),f=v(x("diy-form-date-scope"),zl),p=v(x("diy-form-email"),$l),m=v(x("diy-form-file"),Dl),w=v(x("diy-form-identity"),Il),C=v(x("diy-form-identity-privacy"),El),z=v(x("diy-form-image"),Ml),D=v(x("diy-form-input"),Al),E=v(x("diy-form-location"),jl),I=v(x("diy-form-mobile"),Wl),M=v(x("diy-form-number"),Vl),A=v(x("diy-form-privacy"),Nl),j=v(x("diy-form-privacy-pop"),Gt),W=v(x("diy-form-radio"),Yl),V=v(x("diy-form-submit"),Hl),Y=v(x("diy-form-table"),Ul),H=v(x("diy-form-textarea"),Pl),U=v(x("diy-form-time"),Ol),P=v(x("diy-form-time-scope"),Ll),O=v(x("diy-form-video"),Gl),L=v(x("diy-form-wechat-name"),ql),G=v(x("diy-graphic-nav"),Xl),q=v(x("diy-horz-blank"),Zl),X=v(x("diy-horz-line"),Jl),Z=v(x("diy-hot-area"),Ql),J=v(x("diy-image-ads"),Kl),Q=v(x("diy-member-info"),eo),K=v(x("diy-member-level"),to),ee=v(x("diy-notice"),lo),te=v(x("diy-picture-show"),oo),le=v(x("diy-rich-text"),ao),oe=v(x("diy-rubik-cube"),ro),ae=v(x("diy-text"),no),re=v(x("tabbar"),ot);return g(),b(l,{class:"diy-group",id:"componentList"},{default:y((()=>[s.value.global&&Object.keys(s.value.global).length&&s.value.global.topStatusBar&&s.value.global.topStatusBar.isShow?(g(),b(He,{key:0,scrollBool:k(n).componentsScrollBool.TopTabbar,ref:"topTabbarRef",data:s.value.global},null,8,["scrollBool","data"])):S("v-if",!0),s.value.global&&Object.keys(s.value.global).length&&s.value.global.popWindow&&s.value.global.popWindow.show?(g(),b(wo,{key:1,ref:"popAbsRef",data:s.value.global},null,8,["data"])):S("v-if",!0),(g(!0),R(B,null,F(s.value.value,((e,t)=>(g(),b(l,{key:e.id,onClick:l=>k(r).changeCurrentIndex(t,e),class:T(k(n).getComponentClass(t,e)),style:_(e.pageStyle)},{default:y((()=>[h(l,{class:"relative",style:_({marginTop:e.margin.top<0?2*e.margin.top+"rpx":"0"})},{default:y((()=>[S(" 装修模式下，设置负上边距后超出的内容，禁止选中设置 "),k(n).isShowPlaceHolder(t,e)?(g(),b(l,{key:0,class:"absolute w-full z-1",style:_({height:2*e.margin.top*-1+"rpx"}),onClick:N(k(n).placeholderEvent,["stop"])},null,8,["style","onClick"])):S("v-if",!0),"ActiveCube"==e.componentName?(g(),b(o,{key:1,ref_for:!0,ref:"diyActiveCubeRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ActiveCube},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"CarouselSearch"==e.componentName?(g(),b(a,{key:2,ref_for:!0,ref:"diyCarouselSearchRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.CarouselSearch},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FloatBtn"==e.componentName?(g(),b(i,{key:3,ref_for:!0,ref:"diyFloatBtnRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FloatBtn},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormAddress"==e.componentName?(g(),b(d,{key:4,ref_for:!0,ref:"diyFormAddressRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormAddress},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormCheckbox"==e.componentName?(g(),b(u,{key:5,ref_for:!0,ref:"diyFormCheckboxRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormCheckbox},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormDate"==e.componentName?(g(),b(c,{key:6,ref_for:!0,ref:"diyFormDateRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormDate},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormDateScope"==e.componentName?(g(),b(f,{key:7,ref_for:!0,ref:"diyFormDateScopeRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormDateScope},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormEmail"==e.componentName?(g(),b(p,{key:8,ref_for:!0,ref:"diyFormEmailRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormEmail},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormFile"==e.componentName?(g(),b(m,{key:9,ref_for:!0,ref:"diyFormFileRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormFile},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormIdentity"==e.componentName?(g(),b(w,{key:10,ref_for:!0,ref:"diyFormIdentityRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormIdentity},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormIdentityPrivacy"==e.componentName?(g(),b(C,{key:11,ref_for:!0,ref:"diyFormIdentityPrivacyRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormIdentityPrivacy},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormImage"==e.componentName?(g(),b(z,{key:12,ref_for:!0,ref:"diyFormImageRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormImage},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormInput"==e.componentName?(g(),b(D,{key:13,ref_for:!0,ref:"diyFormInputRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormInput},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormLocation"==e.componentName?(g(),b(E,{key:14,ref_for:!0,ref:"diyFormLocationRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormLocation},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormMobile"==e.componentName?(g(),b(I,{key:15,ref_for:!0,ref:"diyFormMobileRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormMobile},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormNumber"==e.componentName?(g(),b(M,{key:16,ref_for:!0,ref:"diyFormNumberRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormNumber},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormPrivacy"==e.componentName?(g(),b(A,{key:17,ref_for:!0,ref:"diyFormPrivacyRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormPrivacy},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormPrivacyPop"==e.componentName?(g(),b(j,{key:18,ref_for:!0,ref:"diyFormPrivacyPopRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormPrivacyPop},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormRadio"==e.componentName?(g(),b(W,{key:19,ref_for:!0,ref:"diyFormRadioRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormRadio},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormSubmit"==e.componentName?(g(),b(V,{key:20,ref_for:!0,ref:"diyFormSubmitRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormSubmit},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTable"==e.componentName?(g(),b(Y,{key:21,ref_for:!0,ref:"diyFormTableRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTable},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTextarea"==e.componentName?(g(),b(H,{key:22,ref_for:!0,ref:"diyFormTextareaRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTextarea},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTime"==e.componentName?(g(),b(U,{key:23,ref_for:!0,ref:"diyFormTimeRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTime},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormTimeScope"==e.componentName?(g(),b(P,{key:24,ref_for:!0,ref:"diyFormTimeScopeRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormTimeScope},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormVideo"==e.componentName?(g(),b(O,{key:25,ref_for:!0,ref:"diyFormVideoRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormVideo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"FormWechatName"==e.componentName?(g(),b(L,{key:26,ref_for:!0,ref:"diyFormWechatNameRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.FormWechatName},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"GraphicNav"==e.componentName?(g(),b(G,{key:27,ref_for:!0,ref:"diyGraphicNavRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.GraphicNav},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"HorzBlank"==e.componentName?(g(),b(q,{key:28,ref_for:!0,ref:"diyHorzBlankRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.HorzBlank},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"HorzLine"==e.componentName?(g(),b(X,{key:29,ref_for:!0,ref:"diyHorzLineRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.HorzLine},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"HotArea"==e.componentName?(g(),b(Z,{key:30,ref_for:!0,ref:"diyHotAreaRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.HotArea},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ImageAds"==e.componentName?(g(),b(J,{key:31,ref_for:!0,ref:"diyImageAdsRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ImageAds},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"MemberInfo"==e.componentName?(g(),b(Q,{key:32,ref_for:!0,ref:"diyMemberInfoRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.MemberInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"MemberLevel"==e.componentName?(g(),b(K,{key:33,ref_for:!0,ref:"diyMemberLevelRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.MemberLevel},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"Notice"==e.componentName?(g(),b(ee,{key:34,ref_for:!0,ref:"diyNoticeRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.Notice},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"PictureShow"==e.componentName?(g(),b(te,{key:35,ref_for:!0,ref:"diyPictureShowRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.PictureShow},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"RichText"==e.componentName?(g(),b(le,{key:36,ref_for:!0,ref:"diyRichTextRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.RichText},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"RubikCube"==e.componentName?(g(),b(oe,{key:37,ref_for:!0,ref:"diyRubikCubeRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.RubikCube},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"Text"==e.componentName?(g(),b(ae,{key:38,ref_for:!0,ref:"diyTextRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.Text},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"NiucrowdBanner"==e.componentName?(g(),b(io,{key:39,ref_for:!0,ref:"diyNiucrowdBannerRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.NiucrowdBanner},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"NiucrowdCategory"==e.componentName?(g(),b(so,{key:40,ref_for:!0,ref:"diyNiucrowdCategoryRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.NiucrowdCategory},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"NiucrowdProject"==e.componentName?(g(),b(uo,{key:41,ref_for:!0,ref:"diyNiucrowdProjectRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.NiucrowdProject},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"NiucrowdSearch"==e.componentName?(g(),b(co,{key:42,ref_for:!0,ref:"diyNiucrowdSearchRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.NiucrowdSearch},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"GoodsCoupon"==e.componentName?(g(),b(fo,{key:43,ref_for:!0,ref:"diyGoodsCouponRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.GoodsCoupon},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"GoodsList"==e.componentName?(g(),b(ut,{key:44,ref_for:!0,ref:"diyGoodsListRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.GoodsList},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ManyGoodsList"==e.componentName?(g(),b(po,{key:45,ref_for:!0,ref:"diyManyGoodsListRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ManyGoodsList},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopExchangeGoods"==e.componentName?(g(),b(mo,{key:46,ref_for:!0,ref:"diyShopExchangeGoodsRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopExchangeGoods},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopExchangeInfo"==e.componentName?(g(),b(vo,{key:47,ref_for:!0,ref:"diyShopExchangeInfoRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopExchangeInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopGoodsRanking"==e.componentName?(g(),b(xo,{key:48,ref_for:!0,ref:"diyShopGoodsRankingRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopGoodsRanking},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopGoodsRecommend"==e.componentName?(g(),b(go,{key:49,ref_for:!0,ref:"diyShopGoodsRecommendRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopGoodsRecommend},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopMemberInfo"==e.componentName?(g(),b(bo,{key:50,ref_for:!0,ref:"diyShopMemberInfoRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopMemberInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopNewcomer"==e.componentName?(g(),b(yo,{key:51,ref_for:!0,ref:"diyShopNewcomerRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopNewcomer},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopOrderInfo"==e.componentName?(g(),b(ho,{key:52,ref_for:!0,ref:"diyShopOrderInfoRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopOrderInfo},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"ShopSearch"==e.componentName?(g(),b(_o,{key:53,ref_for:!0,ref:"diyShopSearchRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.ShopSearch},null,8,["component","global","index","scrollBool"])):S("v-if",!0),"SingleRecommend"==e.componentName?(g(),b(ko,{key:54,ref_for:!0,ref:"diySingleRecommendRef",component:e,global:s.value.global,index:t,scrollBool:k(n).componentsScrollBool.SingleRecommend},null,8,["component","global","index","scrollBool"])):S("v-if",!0)])),_:2},1032,["style"])])),_:2},1032,["onClick","class","style"])))),128)),""==k(r).mode&&s.value.global&&s.value.global.bottomTabBarSwitch?(g(),R(B,{key:2},[h(l,{class:"pt-[20rpx]"}),h(re)],64)):S("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-03fc771b"]]);export{Fl as _,Fr as d};

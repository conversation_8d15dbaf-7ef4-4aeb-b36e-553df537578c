import{L as e,y as t,aw as a,b8 as s,o,c as r,w as l,k as i,b as n,A as d,g as c,R as p,a3 as u,S as m,B as f,f as _,v as h,aT as g,i as y,j as b,I as w,aH as j,aI as I,bb as k,ag as L,ay as v,T as F}from"./index-dd56d0cc.js";import{_ as x}from"./u-loading-icon.f15d7447.js";import{_ as S}from"./u-image.dfca355c.js";import{_ as T}from"./u-tag.9d3e1313.js";import{_ as C}from"./u-line-progress.9dea5db3.js";import{_ as A}from"./u-avatar.ea828bd7.js";import{_ as M}from"./u-icon.5895f8fc.js";import{_ as U}from"./u-tabs.58e465c6.js";import{_ as V}from"./u-empty.354f2b69.js";import{_ as P}from"./u-button.b6743e99.js";import{_ as R}from"./u-checkbox.e4ea7913.js";import{_ as $}from"./u-popup.457e1f1f.js";import{_ as D}from"./pay.1561f7e1.js";import{d as z,e as O,f as J,h as N,i as q,j as B}from"./project.e6204607.js";import{c as E,d as H,r as Q,h as Y}from"./auth.d5d24670.js";import{_ as G}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-text.1f240d34.js";/* empty css                                                               */import"./u-badge.206da3ef.js";import"./u-safe-bottom.22d4d63b.js";import"./pay.5385ae25.js";const K=G({components:{Pay:D},data:()=>({projectId:0,projectInfo:{},rewardList:[],supportList:[],updateList:[],memberInfo:{},loading:!0,rewardLoading:!1,supportLoading:!1,updateLoading:!1,showSupportModal:!1,currentTab:0,tabList:[{name:"项目详情"},{name:"奖励档位"},{name:"项目动态"},{name:"支持记录"}],supportForm:{reward_id:0,amount:0,message:"",is_anonymous:!1,contact_name:"",contact_mobile:""}}),computed:{imageList(){return this.projectInfo.images?Array.isArray(this.projectInfo.images)?this.projectInfo.images.filter((e=>e)):this.projectInfo.images.split(",").filter((e=>e)):[]}},onLoad(e){this.projectId=parseInt(e.id),this.loadProjectInfo(),this.loadRewardList(),this.loadUpdateList(),this.loadSupportList(),E()&&this.loadMemberInfo()},onShow(){E()&&!this.memberInfo.member_id&&this.loadMemberInfo()},methods:{handleSupportClick(){console.log("点击立即支持按钮"),H(),console.log("当前登录状态:",E()),Q((()=>{console.log("登录验证通过，显示支持弹窗"),console.log("设置前 showSupportModal:",this.showSupportModal),this.showSupportModal=!0,console.log("设置后 showSupportModal:",this.showSupportModal),this.$forceUpdate(),setTimeout((()=>{console.log("延迟检查 showSupportModal:",this.showSupportModal)}),100)}),"请先登录后再支持项目")},async loadMemberInfo(){try{const e=uni.getStorageSync("wap_member_info");if(e)return this.memberInfo=e,console.log("从本地存储获取用户信息:",this.memberInfo),void this.fillContactInfo();const t=await z();this.memberInfo=t.data,console.log("从API获取用户信息:",this.memberInfo),this.fillContactInfo()}catch(e){if(console.error("获取用户信息失败:",e),Y(e,(()=>this.loadMemberInfo())))return}},fillContactInfo(){this.memberInfo&&(this.supportForm.contact_name=this.memberInfo.nickname||this.memberInfo.username||"",this.supportForm.contact_mobile=this.memberInfo.mobile||"")},async loadProjectInfo(){try{const t=await O(this.projectId);this.projectInfo=t.data,e({title:this.projectInfo.project_name})}catch(a){console.error(a),t({title:"加载失败",icon:"none"})}finally{this.loading=!1}},async loadRewardList(){this.rewardLoading=!0;try{console.log("开始加载奖励档位，项目ID:",this.projectId);const e=await J(this.projectId);console.log("奖励档位API响应:",e),e&&e.data?(this.rewardList=Array.isArray(e.data)?e.data:[],console.log("奖励档位数据:",this.rewardList),console.log("奖励档位数量:",this.rewardList.length),console.log("第一个奖励档位:",this.rewardList[0]),console.log("rewardList 赋值后:",JSON.stringify(this.rewardList)),this.$forceUpdate(),setTimeout((()=>{this.$forceUpdate(),console.log("延迟强制更新后 rewardList:",this.rewardList.length)}),100)):(this.rewardList=[],console.warn("奖励档位API返回数据格式异常:",e))}catch(e){console.error("加载奖励档位失败:",e),this.rewardList=[],t({title:"加载奖励档位失败",icon:"none"})}finally{this.rewardLoading=!1}},async loadUpdateList(){this.updateLoading=!0;try{console.log("开始加载项目动态，项目ID:",this.projectId);const e=await N(this.projectId,{limit:20});console.log("项目动态API响应:",e),e&&e.data&&e.data.data?this.updateList=Array.isArray(e.data.data)?e.data.data:[]:(this.updateList=[],console.warn("项目动态API返回数据格式异常:",e))}catch(e){console.error("加载项目动态失败:",e),this.updateList=[]}finally{this.updateLoading=!1}},async loadSupportList(){this.supportLoading=!0;try{console.log("开始加载支持记录，项目ID:",this.projectId);const e=await q({project_id:this.projectId,limit:20});console.log("支持记录API响应:",e),e&&e.data&&e.data.data?this.supportList=Array.isArray(e.data.data)?e.data.data:[]:(this.supportList=[],console.warn("支持记录API返回数据格式异常:",e))}catch(e){console.error("加载支持记录失败:",e),this.supportList=[]}finally{this.supportLoading=!1}},handleTabChange(e){console.log("切换标签页:",e),console.log("当前奖励档位数量:",this.rewardList.length),console.log("当前支持记录数量:",this.supportList.length),this.currentTab=e,this.$nextTick((()=>{this.$forceUpdate()})),1===e&&0===this.rewardList.length?this.loadRewardList():2===e&&0===this.updateList.length?this.loadUpdateList():3===e&&0===this.supportList.length&&this.loadSupportList()},selectReward(e){this.supportForm.reward_id=e.reward_id,this.supportForm.amount=e.amount,this.showSupportModal=!0},selectRewardOption(e){this.supportForm.reward_id=e.reward_id,this.supportForm.amount=e.amount},async handleSupport(){if(!this.supportForm.amount||this.supportForm.amount<=0)return void t({title:"请输入正确的支持金额",icon:"none"});if(!this.supportForm.contact_mobile)return void t({title:"请输入联系人手机号",icon:"none"});if(/^1[3-9]\d{9}$/.test(this.supportForm.contact_mobile))if(this.memberInfo.member_id){this.supportLoading=!0;try{const e={project_id:this.projectId,reward_id:this.supportForm.reward_id,amount:this.supportForm.amount,message:this.supportForm.message,is_anonymous:this.supportForm.is_anonymous?1:0,contact_name:this.supportForm.contact_name||this.memberInfo.nickname||this.memberInfo.username||"用户",contact_mobile:this.supportForm.contact_mobile,contact_address:this.memberInfo.address||""},t=await B(e);this.showSupportModal=!1,console.log("准备调用支付组件:",{trade_type:t.data.trade_type,trade_id:t.data.trade_id,return_url:"/addon/niucrowd/pages/member/support-list"}),uni.setStorageSync("payReturn",encodeURIComponent("/addon/niucrowd/pages/member/supports")),console.log("手动设置payReturn:",uni.getStorageSync("payReturn")),this.$refs.payRef.open(t.data.trade_type,t.data.trade_id,"/addon/niucrowd/pages/member/supports"),this.loadProjectInfo(),this.loadUpdateList(),this.loadSupportList()}catch(e){if(console.error(e),Y(e,(()=>this.handleSupport())))return;t({title:e.message||"支持失败",icon:"none"})}finally{this.supportLoading=!1}}else t({title:"请先登录",icon:"none"});else t({title:"请输入正确的手机号格式",icon:"none"})},getStatusType:e=>["warning","primary","success","error","info"][e]||"info",formatTime:e=>new Date(1e3*e).toLocaleString(),formatJoinTime(e){if(!e)return"未知";const t=new Date(1e3*e);return`${t.getFullYear()}年${t.getMonth()+1}月加入`},getUpdateImages(e){if(!e)return[];try{if("string"==typeof e){const t=JSON.parse(e);return Array.isArray(t)?t.filter((e=>e)):[]}return Array.isArray(e)?e.filter((e=>e)):[]}catch(t){return console.error("解析项目动态图片失败:",t),[]}},previewImage(e,t){a({current:e,urls:t})},viewPublisherProfile(){this.projectInfo.member&&this.projectInfo.member.member_id?s({url:`/addon/niucrowd/pages/member/profile?member_id=${this.projectInfo.member.member_id}`}).catch((()=>{t({title:"发布人主页暂未开放",icon:"none"})})):t({title:"发布人信息不存在",icon:"none"})}}},[["render",function(e,t,a,s,z,O){const J=y(b("u-loading-icon"),x),N=w,q=i,B=y(b("u-image"),S),E=j,H=I,Q=y(b("u-tag"),T),Y=y(b("u-line-progress"),C),G=y(b("u-avatar"),A),K=y(b("u-icon"),M),W=y(b("u-tabs"),U),X=k,Z=y(b("u-empty"),V),ee=y(b("u-button"),P),te=L,ae=v,se=y(b("u-checkbox"),R),oe=y(b("u-popup"),$),re=y(b("pay"),D);return o(),r(q,{class:"project-detail"},{default:l((()=>[z.loading?(o(),r(q,{key:0,class:"loading-wrap"},{default:l((()=>[n(J),n(N,{class:"loading-text"},{default:l((()=>[d("加载中...")])),_:1})])),_:1})):z.projectInfo.project_id?(o(),r(q,{key:1,class:"detail-content"},{default:l((()=>[c(" 项目头图 "),n(q,{class:"project-header"},{default:l((()=>[n(H,{class:"project-swiper","indicator-dots":"",autoplay:""},{default:l((()=>[z.projectInfo.cover_image?(o(),r(E,{key:0},{default:l((()=>[n(B,{src:z.projectInfo.cover_image,width:"100%",height:"400rpx",mode:"aspectFill"},null,8,["src"])])),_:1})):c("v-if",!0),(o(!0),p(m,null,u(O.imageList,((e,t)=>(o(),r(E,{key:t},{default:l((()=>[n(B,{src:e,width:"100%",height:"400rpx",mode:"aspectFill"},null,8,["src"])])),_:2},1024)))),128))])),_:1}),n(q,{class:"project-status"},{default:l((()=>[n(Q,{text:z.projectInfo.status_name,type:O.getStatusType(z.projectInfo.status)},null,8,["text","type"])])),_:1})])),_:1}),c(" 项目信息 "),n(q,{class:"project-info"},{default:l((()=>[n(q,{class:"project-title"},{default:l((()=>[d(f(z.projectInfo.project_name),1)])),_:1}),n(q,{class:"project-desc"},{default:l((()=>[d(f(z.projectInfo.project_desc),1)])),_:1}),c(" 进度条 "),n(q,{class:"progress-section"},{default:l((()=>[n(q,{class:"progress-bar"},{default:l((()=>[n(Y,{percentage:z.projectInfo.progress_percent,"show-percent":!1,height:"12","active-color":"#007aff"},null,8,["percentage"])])),_:1}),n(q,{class:"progress-text"},{default:l((()=>[d(f(z.projectInfo.progress_percent)+"%",1)])),_:1})])),_:1}),c(" 统计数据 "),n(q,{class:"stats-grid"},{default:l((()=>[n(q,{class:"stat-card"},{default:l((()=>[n(q,{class:"stat-value"},{default:l((()=>[d("¥"+f(z.projectInfo.current_amount),1)])),_:1}),n(q,{class:"stat-label"},{default:l((()=>[d("已筹金额")])),_:1})])),_:1}),n(q,{class:"stat-card"},{default:l((()=>[n(q,{class:"stat-value"},{default:l((()=>[d("¥"+f(z.projectInfo.target_amount),1)])),_:1}),n(q,{class:"stat-label"},{default:l((()=>[d("目标金额")])),_:1})])),_:1}),n(q,{class:"stat-card"},{default:l((()=>[n(q,{class:"stat-value"},{default:l((()=>[d(f(z.projectInfo.support_count),1)])),_:1}),n(q,{class:"stat-label"},{default:l((()=>[d("支持人数")])),_:1})])),_:1}),n(q,{class:"stat-card"},{default:l((()=>[n(q,{class:"stat-value"},{default:l((()=>[d(f(z.projectInfo.remain_days),1)])),_:1}),n(q,{class:"stat-label"},{default:l((()=>[d("剩余天数")])),_:1})])),_:1})])),_:1})])),_:1}),c(" 发布人信息 "),z.projectInfo.member?(o(),r(q,{key:0,class:"publisher-info"},{default:l((()=>[n(q,{class:"publisher-header"},{default:l((()=>[n(q,{class:"publisher-avatar"},{default:l((()=>[n(G,{src:z.projectInfo.member.headimg||"/static/images/default-avatar.png",size:"60",shape:"circle"},null,8,["src"])])),_:1}),n(q,{class:"publisher-details"},{default:l((()=>[n(q,{class:"publisher-name"},{default:l((()=>[n(N,{class:"name"},{default:l((()=>[d(f(z.projectInfo.member.display_name),1)])),_:1}),z.projectInfo.member.is_verified?(o(),r(q,{key:0,class:"verified-badge"},{default:l((()=>[n(K,{name:"checkmark-circle-fill",color:"#52c41a",size:"16"}),n(N,{class:"verified-text"},{default:l((()=>[d("已认证")])),_:1})])),_:1})):(o(),r(q,{key:1,class:"unverified-badge"},{default:l((()=>[n(K,{name:"info-circle",color:"#faad14",size:"16"}),n(N,{class:"unverified-text"},{default:l((()=>[d(f(z.projectInfo.member.auth_status_text),1)])),_:1})])),_:1}))])),_:1}),n(q,{class:"publisher-meta"},{default:l((()=>[n(N,{class:"join-time"},{default:l((()=>[d("项目发布人")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):c("v-if",!0),c(" 标签页 "),n(q,{class:"tabs-section"},{default:l((()=>[n(W,{list:z.tabList,modelValue:z.currentTab,"onUpdate:modelValue":t[0]||(t[0]=e=>z.currentTab=e),onChange:O.handleTabChange,scrollable:!1},null,8,["list","modelValue","onChange"]),n(q,{class:"tab-content"},{default:l((()=>[c(" 项目详情 "),_((o(),r(q,{class:"tab-panel",key:"detail-"+z.projectInfo.project_id},{default:l((()=>[n(q,{class:"detail-section"},{default:l((()=>[n(q,{class:"section-title"},{default:l((()=>[d("项目详情")])),_:1}),z.projectInfo.project_content?(o(),r(X,{nodes:z.projectInfo.project_content,key:"content-"+z.projectInfo.project_id},null,8,["nodes"])):(o(),r(q,{key:1,class:"empty-content"},{default:l((()=>[d("暂无项目详情")])),_:1}))])),_:1}),z.projectInfo.reward_description?(o(),r(q,{key:0,class:"detail-section"},{default:l((()=>[n(q,{class:"section-title"},{default:l((()=>[d("回报说明")])),_:1}),(o(),r(X,{nodes:z.projectInfo.reward_description,key:"reward-desc-"+z.projectInfo.project_id},null,8,["nodes"]))])),_:1})):c("v-if",!0)])),_:1})),[[h,0===z.currentTab.index||0===z.currentTab]]),c(" 奖励档位 "),_((o(),r(q,{class:"tab-panel",key:"reward-"+z.projectInfo.project_id},{default:l((()=>[z.rewardLoading?(o(),r(q,{key:0,class:"loading-wrap"},{default:l((()=>[n(J),n(N,{class:"loading-text"},{default:l((()=>[d("加载奖励档位中...")])),_:1})])),_:1})):0===z.rewardList.length?(o(),r(q,{key:1,class:"empty-wrap"},{default:l((()=>[n(Z,{text:"暂无奖励档位"})])),_:1})):(o(),r(q,{key:2,class:"reward-list"},{default:l((()=>[(o(!0),p(m,null,u(z.rewardList,(e=>(o(),r(q,{key:e.reward_id,class:"reward-card",onClick:t=>O.selectReward(e)},{default:l((()=>[n(q,{class:"reward-header"},{default:l((()=>[n(q,{class:"reward-amount"},{default:l((()=>[d("¥"+f(e.amount),1)])),_:2},1024),n(q,{class:"reward-stock"},{default:l((()=>[e.stock>0?(o(),r(N,{key:0},{default:l((()=>[d("剩余"+f(e.remain_stock||e.stock),1)])),_:2},1024)):(o(),r(N,{key:1},{default:l((()=>[d("无限制")])),_:1}))])),_:2},1024)])),_:2},1024),n(q,{class:"reward-name"},{default:l((()=>[d(f(e.reward_name),1)])),_:2},1024),n(q,{class:"reward-desc"},{default:l((()=>[d(f(e.reward_desc),1)])),_:2},1024),n(q,{class:"reward-delivery"},{default:l((()=>[d("预计发货："+f(e.delivery_time),1)])),_:2},1024),n(q,{class:"reward-support"},{default:l((()=>[d(f(e.support_count)+"人支持",1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1})),[[h,1===z.currentTab.index||1===z.currentTab]]),c(" 项目动态 "),_((o(),r(q,{class:"tab-panel",key:"update-"+z.projectInfo.project_id},{default:l((()=>[z.updateLoading?(o(),r(q,{key:0,class:"loading-wrap"},{default:l((()=>[n(J),n(N,{class:"loading-text"},{default:l((()=>[d("加载项目动态中...")])),_:1})])),_:1})):0===z.updateList.length?(o(),r(q,{key:1,class:"empty-wrap"},{default:l((()=>[n(Z,{text:"暂无项目动态"})])),_:1})):(o(),r(q,{key:2,class:"update-list"},{default:l((()=>[(o(!0),p(m,null,u(z.updateList,(e=>(o(),r(q,{key:e.update_id,class:"update-card"},{default:l((()=>[n(q,{class:"update-header"},{default:l((()=>[n(q,{class:"update-title"},{default:l((()=>[d(f(e.title),1)])),_:2},1024),n(q,{class:"update-time"},{default:l((()=>[d(f(O.formatTime(e.create_time)),1)])),_:2},1024)])),_:2},1024),n(q,{class:"update-content"},{default:l((()=>[e.content?(o(),r(X,{nodes:e.content,key:"update-content-"+e.update_id},null,8,["nodes"])):c("v-if",!0)])),_:2},1024),e.images&&O.getUpdateImages(e.images).length>0?(o(),r(q,{key:0,class:"update-images"},{default:l((()=>[n(q,{class:"image-grid"},{default:l((()=>[(o(!0),p(m,null,u(O.getUpdateImages(e.images),((t,a)=>(o(),r(q,{key:a,class:"image-item",onClick:a=>O.previewImage(t,O.getUpdateImages(e.images))},{default:l((()=>[n(B,{src:t,width:"200rpx",height:"200rpx",mode:"aspectFill","border-radius":"8"},null,8,["src"])])),_:2},1032,["onClick"])))),128))])),_:2},1024)])),_:2},1024)):c("v-if",!0),e.member?(o(),r(q,{key:1,class:"update-author"},{default:l((()=>[n(N,{class:"author-label"},{default:l((()=>[d("发布者：")])),_:1}),n(N,{class:"author-name"},{default:l((()=>[d(f(e.member.nickname||e.member.username||"项目方"),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)])),_:2},1024)))),128))])),_:1}))])),_:1})),[[h,2===z.currentTab.index||2===z.currentTab]]),c(" 支持记录 "),_((o(),r(q,{class:"tab-panel",key:"support-"+z.projectInfo.project_id},{default:l((()=>[z.supportLoading?(o(),r(q,{key:0,class:"loading-wrap"},{default:l((()=>[n(J),n(N,{class:"loading-text"},{default:l((()=>[d("加载支持记录中...")])),_:1})])),_:1})):0===z.supportList.length?(o(),r(q,{key:1,class:"empty-wrap"},{default:l((()=>[n(Z,{text:"暂无支持记录"})])),_:1})):(o(),r(q,{key:2,class:"support-list"},{default:l((()=>[(o(!0),p(m,null,u(z.supportList,(e=>(o(),r(q,{key:e.support_id,class:"support-item"},{default:l((()=>[n(q,{class:"support-header"},{default:l((()=>[n(q,{class:"supporter-name"},{default:l((()=>{var t;return[d(f(e.is_anonymous?"匿名用户":null==(t=e.member)?void 0:t.nickname),1)]})),_:2},1024),n(q,{class:"support-time"},{default:l((()=>[d(f(O.formatTime(e.create_time)),1)])),_:2},1024)])),_:2},1024),n(q,{class:"support-amount"},{default:l((()=>[d("支持了 ¥"+f(e.amount),1)])),_:2},1024),e.message?(o(),r(q,{key:0,class:"support-message"},{default:l((()=>[d(f(e.message),1)])),_:2},1024)):c("v-if",!0)])),_:2},1024)))),128))])),_:1}))])),_:1})),[[h,3===z.currentTab.index||3===z.currentTab]])])),_:1})])),_:1})])),_:1})):c("v-if",!0),c(" 底部操作栏 "),z.projectInfo.project_id&&1===z.projectInfo.status?(o(),r(q,{key:2,class:"bottom-bar"},{default:l((()=>[n(q,{class:"action-buttons"},{default:l((()=>[n(ee,{type:"primary",size:"large",onClick:O.handleSupportClick,disabled:1!==z.projectInfo.status},{default:l((()=>[d(" 立即支持 ")])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})):c("v-if",!0),c(" 支持弹窗 "),n(oe,{show:z.showSupportModal,onClose:t[8]||(t[8]=e=>z.showSupportModal=!1),mode:"bottom","border-radius":"20","safe-area-inset-bottom":!0},{default:l((()=>[n(q,{class:"support-modal"},{default:l((()=>[n(q,{class:"modal-header"},{default:l((()=>[n(q,{class:"modal-title"},{default:l((()=>[d("选择支持档位")])),_:1}),n(K,{name:"close",onClick:t[1]||(t[1]=e=>z.showSupportModal=!1)})])),_:1}),n(q,{class:"modal-content"},{default:l((()=>[0===z.rewardList.length?(o(),r(q,{key:0,class:"custom-support"},{default:l((()=>[n(q,{class:"support-title"},{default:l((()=>[d("自定义支持金额")])),_:1}),n(te,{modelValue:z.supportForm.amount,"onUpdate:modelValue":t[2]||(t[2]=e=>z.supportForm.amount=e),type:"number",placeholder:"请输入支持金额",class:"custom-input"},null,8,["modelValue"])])),_:1})):(o(),r(q,{key:1,class:"reward-options"},{default:l((()=>[(o(!0),p(m,null,u(z.rewardList,(e=>(o(),r(q,{key:e.reward_id,class:F(["reward-option",{selected:z.supportForm.reward_id===e.reward_id}]),onClick:t=>O.selectRewardOption(e)},{default:l((()=>[n(q,{class:"option-header"},{default:l((()=>[n(q,{class:"option-amount"},{default:l((()=>[d("¥"+f(e.amount),1)])),_:2},1024),z.supportForm.reward_id===e.reward_id?(o(),r(K,{key:0,name:"checkmark-circle-fill",color:"#007aff"})):c("v-if",!0)])),_:2},1024),n(q,{class:"option-name"},{default:l((()=>[d(f(e.reward_name),1)])),_:2},1024),n(q,{class:"option-desc"},{default:l((()=>[d(f(e.reward_desc),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})),n(q,{class:"support-form"},{default:l((()=>[n(q,{class:"form-item"},{default:l((()=>[n(q,{class:"form-label"},{default:l((()=>[d("联系手机号 *")])),_:1}),n(te,{modelValue:z.supportForm.contact_mobile,"onUpdate:modelValue":t[3]||(t[3]=e=>z.supportForm.contact_mobile=e),type:"number",placeholder:"请输入联系手机号",maxlength:"11",class:"custom-input"},null,8,["modelValue"])])),_:1}),n(q,{class:"form-item"},{default:l((()=>[n(q,{class:"form-label"},{default:l((()=>[d("联系人姓名")])),_:1}),n(te,{modelValue:z.supportForm.contact_name,"onUpdate:modelValue":t[4]||(t[4]=e=>z.supportForm.contact_name=e),placeholder:"请输入联系人姓名",maxlength:"20",class:"custom-input"},null,8,["modelValue"])])),_:1}),n(q,{class:"form-item"},{default:l((()=>[n(q,{class:"form-label"},{default:l((()=>[d("留言给项目方（可选）")])),_:1}),n(ae,{modelValue:z.supportForm.message,"onUpdate:modelValue":t[5]||(t[5]=e=>z.supportForm.message=e),placeholder:"留言给项目方（可选）",maxlength:"200",class:"custom-textarea"},null,8,["modelValue"])])),_:1}),n(q,{class:"form-row"},{default:l((()=>[n(se,{modelValue:z.supportForm.is_anonymous,"onUpdate:modelValue":t[6]||(t[6]=e=>z.supportForm.is_anonymous=e)},{default:l((()=>[d("匿名支持")])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(q,{class:"modal-footer"},{default:l((()=>[g("uni-button",{class:"primary-btn-bg btn",type:"primary",onClick:t[7]||(t[7]=(...e)=>O.handleSupport&&O.handleSupport(...e)),loading:z.supportLoading,disabled:!z.supportForm.amount||z.supportForm.amount<=0}," 确认支付 ¥"+f(z.supportForm.amount||0),9,["loading","disabled"])])),_:1})])),_:1})])),_:1},8,["show"]),c(" 支付组件 "),n(re,{ref:"payRef"},null,512)])),_:1})}],["__scopeId","data-v-fc3775e3"]]);export{K as default};

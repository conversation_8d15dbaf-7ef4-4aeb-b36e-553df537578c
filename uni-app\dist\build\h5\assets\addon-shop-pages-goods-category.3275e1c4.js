import{d as e,r as t,Q as o,o as l,c as r,w as a,e as s,b as i,C as d,g as c,R as n,a3 as u,S as p,T as x,a as f,I as g,ag as _,k as m,ap as v,i as b,j as y,A as h,B as k,E as w,G as C,l as j,p as S,f as I,v as F,n as N,ah as V,ai as B,y as M,H as R,J as z,b2 as E,s as $,z as U,L as T}from"./index-dd56d0cc.js";import{_ as H}from"./u--image.cd475bba.js";import{_ as L}from"./tabbar.0d5e534b.js";import{_ as O}from"./loading-page.vue_vue_type_script_setup_true_lang.c88f563e.js";import{d as P,e as D,p as A}from"./goods.dbf7b09d.js";import{M as J}from"./mescroll-empty.8630a00e.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import{M as W}from"./mescroll-body.520ba8e3.js";import{u as Y}from"./useMescroll.26ccf5de.js";import{u as q,a as G}from"./add-cart-popup.4746de5d.js";import{b as Z}from"./bind-mobile.9929c841.js";import{_ as K}from"./u-popup.457e1f1f.js";import"./u-image.dfca355c.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./u-safe-bottom.22d4d63b.js";import"./u-loading-icon.f15d7447.js";import"./mescroll-i18n.92d484d5.js";import"./u-number-box.41986fc4.js";import"./u-form.b5669646.js";import"./u-line.ddd38835.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-input.ef44c0c4.js";import"./u-modal.0666cf44.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";const X=Q(e({__name:"category-template-two-one",props:{config:{type:Object,default:()=>({})},categoryId:{type:[String,Number],default:0}},setup(e){const j=e;let S=j.config,I=j.categoryId;const F=t(""),N=t(!0);o((()=>{B()}));const V=t([]),B=()=>{N.value=!0,P().then((e=>{if(V.value=e.data,I)for(let t=0;t<V.value.length;t++){if(V.value[t].category_id==I){M.value=t;break}if(V.value[t].child_list)for(let e=0;e<V.value[t].child_list.length;e++)if(V.value[t].child_list[e].category_id==I){M.value=t;break}}N.value=!1})).catch((()=>{N.value=!1}))},M=t(0),R=()=>{f({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(F.value)}})};return(e,t)=>{const o=g,j=_,I=m,B=v,z=C,E=b(y("u--image"),H),$=b(y("tabbar"),L),U=b(y("loading-page"),O);return l(),r(I,{class:"bg-[var(--page-bg-color)] overflow-hidden min-h-screen"},{default:a((()=>[V.value.length?(l(),r(I,{key:0,class:"mescroll-box"},{default:a((()=>[s(S).search.control?(l(),r(I,{key:0,class:"search-box box-border z-10 fixed top-0 left-0 right-0 h-[100rpx] bg-[#fff]"},{default:a((()=>[i(I,{class:"flex-1 search-input"},{default:a((()=>[i(o,{onClick:d(R,["stop"]),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"},null,8,["onClick"]),i(j,{class:"input",type:"text",modelValue:F.value,"onUpdate:modelValue":t[0]||(t[0]=e=>F.value=e),modelModifiers:{trim:!0},placeholder:s(S).search.title,placeholderClass:"text-[var(--text-color-light9)]",onConfirm:R},null,8,["modelValue","placeholder"]),F.value?(l(),r(o,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[1]||(t[1]=e=>F.value="")})):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0),i(I,{class:x(["tabs-box z-2 fixed left-0 bg-[#fff] bottom-[50px] top-0",{"!top-[100rpx]":s(S).search.control}])},{default:a((()=>[i(B,{"scroll-y":!0,class:"scroll-height"},{default:a((()=>[i(I,{class:"bg-[var(--temp-bg)]"},{default:a((()=>[(l(!0),n(p,null,u(V.value,((e,t)=>(l(),r(I,{class:x(["tab-item",{"tab-item-active":t==M.value,"rounded-br-[12rpx]":M.value-1===t,"rounded-tr-[12rpx]":M.value+1===t}]),key:t,onClick:e=>((e,t)=>{M.value=e})(t)},{default:a((()=>[i(I,{class:"text-box text-[26rpx] text-left leading-[1.3] break-words px-[16rpx]"},{default:a((()=>[h(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["class"]),i(B,{class:"h-[100vh]","scroll-y":!0},{default:a((()=>[i(I,{class:x(["pl-[188rpx] scroll-ios pt-[20rpx] pr-[20rpx]",{"!pt-[120rpx]":s(S).search.control}])},{default:a((()=>{var e,t;return[(null==(e=V.value[M.value])?void 0:e.child_list)&&!N.value?(l(),r(I,{key:0,class:"bg-[#fff] grid grid-cols-3 gap-x-[50rpx] gap-y-[32rpx] py-[32rpx] px-[24rpx] rounded-[var(--rounded-big)]"},{default:a((()=>{var e;return[(l(!0),n(p,null,u(null==(e=V.value[M.value])?void 0:e.child_list,((e,t)=>(l(),r(I,{key:e.category_id,onClick:t=>{return o=e.category_id,void f({url:"/addon/shop/pages/goods/list",param:{curr_goods_category:o}});var o},class:"flex items-center justify-center flex-col"},{default:a((()=>[i(E,{radius:"var(--goods-rounded-big)",width:"120rpx",height:"120rpx",src:s(w)(e.image?e.image:""),model:"aspectFill"},{error:a((()=>[i(z,{class:"rounded-[var(--goods-rounded-big)] overflow-hidden w-[120rpx] h-[120rpx]",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),i(I,{class:"text-[24rpx] text-center mt-[16rpx] leading-[34rpx]"},{default:a((()=>[h(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))]})),_:1})):c("v-if",!0),(null==(t=V.value[M.value])?void 0:t.child_list)||N.value?c("v-if",!0):(l(),r(J,{key:1,class:"part",option:{tip:"暂无商品分类"}}))]})),_:1},8,["class"])])),_:1})])),_:1})):c("v-if",!0),i($),V.value.length||N.value?c("v-if",!0):(l(),r(J,{key:1,option:{tip:"暂无商品分类"}})),i(U,{loading:N.value},null,8,["loading"])])),_:1})}}}),[["__scopeId","data-v-1690b85e"]]),ee=Q(e({__name:"category-template-one-one",props:{config:{type:Object,default:()=>({})},categoryId:{type:[String,Number],default:0}},setup(e){const $=e,U=q();U.getList();const T=j((()=>U.cartList)),A=j((()=>U.totalNum)),Q=j((()=>U.totalMoney)),K=S(),X=j((()=>K.info)),{mescrollInit:ee,getMescroll:te}=Y(B,V);let oe=$.config,le=$.categoryId;const re=t([]),ae=t(""),se=t(!0),ie=t(!1);t([]).value=uni.getStorageSync("shopCart")||[];const de=e=>{ie.value=!1,D({page:e.num,limit:e.size,goods_category:le}).then((t=>{let o=t.data.data;1==e.num&&(re.value=[]),re.value=re.value.concat(o),ce(),se.value=!1,e.endSuccess(o.length),re.value.length||(ie.value=!0)})).catch((()=>{se.value=!1,ie.value=!0,e.endErr()}))},ce=()=>{re.value.forEach(((e,t)=>{e.isMaxBuy=!1;let o=-1;if(e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let o=e.max_buy-(e.has_buy||0);t=o>0?o:0}t>e.stock?o=e.stock:t<=e.stock&&(o=t)}0==o&&(e.isMaxBuy=!0)}))},ne=e=>{f({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})};o((()=>{_e()}));const ue=t(""),pe=t(!1),xe=t(!1),fe=(e,t)=>{if(pe.value||xe.value)return!1;pe.value=!0,xe.value=!0;let o={goods_id:e.goodsSku.goods_id,sku_id:e.goodsSku.sku_id,sale_price:je(e),stock:e.goodsSku.stock};e.id&&(o.num=e.num,o.id=e.id);let l=1;l=e.min_buy>0&&!e.num?e.min_buy:1,U.increase(o,l,(()=>{xe.value=!1})),setTimeout((()=>{const e=window.document.getElementById("animation-end"),o=e.getBoundingClientRect().left,l=e.getBoundingClientRect().top,r=window.document.getElementById(t),a=r.getBoundingClientRect().left,s=r.getBoundingClientRect().top;ue.value=`top: ${s}px; left: ${a}px;`,setTimeout((()=>{ue.value=`top: ${l+e.offsetHeight/2-r.offsetHeight/3}px; left: ${o+e.offsetWidth/2-r.offsetHeight/3}px; transition: all 0.8s; transform: rotate(-720deg);`}),20),setTimeout((()=>{ue.value="",pe.value=!1}),1020)}),100)},ge=t([]),_e=()=>{se.value=!0,P().then((e=>{if(ge.value=e.data,le)for(let t=0;t<ge.value.length;t++){if(ge.value[t].category_id==le){me.value=t;break}if(ge.value[t].child_list)for(let e=0;e<ge.value[t].child_list.length;e++)if(ge.value[t].child_list[e].category_id==le){me.value=t;break}}else le=e.data[0].category_id;se.value=!1})).catch((()=>{se.value=!1}))},me=t(0),ve=()=>{f({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(ae.value)}})},be=t(),ye=(e,t)=>{if("virtual"==e.goods_type&&"verify"==e.virtual_receive_type)return ne(e.goodsSku.goods_id);if("cart"!==oe.cart.event)return ne(e.goodsSku.goods_id);if(!X.value)return z().setLoginBack({url:"/addon/shop/pages/goods/category"}),!1;if(e.goodsSku.sku_spec_format)be.value.open(e.goodsSku.sku_id);else{if(!e.goodsSku.stock||parseInt(e.goodsSku.num||0)>parseInt(e.goodsSku.stock))return void M({title:"商品库存不足",icon:"none"});if(e.min_buy&&e.min_buy>parseInt(e.stock))return void M({title:"商品库存小于起购数量",icon:"none"});fe(e,t)}},he=()=>{f({url:"/addon/shop/pages/goods/cart"})},ke=t(null);t(uni.getStorageSync("isBindMobile"));const we=()=>{if(!A.value)return void M({title:"还没有选择商品",icon:"none"});const e=[];Object.values(T.value).forEach((t=>{Object.keys(t).forEach((o=>{"totalNum"!=o&&"totalMoney"!=o&&e.push(t[o].id)}))})),0!=e.length&&uni.setStorage({key:"orderCreateData",data:{cart_ids:e},success(){f({url:"/addon/shop/pages/order/payment"})}})},Ce=e=>{let t="";return t=e.goodsSku.show_type,t},je=e=>{let t="0.00";return t=e.goodsSku.show_price,t};return(e,t)=>{const o=g,f=_,j=m,S=v,V=C,B=b(y("u--image"),H),z=R,$=b(y("loading-page"),O),P=b(y("tabbar"),L);return l(),r(j,{class:"min-h-screen bg-[var(--page-bg-color)] overflow-hidden"},{default:a((()=>[ge.value.length?(l(),r(j,{key:0,class:x(["mescroll-box bg-[var(--page-bg-color)]",{cart:s(oe).cart.control&&"cart"===s(oe).cart.event,detail:!(s(oe).cart.control&&"cart"===s(oe).cart.event)}])},{default:a((()=>[i(W,{ref:"mescrollRef",down:{use:!1},onInit:s(ee),onUp:de},{default:a((()=>[s(oe).search.control?(l(),r(j,{key:0,class:"search-box z-10 bg-[#fff] fixed top-0 left-0 right-0 h-[100rpx] box-border"},{default:a((()=>[i(j,{class:"flex-1 search-input"},{default:a((()=>[i(o,{onClick:d(ve,["stop"]),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"},null,8,["onClick"]),i(f,{class:"input",type:"text",modelValue:ae.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value=e),modelModifiers:{trim:!0},placeholder:s(oe).search.title,onConfirm:ve,placeholderClass:"text-[var(--text-color-light9)]"},null,8,["modelValue","placeholder"]),ae.value?(l(),r(o,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[1]||(t[1]=e=>ae.value="")})):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0),i(j,{class:x(["tabs-box z-2 fixed left-0 bg-[#fff] bottom-[50px] top-0",{"!top-[100rpx]":s(oe).search.control,"pb-[98rpx]":s(oe).cart.control&&"cart"===s(oe).cart.event}])},{default:a((()=>[i(S,{"scroll-y":!0,class:"scroll-height"},{default:a((()=>[i(j,{class:"bg-[var(--temp-bg)]"},{default:a((()=>[(l(!0),n(p,null,u(ge.value,((e,t)=>(l(),r(j,{class:x(["tab-item",{"tab-item-active":t==me.value,"rounded-br-[12rpx]":me.value-1===t,"rounded-tr-[12rpx]":me.value+1===t}]),key:t,onClick:o=>((e,t)=>{me.value=e,le=t.category_id,re.value=[],te().resetUpScroll()})(t,e)},{default:a((()=>[i(j,{class:"text-box text-left leading-[1.3] break-words px-[16rpx]"},{default:a((()=>[h(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["class"]),i(j,{class:x(["flex justify-center flex-wrap pl-[188rpx] pb-[20rpx]",{" pt-[120rpx]":s(oe).search.control," pt-[20rpx]":!s(oe).search.control}])},{default:a((()=>[(l(!0),n(p,null,u(re.value,((e,t)=>(l(),r(j,{key:e.goods_id,class:x(["box-border bg-white w-full flex mr-[20rpx] py-[24rpx] px-[20rpx] rounded-[var(--rounded-small)]",{"mt-[16rpx]":t}]),onClick:d((t=>ne(e.goods_id)),["stop"])},{default:a((()=>[i(j,{class:"w-[168rpx] h-[168rpx] flex items-center justify-center rounded-[var(--goods-rounded-small)] overflow-hidden"},{default:a((()=>[i(B,{width:"168rpx",height:"168rpx",radius:"var(--goods-rounded-small)",src:s(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[i(V,{class:"w-[168rpx] h-[168rpx]",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),i(j,{class:"flex flex-1 ml-[20rpx] flex-wrap flex-col"},{default:a((()=>[i(j,{class:"max-h-[80rpx] text-[26rpx] leading-[40rpx] multi-hidden"},{default:a((()=>[h(k(e.goods_name),1)])),_:2},1024),i(j,{class:"flex-1 flex items-end justify-between"},{default:a((()=>[i(j,{class:"text-[var(--price-text-color)] price-font -mb-[8rpx]"},{default:a((()=>[i(o,{class:"text-[24rpx] font-500"},{default:a((()=>[h("￥")])),_:1}),i(o,{class:"text-[40rpx] font-500"},{default:a((()=>[h(k(parseFloat(je(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),i(o,{class:"text-[24rpx] font-500"},{default:a((()=>[h("."+k(parseFloat(je(e)).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==Ce(e)?(l(),r(V,{key:0,class:"h-[24rpx] max-w-[46rpx] ml-[6rpx]",src:s(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0),"newcomer_price"==Ce(e)?(l(),r(V,{key:1,class:"h-[24rpx] max-w-[60rpx] ml-[6rpx]",src:s(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0),"discount_price"==Ce(e)?(l(),r(V,{key:2,class:"h-[24rpx] max-w-[80rpx] ml-[6rpx]",src:s(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0)])),_:2},1024),e.isMaxBuy?c("v-if",!0):(l(),n(p,{key:0},[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&s(oe).cart.control&&"cart"===s(oe).cart.event?(l(),r(j,{key:0,class:"flex items-center"},{default:a((()=>[i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(o,{class:"text-[32rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:d((t=>((e,t)=>{if(xe.value)return!1;xe.value=!0;let o=1;e.min_buy>0&&e.min_buy==t.num&&(o=e.min_buy),U.reduce({id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.stock,sale_price:t.sale_price,num:t.num},o,(()=>{xe.value=!1}))})(e,s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),i(o,{class:"text-[#333] text-[24rpx] mx-[16rpx]"},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(o,{class:"text-[32rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+t,onClick:d((o=>((e,t,o)=>{if(parseInt(t.num)>=parseInt(t.stock))return void M({title:"商品库存不足",icon:"none"});let l=t.num;if(e.min_buy>0&&e.min_buy>l&&(l=e.min_buy),e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let o=e.max_buy-(e.has_buy||0);t=o>0?o:0}e.goodsSku.stock,e.goodsSku.stock}if(e.is_limit&&l>=e.max_buy){let t=`该商品单次限购${e.max_buy}件`;return 1!=e.limit_type&&(t=`该商品每人限购${e.max_buy}件`),M({title:t,icon:"none"}),!1}let r=E(e);r.num=l,r.id=t.id,fe(r,o)})(e,s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id],"itemCart"+t)),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type||"real"==e.goods_type?(l(),n(p,{key:1},[s(oe).cart.control&&"style-1"===s(oe).cart.style?(l(),r(j,{key:0,class:"h-[44rpx] relative"},{default:a((()=>[i(j,{id:"itemCart"+t,class:"w-[102rpx] box-border text-center text-[#fff] primary-btn-bg h-[46rpx] text-[22rpx] leading-[46rpx] rounded-[100rpx]",onClick:d((o=>ye(e,"itemCart"+t)),["stop"])},{default:a((()=>[h(k(s(oe).cart.text),1)])),_:2},1032,["id","onClick"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)]  text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1024)):c("v-if",!0),s(oe).cart.control&&"style-2"===s(oe).cart.style?(l(),r(j,{key:1,class:"w-[50rpx] h-[50rpx] relative",onClick:d((o=>ye(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(o,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-tianjiaV6xx text-[44rpx]"},null,8,["id"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1032,["onClick"])):c("v-if",!0),s(oe).cart.control&&"style-3"===s(oe).cart.style?(l(),r(j,{key:2,class:"w-[50rpx] flex justify-center items-end h-[50rpx] relative",onClick:d((o=>ye(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(o,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-gouwucheV6xx6 !text-[34rpx]"},null,8,["id"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-2rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1032,["onClick"])):c("v-if",!0),s(oe).cart.control&&"style-4"===s(oe).cart.style?(l(),r(j,{key:3,class:"w-[50rpx] h-[50rpx] justify-center flex items-end relative",onClick:d((o=>ye(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(j,{id:"itemCart"+t,class:"flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(o,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[26rpx]"})])),_:2},1032,["id"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-10rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1032,["onClick"])):c("v-if",!0)],64)):c("v-if",!0)],64))])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)),re.value.length||se.value||!ie.value?c("v-if",!0):(l(),r(J,{key:0,class:"part",option:{tip:"暂无商品"}}))])),_:1},8,["class"]),i(G,{ref_key:"cartRef",ref:be},null,512)])),_:1},8,["onInit"]),s(oe).cart.control&&"cart"===s(oe).cart.event?(l(),r(j,{key:0,class:"bg-[#fff] z-10 flex justify-between items-center fixed left-0 right-0 bottom-[50px] box-solid px-[24rpx] py-[17rpx] mb-ios border-[0] border-t-[2rpx] border-solid border-[#f6f6f6]"},{default:a((()=>[i(j,{class:"flex items-center"},{default:a((()=>[i(j,{class:"w-[66rpx] h-[66rpx] mr-[27rpx] relative"},{default:a((()=>[i(j,{id:"animation-end",class:"w-[66rpx] h-[66rpx] rounded-[35rpx] bg-[var(--primary-color)] text-center leading-[70rpx]",onClick:d(he,["stop"])},{default:a((()=>[i(o,{class:"nc-iconfont nc-icon-gouwucheV6mm1 text-[#fff] text-[32rpx]"})])),_:1},8,["onClick"]),s(A)?(l(),r(j,{key:0,class:x(["border-[1rpx] border-solid border-[#fff]",["absolute left-[40rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border",s(A)>9?"px-[10rpx]":""]])},{default:a((()=>[h(k(s(A)>99?"99+":s(A)),1)])),_:1},8,["class"])):c("v-if",!0)])),_:1}),i(o,{class:"text-[26rpx] font-500 text-[#333]"},{default:a((()=>[h("总计：")])),_:1}),i(j,{class:"text-[var(--price-text-color)] price-font font-bold flex items-baseline"},{default:a((()=>[i(o,{class:"text-[26rpx] mr-[6rpx]"},{default:a((()=>[h("￥")])),_:1}),i(o,{class:"text-[44rpx]"},{default:a((()=>[h(k(parseFloat(s(Q))),1)])),_:1})])),_:1})])),_:1}),i(z,{class:x(["w-[180rpx] h-[70rpx] text-[26rpx] leading-[70rpx] font-500 m-0 rounded-full remove-border",{"primary-btn-bg !text-[#fff]":parseFloat(s(Q))>0,"bg-[#F7F7F7] !text-[var(--text-color-light9)]":parseFloat(s(Q))<=0}]),onClick:we},{default:a((()=>[h("去结算")])),_:1},8,["class"])])),_:1})):c("v-if",!0)])),_:1},8,["class"])):c("v-if",!0),I(i(j,{style:N(ue.value),class:"fixed z-999 flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(o,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[30rpx]"})])),_:1},8,["style"]),[[F,ue.value]]),ge.value.length||se.value?c("v-if",!0):(l(),r(J,{key:1,option:{tip:"暂无商品分类"}})),i($,{loading:se.value},null,8,["loading"]),i(P),c(" 强制绑定手机号 "),i(Z,{ref_key:"bindMobileRef",ref:ke},null,512)])),_:1})}}}),[["__scopeId","data-v-bc91a642"]]),te=Q(e({__name:"category-template-two-two",props:{config:{type:Object,default:()=>({})},categoryId:{type:[String,Number],default:0}},setup(e){const $=e,U=q();U.getList();const T=j((()=>U.cartList)),A=j((()=>U.totalNum)),Q=j((()=>U.totalMoney)),X=S(),ee=j((()=>X.info)),{mescrollInit:te,getMescroll:oe}=Y(B,V);let le=$.config,re=$.categoryId;const ae=t([]),se=t(""),ie=t(!0),de=t(!1),ce=t(!1);t([]).value=uni.getStorageSync("shopCart")||[];const ne=e=>{de.value=!1,D({page:e.num,limit:e.size,goods_category:re}).then((t=>{let o=t.data.data;1==e.num&&(ae.value=[]),ae.value=ae.value.concat(o),ue(),ie.value=!1,e.endSuccess(o.length),ae.value.length||(de.value=!0)})).catch((()=>{ie.value=!1,de.value=!0,e.endErr()}))},ue=()=>{ae.value.forEach(((e,t)=>{e.isMaxBuy=!1;let o=-1;if(e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let o=e.max_buy-(e.has_buy||0);t=o>0?o:0}t>e.stock?o=e.stock:t<=e.stock&&(o=t)}0==o&&(e.isMaxBuy=!0)}))},pe=e=>{f({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})};o((()=>{ve()}));const xe=t(""),fe=t(!1),ge=t(!1),_e=(e,t)=>{if(fe.value||ge.value)return!1;fe.value=!0,ge.value=!0;let o={goods_id:e.goodsSku.goods_id,sku_id:e.goodsSku.sku_id,sale_price:Ne(e),stock:e.goodsSku.stock};e.id&&(o.num=e.num,o.id=e.id);let l=1;l=e.min_buy>0&&!e.num?e.min_buy:1,U.increase(o,l,(()=>{ge.value=!1})),setTimeout((()=>{const e=window.document.getElementById("animation-end"),o=e.getBoundingClientRect().left,l=e.getBoundingClientRect().top,r=window.document.getElementById(t),a=r.getBoundingClientRect().left,s=r.getBoundingClientRect().top;xe.value=`top: ${s}px; left: ${a}px;`,setTimeout((()=>{xe.value=`top: ${l+e.offsetHeight/2-r.offsetHeight/3}px; left: ${o+e.offsetWidth/2-r.offsetHeight/3}px; transition: all 0.8s; transform: rotate(-720deg);`}),20),setTimeout((()=>{xe.value="",fe.value=!1}),1020)}),100)};t({allActive:-1,data:{category_name:"全部",category_id:""}});const me=t([]),ve=()=>{ie.value=!0,P().then((e=>{me.value=e.data;for(let t=0;t<me.value.length;t++)if(me.value[t].child_list){let e={category_name:"全部",category_id:me.value[t].category_id};me.value[t].child_list.unshift(e)}if(re)for(let t=0;t<me.value.length;t++){if(me.value[t].category_id==re){be.value=t;break}if(me.value[t].child_list)for(let e=0;e<me.value[t].child_list.length;e++)if(me.value[t].child_list[e].category_id==re){be.value=t,ye.value=e;break}}else re=e.data[0].category_id;ie.value=!1})).catch((()=>{ie.value=!1}))},be=t(0),ye=t(0),he=(e,t)=>{ye.value=e,re=t.category_id,ce.value=!1,ae.value=[],oe().resetUpScroll()},ke=()=>{f({url:"/addon/shop/pages/goods/list",param:{goods_name:encodeURIComponent(se.value)}})},we=t(),Ce=(e,t)=>{if("virtual"==e.goods_type&&"verify"==e.virtual_receive_type)return pe(e.goodsSku.goods_id);if("cart"!==le.cart.event)return pe(e.goods_id);if(!ee.value)return z().setLoginBack({url:"/addon/shop/pages/goods/category"}),!1;if(e.goodsSku.sku_spec_format)we.value.open(e.goodsSku.sku_id);else{if(!e.goodsSku.stock||parseInt(e.goodsSku.num||0)>parseInt(e.goodsSku.stock))return void M({title:"商品库存不足",icon:"none"});if(e.min_buy&&e.min_buy>parseInt(e.stock))return void M({title:"商品库存小于起购数量",icon:"none"});_e(e,t)}},je=()=>{f({url:"/addon/shop/pages/goods/cart"})},Se=t(null);t(uni.getStorageSync("isBindMobile"));const Ie=()=>{if(!A.value)return void M({title:"还没有选择商品",icon:"none"});const e=[];Object.values(T.value).forEach((t=>{Object.keys(t).forEach((o=>{"totalNum"!=o&&"totalMoney"!=o&&e.push(t[o].id)}))})),0!=e.length&&uni.setStorage({key:"orderCreateData",data:{cart_ids:e},success(){f({url:"/addon/shop/pages/order/payment"})}})},Fe=e=>{let t="";return t=e.goodsSku.show_type,t},Ne=e=>{let t="0.00";return t=e.goodsSku.show_price,t};return(e,t)=>{const o=g,f=_,j=m,S=v,V=b(y("u-popup"),K),B=C,z=b(y("u--image"),H),$=R,P=b(y("loading-page"),O),D=b(y("tabbar"),L);return l(),r(j,{class:"min-h-screen bg-[var(--page-bg-color)] overflow-hidden"},{default:a((()=>[me.value.length?(l(),r(j,{key:0,class:x(["mescroll-box bg-[#f6f6f6]",{cart:s(le).cart.control&&"cart"===s(le).cart.event,detail:!(s(le).cart.control&&"cart"===s(le).cart.event)}])},{default:a((()=>[i(W,{ref:"mescrollRef",down:{use:!1},onInit:s(te),onUp:ne},{default:a((()=>{var e,g,_,m,v,b;return[s(le).search.control?(l(),r(j,{key:0,class:"box-border search-box z-10 bg-[#fff] fixed top-0 left-0 right-0 h-[96rpx]"},{default:a((()=>[i(j,{class:"flex-1 search-input"},{default:a((()=>[i(o,{onClick:d(ke,["stop"]),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"},null,8,["onClick"]),i(f,{class:"input",type:"text",modelValue:se.value,"onUpdate:modelValue":t[0]||(t[0]=e=>se.value=e),modelModifiers:{trim:!0},placeholder:s(le).search.title,placeholderClass:"text-[var(--text-color-light9)]",onConfirm:ke},null,8,["modelValue","placeholder"]),se.value?(l(),r(o,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[1]||(t[1]=e=>se.value="")})):c("v-if",!0)])),_:1})])),_:1})):c("v-if",!0),i(j,{class:x(["tabs-box z-2 fixed left-0 bg-[#fff] bottom-[50px] top-0",{"!top-[96rpx]":s(le).search.control,"pb-[98rpx]":s(le).cart.control&&"cart"===s(le).cart.event}])},{default:a((()=>[i(S,{"scroll-y":!0,class:"scroll-height"},{default:a((()=>[i(j,{class:"bg-[var(--temp-bg)]"},{default:a((()=>[(l(!0),n(p,null,u(me.value,((e,t)=>(l(),r(j,{class:x(["tab-item",{"tab-item-active ":t==be.value,"rounded-br-[12rpx]":!(be.value-1!==t||me.value[be.value].child_list&&me.value[be.value].child_list.length),"rounded-tr-[12rpx]":!(be.value+1!==t||me.value[be.value].child_list&&me.value[be.value].child_list.length)}]),key:t,onClick:o=>((e,t)=>{be.value=e,ce.value=!1,t.child_list&&t.child_list.length?he(0,t.child_list[0]):(re=t.category_id,ae.value=[],oe().resetUpScroll())})(t,e)},{default:a((()=>[i(j,{class:"text-box leading-[1.3] break-words px-[24rpx]"},{default:a((()=>[h(k(e.category_name),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},8,["class"]),(null==(e=me.value[be.value])?void 0:e.child_list)&&(null==(g=me.value[be.value])?void 0:g.child_list.length)?(l(),r(j,{key:1,class:x(["flex items-center h-[98rpx] pl-[24rpx] pr-[48rpx] py-[20rpx] z-10 bg-white fixed left-[168rpx] right-0 box-border top-0",{"!top-[94rpx]":s(le).search.control}])},{default:a((()=>[ce.value?(l(),n(p,{key:1},[i(j,{class:"flex-1 h-[48rpx] text-[28rpx] text-[var(--text-color-light9)] pr-[24rpx] leading-[48rpx]"},{default:a((()=>[h("全部分类")])),_:1}),i(o,{class:"absolute right-[24rpx] nc-iconfont nc-icon-shangV6xx-1 text-[#333] text-[30rpx]",onClick:t[3]||(t[3]=e=>ce.value=!1)})],64)):(l(),n(p,{key:0},[i(S,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(ye.value?ye.value-1:0),class:"flex-1 h-[54rpx] scroll-Y box-border pr-[24rpx] bg-white"},{default:a((()=>[i(j,{class:"flex items-center h-[54rpx] box-border"},{default:a((()=>{var e;return[(l(!0),n(p,null,u(null==(e=me.value[be.value])?void 0:e.child_list,((e,t)=>(l(),r(o,{class:x(["w-[150rpx] flex-shrink-0 px-[14rpx] h-[54rpx] truncate text-center !leading-[50rpx] !text-[24rpx] border-[2rpx] border-solid !rounded-[100rpx] box-border text-[#333] box-border",{"bg-[var(--primary-color-light)] font-500 text-[var(--primary-color)] border-[var(--primary-color)]":t===ye.value,"border-[var(--temp-bg)]  bg-[var(--temp-bg)]":t!=ye.value," ml-[24rpx]":0!=t}]),key:me.value[be.value].category_id,id:"id"+t,onClick:o=>he(t,e)},{default:a((()=>[h(k(e.category_name),1)])),_:2},1032,["class","id","onClick"])))),128))]})),_:1})])),_:1},8,["scroll-into-view"]),i(j,{class:"absolute right-[24rpx] nc-iconfont nc-icon-xiaV6xx text-[30rpx] w-[30rpx] h-[30rpx] text-center transform",onClick:t[2]||(t[2]=e=>ce.value=!0)})],64))])),_:1},8,["class"])):c("v-if",!0),i(j,{class:x(["labelPopup",{active:s(le).search.control}])},{default:a((()=>[i(V,{show:ce.value,mode:"top",onClose:t[5]||(t[5]=e=>ce.value=!1)},{default:a((()=>[i(j,{class:"flex flex-wrap pt-[20rpx] pb-[24rpx]",onTouchmove:t[4]||(t[4]=d((()=>{}),["prevent","stop"]))},{default:a((()=>{var e;return[(l(!0),n(p,null,u(null==(e=me.value[be.value])?void 0:e.child_list,((e,t)=>(l(),r(o,{class:x(["px-[14rpx] flex-shrink-0 w-[160rpx] box-border ml-[20rpx] mb-[26rpx] h-[60rpx] text-center leading-[56rpx] text-[24rpx] border-[2rpx] border-solid !rounded-[100rpx] text-[#333] truncate",{"bg-[var(--primary-color-light)] font-500 text-[var(--primary-color)] border-[var(--primary-color)]":t===ye.value,"border-[var(--temp-bg)]  bg-[var(--temp-bg)]":t!=ye.value}]),key:me.value[be.value].category_id,onClick:o=>he(t,e)},{default:a((()=>[h(k(e.category_name),1)])),_:2},1032,["class","onClick"])))),128))]})),_:1})])),_:1},8,["show"])])),_:1},8,["class"]),i(j,{class:x(["flex justify-center flex-wrap pl-[168rpx] pt-[20rpx] pb-[20rpx]",{"!pt-[214rpx]":s(le).search.control&&(null==(_=me.value[be.value])?void 0:_.child_list)&&(null==(m=me.value[be.value])?void 0:m.child_list.length),"pt-[120rpx]":s(le).search.control&&(!me.value[be.value].child_list||!me.value[be.value].child_list.length),"pt-[118rpx]":(null==(v=me.value[be.value])?void 0:v.child_list)&&(null==(b=me.value[be.value])?void 0:b.child_list.length)&&!s(le).search.control}])},{default:a((()=>[(l(!0),n(p,null,u(ae.value,((e,t)=>(l(),r(j,{key:e.goods_id,class:x(["w-[536rpx] box-border bg-white w-full flex mx-[20rpx] py-[24rpx] px-[20rpx] rounded-[var(--rounded-small)]",{"mt-[20rpx]":t}]),onClick:d((t=>pe(e.goods_id)),["stop"])},{default:a((()=>[i(j,{class:"w-[168rpx] h-[168rpx] flex items-center justify-center mr-[20rpx] rounded-[var(--goods-rounded-small)] overflow-hidden"},{default:a((()=>[i(z,{width:"168rpx",height:"168rpx",radius:"var(--goods-rounded-small)",src:s(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[i(B,{class:"w-[168rpx] h-[168rpx]",src:s(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"])])),_:2},1024),i(j,{class:"flex flex-1 flex-wrap flex-col"},{default:a((()=>[i(j,{class:"max-h-[80rpx] text-[26rpx] leading-[40rpx] multi-hidden"},{default:a((()=>[h(k(e.goods_name),1)])),_:2},1024),i(j,{class:"flex items-end justify-between flex-1"},{default:a((()=>[i(j,{class:"text-[var(--price-text-color)] price-font -mb-[8rpx]"},{default:a((()=>[i(o,{class:"text-[24rpx] font-500"},{default:a((()=>[h("￥")])),_:1}),i(o,{class:"text-[40rpx] font-500"},{default:a((()=>[h(k(parseFloat(Ne(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),i(o,{class:"text-[24rpx] font-500"},{default:a((()=>[h("."+k(parseFloat(Ne(e)).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==Fe(e)?(l(),r(B,{key:0,class:"h-[24rpx] max-w-[46rpx] ml-[6rpx]",src:s(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0),"newcomer_price"==Fe(e)?(l(),r(B,{key:1,class:"h-[24rpx] max-w-[60rpx] ml-[6rpx]",src:s(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0),"discount_price"==Fe(e)?(l(),r(B,{key:2,class:"h-[24rpx] max-w-[80rpx] ml-[6rpx]",src:s(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):c("v-if",!0)])),_:2},1024),e.isMaxBuy?c("v-if",!0):(l(),n(p,{key:0},[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&s(le).cart.control&&"cart"===s(le).cart.event?(l(),r(j,{key:0,class:"flex items-center"},{default:a((()=>[i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(o,{class:"text-[32rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[12rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:d((t=>((e,t)=>{if(ge.value)return!1;ge.value=!0;let o=1;e.min_buy>0&&e.min_buy==t.num&&(o=e.min_buy),U.reduce({id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.stock,sale_price:t.sale_price,num:t.num},o,(()=>{ge.value=!1}))})(e,s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),i(o,{class:"text-[#333] text-[24rpx] mx-[16rpx]"},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),i(j,{class:"relative w-[32rpx] h-[32rpx]"},{default:a((()=>[i(o,{class:"text-[32rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+t,onClick:d((o=>((e,t,o)=>{if(parseInt(t.num)>=parseInt(t.stock))return void M({title:"商品库存不足",icon:"none"});let l=t.num;if(e.min_buy>0&&e.min_buy>t.num&&(l=e.min_buy),e.is_limit&&e.max_buy&&(1==e.limit_type?e.max_buy:(e.max_buy,e.has_buy)),e.is_limit&&l>=e.max_buy){let t=`该商品单次限购${e.max_buy}件`;return 1!=e.limit_type&&(t=`该商品每人限购${e.max_buy}件`),M({title:t,icon:"none"}),!1}let r=E(e);r.num=l,r.id=t.id,_e(r,o)})(e,s(T)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id],"itemCart"+t)),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type||"real"==e.goods_type?(l(),n(p,{key:1},[s(le).cart.control&&"style-1"===s(le).cart.style?(l(),r(j,{key:0,class:"h-[44rpx] relative pl-[20rpx]"},{default:a((()=>[i(j,{id:"itemCart"+t,class:"w-[102rpx] box-border text-center text-[#fff] primary-btn-bg h-[46rpx] text-[22rpx] leading-[46rpx] rounded-[100rpx]",onClick:d((o=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[h(k(s(le).cart.text),1)])),_:2},1032,["id","onClick"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1024)):c("v-if",!0),s(le).cart.control&&"style-2"===s(le).cart.style?(l(),r(j,{key:1,class:"w-[50rpx] h-[50rpx] relative",onClick:d((o=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(o,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-tianjiaV6xx text-[44rpx]"},null,8,["id"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1032,["onClick"])):c("v-if",!0),s(le).cart.control&&"style-3"===s(le).cart.style?(l(),r(j,{key:2,class:"w-[50rpx] h-[50rpx] flex justify-center items-end relative",onClick:d((o=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(o,{id:"itemCart"+t,class:"text-color nc-iconfont nc-icon-gouwucheV6xx6 !text-[34rpx]"},null,8,["id"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-2rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1032,["onClick"])):c("v-if",!0),s(le).cart.control&&"style-4"===s(le).cart.style?(l(),r(j,{key:3,class:"w-[50rpx] h-[50rpx] flex items-end relative",onClick:d((o=>Ce(e,"itemCart"+t)),["stop"])},{default:a((()=>[i(j,{id:"itemCart"+t,class:"flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(o,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[26rpx]"})])),_:2},1032,["id"]),s(T)["goods_"+e.goods_id]&&s(T)["goods_"+e.goods_id].totalNum?(l(),r(j,{key:0,class:x(["absolute right-[-10rpx] top-[-10rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border border-[2rpx] border-solid border-[#fff]",s(T)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[h(k(s(T)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):c("v-if",!0)])),_:2},1032,["onClick"])):c("v-if",!0)],64)):c("v-if",!0)],64))])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)),ae.value.length||ie.value||!de.value?c("v-if",!0):(l(),r(J,{key:0,class:"part",option:{tip:"暂无商品"}}))])),_:1},8,["class"]),i(G,{ref_key:"cartRef",ref:we},null,512)]})),_:1},8,["onInit"]),s(le).cart.control&&"cart"===s(le).cart.event?(l(),r(j,{key:0,class:"bg-[#fff] z-10 flex justify-between items-center fixed left-0 right-0 bottom-[50px] box-solid px-[24rpx] py-[17rpx] mb-ios border-[0] border-t-[2rpx] border-solid border-[#f6f6f6]"},{default:a((()=>[i(j,{class:"flex items-center"},{default:a((()=>[i(j,{class:"w-[66rpx] h-[66rpx] mr-[27rpx] relative"},{default:a((()=>[i(j,{id:"animation-end",class:"w-[66rpx] h-[66rpx] rounded-[35rpx] bg-[var(--primary-color)] text-center leading-[70rpx]",onClick:d(je,["stop"])},{default:a((()=>[i(o,{class:"nc-iconfont nc-icon-gouwucheV6mm1 text-[#fff] text-[32rpx]"})])),_:1},8,["onClick"]),s(A)?(l(),r(j,{key:0,class:x(["border-[1rpx] border-solid border-[#fff]",["absolute left-[40rpx] top-[-10rpx] rounded-[28rpx] h-[28rpx] min-w-[28rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border",s(A)>9?"px-[10rpx]":""]])},{default:a((()=>[h(k(s(A)>99?"99+":s(A)),1)])),_:1},8,["class"])):c("v-if",!0)])),_:1}),i(o,{class:"text-[26rpx] text-[#333]"},{default:a((()=>[h("总计：")])),_:1}),i(j,{class:"text-[var(--price-text-color)] price-font font-bold flex items-baseline"},{default:a((()=>[i(o,{class:"text-[26rpx] mr-[6rpx]"},{default:a((()=>[h("￥")])),_:1}),i(o,{class:"text-[44rpx]"},{default:a((()=>[h(k(parseFloat(s(Q))),1)])),_:1})])),_:1})])),_:1}),i($,{class:x(["w-[180rpx] h-[70rpx] text-[26rpx] leading-[70rpx] font-500 m-0 rounded-full remove-border",{"primary-btn-bg !text-[#fff]":parseFloat(s(Q))>0,"bg-[#F7F7F7] !text-[var(--text-color-light9)]":parseFloat(s(Q))<=0}]),onClick:Ie},{default:a((()=>[h("去结算")])),_:1},8,["class"])])),_:1})):c("v-if",!0)])),_:1},8,["class"])):c("v-if",!0),I(i(j,{style:N(xe.value),class:"fixed z-999 flex items-center justify-center text-[#fff] bg-color h-[44rpx] w-[44rpx] rounded-[22rpx] text-center"},{default:a((()=>[i(o,{class:"nc-iconfont nc-icon-gouwucheV6xx6 !text-[30rpx]"})])),_:1},8,["style"]),[[F,xe.value]]),me.value.length||ie.value?c("v-if",!0):(l(),r(j,{key:1,class:"flex justify-center items-center w-[100%]"},{default:a((()=>[i(J,{option:{tip:"暂无商品分类"}})])),_:1})),i(P,{loading:ie.value},null,8,["loading"]),i(D),c(" 强制绑定手机号 "),i(Z,{ref_key:"bindMobileRef",ref:Se},null,512)])),_:1})}}}),[["__scopeId","data-v-b0adbcfa"]]),oe=Q(e({__name:"category",setup(e){const o=q(),s=t({}),i=t(0);return $((e=>{i.value=e.category_id||0,A().then((e=>{s.value=e.data,T({title:s.value.page_title})}))})),U((()=>{o.getList()})),(e,t)=>{const o=m;return l(),r(o,{style:N(e.themeColor())},{default:a((()=>[1===s.value.level&&"style-1"===s.value.template?(l(),r(ee,{key:0,class:"category",categoryId:i.value,config:s.value},null,8,["categoryId","config"])):c("v-if",!0),2===s.value.level&&"style-1"===s.value.template?(l(),r(X,{key:1,categoryId:i.value,config:s.value},null,8,["categoryId","config"])):c("v-if",!0),2===s.value.level&&"style-2"===s.value.template?(l(),r(te,{key:2,class:"category",categoryId:i.value,config:s.value},null,8,["categoryId","config"])):c("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-eac5064e"]]);export{oe as default};

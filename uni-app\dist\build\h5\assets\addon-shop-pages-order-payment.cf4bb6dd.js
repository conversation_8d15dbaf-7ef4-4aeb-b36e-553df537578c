import{d as e,r as t,a9 as l,l as a,i as r,j as s,o,c as i,w as d,b as u,C as n,A as p,e as c,g as x,f as _,R as v,S as m,a3 as f,T as y,B as g,v as b,k,I as h,ap as j,H as w,ao as F,aW as C,E as S,N as V,Q as O,F as T,y as D,G as $,ag as B,p as z,q as E,z as I,n as R,aT as L,b2 as A,an as G,a as U,bG as K}from"./index-dd56d0cc.js";import{_ as P}from"./u--image.cd475bba.js";import{_ as N,a as q,b as Q,n as W,c as H}from"./index.9bf7e6bb.js";import{_ as J}from"./u-tabbar.3565fd74.js";import{_ as M}from"./pay.1561f7e1.js";import{f as X,h as Y,i as Z,j as ee}from"./order.22f5d222.js";import{_ as te}from"./u-tabs.58e465c6.js";import{_ as le}from"./u-popup.457e1f1f.js";import{n as ae}from"./ns-goods-manjian.9fddf49d.js";import{_ as re}from"./u-number-box.41986fc4.js";import{_ as se}from"./u-parse.ae2d35cb.js";import{_ as oe}from"./u-swiper.01028265.js";import{_ as ie}from"./_plugin-vue_export-helper.1b428a4d.js";import{t as de}from"./topTabbar.986d54c4.js";import{d as ue}from"./index.aece06fa.js";import"./u-image.dfca355c.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.f15d7447.js";import"./u-empty.354f2b69.js";import"./u-form.b5669646.js";import"./u-line.ddd38835.js";import"./u-input.ef44c0c4.js";import"./u-safe-bottom.22d4d63b.js";import"./pay.5385ae25.js";import"./u-badge.206da3ef.js";import"./useDiyForm.1a531152.js";import"./diy_form.2c8e4c0e.js";import"./index.2c75d097.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./u-button.b6743e99.js";import"./u-picker.1af38a2a.js";import"./u-upload.44346d61.js";import"./u-radio-group.3476fb8a.js";import"./u-action-sheet.daa5fa92.js";import"./u-avatar.ea828bd7.js";import"./u-text.1f240d34.js";import"./tabbar.0d5e534b.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./goods.dbf7b09d.js";import"./useGoods.392f2eb1.js";import"./add-cart-popup.4746de5d.js";import"./coupon.506f719c.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./bind-mobile.9929c841.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-modal.0666cf44.js";import"./newcomer.c56b90d6.js";const ne=e({__name:"select-coupon",props:{orderKey:{type:String,default:""}},emits:["confirm"],setup(e,{expose:F,emit:C}){const S=e,V=t(0),O=t([]),T=t([]),D=t(!1),$=t(null);l((()=>S.orderKey),(()=>{S.orderKey&&!O.value.length&&X({order_key:S.orderKey}).then((({data:e})=>{const t=[],l=[];e.length&&e.forEach((e=>{e.is_normal?t.push(e):l.push(e)})),T.value=l,O.value=t,t.length&&($.value=t[0],C("confirm",$.value))})).catch()}),{immediate:!0});const B=a((()=>[{name:`可用优惠券(${O.value.length})`,key:"normal"},{name:`不可用优惠券(${T.value.length})`,key:"disabled"}])),z=e=>{V.value=e.index},E=()=>{C("confirm",$.value),D.value=!1};return F({open:e=>{if(D.value=!0,e)for(let t=0;t<O.value.length;t++)if(O.value[t].id==e){$.value=O.value[t];break}},couponList:O}),(e,t)=>{const l=k,a=r(s("u-tabs"),te),F=h,C=j,S=w,I=r(s("u-popup"),le);return o(),i(I,{show:D.value,onClose:t[1]||(t[1]=e=>D.value=!1),mode:"bottom",round:10,closeable:!0},{default:d((()=>[u(l,{onTouchmove:t[0]||(t[0]=n((()=>{}),["prevent","stop"])),class:"popup-common"},{default:d((()=>[u(l,{class:"title"},{default:d((()=>[p("请选择优惠券")])),_:1}),e.type?x("v-if",!0):(o(),i(l,{key:0,class:"-mt-[20rpx]"},{default:d((()=>[u(a,{list:c(B),onClick:z,current:V.value,itemStyle:"width:50%;height:88rpx;box-sizing: border-box; font-size: 28rpx;",activeStyle:{color:"var(--primary-color)"},lineColor:"var(--primary-color)"},null,8,["list","current","activeStyle"])])),_:1})),u(C,{"scroll-y":"true",class:"h-[50vh] pt-[10rpx]"},{default:d((()=>[_(u(l,{class:"px-[var(--popup-sidebar-m)] pb-[30rpx] pt-0 text-sm"},{default:d((()=>[(o(!0),v(m,null,f(O.value,(e=>(o(),i(l,{class:y(["mt-[var(--top-m)] px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] border-1 border-[#eee] border-solid rounded-[20rpx]",{"!border-[var(--primary-color)] bg-[var(--primary-color-light2)]":$.value&&$.value.id==e.id}]),onClick:t=>{return l=e,void($.value?$.value=$.value.id!=l.id?l:null:$.value=l);var l}},{default:d((()=>[u(l,{class:y(["flex border-0 !border-b border-[#eee] border-dashed pb-[20rpx]",{"!border-[var(--primary-color)]":$.value&&$.value.id==e.id}])},{default:d((()=>[u(l,{class:"flex-1 w-0"},{default:d((()=>[u(l,{class:"text-[30rpx] mb-[20rpx] font-500"},{default:d((()=>[p(g(e.title),1)])),_:2},1024),e.min_condition_money>0?(o(),i(l,{key:0,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:d((()=>[p("满"+g(e.min_condition_money)+"可用",1)])),_:2},1024)):(o(),i(l,{key:1,class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:d((()=>[p("无门槛券")])),_:1}))])),_:2},1024),u(l,{class:"text-[36rpx] price-font"},{default:d((()=>[u(F,{class:"text-xs mr-[2rpx]"},{default:d((()=>[p("￥")])),_:1}),p(" "+g(e.price),1)])),_:2},1024)])),_:2},1032,["class"]),u(l,{class:"pt-[20rpx] text-[24rpx] text-[var(--text-color-light6)]"},{default:d((()=>[p(g(e.create_time)+" ~ "+g(e.expire_time)+"有效",1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),256))])),_:1},512),[[b,0==V.value]]),_(u(l,{class:"px-[var(--popup-sidebar-m)] pb-[30rpx] pt-0 text-sm"},{default:d((()=>[(o(!0),v(m,null,f(T.value,(e=>(o(),i(l,{class:"mt-[var(--top-m)] px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] border-1 !border-[#eee] border-solid rounded-[var(--rounded-mid)] bg-[var(--temp-bg)]"},{default:d((()=>[u(l,{class:"flex border-0 !border-b !border-[#ddd] border-dashed pb-[20rpx]"},{default:d((()=>[u(l,{class:"flex-1 w-0"},{default:d((()=>[u(l,{class:"text-[30rpx] mb-[20rpx] font-500"},{default:d((()=>[p(g(e.title),1)])),_:2},1024),e.min_condition_money>0?(o(),i(l,{key:0,class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("满"+g(e.min_condition_money)+"可用",1)])),_:2},1024)):(o(),i(l,{key:1,class:"text-[24rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("无门槛券")])),_:1}))])),_:2},1024),u(l,{class:"text-[36rpx] price-font"},{default:d((()=>[u(F,{class:"text-xs mr-[2rpx]"},{default:d((()=>[p("￥")])),_:1}),p(" "+g(e.price),1)])),_:2},1024)])),_:2},1024),u(l,{class:"pt-[20rpx] text-[24rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p(g(e.create_time)+" ~ "+g(e.expire_time)+"期间有效",1)])),_:2},1024),u(l,{class:"text-[24rpx] pt-[10rpx] flex text-[var(--text-color-light9)]"},{default:d((()=>[p("不可用原因："+g(e.error),1)])),_:2},1024)])),_:2},1024)))),256))])),_:1},512),[[b,1==V.value]])])),_:1}),u(l,{class:"btn-wrap"},{default:d((()=>[u(S,{class:"primary-btn-bg btn",onClick:E},{default:d((()=>[p("确认")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])}}});const pe=e({__name:"goods-detail",setup(e,{expose:l}){const a=t(!1),c=t(!1),_=t(),v=t(),m=e=>{var t;(t=e,F.get(`shop_impulse_buy/detail/${t.impulse_buy_goods_id}`,t)).then((({data:e})=>{_.value=C(e),_.value.goods.goods_image=_.value.goods.goods_image_thumb_big,_.value.goods.goods_image.forEach(((e,t)=>{_.value.goods.goods_image[t]=S(e)})),c.value=!1}))},f=()=>{v.value&&v.value.num,a.value=!1},b=t(!1),V=e=>{let t=e.detail.scrollTop;b.value=t>375};return l({open:e=>{_.value="",a.value=!0,v.value=e,m({impulse_buy_goods_id:e.impulse_buy_goods_id})}}),(e,t)=>{const l=k,c=r(s("u-swiper"),oe),m=h,F=r(s("u-parse"),se),C=j,S=w,O=r(s("u-popup"),le);return o(),i(O,{show:a.value,onClose:t[2]||(t[2]=e=>a.value=!1),mode:"bottom",round:10},{default:d((()=>[_.value?(o(),i(l,{key:0,onTouchmove:t[1]||(t[1]=n((()=>{}),["prevent","stop"])),class:"popup-common bg-[var(--page-bg-color)]"},{default:d((()=>[u(l,{class:y(["absolute top-[30rpx] right-[20rpx] z-10 p-[10rpx] bg-[rgba(255,255,255,.4)] rounded-full border-[2rpx] border-solid border-transparent box-border nc-iconfont nc-icon-guanbiV6xx2 font-bold text-[#303133] text-[36rpx]",{"border-[#d8d8d8]":b.value}]),onClick:t[0]||(t[0]=e=>a.value=!1)},null,8,["class"]),u(C,{"scroll-y":"true",class:"h-[80vh]",onScroll:V},{default:d((()=>[u(l,{class:"w-full transition-transform duration-300 ease-linear transform translate-x-0"},{default:d((()=>[u(l,{class:"swiper-box"},{default:d((()=>[u(c,{list:_.value.goods.goods_image,indicator:_.value.goods.goods_image.length,indicatorStyle:{bottom:"70rpx"},autoplay:!0,height:"100vw",radius:"0"},null,8,["list","indicator"])])),_:1})])),_:1}),u(l,{class:"bg-[var(--page-bg-color)] rounded-tr-[40rpx] rounded-tl-[40rpx] overflow-hidden -mt-[34rpx] relative"},{default:d((()=>[u(l,{class:"flex flex-col px-[30rpx] pt-[30rpx]"},{default:d((()=>[u(l,{class:"text-[var(--price-text-color)] flex items-baseline mb-[12rpx]"},{default:d((()=>[u(l,{class:"inline-block goods-price-time"},{default:d((()=>[u(m,{class:"price-font text-[32rpx]"},{default:d((()=>[p("￥")])),_:1}),u(m,{class:"price-font text-[48rpx]"},{default:d((()=>[p(g(parseFloat(_.value.impulse_buy_price).toFixed(2).split(".")[0]),1)])),_:1}),u(m,{class:"price-font text-[32rpx] mr-[10rpx]"},{default:d((()=>[p("."+g(parseFloat(_.value.impulse_buy_price).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),x(' <text class="text-[26rpx] text-[var(--text-color-light9)] line-through price-font" v-if="goodsDetail.market_price && parseFloat(goodsDetail.market_price)">\r\n                            ￥{{ goodsDetail.market_price }}\r\n                            </text> ')])),_:1}),u(l,{class:"text-[#333] font-medium text-[30rpx] multi-hidden leading-[40rpx]"},{default:d((()=>[p(g(_.value.goods.goods_name),1)])),_:1}),u(l,{class:"flex justify-between items-start mt-[24rpx]"},{default:d((()=>[_.value.sku&&_.value.sku.sku_name?(o(),i(l,{key:0,class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:d((()=>[u(m,{class:"whitespace-nowrap mr-[4rpx]"},{default:d((()=>[p("已选:")])),_:1}),u(m,null,{default:d((()=>[p(g(_.value.sku.sku_name),1)])),_:1})])),_:1})):x("v-if",!0),u(l,{class:"text-[24rpx] leading-[34rpx] text-[var(--text-color-light6)]"},{default:d((()=>[u(m,{class:"whitespace-nowrap mr-[4rpx]"},{default:d((()=>[p("库存:")])),_:1}),u(m,null,{default:d((()=>[p(g(_.value.sku.stock),1)])),_:1}),u(m,null,{default:d((()=>[p(g(_.value.goods.unit),1)])),_:1})])),_:1})])),_:1})])),_:1}),u(l,{class:"mt-[40rpx] sidebar-margin card-template px-[var(--pad-sidebar-m)]"},{default:d((()=>[u(l,{class:"title !py-[0] !text-left !text-[30rpx]"},{default:d((()=>[p("商品详情")])),_:1}),u(l,{class:"u-content"},{default:d((()=>[u(F,{content:_.value.goods.goods_desc,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;"}},null,8,["content"])])),_:1})])),_:1})])),_:1})])),_:1}),u(l,{class:"btn-wrap"},{default:d((()=>[u(S,{class:y(["primary-btn-bg btn",{"opacity-80":v.value&&v.value.num}]),onClick:f},{default:d((()=>[p("确定")])),_:1},8,["class"])])),_:1})])),_:1})):x("v-if",!0)])),_:1},8,["show"])}}}),ce=ie(e({__name:"ns-impulse-buy",props:["data","orderKey","deliveryType","calculateLoading"],emits:["confirm"],setup(e,{expose:l,emit:a}){const _=e,b=t(!1),C=t({}),z=t(!0),E=V({is_change:0,order_key:"",delivery_type:"",buy_sku_id:[]}),I=t(),R=t([]);F.get("shop_impulse_buy/config").then((e=>{C.value=e.data}));const L=(e={})=>{(function(e){return F.get("shop_impulse_buy/list",e)})(Object.assign(E,e)).then((e=>{R.value=e.data.list,z.value=e.data.is_show_change,R.value&&R.value.forEach((e=>{e.num=0}))}))},A=e=>{const t={impulse_buy_goods_id:e.impulse_buy_goods_id,num:e.num};I.value.open(t)};O((()=>{E.order_key=_.orderKey,E.delivery_type=_.deliveryType,L()}));const G=e=>({min:1,max:e.sku.stock||1}),U=e=>{e&&(!e||0!=Object.keys(e).length)&&e.goods_id&&e.sku_id&&(e.num>parseInt(e.stock)?D({title:T("stockNotEnough"),icon:"none"}):uni.$u.debounce((t=>{const l={sku_id:e.sku_id,num:e.num,impulse_buy_goods_id:e.impulse_buy_goods_id};a("confirm",l)}),500))},K=()=>{L({is_change:1}),a("confirm","")},N=e=>{let t="0.00";return t=Object.keys(e).length&&Object.keys(e.goods).length&&e.goods.member_discount&&e.member_price!=e.sku.price&&e.sku.member_price?e.sku.member_price:e.sku.price,t};return l({}),(e,t)=>{const l=h,a=$,F=k,V=r(s("u--image"),P),O=w,E=B,L=r(s("u-number-box"),re),q=r(s("u-parse"),se),Q=j,W=r(s("u-popup"),le);return R.value&&R.value.length?(o(),i(F,{key:0},{default:d((()=>[u(F,{class:"container rounded-[var(--rounded-big)] px-[24rpx] py-[30rpx] mb-[20rpx]"},{default:d((()=>[u(F,{class:"flex items-center justify-between"},{default:d((()=>[u(F,{class:"flex items-center"},{default:d((()=>["text"==C.value.type?(o(),i(l,{key:0,class:y(["head-title text-[28rpx] font-bold pr-[10rpx]",{"column-size !pr-[20rpx]":C.value.sub_title}])},{default:d((()=>[p(g(C.value.title),1)])),_:1},8,["class"])):x("v-if",!0),"image"==C.value.type?(o(),i(a,{key:1,class:y(["head-title h-[28rpx] w-[auto] pr-[10rpx]",{"column-size !pr-[20rpx]":C.value.sub_title}]),src:c(S)(C.value.title_image),mode:"heightFix"},null,8,["class","src"])):x("v-if",!0),C.value.sub_title?(o(),i(l,{key:2,class:"text-[24rpx] text-[#FF5900]"},{default:d((()=>[p(g(C.value.sub_title),1)])),_:1})):x("v-if",!0),C.value.rule?(o(),i(l,{key:3,class:"iconfont icon24gl-questionCircle text-[#666] ml-[4rpx] !text-[28rpx]",onClick:t[0]||(t[0]=e=>b.value=!0)})):x("v-if",!0)])),_:1}),z.value?(o(),i(F,{key:0,class:"flex items-center",onClick:K},{default:d((()=>[u(l,{class:"text-[24rpx] text-[#666]"},{default:d((()=>[p(g(c(T)("changeIt")),1)])),_:1}),u(l,{class:"iconfont iconshuaxin1 ml-[4rpx] text-[#666] !text-[28rpx]"})])),_:1})):x("v-if",!0)])),_:1}),u(F,{class:"space-y-4 mt-[30rpx]"},{default:d((()=>[(o(!0),v(m,null,f(R.value,((e,r)=>(o(),i(F,{key:r,class:"rounded-[20rpx] flex"},{default:d((()=>[u(V,{radius:"var(--goods-rounded-big)",width:"180rpx",height:"180rpx",src:c(S)(e.goods.goods_cover),model:"aspectFill",onClick:t=>A(e)},{error:d((()=>[u(a,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:c(S)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src","onClick"]),u(F,{class:"flex flex-1 w-0 flex-col justify-between ml-[20rpx] py-[6rpx]"},{default:d((()=>[u(F,{class:"line-normal"},{default:d((()=>[u(F,{class:"truncate text-[#303133] text-[28rpx] mb-[6rpx] leading-[32rpx]",onClick:t=>A(e)},{default:d((()=>[p(g(e.goods.goods_name),1)])),_:2},1032,["onClick"]),u(F,{class:"truncate text-[#666] text-[24rpx] mb-[4rpx] leading-[32rpx]"},{default:d((()=>[p(g(e.sku.sku_name),1)])),_:2},1024),Number(e.limit_buy)>0?(o(),i(F,{key:0,class:"text-[#DA7E2D] break-words text-[24rpx] leading-[32rpx]"},{default:d((()=>[p(g(`${c(T)("limitPlaceholderOne")}${e.limit_buy}${c(T)("limitPlaceholderTwo")}￥${N(e)}${c(T)("limitPlaceholderThree")}`),1)])),_:2},1024)):x("v-if",!0)])),_:2},1024),u(F,{class:"flex items-center justify-between mt-2"},{default:d((()=>[u(F,{class:"text-[var(--price-text-color)] flex items-baseline price-font"},{default:d((()=>[u(l,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:d((()=>[p("￥")])),_:1}),u(l,{class:"text-[40rpx] font-500"},{default:d((()=>[p(g(parseFloat(e.impulse_buy_price).toFixed(2).split(".")[0]),1)])),_:2},1024),u(l,{class:"text-[24rpx] font-500"},{default:d((()=>[p("."+g(parseFloat(e.impulse_buy_price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),u(F,{class:"h-[50rpx] flex items-center"},{default:d((()=>[e.num?(o(),i(L,{key:1,modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,min:0,max:G(e).max,integer:"",step:1,"input-width":"68rpx",disabled:_.calculateLoading,"input-height":"52rpx","button-size":"52rpx",disabledInput:"",onChange:e=>((e,t)=>{const l=R.value[t];U(l)})(0,r)},{minus:d((()=>[u(F,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>{var l;1==(l=e).num&&(l.num=0,U(l))}},{default:d((()=>[u(l,{class:y([{"text-[#303133]":e.num!==G(e).min,"text-[var(--text-color-light9)]":_.calculateLoading},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jianV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),input:d((()=>[u(E,{class:"text-[#303133] text-[28rpx] mx-[14rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onBlur:e=>((e,t)=>{setTimeout((()=>{const e=R.value[t];e.num=parseInt(e.num),e.num?e.num<=G(e).min&&(e.num=G(e).min):e.num=0,e.num>=G(e).max&&(e.num=G(e).max),U(e)}),0)})(0,r),onClick:t[1]||(t[1]=n((()=>{}),["stop"])),modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,disabled:_.calculateLoading},null,8,["onBlur","modelValue","onUpdate:modelValue","disabled"])])),plus:d((()=>[u(F,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>{var l;(l=e).num>=l.sku.stock&&D({title:T("stockNotEnough"),icon:"none"})}},{default:d((()=>[u(l,{class:y([{"text-[var(--text-color-light9)]":e.num===G(e).max||_.calculateLoading," text-[#303133]":e.num!==G(e).max},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jiahaoV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue","max","disabled","onChange"])):(o(),i(O,{key:0,onClick:t=>{return(l=e).num=G(l).min,void U(l);var l},class:y(["!rounded-[30rpx] !bg-transparent !text-[var(--primary-color)] text-[22rpx] border-[2rpx] border-solid border-[var(--primary-color)] min-w-[130rpx] h-[50rpx] flex items-center",{"opacity-80":_.calculateLoading}]),disabled:_.calculateLoading,"hover-class":"none"},{default:d((()=>[p(g(c(T)("buyWithOneClick")),1)])),_:2},1032,["onClick","disabled","class"]))])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),u(F,{onTouchmove:t[4]||(t[4]=n((()=>{}),["prevent","stop"]))},{default:d((()=>[u(W,{show:b.value,onClose:t[3]||(t[3]=e=>b.value=!1),mode:"center",round:"var(--rounded-big)",safeAreaInsetBottom:!1},{default:d((()=>[u(F,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:d((()=>[u(F,{class:"title"},{default:d((()=>[p(g(c(T)("activityInstructions")),1)])),_:1}),u(Q,{"scroll-y":!0,class:"px-[10rpx] box-border h-[350rpx]"},{default:d((()=>[u(q,{content:C.value.rule,tagStyle:{img:"vertical-align: top;",p:"overflow: hidden;word-break:break-word;",span:"text-wrap-mode:wrap !important;"}},null,8,["content"])])),_:1}),u(F,{class:"btn-wrap !pt-[40rpx]"},{default:d((()=>[u(O,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:t[2]||(t[2]=e=>b.value=!1)},{default:d((()=>[p(g(c(T)("know")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),x(" 商品详情 "),u(pe,{ref_key:"goodsDetailRef",ref:I},null,512)])),_:1})])),_:1})):x("v-if",!0)}}}),[["__scopeId","data-v-e4c7b9f3"]]),xe=ie(e({__name:"payment",setup(e){const _=z(),b=a((()=>_.info));de().setTopTabbarParam({title:"待付款订单"});const j=E(),F=t(),C=t({order_key:"",member_remark:"",discount:{},invoice:{},delivery:{delivery_type:"",buyer_ask_delivery_time:"",taker_name:"",taker_mobile:"",local_delivery_type:"now"},extend_data:{},form_data:{}}),V=t(null),O=t(null),T=t(),X=t(),te=t(),le=t(),re=t(),se=t(!1),oe=t(0),ie=t([]),pe=t(!1);uni.getStorageSync("orderCreateData")&&Object.assign(C.value,uni.getStorageSync("orderCreateData"));const xe=t(null),_e=t(null),ve=t(0),me=t({}),fe=t(null),ye=()=>{fe.value?fe.value.show=!0:"store"==C.value.delivery.delivery_type&&D({title:"请选择自提点",icon:"none"})},ge=t(!1),be=e=>{"local_delivery"==C.value.delivery.delivery_type?(ge.value=!0,e.includes("立即配送")?(C.value.delivery.buyer_ask_delivery_time="",C.value.delivery.local_delivery_type="now"):(C.value.delivery.buyer_ask_delivery_time=e,C.value.delivery.local_delivery_type="subscribe")):C.value.delivery.buyer_ask_delivery_time=e},ke=e=>{},he=t(null),je=e=>{"local_delivery"==C.value.delivery.delivery_type&&e.includes("立即配送")?he.value=null:he.value=e},we=t({});I((()=>{Y().then((e=>{we.value=e.data}))}));const Fe=uni.getStorageSync("selectAddressCallback");Fe&&(C.value.order_key="",C.value.delivery.delivery_type=Fe.delivery,C.value.delivery.take_address_id=Fe.address_id,uni.removeStorage({key:"selectAddressCallback"}));const Ce=async(e,t)=>{await G(),X.value&&(C.value.delivery.delivery_type!=e&&C.value&&(delete C.value.impulse_buy_goods,C.value.delivery.buyer_ask_delivery_time="",me.value={}),"store"==e&&0==ve.value&&(ve.value++,X.value.getData((e=>{var t;e.length&&(C.value.delivery.take_store_id=(null==(t=e[0])?void 0:t.store_id)??0,Se())}))),C.value.delivery.delivery_type!=e&&(oe.value=t,C.value.order_key="",C.value.delivery.delivery_type=e,C.value.delivery.take_address_id=0,Se()))},Se=(e={})=>{const t=Object.assign({},C.value,e);pe.value=!0,Z(t).then((({data:e})=>{var t,l;O.value=A(e),pe.value=!1,O.value.goods=[],O.value.goods_data&&Object.values(O.value.goods_data).length&&Object.values(O.value.goods_data).forEach(((e,t)=>{O.value.goods.push(e)})),"store"==C.value.delivery.delivery_type?(C.value.delivery.taker_name=b.value.nickname,C.value.delivery.taker_mobile=b.value.mobile):O.value.delivery&&O.value.delivery.take_address&&(C.value.delivery.taker_name=O.value.delivery.take_address.name,C.value.delivery.taker_mobile=O.value.delivery.take_address.mobile),"local_delivery"==C.value.delivery.delivery_type&&(me.value={time_interval:we.value.time_interval,time_week:(t=we.value.time_type,l=we.value.time_week,0===t?["1","2","3","4","5","6","0"]:l?l.split(",").map((e=>e.trim())):[]),trade_time_json:we.value.delivery_time,most_day:we.value.most_day,advance_day:we.value.advance_day,type:"subscribe"}),C.value.order_key=e.order_key,O.value.delivery.delivery_type_list&&(ie.value=A(Object.values(O.value.delivery.delivery_type_list))),O.value.discount&&O.value.discount.manjian&&(O.value.manjian=O.value.discount.manjian),O.value.delivery.take_store&&(me.value={time_interval:O.value.delivery.take_store.time_interval,time_week:O.value.delivery.take_store.time_week,trade_time_json:O.value.delivery.take_store.trade_time_json}),Fe&&(oe.value=ie.value.findIndex((e=>e.key===O.value.delivery.delivery_type))),!C.value.delivery.delivery_type&&e.delivery.delivery_type&&(C.value.delivery.delivery_type=e.delivery.delivery_type),G((()=>{setTimeout((()=>{ie.value&&Object.keys(ie.value).length&&"store"==ie.value[0].key&&X.value&&X.value.getData((e=>{e.length&&(C.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))}),500)}))})).catch((()=>{pe.value=!1}))};Se(),l((()=>ie.value.length),((e,t)=>{ie.value.length&&uni.getStorageSync("distributionType")&&(ie.value.forEach(((e,t)=>{e.name==uni.getStorageSync("distributionType")&&(oe.value=t,Ce(e.key,t))})),uni.removeStorage({key:"distributionType"}))}));let Ve=0;const Oe=(e={})=>{if(e&&Object.keys(e).length){let t=A(e);C.value.impulse_buy_goods=C.value.impulse_buy_goods&&C.value.impulse_buy_goods.length?C.value.impulse_buy_goods:[],C.value.impulse_buy_goods.forEach(((l,a,r)=>{t.impulse_buy_goods_id==l.impulse_buy_goods_id&&(l.num=e.num,l.num||r.splice(a,1),t="")})),t&&C.value.impulse_buy_goods.push(t)}else C.value.impulse_buy_goods&&delete C.value.impulse_buy_goods;Se({is_need_recalculate:1})},Te=()=>{if(De()&&!se.value){if(_e.value){let e=!0;for(let t=0;t<_e.value.length;t++)if(!_e.value[t].verify()){e=!1;break}if(!e)return}xe.value&&!xe.value.verify()||(se.value=!0,C.value.form_data.order={},C.value.form_data.goods={},xe.value&&(C.value.form_data.form_id=O.value.form_id,C.value.form_data.order=xe.value.getData()),_e.value&&O.value.goods.forEach((e=>{if(e.goods.form_id)for(let t=0;t<_e.value.length;t++){let l=_e.value[t].getData();l.relate_id==e.sku_id&&e.goods.form_id==l.form_id&&(C.value.form_data.goods[e.sku_id]=l)}})),ee(C.value).then((({data:e})=>{var t;if(Ve=e.order_id,xe.value&&xe.value.clearStorage(),_e.value)for(let l=0;l<_e.value.length;l++)_e.value[l].clearStorage();C.value.form_data={},0==O.value.basic.order_money?U({url:"/addon/shop/pages/order/detail",param:{order_id:Ve},mode:"redirectTo"}):null==(t=te.value)||t.open(e.trade_type,e.order_id,`/addon/shop/pages/order/detail?order_id=${e.order_id}`)})).catch((e=>{401==e.code&&(D({icon:"none",title:"登录过期，请重新登录"}),U({url:"/addon/shop/pages/index",mode:"reLaunch"})),C.value.form_data={},se.value=!1})))}},De=()=>{const e=C.value;if(O.value.basic.has_goods_types.includes("real")){if(["express","local_delivery"].includes(e.delivery.delivery_type)&&!O.value.delivery.take_address)return D({title:"请选择收货地址",icon:"none"}),!1;if("store"==e.delivery.delivery_type&&!e.delivery.take_store_id)return D({title:"请选择自提点",icon:"none"}),!1}if("store"==e.delivery.delivery_type){if(!e.delivery.taker_name)return D({title:"请输入姓名",icon:"none"}),!1;if(!e.delivery.taker_mobile)return D({title:"请输入手机号",icon:"none"}),!1;if(!/^1[3-9]\d{9}$/.test(e.delivery.taker_mobile))return D({title:"请输入正确的手机号",icon:"none"}),!1;if(!e.delivery.buyer_ask_delivery_time)return D({title:"请选择自提时间",icon:"none"}),!1}return!("local_delivery"==e.delivery.delivery_type&&!ge.value)||(D({title:"当前时间不支持配送",icon:"none"}),!1)},$e=()=>{U({url:"/addon/shop/pages/order/detail",param:{order_id:Ve},mode:"redirectTo"})},Be=()=>{let e={};e.delivery=C.value.delivery.delivery_type,e.type="local_delivery"==C.value.delivery.delivery_type?"location_address":"address",e.id=O.value.delivery.take_address.id,le.value.open(e)},ze=a((()=>{var e;return(null==(e=T.value)?void 0:e.couponList)||[]})),Ee=e=>{C.value.discount.coupon_id=e?e.id:0,Se()},Ie=e=>{C.value.delivery.take_store_id=e&&e.store_id?e.store_id:0,e?me.value={time_interval:e.time_interval,time_week:e.time_week,trade_time_json:e.trade_time_json}:(me.value={},C.value.delivery.buyer_ask_delivery_time=""),Se()},Re=e=>{C.value.invoice=e},Le=e=>{C.value.order_key="",C.value.delivery.delivery_type=e.delivery,C.value.delivery.take_address_id=e.address_id,Se()};return(e,t)=>{const l=k,a=$,_=h,b=B,D=r(s("u--image"),P),z=r(s("u-alert"),H),E=w,I=r(s("u-tabbar"),J),G=r(s("pay"),M);return o(),i(l,{style:R(e.themeColor()),class:"payment-wrap"},{default:d((()=>[O.value?(o(),i(l,{key:0,class:"payment-body min-h-[100vh]"},{default:d((()=>[u(l,{class:"pt-[30rpx] sidebar-margin payment-bottom"},{default:d((()=>[x(" 配送方式 "),O.value.basic.has_goods_types.includes("real")&&ie.value.length?(o(),i(l,{key:0,class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] bg-white",style:R({backgroundImage:`url(${c(S)("addon/shop/payment/head_bg.png")})`,backgroundSize:"100%",backgroundRepeat:"no-repeat",backgroundPosition:"bottom"})},{default:d((()=>[ie.value.length>1?(o(),i(l,{key:0,class:"rounded-tl-[var(--rounded-big)] rounded-tr-[var(--rounded-big)] head-tab flex items-center w-full bg-[var(--shop-payment-header-tab-color)]"},{default:d((()=>[(o(!0),v(m,null,f(ie.value,((e,t)=>(o(),i(l,{key:t,class:y(["head-tab-item flex-1 relative",{active:t===oe.value}])},{default:d((()=>[u(l,{class:"h-[74rpx] relative z-10 text-center leading-[74rpx] text-[28rpx]",onClick:l=>Ce(e.key,t)},{default:d((()=>[p(g(e.name),1)])),_:2},1032,["onClick"]),t===oe.value&&3==ie.value.length?(o(),i(a,{key:0,class:"tab-image absolute bottom-[-2rpx] h-[94rpx] w-[240rpx]",src:c(S)(`addon/shop/payment/tab_${t}.png`),mode:"aspectFit"},null,8,["src"])):t===oe.value&&2==ie.value.length?(o(),i(a,{key:1,class:"tab-img absolute bottom-[-2rpx] h-[95rpx] w-[354rpx]",src:c(S)(`addon/shop/payment/tabstyle_${t}.png`),mode:"aspectFit"},null,8,["src"])):x("v-if",!0)])),_:2},1032,["class"])))),128))])),_:1})):x("v-if",!0),u(l,{class:"min-h-[140rpx] flex items-center px-[30rpx]"},{default:d((()=>[x(" 收货地址 "),["express","local_delivery"].includes(C.value.delivery.delivery_type)?(o(),i(l,{key:0,class:"w-full",onClick:Be},{default:d((()=>[e.$u.test.isEmpty(O.value.delivery.take_address)?(o(),i(l,{key:1,class:"flex items-center"},{default:d((()=>[u(a,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:c(S)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),u(_,{class:"text-[28rpx]"},{default:d((()=>[p("添加收货地址")])),_:1}),u(_,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)] ml-auto"})])),_:1})):(o(),i(l,{key:0,class:"pt-[20rpx] pb-[30rpx] flex items-center"},{default:d((()=>[u(a,{class:"w-[60rpx] h-[60rpx] mr-[20rpx] flex-shrink-0",src:c(S)("addon/shop/payment/position_01.png"),mode:"aspectFit"},null,8,["src"]),u(l,{class:"flex flex-col overflow-hidden"},{default:d((()=>[u(_,{class:"text-[26rpx] text-[var(--text-color-light9)] mt-[16rpx] truncate max-w-[536rpx]"},{default:d((()=>[p(g(O.value.delivery.take_address.full_address.split(O.value.delivery.take_address.address)[0]),1)])),_:1}),u(_,{class:"font-500 text-[30rpx] mt-[14rpx] text-[#333] truncate max-w-[536rpx]"},{default:d((()=>[p(g(O.value.delivery.take_address.address),1)])),_:1}),u(l,{class:"flex items-center text-[26rpx] text-[var(--text-color-light6)] mt-[16rpx]"},{default:d((()=>[u(_,{class:"mr-[16rpx]"},{default:d((()=>[p(g(O.value.delivery.take_address.name),1)])),_:1}),u(_,null,{default:d((()=>[p(g(c(K)(O.value.delivery.take_address.mobile)),1)])),_:1})])),_:1})])),_:1}),u(_,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):x("v-if",!0),x(" 自提点 "),"store"==C.value.delivery.delivery_type?(o(),i(l,{key:1,class:"flex items-center w-full",onClick:t[0]||(t[0]=e=>(X.value&&(C.value.delivery.take_store_id||X.value.getData((e=>{e.length&&(C.value.delivery.take_store_id=e[0]&&e[0].store_id?e[0].store_id:0)}))),void X.value.open()))},{default:d((()=>[e.$u.test.isEmpty(O.value.delivery.take_store)?(o(),i(l,{key:1,class:"flex items-center w-full"},{default:d((()=>[u(a,{class:"w-[26rpx] h-[30rpx] mr-[10rpx]",src:c(S)("addon/shop/payment/position_02.png"),mode:"aspectFit"},null,8,["src"]),u(_,{class:"text-[28rpx]"},{default:d((()=>[p("请选择自提点")])),_:1}),u(_,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):(o(),i(l,{key:0,class:"pt-[40rpx] pb-[30rpx] w-full flex items-center"},{default:d((()=>[u(l,{class:"flex flex-col"},{default:d((()=>[u(l,{class:"text-[30rpx] font-500 text-[#303133] mb-[20rpx]"},{default:d((()=>[p(g(O.value.delivery.take_store.store_name),1)])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[20rpx] leading-[1.4] flex"},{default:d((()=>[u(_,{class:"flex-shrink-0"},{default:d((()=>[p("门店地址：")])),_:1}),u(_,{class:"max-w-[490rpx]"},{default:d((()=>[p(g(O.value.delivery.take_store.full_address),1)])),_:1})])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)] mb-[20rpx]"},{default:d((()=>[u(_,null,{default:d((()=>[p("联系电话：")])),_:1}),u(_,null,{default:d((()=>[p(g(O.value.delivery.take_store.store_mobile),1)])),_:1})])),_:1}),u(l,{class:"text-[24rpx] text-[var(--text-color-light6)]"},{default:d((()=>[u(_,null,{default:d((()=>[p("营业时间：")])),_:1}),u(_,null,{default:d((()=>[p(g(O.value.delivery.take_store.trade_time),1)])),_:1})])),_:1})])),_:1}),u(_,{class:"ml-auto nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})):x("v-if",!0)])),_:1}),"store"==C.value.delivery.delivery_type?(o(),i(l,{key:1},{default:d((()=>[x(" 姓名 "),u(l,{class:"px-[20rpx] py-[14rpx]"},{default:d((()=>[u(l,{class:"flex justify-between items-center"},{default:d((()=>[u(l,{class:"text-color text-[26rpx]",onClick:ye},{default:d((()=>[p("姓名")])),_:1}),u(b,{class:"text-right",maxlength:"20","placeholder-style":"color:#B1B3B5;font-size:26rpx",placeholder:"请输入",modelValue:C.value.delivery.taker_name,"onUpdate:modelValue":t[1]||(t[1]=e=>C.value.delivery.taker_name=e)},null,8,["modelValue"])])),_:1})])),_:1}),x(" 预留手机 "),u(l,{class:"px-[20rpx] py-[14rpx]"},{default:d((()=>[u(l,{class:"flex justify-between items-center"},{default:d((()=>[u(l,{class:"text-color text-[26rpx]"},{default:d((()=>[p("预留手机")])),_:1}),u(b,{class:"text-right",maxlength:"11","placeholder-style":"color:#B1B3B5;font-size:26rpx",placeholder:"请输入",modelValue:C.value.delivery.taker_mobile,"onUpdate:modelValue":t[2]||(t[2]=e=>C.value.delivery.taker_mobile=e)},null,8,["modelValue"])])),_:1})])),_:1}),x(" 提货时间 "),u(l,{class:"flex justify-between items-center px-[20rpx] pt-[14rpx] pb-[24rpx]"},{default:d((()=>[u(l,{class:"text-color text-[26rpx]"},{default:d((()=>[p("提货时间")])),_:1}),u(l,{class:"flex",onClick:ye},{default:d((()=>[u(l,{class:y(["text-[26rpx] ml-2 text-right",{"text-[#63676D]":!C.value.delivery.buyer_ask_delivery_time}])},{default:d((()=>[p(g(C.value.delivery.buyer_ask_delivery_time?he.value:"选择提货时间"),1)])),_:1},8,["class"]),u(_,{class:"text-[26rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1})])),_:1})):x("v-if",!0),"local_delivery"==C.value.delivery.delivery_type?(o(),i(l,{key:2,class:""},{default:d((()=>[u(l,{class:"flex justify-between items-center px-[20rpx] pt-[14rpx] pb-[24rpx]",onClick:ye},{default:d((()=>[u(l,{class:"text-color text-[26rpx]"},{default:d((()=>[p(g("subscribe"==C.value.delivery.local_delivery_type?"预约配送":""),1)])),_:1}),u(l,{class:"flex items-center"},{default:d((()=>["subscribe"==C.value.delivery.local_delivery_type?(o(),i(l,{key:0,class:y(["text-[26rpx] ml-2 text-right",{"text-[#63676D]":!C.value.delivery.buyer_ask_delivery_time}])},{default:d((()=>[p(g(C.value.delivery.buyer_ask_delivery_time?he.value:"选择时间"),1)])),_:1},8,["class"])):(o(),i(l,{key:1,class:"text-[26rpx] ml-2 text-right"},{default:d((()=>[p(g(ge.value?"立即配送":"当前时间不支持配送"),1)])),_:1})),u(_,{class:"text-[26rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-youV6xx"})])),_:1})])),_:1})])),_:1})):x("v-if",!0)])),_:1},8,["style"])):x("v-if",!0),O.value.basic.has_goods_types.includes("real")&&!ie.value.length?(o(),i(l,{key:1,class:"mb-[var(--top-m)] card-template h-[100rpx] flex items-center"},{default:d((()=>[L("p",{class:"text-[28rpx] text-[var(--primary-color)]"},"商家尚未配置配送方式")])),_:1})):x("v-if",!0),u(l,{class:"mb-[var(--top-m)] card-template p-[0] pb-[var(--pad-top-m)]"},{default:d((()=>[u(l,{class:"pt-[var(--pad-top-m)] pb-[14rpx]"},{default:d((()=>[(o(!0),v(m,null,f(O.value.goods,((e,t)=>(o(),v(m,{key:t},[1!=e.is_impulse_buy?(o(),i(l,{key:0,class:y(["px-[var(--pad-sidebar-m)]",{"mb-[20rpx]":t+1!=O.value.goods.length}])},{default:d((()=>[u(l,{class:"flex"},{default:d((()=>[u(D,{radius:"var(--goods-rounded-big)",width:"180rpx",height:"180rpx",src:c(S)(e.sku_image),model:"aspectFill"},{error:d((()=>[u(a,{class:"w-[180rpx] h-[180rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:c(S)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),u(l,{class:"flex flex-1 w-0 flex-col justify-between ml-[20rpx] py-[6rpx]"},{default:d((()=>[u(l,{class:"line-normal"},{default:d((()=>[u(l,{class:"truncate text-[#303133] text-[28rpx] leading-[32rpx]"},{default:d((()=>[p(g(e.goods.goods_name),1)])),_:2},1024),e.sku_name?(o(),i(l,{key:0,class:"mt-[14rpx] flex"},{default:d((()=>[u(_,{class:"truncate text-[24rpx] text-[var(--text-color-light9)] leading-[28rpx]"},{default:d((()=>[p(g(e.sku_name),1)])),_:2},1024)])),_:2},1024)):x("v-if",!0)])),_:2},1024),e.manjian_info&&Object.keys(e.manjian_info).length?(o(),i(l,{key:0,class:"flex items-center mt-[10rpx] mb-[auto]",onClick:n((t=>(e=>{let t={};t.condition_type=A(e).condition_type,t.rule_json=[A(e).rule],t.name=A(e).manjian_name,V.value.open(t)})(e.manjian_info)),["stop"])},{default:d((()=>[u(l,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[20rpx] flex items-center justify-center w-[88rpx] h-[36rpx] mr-[6rpx]"},{default:d((()=>[p("满减送")])),_:1}),u(_,{class:"text-[22rpx] text-[#999]"},{default:d((()=>[p(g(e.manjian_info.manjian_name),1)])),_:2},1024)])),_:2},1032,["onClick"])):x("v-if",!0),e.not_support_delivery?(o(),i(l,{key:1,class:y(["mb-auto",{"mt-[6rpx]":!e.sku_name}])},{default:d((()=>[u(z,{type:"error",description:"该商品不支持当前所选配送方式",class:"leading-[30rpx] !inline-block",fontSize:"11"})])),_:2},1032,["class"])):x("v-if",!0),u(l,{class:"flex justify-between items-baseline"},{default:d((()=>[u(l,{class:"text-[var(--price-text-color)] flex items-baseline price-font"},{default:d((()=>[u(_,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:d((()=>[p("￥")])),_:1}),u(_,{class:"text-[40rpx] font-500"},{default:d((()=>[p(g(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),u(_,{class:"text-[24rpx] font-500"},{default:d((()=>[p("."+g(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),u(l,{class:"font-400 text-[28rpx] text-[#303133]"},{default:d((()=>[u(_,null,{default:d((()=>[p("x")])),_:1}),u(_,null,{default:d((()=>[p(g(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),e.is_newcomer&&e.newcomer_price!=e.price&&e.num>1?(o(),i(l,{key:0,class:y(["flex items-center mt-[8rpx]",{"pb-[40rpx]":t+1!=Object.keys(O.value.goods_data).length}])},{default:d((()=>[u(a,{class:"h-[24rpx] w-[56rpx]",src:c(S)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"]),u(l,{class:"text-[24rpx] text-[#FFB000] leading-[34rpx] ml-[8rpx]"},{default:d((()=>[p("第1"+g(e.goods.unit)+"，￥"+g(parseFloat(e.newcomer_price).toFixed(2))+"/"+g(e.goods.unit)+"；第"+g(e.num>2?"2~"+e.num:"2")+g(e.goods.unit)+"，￥"+g(parseFloat(e.price).toFixed(2))+"/"+g(e.goods.unit),1)])),_:2},1024)])),_:2},1032,["class"])):x("v-if",!0),e.goods.form_id?(o(),i(l,{key:1,class:"card-template !p-[0]"},{default:d((()=>[u(ue,{ref_for:!0,ref_key:"diyFormGoodsRef",ref:_e,form_id:e.goods.form_id,relate_id:e.sku_id,storage_name:"diyFormStorageByGoodsDetail_"+e.sku_id,form_border:"none"},null,8,["form_id","relate_id","storage_name"])])),_:2},1024)):x("v-if",!0)])),_:2},1032,["class"])):x("v-if",!0)],64)))),128)),x(" 赠品 "),O.value.gift_goods&&Object.keys(O.value.gift_goods).length?(o(),i(l,{key:0,class:"pt-[20rpx] mb-[10rpx] bg-[#f9f9f9] mt-[24rpx] mx-[var(--pad-sidebar-m)] rounded-[30rpx]"},{default:d((()=>[(o(!0),v(m,null,f(O.value.gift_goods,((e,t,r)=>(o(),i(l,{key:r,class:"flex px-[var(--pad-sidebar-m)] pb-[20rpx]"},{default:d((()=>[u(D,{radius:"var(--goods-rounded-big)",width:"120rpx",height:"120rpx",src:c(S)(e.sku_image),model:"aspectFill"},{error:d((()=>[u(a,{class:"w-[120rpx] h-[120rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:c(S)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),u(l,{class:"ml-[16rpx] py-[8rpx] flex flex-1 flex-col justify-between"},{default:d((()=>[u(l,{class:"flex items-center"},{default:d((()=>[u(l,{class:"bg-[var(--primary-color-light)] whitespace-nowrap text-[var(--primary-color)] rounded-[6rpx] text-[22rpx] flex items-center justify-center w-[64rpx] h-[34rpx] mr-[6rpx]"},{default:d((()=>[p("赠品")])),_:1}),u(l,{class:"text-[26rpx] max-w-[400rpx] truncate leading-[40rpx] text-[#333]"},{default:d((()=>[p(g(e.goods.goods_name),1)])),_:2},1024)])),_:2},1024),u(l,{class:"flex items-center"},{default:d((()=>[e.sku_name?(o(),i(l,{key:0,class:"text-[22rpx] text-[var(--text-color-light9)] truncate max-w-[400rpx] leading-[28rpx]"},{default:d((()=>[p(g(e.sku_name),1)])),_:2},1024)):x("v-if",!0),u(l,{class:"ml-[auto] font-400 text-[26rpx] text-[#303133]"},{default:d((()=>[u(_,null,{default:d((()=>[p("x")])),_:1}),u(_,null,{default:d((()=>[p(g(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):x("v-if",!0)])),_:1}),x(" 买家留言 "),u(l,{class:"bg-white flex items-center leading-[30rpx] px-[var(--pad-sidebar-m)]"},{default:d((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:d((()=>[p("买家留言")])),_:1}),u(l,{class:"flex-1 text-[#303133]"},{default:d((()=>[u(b,{type:"text",modelValue:C.value.member_remark,"onUpdate:modelValue":t[3]||(t[3]=e=>C.value.member_remark=e),class:"text-right text-[#333] text-[28rpx]",maxlength:"50",placeholder:"请输入留言信息给卖家","placeholder-class":"text-[var(--text-color-light9)] text-[28rpx]"},null,8,["modelValue"])])),_:1})])),_:1}),x(" 发票 "),re.value&&re.value.invoiceOpen?(o(),i(l,{key:0,class:"flex items-center text-[#303133] leading-[30rpx] mt-[30rpx] px-[var(--pad-sidebar-m)]",onClick:t[4]||(t[4]=e=>re.value.open())},{default:d((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133]"},{default:d((()=>[p("发票信息")])),_:1}),u(l,{class:"flex-1 w-0 text-right truncate"},{default:d((()=>[u(_,{class:"text-[28rpx] text-[#333]"},{default:d((()=>[p(g(C.value.invoice.header_name||"不需要发票"),1)])),_:1})])),_:1}),u(_,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):x("v-if",!0)])),_:1}),T.value&&c(ze).length?(o(),i(l,{key:2,class:"mb-[var(--top-m)] card-template"},{default:d((()=>[x(" 优惠券 "),c(ze).length?(o(),i(l,{key:0,class:"flex items-center h-[40rpx] leading-[40rpx]",onClick:t[5]||(t[5]=e=>T.value.open(C.value.discount.coupon_id))},{default:d((()=>[u(l,{class:"text-[28rpx] w-[150rpx] text-[#303133] flex-shrink-0"},{default:d((()=>[p("优惠券")])),_:1}),u(l,{class:"flex-1 flex justify-end truncate"},{default:d((()=>[O.value.discount&&O.value.discount.coupon?(o(),i(_,{key:0,class:"text-[var(--primary-color)] text-[28rpx] truncate"},{default:d((()=>[p(g(O.value.discount.coupon.title),1)])),_:1})):(o(),i(_,{key:1,class:"text-[28rpx] text-gray-subtitle"},{default:d((()=>[p("请选择优惠券")])),_:1}))])),_:1}),u(_,{class:"nc-iconfont nc-icon-youV6xx -mb-[2rpx] text-[26rpx] text-[var(--text-color-light9)]"})])),_:1})):x("v-if",!0)])),_:1})):x("v-if",!0),O.value.form_id?(o(),i(l,{key:3,class:"card-template py-[10rpx] mb-[var(--top-m)]"},{default:d((()=>[u(ue,{ref_key:"diyFormRef",ref:xe,form_id:O.value.form_id,storage_name:"diyFormStorageByOrderPayment"},null,8,["form_id"])])),_:1})):x("v-if",!0),x(" 顺手买 "),c(j).siteAddons.includes("shop_impulse_buy")&&(!C.value.extend_data||C.value.extend_data&&!C.value.extend_data.activity_type||"discount"==C.value.extend_data.activity_type)?(o(),i(c(ce),{key:oe.value,ref_key:"impulseBuyRef",ref:F,data:O.value.goods,"order-key":O.value.order_key,"delivery-type":C.value.delivery.delivery_type,"calculate-loading":pe.value,onConfirm:Oe},null,8,["data","order-key","delivery-type","calculate-loading"])):x("v-if",!0),u(l,{class:"card-template"},{default:d((()=>[u(l,{class:"title"},{default:d((()=>[p("价格明细")])),_:1}),u(l,{class:"card-template-item"},{default:d((()=>[u(l,{class:"text-[28rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:d((()=>[p("商品金额")])),_:1}),u(l,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:d((()=>[p("￥"+g(parseFloat(O.value.basic.goods_money).toFixed(2)),1)])),_:1})])),_:1}),parseFloat(O.value.basic.delivery_money)?(o(),i(l,{key:0,class:"card-template-item"},{default:d((()=>[u(l,{class:"text-[28rpx] w-[150rpx] leading-[30rpx] text-[#303133]"},{default:d((()=>[p("配送费用")])),_:1}),u(l,{class:"flex-1 w-0 text-right price-font text-[#333] text-[32rpx]"},{default:d((()=>[p("￥"+g(parseFloat(O.value.basic.delivery_money).toFixed(2)),1)])),_:1})])),_:1})):x("v-if",!0),parseFloat(O.value.basic.coupon_money)?(o(),i(l,{key:1,class:"card-template-item"},{default:d((()=>[u(l,{class:"text-[28rpx] w-[170rpx] leading-[30rpx] text-[#303133]"},{default:d((()=>[p("优惠券优惠")])),_:1}),u(l,{class:"flex-1 w-0 text-right text-[var(--price-text-color)] text-[32rpx] price-font leading-[1]"},{default:d((()=>[p("-￥"+g(parseFloat(O.value.basic.coupon_money).toFixed(2)),1)])),_:1})])),_:1})):x("v-if",!0),parseFloat(O.value.basic.manjian_discount_money)?(o(),i(l,{key:2,class:"card-template-item"},{default:d((()=>[u(l,{class:"text-[28rpx] w-[170rpx] leading-[30rpx] text-[#303133]"},{default:d((()=>[p("满减优惠")])),_:1}),u(l,{class:"flex-1 w-0 text-right text-[var(--price-text-color)] text-[32rpx] price-font leading-[1]"},{default:d((()=>[p("-￥"+g(parseFloat(O.value.basic.manjian_discount_money).toFixed(2)),1)])),_:1})])),_:1})):x("v-if",!0)])),_:1})])),_:1}),u(I,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,zIndex:"10"},{default:d((()=>[u(l,{class:"flex-1 flex items-center justify-between pl-[30rpx] pr-[20rpx]"},{default:d((()=>[u(l,{class:"flex items-baseline"},{default:d((()=>[u(_,{class:"text-[26rpx] text-[#333] leading-[32rpx]"},{default:d((()=>[p("合计：")])),_:1}),u(l,{class:"inline-block"},{default:d((()=>[u(_,{class:"text-[26rpx] font-500 text-[var(--price-text-color)] price-font leading-[30rpx]"},{default:d((()=>[p("￥")])),_:1}),u(_,{class:"text-[44rpx] font-500 text-[var(--price-text-color)] price-font leading-[46rpx]"},{default:d((()=>[p(g(parseFloat(O.value.basic.order_money).toFixed(2).split(".")[0]),1)])),_:1}),u(_,{class:"text-[26rpx] font-500 text-[var(--price-text-color)] price-font leading-[46rpx]"},{default:d((()=>[p("."+g(parseFloat(O.value.basic.order_money).toFixed(2).split(".")[1]),1)])),_:1})])),_:1})])),_:1}),u(E,{class:y(["w-[196rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",{"opacity-80":pe.value}]),"hover-class":"none",disabled:pe.value,onClick:Te},{default:d((()=>[p("提交订单")])),_:1},8,["disabled","class"])])),_:1})])),_:1}),x(" 选择优惠券 "),u(c(ne),{"order-key":C.value.order_key,ref_key:"couponRef",ref:T,onConfirm:Ee},null,8,["order-key"])])),_:1})):x("v-if",!0),x(" 选择自提点 "),O.value&&O.value.basic&&O.value.basic.has_goods_types&&O.value.basic.has_goods_types.includes("real")?(o(),i(c(N),{key:1,ref_key:"storeRef",ref:X,onConfirm:Ie},null,512)):x("v-if",!0),x(" 发票 "),u(c(q),{ref_key:"invoiceRef",ref:re,onConfirm:Re},null,512),x(" 地址 "),u(c(Q),{ref_key:"addressRef",ref:le,onConfirm:Le,back:"/addon/shop/pages/order/payment"},null,512),x(" 满减 "),u(ae,{ref_key:"manjianShowRef",ref:V},null,512),u(G,{ref_key:"payRef",ref:te,onClose:$e},null,512),Object.keys(me.value).length?(o(),i(c(W),{key:2,ref_key:"selectTime",ref:fe,rules:me.value,isQuantum:!0,isOpen:we.value.time_is_open,onChange:be,onGetStamp:ke,onGetDate:je},null,8,["rules","isOpen"])):x("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-cd834a44"]]);export{xe as default};

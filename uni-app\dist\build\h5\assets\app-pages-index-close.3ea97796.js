import{d as s,q as a,a9 as e,o as t,c as l,w as o,b as c,e as r,A as n,B as d,n as i,G as u,k as p,E as m,F as g,a as f}from"./index-dd56d0cc.js";const h=s({__name:"close",setup(s){const h=a();return e((()=>h.site),((s,a)=>{s&&1==s.status&&f({url:"/app/pages/index/index",mode:"reLaunch"})})),(s,a)=>{const e=u,f=p;return t(),l(f,{class:"min-h-[100vh] bg-[var(--page-bg-color)] overflow-hidden",style:i(s.themeColor())},{default:o((()=>[c(f,{class:"empty-page"},{default:o((()=>[c(e,{class:"img",src:r(m)("static/resource/images/site/close.png"),model:"aspectFit"},null,8,["src"]),c(f,{class:"desc"},{default:o((()=>[n(d(r(g)("siteClose")),1)])),_:1})])),_:1})])),_:1},8,["style"])}}});export{h as default};

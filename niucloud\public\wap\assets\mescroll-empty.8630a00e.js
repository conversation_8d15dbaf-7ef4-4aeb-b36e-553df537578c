import{o as t,c as i,w as e,b as n,g as o,A as p,B as s,T as l,n as c,G as a,k as r}from"./index-dd56d0cc.js";import{m,G as u}from"./mescroll-i18n.92d484d5.js";import{_ as d}from"./_plugin-vue_export-helper.1b428a4d.js";const y=d({props:{option:{type:Object,default:()=>({})}},computed:{icon(){if(null!=this.option.icon)return this.option.icon;{let t=m.getType();return this.option.i18n?this.option.i18n[t].icon:u.i18n[t].up.empty.icon||u.up.empty.icon}},tip(){if(null!=this.option.tip)return this.option.tip;{let t=m.getType();return this.option.i18n?this.option.i18n[t].tip:u.i18n[t].up.empty.tip||u.up.empty.tip}},btnText(){if(this.option.i18n){let t=m.getType();return this.option.i18n[t].btnText}return this.option.btnText}},methods:{emptyClick(){this.$emit("emptyclick")}}},[["render",function(m,u,d,y,f,h){const x=a,b=r;return t(),i(b,{class:l(["mescroll-empty empty-page",{"empty-fixed":d.option.fixed}]),style:c({"z-index":d.option.zIndex,top:d.option.top})},{default:e((()=>[n(b,null,{default:e((()=>[h.icon?(t(),i(x,{key:0,class:"img",src:h.icon,mode:"aspectFit"},null,8,["src"])):o("v-if",!0)])),_:1}),h.tip?(t(),i(b,{key:0,class:"desc"},{default:e((()=>[p(s(h.tip),1)])),_:1})):o("v-if",!0),h.btnText?(t(),i(b,{key:1,class:"btn",onClick:h.emptyClick},{default:e((()=>[p(s(h.btnText),1)])),_:1},8,["onClick"])):o("v-if",!0)])),_:1},8,["class","style"])}],["__scopeId","data-v-1da7b482"]]);export{y as M};

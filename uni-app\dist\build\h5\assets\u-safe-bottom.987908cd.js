import{a5 as s,a6 as a,bc as t,a8 as e,o,c as r,T as n,n as u,k as m}from"./index-4b8dc7db.js";import{_ as i}from"./_plugin-vue_export-helper.1b428a4d.js";const c=i({name:"u-safe-bottom",mixins:[s,a,{props:{}}],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return t({},e(this.customStyle))}},mounted(){}},[["render",function(s,a,t,e,i,c){const l=m;return o(),r(l,{class:n(["u-safe-bottom",[!i.isNvue&&"u-safe-area-inset-bottom"]]),style:u([c.style])},null,8,["style","class"])}],["__scopeId","data-v-66aaaf9c"]]);export{c as _};

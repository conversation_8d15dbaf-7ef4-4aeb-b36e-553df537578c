import{d as e,r as a,s as t,ab as l,M as r,l as o,o as s,c as u,w as d,b as n,e as i,A as p,B as c,g as _,C as x,R as m,a3 as v,S as f,n as g,E as y,ax as h,y as b,a as w,G as k,i as j,j as C,k as V,I,ap as N,aH as S,ay as A,H as D,aI as F}from"./index-dd56d0cc.js";import{_ as O}from"./u--image.cd475bba.js";import{_ as P}from"./u-upload.44346d61.js";import{_ as z,a as B}from"./u-radio-group.3476fb8a.js";import{_ as E}from"./u-popup.457e1f1f.js";import{g as H,c as L,d as M,e as R}from"./refund.14559ad1.js";import{_ as T}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.dfca355c.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-transition.ab3d3894.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.f15d7447.js";import"./u-safe-bottom.22d4d63b.js";const U=T(e({__name:"edit_apply",setup(e){var T;const U=a(null),q=a({}),G=a(0),Q=a(0),W=a(!1),J=a({order_id:null==(T=U.value)?void 0:T.order_id,order_goods_id:G.value,order_refund_no:"",refund_type:"",apply_money:"",reason:"",remark:"",voucher:[]}),K=a({}),X=a([]),Y=a("");H().then((({data:e})=>{X.value=e,X.value&&X.value.length&&(Y.value=X.value[0])})),t((e=>{if(G.value=e.order_goods_id||0,e.order_refund_no)L(e.order_refund_no).then((({data:e})=>{U.value=e,q.value=e.order_goods,J.value.order_goods_id=e.order_goods_id,J.value.order_id=e.order_id,J.value.order_refund_no=e.order_refund_no,J.value.remark=e.remark,J.value.reason=e.reason,Y.value=e.reason,J.value.voucher=e.voucher})),M({order_refund_no:e.order_refund_no}).then((e=>{K.value=e.data,J.value.apply_money=l(K.value.refund_money)}));else{r({url:"/addon/shop/pages/refund/list",title:"缺少订单号"})}})),o((e=>function(e){return""==e||0==e?"70rpx":17*String(e).length+"rpx"}));const Z=e=>{J.value.refund_type=e,Q.value=1},$=o((()=>J.value.voucher.map((e=>({url:y(e)}))))),ee=e=>{e.file.forEach((e=>{h({filePath:e.url,name:"file"}).then((e=>{J.value.voucher.length<9&&J.value.voucher.push(e.data.url)})).catch((()=>{}))}))},ae=e=>{J.value.voucher.splice(e.index,1)},te=a(!1),le=()=>J.value.reason?Number(J.value.apply_money).toFixed(2)<0?(b({title:"退款金额不能为0,保留两位小数",icon:"none"}),!1):Number(J.value.apply_money)>Number(K.value.refund_money)?(b({title:"退款金额不能大于可退款总额",icon:"none"}),!1):void(te.value||(te.value=!0,R(J.value).then((e=>{te.value=!1,setTimeout((()=>{w({url:"/addon/shop/pages/order/detail",param:{order_id:J.value.order_id}})}),1e3)})).catch((()=>{te.value=!1})))):(b({title:"请选择退款原因",icon:"none"}),!1),re=()=>{J.value.reason=Y.value,W.value=!1};return(e,a)=>{const t=k,l=j(C("u--image"),O),r=V,o=I,h=N,b=S,w=j(C("u-upload"),P),H=A,L=D,M=j(C("u-radio"),z),R=j(C("u-radio-group"),B),T=j(C("u-popup"),E),G=F;return s(),u(r,{style:g(e.themeColor())},{default:d((()=>[U.value?(s(),u(G,{key:0,"indicator-dots":!1,autoplay:!1,"disable-touch":!0,current:Q.value,class:"h-screen",duration:300},{default:d((()=>[n(b,null,{default:d((()=>[n(h,{"scroll-y":"true",class:"bg-page min-h-screen overflow-hidden"},{default:d((()=>[n(r,{class:"m-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] py-[var(--pad-top-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"flex"},{default:d((()=>[n(l,{radius:"var(--goods-rounded-small)",width:"120rpx",height:"120rpx",src:i(y)(q.value.sku_image.split(",")[0]),model:"aspectFill"},{error:d((()=>[n(t,{class:"w-[120rpx] h-[120rpx]",radius:"var(--goods-rounded-small)",src:i(y)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["radius","src"])])),_:1},8,["radius","src"]),n(r,{class:"flex-1 w-0 ml-[20rpx]"},{default:d((()=>[n(r,{class:"text-ellipsis text-[28rpx] leading-normal truncate"},{default:d((()=>[p(c(q.value.goods_name),1)])),_:1}),q.value.sku_name?(s(),u(r,{key:0,class:"mt-[6rpx] text-[24rpx] leading-[1.3] text-[var(--text-color-light9)] truncate"},{default:d((()=>[p(c(q.value.sku_name),1)])),_:1})):_("v-if",!0)])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)] flex items-center",onClick:a[0]||(a[0]=e=>Z(1))},{default:d((()=>[n(r,{class:"flex-1"},{default:d((()=>[n(r,{class:"text-[30rpx]"},{default:d((()=>[p("仅退款")])),_:1}),"real"==q.value.goods_type?(s(),u(r,{key:0,class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("未收到货，或与商家协商一致不用退货只退款")])),_:1})):"virtual"==q.value.goods_type?(s(),u(r,{key:1,class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("与商家协商一致不用退货只退款")])),_:1})):_("v-if",!0)])),_:1}),n(o,{class:"nc-iconfont nc-icon-youV6xx text-[28rpx] text-[var(--text-color-light9)]"})])),_:1}),"real"!=q.value.goods_type||q.value.delivery_status&&"wait_delivery"==q.value.delivery_status?_("v-if",!0):(s(),u(r,{key:0,class:"py-[var(--pad-top-m)] flex items-center border-0 !border-t !border-[#f5f5f5] border-solid",onClick:a[1]||(a[1]=e=>Z(2))},{default:d((()=>[n(r,{class:"flex-1"},{default:d((()=>[n(r,{class:"text-[30rpx]"},{default:d((()=>[p("退货退款")])),_:1}),n(r,{class:"text-[24rpx] mt-[20rpx] text-[var(--text-color-light9)]"},{default:d((()=>[p("已收到货，需退还收到的货物")])),_:1})])),_:1}),n(o,{class:"nc-iconfont nc-icon-youV6xx text-[28rpx] text-[var(--text-color-light9)]"})])),_:1}))])),_:1})])),_:1})])),_:1}),n(b,null,{default:d((()=>[n(h,{"scroll-y":"true",class:"bg-page min-h-screen overflow-hidden"},{default:d((()=>[n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)] flex justify-between items-center"},{default:d((()=>[n(r,{class:"text-[28rpx]"},{default:d((()=>[p("退款原因")])),_:1}),n(r,{class:"flex ml-[auto] items-center h-[30rpx]",onClick:a[2]||(a[2]=e=>W.value=!0)},{default:d((()=>[n(o,{class:"text-[26rpx] text-[var(--text-color-light9)] truncate max-w-[460rpx]"},{default:d((()=>[p(c(J.value.reason||"请选择"),1)])),_:1}),n(o,{class:"nc-iconfont nc-icon-youV6xx pt-[4rpx] text-[24rpx] text-[var(--text-color-light9)]"})])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)]"},{default:d((()=>[n(r,{class:"flex items-center justify-between"},{default:d((()=>[n(r,{class:"text-[28rpx] font-500"},{default:d((()=>[p("退款金额")])),_:1}),n(r,{class:"flex justify-end items-center text-[var(--price-text-color)] price-font"},{default:d((()=>[n(o,{class:"font-500 text-[36rpx] leading-none"},{default:d((()=>[p("￥")])),_:1}),n(o,{class:"font-500 text-[36rpx] leading-none"},{default:d((()=>[p(c(J.value.apply_money),1)])),_:1}),_(' <input type="digit" v-model.number="formData.apply_money" class="font-500 text-[36rpx] leading-none" :style="{ width: inputWidth(formData.apply_money) }" @blur="handleInput"> ')])),_:1})])),_:1}),n(r,{class:"text-right text-[24rpx] text-[var(--text-color-light9)] mt-[10rpx]"},{default:d((()=>[_(" <text>最多可退￥{{ refundMoney.refund_money }}</text> "),1===K.value.is_refund_delivery&&Number(K.value.refund_delivery_money)>0?(s(),u(o,{key:0,class:"ml-[10rpx]"},{default:d((()=>[p("(包含运费￥"+c(K.value.refund_delivery_money)+") ",1)])),_:1})):_("v-if",!0)])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[var(--top-m)] sidebar-margin px-[var(--pad-sidebar-m)] rounded-[var(--rounded-big)] bg-white"},{default:d((()=>[n(r,{class:"pt-[var(--pad-top-m)] pb-[14rpx]"},{default:d((()=>[n(r,{class:"text-[28rpx] flex items-center"},{default:d((()=>[n(o,{class:"font-500"},{default:d((()=>[p("上传凭证")])),_:1}),n(o,{class:"text-[24rpx] text-[var(--text-color-light9)] ml-[10rpx]"},{default:d((()=>[p("选填")])),_:1})])),_:1}),n(r,{class:"mt-[30rpx]"},{default:d((()=>[n(w,{fileList:i($),onAfterRead:ee,onDelete:ae,multiple:"",maxCount:9},null,8,["fileList"])])),_:1})])),_:1})])),_:1}),n(r,{class:"my-[24rpx] sidebar-margin px-[24rpx] rounded-md bg-white"},{default:d((()=>[n(r,{class:"py-[var(--pad-top-m)]"},{default:d((()=>[n(r,{class:"text-[28rpx] flex items-center"},{default:d((()=>[n(o,{class:"font-500"},{default:d((()=>[p("补充描述")])),_:1}),n(o,{class:"text-[24rpx] text-[var(--text-color-light9)] ml-[10rpx]"},{default:d((()=>[p("选填")])),_:1})])),_:1}),n(r,{class:"mt-[30rpx] h-[200rpx]"},{default:d((()=>[n(H,{class:"leading-[1.5] h-[100%] w-[100%] text-[28rpx]",modelValue:J.value.remark,"onUpdate:modelValue":a[3]||(a[3]=e=>J.value.remark=e),cols:"30",rows:"5",placeholder:"补充描述,有助于更好的处理售后问题","placeholder-class":"text-[26rpx] text-[var(--text-color-light9)]"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(r,{class:"w-full"},{default:d((()=>[n(r,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] box-border"},{default:d((()=>[n(L,{class:"primary-btn-bg !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",loading:te.value,onClick:le},{default:d((()=>[p("提交")])),_:1},8,["loading"])])),_:1})])),_:1}),_(" 退款原因 "),n(T,{show:W.value,onClose:a[6]||(a[6]=e=>W.value=!1),onOpen:e.open,closeable:!0},{default:d((()=>[n(r,{class:"popup-common",onTouchmove:a[5]||(a[5]=x((()=>{}),["prevent","stop"]))},{default:d((()=>[n(r,{class:"title"},{default:d((()=>[p("退款原因")])),_:1}),n(h,{"scroll-y":"true",class:"h-[450rpx] px-[30rpx] box-border"},{default:d((()=>[n(R,{modelValue:Y.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.value=e),placement:"column",iconPlacement:"right"},{default:d((()=>[(s(!0),m(f,null,v(X.value,((e,a)=>(s(),u(M,{activeColor:"var(--primary-color)",labelSize:"30rpx",labelColor:"#333",customStyle:{marginBottom:"34rpx"},key:a,label:e,name:e},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(r,{class:"btn-wrap"},{default:d((()=>[n(L,{class:"primary-btn-bg btn",onClick:re},{default:d((()=>[p("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show","onOpen"])])),_:1})])),_:1})])),_:1},8,["current"])):_("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-90a21bff"]]);export{U as default};

import{d as e,r as a,s as t,z as s,o as l,c as r,w as o,b as d,R as u,a3 as n,S as c,g as p,A as i,B as f,e as x,n as _,bI as m,a as v,cd as b,k as h,H as g,ap as y,i as k,j as C,bG as j,C as w,T as A,F as S,ce as I,I as z}from"./index-4b8dc7db.js";import{_ as B}from"./u-popup.e2790691.js";import{M as E}from"./mescroll-empty.925a8be3.js";import{_ as R}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.5763ee65.js";/* empty css                                                                     */import"./u-icon.33002907.js";/* empty css                                                               */import"./u-safe-bottom.987908cd.js";import"./mescroll-i18n.d05488f8.js";const T=R(e({__name:"address",setup(e){const R=a(!0),T=a(0),V=a([]),q=a(""),F=a("");t((e=>{q.value=e.type||"",F.value=e.source||"",e.type&&(T.value="address"==e.type?0:1),uni.getStorageSync("selectAddressCallback")&&uni.removeStorage({key:"selectAddressCallback"})}));s((()=>{m({}).then((({data:e})=>{const a=[],t=[];e.forEach((e=>{"address"==e.type?a.push(e):t.push(e)})),F.value?V.value=0==T.value?a:t:V.value=e,R.value=!1})).catch((()=>{R.value=!1}))}));const G=()=>{v({url:"/app/pages/member/address_edit",param:{source:F.value}})},H=a(!1),M=a(0),D=()=>{const e=V.value[M.value];b(e.id).then((()=>{V.value.splice(M.value,1),H.value=!1})).catch()};return(e,a)=>{const t=h,s=z,m=g,b=y,T=k(C("u-popup"),B);return R.value?p("v-if",!0):(l(),r(t,{key:0,class:"address bg-[var(--page-bg-color)] min-h-[100vh]",style:_(e.themeColor())},{default:o((()=>[d(b,{"scroll-y":"true"},{default:o((()=>[V.value.length?(l(),r(t,{key:0,class:"sidebar-margin pt-[var(--top-m)]"},{default:o((()=>[(l(!0),u(c,null,n(V.value,((e,a)=>(l(),r(t,{class:"mb-[var(--top-m)] rounded-[var(--rounded-big)] overflow-hidden"},{default:o((()=>[d(t,{class:"flex flex-col card-template"},{default:o((()=>[d(t,{class:"flex-1 line-feed mr-[20rpx]",onClick:a=>(e=>{const a=uni.getStorageSync("selectAddressCallback");a&&(a.address_id=e.id,uni.setStorage({key:"selectAddressCallback",data:a,success(){v({url:a.back,mode:"redirectTo"})}}))})(e)},{default:o((()=>[d(t,{class:"flex items-center"},{default:o((()=>[d(t,{class:"text-[#333] text-[30rpx] leading-[34rpx] font-500"},{default:o((()=>[i(f(e.name),1)])),_:2},1024),d(s,{class:"text-[#333] text-[30rpx] ml-[10rpx]"},{default:o((()=>[i(f(x(j)(e.mobile)),1)])),_:2},1024)])),_:2},1024),d(t,{class:"mt-[16rpx] text-[26rpx] line-feed text-[var(--text-color-light9)] leading-[1.4]"},{default:o((()=>[i(f(e.full_address),1)])),_:2},1024)])),_:2},1032,["onClick"]),d(t,{class:"flex justify-between pt-[26rpx]"},{default:o((()=>[d(t,{class:"flex items-center text-[26rpx] leading-none",onClick:w((e=>(e=>{const a=V.value[e];a.is_default||(a.is_default=1,I(a).then((()=>{V.value.forEach(((a,t)=>{a.is_default&&(a.is_default=0),t==e&&(a.is_default=1)}))})).catch())})(a)),["stop"])},{default:o((()=>[d(s,{class:A(["iconfont !text-[26rpx] mr-[10rpx]",{"iconduigou text-primary":e.is_default,iconcheckbox_nol:!e.is_default}])},null,8,["class"]),i(" 设为默认 ")])),_:2},1032,["onClick"]),d(t,{class:"flex"},{default:o((()=>[d(t,{class:"text-[26rpx]",onClick:w((a=>{return t=e.id,void v({url:"/app/pages/member/address_edit",param:{id:t,source:F.value}});var t}),["stop"])},{default:o((()=>[d(s,{class:"nc-iconfont nc-icon-xiugaiV6xx shrink-0 text-[26rpx] mr-[4rpx]"}),i(" 编辑 ")])),_:2},1032,["onClick"]),d(t,{onClick:w((e=>(e=>{M.value=e,H.value=!0})(a)),["stop"]),class:"ml-[40rpx] text-[26rpx]"},{default:o((()=>[d(s,{class:"nc-iconfont nc-icon-shanchu-yuangaizhiV6xx shrink-0 text-[26rpx] mr-[4rpx]"}),i(" 删除 ")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})):p("v-if",!0),V.value.length?p("v-if",!0):(l(),r(E,{key:1,option:{tip:"暂无收货地址"}})),d(t,{class:"w-full footer"},{default:o((()=>[d(t,{class:"py-[var(--top-m)] px-[var(--sidebar-m)] footer w-full fixed bottom-0 left-0 right-0 box-border"},{default:o((()=>[d(m,{"hover-class":"none",class:"primary-btn-bg text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500",onClick:G},{default:o((()=>[i(f(x(S)("createAddress")),1)])),_:1})])),_:1})])),_:1})])),_:1}),d(T,{ref:"popupRef",show:H.value,mode:"center",onClose:a[1]||(a[1]=e=>H.value=!1),round:30,zIndex:"99999",safeAreaInsetBottom:!1},{default:o((()=>[d(t,{class:"bg-[#fff] flex flex-col justify-between w-[610rpx] h-[306rpx] rounded-[30rpx] box-border px-[40rpx] pt-[70rpx] pb-[40rpx] relative overflow-hidden"},{default:o((()=>[d(t,{class:"flex-center mb-[80rpx] text-[#333]"},{default:o((()=>[i("确定要删除该地址吗？")])),_:1}),d(t,{class:"flex-between-center"},{default:o((()=>[d(t,{class:"w-[250rpx] h-[66rpx] rounded-full bg-[#eee] flex-center text-[26rpx] font-500 text-[#333]",onClick:a[0]||(a[0]=e=>H.value=!1)},{default:o((()=>[i("取消")])),_:1}),d(t,{class:"w-[250rpx] h-[66rpx] rounded-full primary-btn-bg flex-center text-[26rpx] font-500 text-[#fff]",onClick:D},{default:o((()=>[i("确定")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1},8,["style"]))}}}),[["__scopeId","data-v-83114eb4"]]);export{T as default};

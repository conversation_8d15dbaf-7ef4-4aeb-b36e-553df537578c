import{d as e,t as a,af as t,r,bA as l,a as p,o,c as s,w as n,C as i,b as d,A as u,B as y,e as c,R as v,a3 as m,S as _,g as f,y as x,F as g,k as b,i as h,j as k,ap as S,H as w,ab as j,E as C}from"./index-dd56d0cc.js";import{_ as T}from"./u-image.dfca355c.js";import{_ as P}from"./u-icon.5895f8fc.js";import{_ as I}from"./u-popup.457e1f1f.js";import{p as R,g as A}from"./pay.5385ae25.js";import{_ as E}from"./_plugin-vue_export-helper.1b428a4d.js";const H=E(e({__name:"pay",props:{ignorePay:{type:Array,default:()=>[]}},emits:["close","confirm"],setup(e,{expose:E,emit:H}){const O=e;a()&&t.init();const U=r(!1),$=r(!1),z=r(null),B=r(""),F=()=>{var e,r,l,o;if(!uni.$u.test.isEmpty(B.value))return"friendspay"==B.value?(p({url:"/app/pages/friendspay/share",param:{id:null==(e=z.value)?void 0:e.trade_id,type:null==(r=z.value)?void 0:r.trade_type},mode:"redirectTo"}),!1):void($.value||($.value=!0,R({trade_type:null==(l=z.value)?void 0:l.trade_type,trade_id:null==(o=z.value)?void 0:o.trade_id,type:B.value,openid:uni.getStorageSync("openid")||""}).then((e=>{var r,l,o,s,n,i;switch(B.value){case"wechatpay":a()?(e.data.timestamp=e.data.timeStamp,delete e.data.timeStamp,t.pay({...e.data,success:()=>{D()},cancel:()=>{$.value=!1}})):(uni.setStorageSync("paymenting",{trade_type:null==(r=z.value)?void 0:r.trade_type,trade_id:null==(l=z.value)?void 0:l.trade_id}),location.href=e.data.h5_url);break;case"alipay":a()?p({url:"/app/pages/pay/browser",param:{trade_type:null==(o=z.value)?void 0:o.trade_type,trade_id:null==(s=z.value)?void 0:s.trade_id,alipay:encodeURIComponent(e.data.url)},mode:"redirectTo"}):(uni.setStorageSync("paymenting",{trade_type:null==(n=z.value)?void 0:n.trade_type,trade_id:null==(i=z.value)?void 0:i.trade_id}),location.href=e.data.url);break;default:if(e.data.url)return void p({url:e.data.url,param:e.data.param||{},mode:"redirectTo"});D()}})).catch((()=>{$.value=!1}))));x({title:g("pay.notHavePayType"),icon:"none"})};l("checkIsReturnAfterPayment",(()=>{const e=uni.getStorageSync("paymenting");uni.getStorageSync("paymenting")&&p({url:"/app/pages/pay/result",param:{trade_type:e.trade_type,trade_id:e.trade_id},mode:"redirectTo",success(){uni.removeStorageSync("paymenting")}})}));const q=r(!1),D=()=>{var e,a;H("confirm"),p({url:"/app/pages/pay/result",param:{trade_type:null==(e=z.value)?void 0:e.trade_type,trade_id:null==(a=z.value)?void 0:a.trade_id},mode:"redirectTo"})},G=()=>{uni.removeStorageSync("paymenting"),U.value=!1,H("close")};return E({open:(e,a,t="",r="")=>{if(q.value)return;q.value=!0,uni.setStorageSync("payReturn",encodeURIComponent(t));const l={};r&&(l.scene=r),A(e,a,l).then((e=>{let{data:a}=e;uni.$u.test.isEmpty(a)?x({title:g("pay.notObtainedInfo"),icon:"none"}):0!=a.money?(z.value=a,O.ignorePay&&(a.pay_type_list=a.pay_type_list.filter((e=>!O.ignorePay.includes(e.key)))),B.value=a.pay_type_list[0]?a.pay_type_list[0].key:"",U.value=!0,q.value=!1):D()})).catch((()=>{q.value=!1}))}}),(e,a)=>{const t=b,r=h(k("u-image"),T),l=h(k("u-icon"),P),p=S,x=w,R=h(k("u-popup"),I);return o(),s(R,{show:U.value,round:10,onClose:G,closeable:!0,bgColor:"#fff",zIndex:"10081",closeOnClickOverlay:!1},{default:n((()=>[z.value?(o(),s(t,{key:0,class:"flex flex-col h-[65vh] popup-common",onTouchmove:a[0]||(a[0]=i((()=>{}),["prevent","stop"]))},{default:n((()=>[d(t,{class:"head"},{default:n((()=>[d(t,{class:"title"},{default:n((()=>[u(y(c(g)("pay.payTitle")),1)])),_:1}),d(t,{class:"flex items-end justify-center w-full text-xl font-bold py-[20rpx] price-font"},{default:n((()=>[d(t,{class:"text-base mr-[4rpx]"},{default:n((()=>[u(y(c(g)("currency")),1)])),_:1}),u(" "+y(c(j)(z.value.money)),1)])),_:1})])),_:1}),d(p,{"scroll-y":"true",class:"flex-1 pt-[20rpx]"},{default:n((()=>[d(t,{class:"flex text-[28rpx] px-[36rpx] py-[20rpx] mb-[10rpx]"},{default:n((()=>[d(t,{class:"text-[var(--text-color-light6)]"},{default:n((()=>[u(y(c(g)("pay.orderInfo")),1)])),_:1}),d(t,{class:"text-right flex-1 pl-[30rpx] truncate"},{default:n((()=>[u(y(z.value.body),1)])),_:1})])),_:1}),d(t,{class:"mx-[var(--popup-sidebar-m)] px-[30rpx] bg-white rounded-[20rpx] bg-[var(--temp-bg)]"},{default:n((()=>[z.value.pay_type_list.length?(o(!0),v(_,{key:0},m(z.value.pay_type_list,((e,a)=>(o(),s(t,{class:"pay-item py-[30rpx] flex items-center border-0 border-b border-solid border-[#eee]",key:a,onClick:a=>B.value=e.key},{default:n((()=>[d(r,{src:c(C)(e.icon),width:"50rpx",height:"50rpx"},null,8,["src"]),d(t,{class:"flex-1 px-[20rpx] text-[28rpx] font-500"},{default:n((()=>[u(y(e.name),1)])),_:2},1024),e.key==B.value?(o(),s(l,{key:0,name:"checkbox-mark",color:"var(--primary-color)"})):f("v-if",!0)])),_:2},1032,["onClick"])))),128)):(o(),s(t,{key:1,class:"py-[30rpx] text-center text-[24rpx] text-gray-subtitle"},{default:n((()=>[u(y(c(g)("pay.notHavePayType")),1)])),_:1}))])),_:1})])),_:1}),d(t,{class:"btn-wrap"},{default:n((()=>[d(x,{class:"primary-btn-bg btn","hover-class":"none",loading:$.value,onClick:F},{default:n((()=>[u(y(c(g)("pay.confirmPay")),1)])),_:1},8,["loading"])])),_:1})])),_:1})):f("v-if",!0)])),_:1},8,["show"])}}}),[["__scopeId","data-v-6d67094e"]]);export{H as _};

import{p as o,_ as r}from"./u-image.18977c35.js";import{a5 as e,a6 as s,a0 as a,o as n,c as i,w as d,$ as t}from"./index-4b8dc7db.js";import{_ as l}from"./_plugin-vue_export-helper.1b428a4d.js";const m=l({name:"u--image",mixins:[e,o,s],components:{uvImage:r},emits:["click","error","load"]},[["render",function(o,r,e,s,l,m){const c=a("uvImage");return n(),i(c,{src:o.src,mode:o.mode,width:o.width,height:o.height,shape:o.shape,radius:o.radius,lazyLoad:o.lazyLoad,showMenuByLongpress:o.showMenuByLongpress,loadingIcon:o.loadingIcon,errorIcon:o.errorIcon,showLoading:o.showLoading,showError:o.showError,fade:o.fade,webp:o.webp,duration:o.duration,bgColor:o.bgColor,customStyle:o.customStyle,onClick:r[0]||(r[0]=r=>o.$emit("click")),onError:r[1]||(r[1]=r=>o.$emit("error")),onLoad:r[2]||(r[2]=r=>o.$emit("load"))},{loading:d((()=>[t(o.$slots,"loading")])),error:d((()=>[t(o.$slots,"error")])),_:3},8,["src","mode","width","height","shape","radius","lazyLoad","showMenuByLongpress","loadingIcon","errorIcon","showLoading","showError","fade","webp","duration","bgColor","customStyle"])}]]);export{m as _};

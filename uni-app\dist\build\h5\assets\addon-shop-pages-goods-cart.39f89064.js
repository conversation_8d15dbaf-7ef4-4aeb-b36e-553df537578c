import{d as e,q as t,p as o,l as s,r as a,z as l,F as r,a9 as n,o as i,c as u,w as d,e as p,b as c,A as x,B as m,R as f,a3 as _,S as g,g as v,C as h,T as b,n as k,J as y,y as j,a as w,G as C,k as F,H as S,I as E,i as V,j as R,ap as T,E as $,an as z,ag as I}from"./index-4b8dc7db.js";import{_ as B}from"./u--image.892273b2.js";import{_ as N}from"./u-number-box.cd35fabd.js";import{_ as O}from"./u-swipe-action-item.2ad577e6.js";import{_ as U}from"./u-swipe-action.07a2890d.js";import{_ as A}from"./u-popup.e2790691.js";import{_ as q}from"./loading-page.vue_vue_type_script_setup_true_lang.ce8783dc.js";import{_ as D}from"./tabbar.805b8203.js";import{u as G,g as H,b as J}from"./add-cart-popup.da2aa6c6.js";import{b as L}from"./bind-mobile.259f3837.js";import{u as M}from"./useGoods.0898f296.js";import{n as P}from"./ns-goods-manjian.0dae0dce.js";import{_ as Q}from"./ns-goods-recommend.vue_vue_type_script_setup_true_lang.00397760.js";import{_ as Y}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.18977c35.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.987908cd.js";import"./u-loading-icon.11ef83b8.js";import"./u-badge.e47151e6.js";import"./u-tabbar.bc2ea30a.js";import"./goods.f34d2594.js";import"./u-form.a144799c.js";import"./u-line.b0d89d5b.js";import"./sms-code.vue_vue_type_script_setup_true_lang.eb737d2f.js";import"./u-input.7a7ec88f.js";import"./u-modal.775e667c.js";import"./u-checkbox.598bfa18.js";import"./u-checkbox-group.0c3be417.js";import"./index.3da4012a.js";const Z=Y(e({__name:"cart",setup(e){const Y=t(),Z=M(),K=o(),W=s((()=>K.info)),X=a(!0),ee=a(!1),te=a({goods_money:0,order_money:0,promotion_money:0}),oe=a([]),se=a([]),ae=a(!1),le=a(!0),re=G(),ne=a(null),ie=a(!1),ue=()=>{H({}).then((({data:e})=>{oe.value=[],se.value=[],e.forEach((e=>{e.checked=!1,e.goodsSku&&(e.goods.status&&0==e.goods.delete_time&&e.goodsSku.stock?(e.num>e.goodsSku.stock&&(e.num=e.goodsSku.stock),oe.value.push(e)):se.value.push(e))})),ve(),ce(),X.value=!1,Y.isAddCartRecommend=!1,le.value&&(le.value=!1)})).catch((e=>{401==e.code&&(oe.value=[],se.value=[],X.value=!1)}))};l((()=>{ue(),re.getList()}));const de=s((()=>{let e=0;return oe.value.forEach((t=>{t.checked&&(e+=1)})),e}));let pe=!1;const ce=()=>{let e=[];return oe.value.forEach((t=>{if(t.checked&&t.goodsSku){let o={};o.num=t.num,o.sku_id=t.sku_id,e.push(o)}})),e.length?!pe&&(pe=!0,void J({sku_ids:e}).then((({data:e})=>{te.value.goods_money=e.goods_money,te.value.order_money=e.order_money,te.value.promotion_money=e.promotion_money,oe.value.forEach(((t,o)=>{for(let s=0;s<e.match_list.length;s++)if(t.goods_id==e.match_list[s].goods_id&&t.sku_id==e.match_list[s].sku_id&&t.manjian_info&&Object.keys(t.manjian_info).length){t.manjian_info.is_show=!0;let o=0;return t.manjian_info.rule_json.forEach(((t,a)=>{a==e.match_list[s].level?(t.is_show=!0,o++):t.is_show=!1})),void(t.manjian_info.is_show=0!=o)}t.manjian_info&&Object.keys(t.manjian_info).length&&(t.manjian_info.is_show=!1)})),pe=!1}))):(te.value.order_money=0,te.value.promotion_money=0,!1)},xe=()=>{y().setLoginBack({url:"/addon/shop/pages/goods/cart"})},me=e=>{let t={min:1,max:e.goodsSku.stock||1};if(e.goods.is_limit&&e.goods.max_buy){let o=0;o=e.goods.max_buy,o>e.goods.stock?t.max=e.goods.stock:o<=e.goods.stock&&(t.max=o)}return e.goods.min_buy>0&&(t.min=e.goods.min_buy),t},fe=a([{text:r("delete"),style:{backgroundColor:"var(--primary-color)",width:"100rpx",height:"100%",borderRadius:"10rpx"}}]),_e=a(),ge=(e={})=>{e.checked=!e.checked,ce()},ve=()=>{const e=oe.value.length!=de.value;oe.value.forEach((t=>{t.checked=e})),ce()},he=a(null);a(uni.getStorageSync("isBindMobile"));const be=()=>{if(!de.value)return void j({title:"还没有选择商品",icon:"none"});const e=[];oe.value.forEach((t=>{t.checked&&e.push(t.id)})),uni.setStorage({key:"orderCreateData",data:{cart_ids:e},success(){w({url:"/addon/shop/pages/order/payment"})}})},ke=()=>{if(!de.value)return void j({title:"还没有选择商品",icon:"none"});if(ee.value)return;ee.value=!0;const e=[];oe.value.forEach((t=>{t.checked&&e.push(t.id)})),re.delete(e,(()=>{ue(),ee.value=!1}))},ye=()=>{if(ee.value)return;ee.value=!0;const e=se.value.map((e=>e.id));re.delete(e,(()=>{ue(),ee.value=!1})),se.value=[]},je=e=>{let t="0.00";return t=e.goodsSku.show_price,t};return n((()=>Y.isAddCartRecommend),((e,t)=>{e&&ue()})),(e,t)=>{const o=C,s=F,a=S,l=E,r=V(R("u--image"),B),n=I,y=V(R("u-number-box"),N),G=V(R("u-swipe-action-item"),O),H=V(R("u-swipe-action"),U),J=T,M=V(R("u-popup"),A),Y=V(R("loading-page"),q),K=V(R("tabbar"),D);return i(),u(s,{style:k(e.themeColor())},{default:d((()=>[X.value?v("v-if",!0):(i(),u(s,{key:0,class:"bg-page min-h-[100vh] overflow-hidden flex flex-col"},{default:d((()=>[p(W)?oe.value.length||se.value.length?(i(),u(s,{key:2,class:"flex-1 h-0"},{default:d((()=>[c(s,{class:"scroll-height"},{default:d((()=>[c(s,{class:"py-[var(--top-m)] sidebar-margin"},{default:d((()=>[oe.value.length?(i(),u(s,{key:0,class:"bg-[#fff] pb-[10rpx] box-border rounded-[var(--rounded-big)]"},{default:d((()=>[c(s,{class:"flex mx-[var(--rounded-big)] pt-[var(--pad-top-m)] justify-between items-center box-border font-400 text-[24rpx] mb-[24rpx] leading-[30rpx]"},{default:d((()=>[c(s,{class:"flex items-baseline text-[24rpx] text-[#333]"},{default:d((()=>[c(l,null,{default:d((()=>[x("共")])),_:1}),c(l,{class:"text-[32rpx] mx-[2rpx] text-[var(--price-text-color)]"},{default:d((()=>[x(m(oe.value.length),1)])),_:1}),c(l,null,{default:d((()=>[x("种商品")])),_:1})])),_:1}),c(l,{onClick:t[1]||(t[1]=e=>ae.value=!ae.value),class:"text-[var(--text-color-light6)] text-[24rpx]"},{default:d((()=>[x(m(ae.value?"完成":"管理"),1)])),_:1})])),_:1}),c(H,{ref_key:"swipeActive",ref:_e},{default:d((()=>[(i(!0),f(g,null,_(oe.value,((e,a)=>(i(),f(g,null,[e.goodsSku?(i(),u(s,{key:0,class:"py-[20rpx] overflow-hidden w-full"},{default:d((()=>[c(G,{options:fe.value,onClick:t=>((e,t)=>{ee.value||(ee.value=!0,re.delete(t.id,(()=>{oe.value.splice(e,1),z((()=>{_e.value&&_e.value.closeOther()})),ce(),ee.value=!1})))})(a,e)},{default:d((()=>[c(s,{class:"flex px-[var(--pad-sidebar-m)]",onClick:h((t=>ge(e)),["stop"])},{default:d((()=>[c(s,{class:"self-center w-[34rpx] mr-[24rpx] h-[60rpx] flex items-center",onClick:h((t=>ge(e)),["stop"])},{default:d((()=>[c(l,{class:b(["iconfont text-color text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:e.checked,"bg-[#F5F5F5]":!e.checked}])},null,8,["class"])])),_:2},1032,["onClick"]),c(s,{class:"w-[200rpx] h-[200rpx] flex items-center justify-center rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:t=>{w({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.goods_id}})}},{default:d((()=>[c(r,{radius:"var(--goods-rounded-big)",width:"200rpx",height:"200rpx",src:p($)(e.goodsSku.sku_image_thumb_mid||""),model:"aspectFill"},{error:d((()=>[c(o,{class:"w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:p($)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1032,["onClick"]),c(s,{class:"flex flex-1 flex-col justify-between ml-[20rpx]"},{default:d((()=>[c(s,{class:"w-[100%] flex flex-col items-baseline"},{default:d((()=>[c(s,{class:"text-[#333] text-[28rpx] max-h-[80rpx] leading-[40rpx] multi-hidden font-400"},{default:d((()=>[x(m(e.goods.goods_name),1)])),_:2},1024),e.goodsSku&&e.goodsSku.sku_spec_format?(i(),u(s,{key:0,class:"box-border max-w-[376rpx] mt-[10rpx] px-[14rpx] h-[36rpx] leading-[36rpx] truncate text-[var(--text-color-light6)] bg-[#F5F5F5] text-[22rpx] rounded-[20rpx]"},{default:d((()=>[x(m(e.goodsSku.sku_spec_format),1)])),_:2},1024)):v("v-if",!0)])),_:2},1024),e.goods&&e.goods.goods_label_name&&e.goods.goods_label_name.length?(i(),u(s,{key:0,class:"flex flex-wrap mb-[auto]"},{default:d((()=>[(i(!0),f(g,null,_(e.goods.goods_label_name,((e,t)=>(i(),f(g,null,["icon"==e.style_type&&e.icon?(i(),u(o,{key:0,class:"img-tag",src:p($)(e.icon),mode:"heightFix",onError:t=>p(Z).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?v("v-if",!0):(i(),u(s,{key:1,class:"base-tag",style:k(p(Z).baseTagStyle(e))},{default:d((()=>[x(m(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):v("v-if",!0),e.manjian_info&&Object.keys(e.manjian_info).length&&e.manjian_info.is_join?(i(),u(s,{key:1,class:"flex items-center mt-[8rpx] mb-[auto]",onClick:h((t=>(e=>{let t={};t.condition_type=e.condition_type,t.rule_json=e.rule_json,t.name=e.manjian_name,ne.value.open(t)})(e.manjian_info)),["stop"])},{default:d((()=>[c(s,{class:"bg-[var(--primary-color-light)] text-[var(--primary-color)] rounded-[6rpx] text-[20rpx] flex items-center justify-center w-[88rpx] h-[36rpx] mr-[6rpx]"},{default:d((()=>[x("满减送")])),_:1}),c(l,{class:"text-[22rpx] text-[#999]"},{default:d((()=>[x(m(e.manjian_info.manjian_name),1)])),_:2},1024)])),_:2},1032,["onClick"])):v("v-if",!0),c(s,{class:"flex justify-between items-end self-end mt-[10rpx] w-[100%]"},{default:d((()=>[c(s,{class:"text-[var(--price-text-color)] price-font truncate max-w-[200rpx]"},{default:d((()=>[c(l,{class:"text-[24rpx] font-500"},{default:d((()=>[x("￥")])),_:1}),c(l,{class:"text-[40rpx] font-500"},{default:d((()=>[x(m(parseFloat(je(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),c(l,{class:"text-[24rpx] font-500"},{default:d((()=>[x("."+m(parseFloat(je(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),c(y,{modelValue:e.num,"onUpdate:modelValue":t=>e.num=t,min:me(e).min,max:me(e).max,integer:"",step:1,"input-width":"68rpx","input-height":"52rpx","button-size":"52rpx",disabledInput:"",onChange:e=>((e,t)=>{uni.$u.debounce((e=>{const o=oe.value[t];re.increase({id:o.id,goods_id:o.goods_id,sku_id:o.sku_id,stock:o.goodsSku.stock,sale_price:o.goodsSku.sale_price,num:o.num},0,ce())}),500)})(0,a)},{minus:d((()=>[c(s,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>(e=>{if(e.goods.is_limit&&e.goods.min_buy){let t=`该商品起购${e.goods.min_buy}件`;e.num<=e.goods.min_buy&&j({title:t,icon:"none"})}})(e)},{default:d((()=>[c(l,{class:b([{"text-[var(--text-color-light9)]":e.num===me(e).min,"text-[#303133]":e.num!==me(e).min},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jianV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),input:d((()=>[c(n,{class:"text-[#303133] text-[28rpx] mx-[14rpx] w-[80rpx] h-[44rpx] bg-[var(--temp-bg)] leading-[44rpx] text-center rounded-[6rpx]",type:"number",onInput:t=>{return o=e,void setTimeout((()=>{(!o.num||o.num<=me(o).min)&&(o.num=me(o).min),o.num>=me(o).max&&(o.num=me(o).max),uni.$u.debounce((e=>{re.increase({id:o.id,goods_id:o.goods_id,sku_id:o.sku_id,stock:o.goodsSku.stock,sale_price:o.goodsSku.sale_price,num:Number(o.num)},0)}),500)}),0);var o},onBlur:e=>((e,t)=>{setTimeout((()=>{const e=oe.value[t];(!e.num||e.num<=me(e).min)&&(e.num=me(e).min),e.num>=me(e).max&&(e.num=me(e).max),uni.$u.debounce((t=>{re.increase({id:e.id,goods_id:e.goods_id,sku_id:e.sku_id,stock:e.goodsSku.stock,sale_price:e.goodsSku.sale_price,num:Number(e.num)},0,ce())}),500)}),0)})(0,a),onClick:t[2]||(t[2]=h((()=>{}),["stop"])),modelValue:e.num,"onUpdate:modelValue":t=>e.num=t},null,8,["onInput","onBlur","modelValue","onUpdate:modelValue"])])),plus:d((()=>[c(s,{class:"relative w-[26rpx] h-[26rpx]",onClick:t=>(e=>{if(e.num>=e.goods.stock)j({title:"商品库存不足",icon:"none"});else if(e.goods.is_limit){let t=`该商品单次限购${e.goods.max_buy}件`;1!=e.goods.limit_type&&(t=`该商品每人限购${e.goods.max_buy}件`),e.num>=e.goods.max_buy&&j({title:t,icon:"none"})}})(e)},{default:d((()=>[c(l,{class:b([{"text-[var(--text-color-light9)]":e.num===me(e).max," text-[#303133]":e.num!==me(e).max},"text-[24rpx] absolute flex items-center justify-center -left-[20rpx] -bottom-[20rpx] -right-[20rpx] -top-[20rpx] font-500 nc-iconfont nc-icon-jiahaoV6xx"])},null,8,["class"])])),_:2},1032,["onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue","min","max","onChange"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])])),_:2},1032,["options","onClick"])])),_:2},1024)):v("v-if",!0)],64)))),256))])),_:1},512)])),_:1})):v("v-if",!0),se.value.length?(i(),u(s,{key:1,class:"bg-[#fff] pb-[10rpx] box-border rounded-[var(--rounded-big)] mt-[var(--top-m)]"},{default:d((()=>[c(s,{class:"flex mx-[var(--pad-sidebar-m)] pt-[var(--pad-top-m)] justify-between items-center box-border font-400 text-[#303133] text-[24rpx] mb-[24rpx] leading-[30rpx]"},{default:d((()=>[c(s,{class:"flex items-center text-[24rpx] text-[#333]"},{default:d((()=>[c(l,null,{default:d((()=>[x("共")])),_:1}),c(l,{class:"text-[28rpx] text-[var(--price-text-color)]"},{default:d((()=>[x(m(se.value.length),1)])),_:1}),c(l,null,{default:d((()=>[x("件失效商品")])),_:1})])),_:1}),c(l,{class:"text-[var(--text-color-light6)] text-[24rpx]",onClick:ye},{default:d((()=>[x("清空")])),_:1})])),_:1}),(i(!0),f(g,null,_(se.value,((e,t)=>(i(),u(s,{class:"py-[20rpx] overflow-hidden"},{default:d((()=>[c(s,{class:"flex px-[var(--pad-sidebar-m)]"},{default:d((()=>[c(l,{class:"self-center iconfont iconxuanze1 text-[34rpx] mr-[32rpx] text-[#F5F5F5] rounded-[50%] overflow-hidden shrink-0"}),c(s,{class:"relative w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden"},{default:d((()=>[c(r,{radius:"var(--goods-rounded-big)",width:"200rpx",height:"200rpx",src:p($)(e.goodsSku.sku_image_thumb_mid),model:"aspectFill"},{error:d((()=>[c(o,{class:"w-[200rpx] h-[200rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:p($)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"]),0==e.goodsSku.stock?(i(),u(s,{key:0,class:"absolute left-0 top-0 w-[200rpx] h-[200rpx] leading-[200rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:d((()=>[c(l,{class:"text-[#fff] text-[28rpx]"},{default:d((()=>[x("已售罄")])),_:1})])),_:1})):v("v-if",!0),0!=e.goodsSku.stock?(i(),u(s,{key:1,class:"absolute left-0 top-0 w-[200rpx] h-[200rpx] leading-[200rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:d((()=>[c(l,{class:"text-[#fff] text-[28rpx]"},{default:d((()=>[x("已失效")])),_:1})])),_:1})):v("v-if",!0)])),_:2},1024),c(s,{class:"flex flex-1 flex-wrap ml-[20rpx]"},{default:d((()=>[c(s,{class:"w-[100%] flex flex-col items-baseline"},{default:d((()=>[c(s,{class:"text-[#333] text-[28rpx] max-h-[80rpx] leading-[40rpx] font-400 multi-hidden"},{default:d((()=>[x(m(e.goods.goods_name),1)])),_:2},1024),e.goodsSku&&e.goodsSku.sku_spec_format?(i(),u(s,{key:0,class:"box-border max-w-[376rpx] mt-[10rpx] px-[14rpx] h-[36rpx] leading-[36rpx] truncate text-[var(--text-color-light6)] bg-[#F5F5F5] text-[22rpx] rounded-[20rpx]"},{default:d((()=>[x(m(e.goodsSku.sku_spec_format),1)])),_:2},1024)):v("v-if",!0)])),_:2},1024),c(s,{class:"flex justify-between items-end self-end w-[100%]"},{default:d((()=>[c(s,{class:"text-[var(--price-text-color)] price-font"},{default:d((()=>[c(l,{class:"text-[24rpx] font-500"},{default:d((()=>[x("￥")])),_:1}),c(l,{class:"text-[36rpx] font-500"},{default:d((()=>[x(m(parseFloat(je(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),c(l,{class:"text-[24rpx] font-500"},{default:d((()=>[x("."+m(parseFloat(je(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1})):v("v-if",!0)])),_:1}),c(Q)])),_:1})])),_:1})):(i(),u(s,{key:1,class:"pb-[100rpx]"},{default:d((()=>[c(s,{class:"empty-page"},{default:d((()=>[c(o,{class:"img",src:p($)("addon/shop/cart-empty.png"),model:"aspectFit"},null,8,["src"]),c(s,{class:"desc"},{default:d((()=>[x("赶紧去逛逛, 购买心仪的商品吧")])),_:1}),c(a,{shape:"circle",plain:"true",class:"btn",onClick:t[0]||(t[0]=e=>p(w)({url:"/addon/shop/pages/goods/list"}))},{default:d((()=>[x("去逛逛")])),_:1})])),_:1}),c(Q)])),_:1})):(i(),u(s,{key:0,class:"pb-[100rpx]"},{default:d((()=>[c(s,{class:"empty-page"},{default:d((()=>[c(o,{class:"img",src:p($)("static/resource/images/system/login.png"),model:"aspectFit"},null,8,["src"]),c(s,{class:"desc"},{default:d((()=>[x("暂未登录")])),_:1}),c(a,{shape:"circle",plain:"true",class:"btn",onClick:xe},{default:d((()=>[x("去登录")])),_:1})])),_:1}),c(Q)])),_:1}))])),_:1})),v(" 优惠明细 "),c(s,{onTouchmove:t[6]||(t[6]=h((()=>{}),["prevent","stop"]))},{default:d((()=>[c(M,{class:"popup-type",show:ie.value,onClose:t[5]||(t[5]=e=>ie.value=!1)},{default:d((()=>[c(s,{class:"min-h-[200rpx] popup-common",onTouchmove:t[4]||(t[4]=h((()=>{}),["prevent","stop"]))},{default:d((()=>[c(s,{class:"flex justify-center items-center pt-[36rpx] pb-[56rpx] px-[26rpx] bg-[#fff] relative"},{default:d((()=>[c(l,{class:"text-[32rpx]"},{default:d((()=>[x("优惠明细")])),_:1}),c(l,{class:"nc-iconfont nc-icon-guanbiV6xx text-[var(--text-color-light6)] absolute text-[32rpx] right-[26rpx]",onClick:t[3]||(t[3]=e=>ie.value=!1)})])),_:1}),c(J,{class:"h-[360rpx]","scroll-y":"true"},{default:d((()=>[c(s,{class:"flex justify-between h-[60rpx] px-[var(--pad-sidebar-m)]"},{default:d((()=>[c(l,{class:"text-[28rpx]"},{default:d((()=>[x("商品总额")])),_:1}),c(l,{class:"text-[28rpx]"},{default:d((()=>[x("￥"+m(te.value.goods_money),1)])),_:1})])),_:1}),Number(te.value.promotion_money)?(i(),u(s,{key:0,class:"flex justify-between h-[60rpx] px-[var(--pad-sidebar-m)]"},{default:d((()=>[c(l,{class:"text-[28rpx]"},{default:d((()=>[x("满减")])),_:1}),c(l,{class:"text-[28rpx] text-[red]"},{default:d((()=>[x("-￥"+m(te.value.promotion_money),1)])),_:1})])),_:1})):v("v-if",!0)])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),oe.value.length?(i(),u(s,{key:1,class:"flex h-[96rpx] items-center bg-[#fff] fixed z-99999 left-0 right-0 bottom-[50px] pl-[30rpx] pr-[20rpx] box-solid mb-ios justify-between border-0 border-t-[2rpx] border-solid border-[#f6f6f6]"},{default:d((()=>[c(s,{class:"flex items-center",onClick:ve},{default:d((()=>[c(l,{class:b(["self-center iconfont text-color text-[34rpx] mr-[10rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",oe.value.length==p(de)?"iconxuanze1":"bg-[#F5F5F5]"])},null,8,["class"]),c(l,{class:"font-400 text-[#303133] text-[26rpx]"},{default:d((()=>[x("全选")])),_:1})])),_:1}),c(s,{class:"flex items-center"},{default:d((()=>[ae.value?(i(),u(s,{key:1,class:"flex-1 flex items-center justify-end"},{default:d((()=>[c(a,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:ke},{default:d((()=>[x("删除")])),_:1})])),_:1})):(i(),u(s,{key:0,class:"flex-1 flex items-center justify-between"},{default:d((()=>[c(s,{class:"mr-[20rpx]"},{default:d((()=>[c(s,{class:"flex items-center text-[var(--price-text-color)] leading-[45rpx]"},{default:d((()=>[c(s,{class:"font-400 text-[#303133] text-[28rpx]"},{default:d((()=>[x("合计：")])),_:1}),c(l,{class:"text-[var(--price-text-color)] price-font text-[32rpx] font-bold"},{default:d((()=>[x("￥"+m(parseFloat(te.value.order_money).toFixed(2)),1)])),_:1})])),_:1}),Number(te.value.promotion_money)?(i(),u(s,{key:0,class:"flex items-center justify-end mt-[6rpx]",onClick:t[7]||(t[7]=e=>ie.value=!0)},{default:d((()=>[c(l,{class:"text-[22rpx] text-[#666]"},{default:d((()=>[x("优惠明细")])),_:1}),c(l,{class:"iconfont iconjiantoushang text-[#666] !text-[22rpx] ml-[4rpx] font-bold"})])),_:1})):v("v-if",!0)])),_:1}),c(a,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:be},{default:d((()=>[x("结算")])),_:1})])),_:1}))])),_:1})])),_:1})):v("v-if",!0),c(Y,{loading:X.value},null,8,["loading"]),c(P,{ref_key:"manjianShowRef",ref:ne},null,512),c(K),v(" 强制绑定手机号 "),c(L,{ref_key:"bindMobileRef",ref:he},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-aa091142"]]);export{Z as default};

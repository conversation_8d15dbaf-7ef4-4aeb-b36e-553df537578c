import{d as e,r as t,l as s,o as l,c as r,w as a,b as o,A as c,B as d,e as i,R as n,a3 as p,S as u,g as f,T as x,n as h,ah as m,ai as _,y as v,I as g,k as b,i as k,j as y,H as w,C as j,E as C,a as F,G as E}from"./index-4b8dc7db.js";import{_ as z}from"./u--image.892273b2.js";import{n as I,o as S}from"./goods.f34d2594.js";import{s as M}from"./select-date.c01f8a5f.js";import{M as R}from"./mescroll-body.0a32866d.js";import{M as U}from"./mescroll-empty.925a8be3.js";import{u as O}from"./useMescroll.26ccf5de.js";import{_ as P}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-image.18977c35.js";import"./u-icon.33002907.js";/* empty css                                                               */import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-popup.e2790691.js";import"./u-safe-bottom.987908cd.js";import"./mescroll-i18n.d05488f8.js";const V=P(e({__name:"browse",setup(e){const{mescrollInit:P,downCallback:V,getMescroll:q}=O(_,m),A=t(!1);let B=t(!1);const D=t(!1),G=t(0);let H=t([]);const N=t([]),Q=e=>{B.value=!1;let t={page:e.num,limit:e.size,date:N.value};I(t).then((t=>{G.value=t.data.total;let s=t.data.data;1===Number(e.num)&&(H.value=[]);const l=s.reduce(((e,t)=>{const s=t.browse_time.split(" ")[0];return e[s]||(e[s]=[]),e[s].push(t),e}),{}),r=Object.keys(l).map((e=>({date:e,list:l[e]})));r.forEach((e=>{e.checked=!1,e.list.forEach((e=>{e.checked=!1}))})),r.forEach((e=>{const t=H.value.findIndex((t=>t.date===e.date));-1!==t?H.value[t].list=[...H.value[t].list,...e.list]:H.value.push(e)})),e.endSuccess(s.length),B.value=!0})).catch((()=>{B.value=!0,e.endErr()}))},T=s((()=>{let e=0;return H.value.forEach((t=>{t.list.forEach((t=>{t.checked&&(e+=1)}))})),e})),J=t(!1),K=()=>{const e=H.value.every((e=>e.checked));J.value=!!e},L=()=>{J.value=!J.value,H.value.forEach((e=>{e.checked=J.value,e.list.forEach((e=>{e.checked=J.value}))}))},W=()=>{if(!T.value)return void v({title:"还没有选择商品",icon:"none"});if(D.value)return;D.value=!0;const e=[];H.value.forEach((t=>{t.list.forEach((t=>{t.checked&&e.push(t.goods_id)}))})),S({goods_ids:e}).then((e=>{D.value=!1,q().resetUpScroll()}))},X=()=>{if(D.value)return;D.value=!0;const e=[];H.value.forEach((t=>{t.list.forEach((t=>{e.push(t.goods_id)}))})),S({goods_ids:e}).then((e=>{q().resetUpScroll(),D.value=!1}))},Y=t(null),Z=()=>{Y.value.show=!0},$=e=>{N.value=e,H.value=[],q().resetUpScroll()};return(e,t)=>{const s=g,m=b,_=E,v=k(y("u--image"),z),I=w;return l(),r(m,{class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden",style:h(e.themeColor())},{default:a((()=>[o(m,{class:"fixed top-0 left-0 right-0 z-200"},{default:a((()=>[o(m,{class:"tab-style-1 py-[20rpx] bg-[#fff] border-0 border-solid border-b-[1rpx] border-[#f6f6f6]"},{default:a((()=>[o(m,{class:"tab-left text-[28rpx]"},{default:a((()=>[o(s,null,{default:a((()=>[c("共")])),_:1}),o(s,{class:"text-primary"},{default:a((()=>[c(d(G.value),1)])),_:1}),o(s,null,{default:a((()=>[c("条")])),_:1})])),_:1}),o(m,{class:"tab-right !items-center"},{default:a((()=>[o(m,{class:"flex items-center",onClick:Z},{default:a((()=>[o(m,{class:"tab-right-date"},{default:a((()=>[c("日期")])),_:1}),o(m,{class:"nc-iconfont nc-icon-a-riliV6xx-36 tab-right-icon"})])),_:1}),o(m,{class:"w-[2rpx] h-[28rpx] mx-[20rpx] bg-gradient-to-b from-[#333] to-[#fff]"}),o(m,{onClick:t[0]||(t[0]=e=>A.value=!A.value),class:"text-[#333] text-[28rpx]"},{default:a((()=>[c(d(A.value?"完成":"管理"),1)])),_:1})])),_:1})])),_:1})])),_:1}),o(R,{ref:"mescrollRef",top:"76",bottom:"168",onInit:i(P),down:{use:!1},onUp:Q},{default:a((()=>[i(H).length?(l(),r(m,{key:0},{default:a((()=>[(l(!0),n(u,null,p(i(H),((e,t)=>(l(),r(m,{class:"bg-[#fff] mb-[20rpx] pt-[30rpx] px-[20rpx]",key:t},{default:a((()=>[o(m,{class:"flex items-center h-[34rpx] mb-[20rpx]"},{default:a((()=>[A.value?(l(),r(m,{key:0,class:"self-center w-[58rpx] flex items-center",onClick:j((t=>{return(s=e).checked=!s.checked,s.list.forEach((e=>{e.checked=s.checked})),void K();var s}),["stop"])},{default:a((()=>[o(m,{class:"bg-[#fff] w-[34rpx] h-[34rpx] rounded-[17rpx] flex items-center justify-center"},{default:a((()=>[o(s,{class:x(["iconfont text-primary text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:e.checked,"bg-[#F5F5F5]":!e.checked}])},null,8,["class"])])),_:2},1024)])),_:2},1032,["onClick"])):f("v-if",!0),o(m,{class:"text-[28rpx] font-500 text-[#333]"},{default:a((()=>[c(d(e.date),1)])),_:2},1024)])),_:2},1024),o(m,{class:"flex flex-wrap"},{default:a((()=>[(l(!0),n(u,null,p(e.list,((t,n)=>(l(),r(m,{class:x(["w-[230rpx] mb-[20rpx]",{"mr-[10rpx]":(n+1)%3}]),key:n,onClick:e=>{F({url:"/addon/shop/pages/goods/detail",param:{goods_id:t.goods_id}})}},{default:a((()=>[o(m,{class:"relative w-[230rpx] h-[230rpx] rounded-[var(--goods-rounded-mid)] overflow-hidden mb-[10rpx]"},{default:a((()=>[o(v,{width:"230rpx",height:"230rpx",radius:"var(--goods-rounded-mid)",src:i(C)(t.goods_cover_thumb_mid?t.goods_cover_thumb_mid:""),mode:"aspectFill"},{error:a((()=>[o(_,{class:"w-[230rpx] h-[230rpx] rounded-[var(--goods-rounded-mid)] overflow-hidden",src:i(C)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["radius","src"]),0==t.status?(l(),r(m,{key:0,class:"absolute left-0 top-0 w-[230rpx] h-[230rpx] leading-[230rpx] text-center",style:{"background-color":"rgba(0,0,0,0.3)"}},{default:a((()=>[o(s,{class:"text-[#fff] text-[28rpx]"},{default:a((()=>[c("已失效")])),_:1})])),_:1})):f("v-if",!0),A.value?(l(),r(m,{key:1,class:"absolute top-0 left-0 right-0 bottom-0 p-[10rpx] flex justify-end items-start z-100",onClick:j((s=>((e,t)=>{t.checked=!t.checked;const s=e.list.every((e=>e.checked));e.checked=!!s,K()})(e,t)),["stop"])},{default:a((()=>[o(m,{class:"bg-[#fff] w-[34rpx] h-[34rpx] rounded-[17rpx] flex items-center justify-center"},{default:a((()=>[o(s,{class:x(["iconfont text-primary text-[34rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden shrink-0",{iconxuanze1:t.checked,"bg-[#F5F5F5]":!t.checked}])},null,8,["class"])])),_:2},1024)])),_:2},1032,["onClick"])):f("v-if",!0)])),_:2},1024),o(m,{class:"text-[var(--price-text-color)] price-font"},{default:a((()=>[o(s,{class:"text-[24rpx] font-500"},{default:a((()=>[c("￥")])),_:1}),o(s,{class:"text-[40rpx] font-500"},{default:a((()=>[c(d(parseFloat(t.show_price).toFixed(2).split(".")[0]),1)])),_:2},1024),o(s,{class:"text-[24rpx] font-500"},{default:a((()=>[c("."+d(parseFloat(t.show_price).toFixed(2).split(".")[1]),1)])),_:2},1024),"member_price"==t.show_type?(l(),r(_,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:i(C)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==t.show_type?(l(),r(_,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:i(C)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==t.show_type?(l(),r(_,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:i(C)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):f("v-if",!0)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1024)))),128))])),_:1})):f("v-if",!0),!i(H).length&&i(B)?(l(),r(U,{key:1,option:{tip:"暂无浏览的商品"}})):f("v-if",!0)])),_:1},8,["onInit"]),i(H).length&&A.value?(l(),r(m,{key:0,class:"fixed left-0 right-0 bottom-0 z-200 bg-[#fff] pb-ios"},{default:a((()=>[i(T)?(l(),r(m,{key:0,class:"h-[66rpx] flex items-center justify-between pl-[30rpx] pr-[20rpx] border-0 border-b-[1rpx] border-solid border-[#f6f6f6]"},{default:a((()=>[o(m,{class:"text-[24rpx]"},{default:a((()=>[o(s,null,{default:a((()=>[c("已选")])),_:1}),o(s,{class:"text-primary"},{default:a((()=>[c(d(i(T)),1)])),_:1}),o(s,null,{default:a((()=>[c("件宝贝")])),_:1})])),_:1}),o(m,{class:"text-[24rpx] text-[#999]",onClick:X},{default:a((()=>[c("一键清空宝贝足迹")])),_:1})])),_:1})):f("v-if",!0),o(m,{class:"flex h-[100rpx] items-center justify-between pl-[30rpx] pr-[20rpx]"},{default:a((()=>[o(m,{class:"flex items-center",onClick:L},{default:a((()=>[o(s,{class:x(["self-center iconfont text-primary text-[34rpx] mr-[10rpx] w-[34rpx] h-[34rpx] rounded-[17rpx] overflow-hidden flex-shrink-0",{iconxuanze1:J.value,"bg-color":!J.value}])},null,8,["class"]),o(s,{class:"font-400 text-[#303133] text-[26rpx]"},{default:a((()=>[c("全选")])),_:1})])),_:1}),o(I,{class:"w-[180rpx] h-[70rpx] font-500 text-[26rpx] leading-[70rpx] !text-[#fff] m-0 rounded-full primary-btn-bg remove-border",onClick:W},{default:a((()=>[c("删除")])),_:1})])),_:1})])),_:1})):f("v-if",!0),f(" 时间选择 "),o(M,{ref_key:"selectDateRef",ref:Y,onConfirm:$},null,512)])),_:1},8,["style"])}}}),[["__scopeId","data-v-94799934"]]);export{V as default};

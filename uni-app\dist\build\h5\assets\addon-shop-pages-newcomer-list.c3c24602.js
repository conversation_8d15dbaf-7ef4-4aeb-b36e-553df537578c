import{d as e,r as t,az as a,l as s,s as l,o as r,c as o,w as n,e as u,R as c,g as i,b as p,S as d,A as m,n as x,B as f,a3 as _,C as v,ah as g,ai as h,D as y,G as b,k,I as w,i as j,j as F,ap as C,H as T,E,T as S,a as I}from"./index-dd56d0cc.js";import{a as z,b as D,_ as M}from"./newcomer.c56b90d6.js";import{_ as B}from"./top-tabbar.c9ba9447.js";import{_ as H}from"./u-popup.457e1f1f.js";import{_ as N}from"./loading-page.vue_vue_type_script_setup_true_lang.c88f563e.js";import{M as R}from"./mescroll-body.520ba8e3.js";import{u as A}from"./useMescroll.26ccf5de.js";import{M as O}from"./mescroll-empty.8630a00e.js";import{t as P}from"./topTabbar.986d54c4.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";import"./manifest.ed582bbb.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-safe-bottom.22d4d63b.js";import"./u-loading-icon.f15d7447.js";import"./mescroll-i18n.92d484d5.js";const q=U(e({__name:"list",setup(e){const{mescrollInit:U,downCallback:q,getMescroll:G}=A(h,g),L=t([]),V=t(null),J=t(!1),K=t(!0),Q=a();let W={};const X=P();X.setTopTabbarParam({title:"",topStatusBar:{textColor:"#fff"}});let Y=X.setTopTabbarParam({title:"新人专享列表",topStatusBar:{textColor:"#333"}});s((()=>y(Number(W.height)+W.top+8)+30+"rpx;"));const Z=t({}),$=e=>{Z.value=e},ee=t(""),te=t(!1),ae=()=>{te.value=!1};l((async e=>{le()}));const se=t({}),le=()=>{z().then((e=>{se.value=e.data,K.value=!1})).catch((()=>{K.value=!1}))},re=t(0),oe=e=>{J.value=!1;let t={page:e.num,limit:e.size};D(t).then((t=>{re.value=t.data.is_join;let a=t.data.data,s=(new Date).getTime();ee.value=1e3*Number(t.data.validity_time)-s,1===Number(e.num)&&(L.value=[]),L.value=L.value.concat(a),e.endSuccess(a.length),J.value=!0})).catch((()=>{J.value=!0,e.endErr()}))};return(e,t)=>{const a=b,s=k,l=w,g=j(F("up-count-down"),M),h=j(F("top-tabbar"),B),y=C,z=T,D=j(F("u-popup"),H),A=j(F("loading-page"),N);return r(),o(s,{class:"min-h-[100vh] bg-[#f6f6f6] overflow-hidden",style:x(e.themeColor())},{default:n((()=>[Object.keys(se.value).length&&"active"==se.value.active_status?(r(),o(R,{key:0,ref_key:"mescrollRef",ref:V,onInit:u(U),down:{use:!1},onUp:oe},{default:n((()=>[K.value?i("v-if",!0):(r(),o(s,{key:0,class:"marketing-head"},{default:n((()=>[se.value.banner_list&&se.value.banner_list.length?(r(),o(a,{key:0,class:"w-[100%] h-[434rpx] -mt-[130rpx]",src:u(E)(se.value.banner_list[0].imageUrl),mode:"aspectFill",onClick:t[0]||(t[0]=e=>u(Q).toRedirect(se.value.banner_list[0].toLink))},null,8,["src"])):(r(),c(d,{key:1},[i(" 占位作用 "),p(s,{class:"w-[100%] h-[434rpx] -mt-[130rpx]"})],2112)),se.value.active_desc?(r(),o(s,{key:2,class:"side-tab !top-[30rpx]",onClick:t[1]||(t[1]=e=>te.value=!0)},{default:n((()=>[p(l,{class:"iconfont iconxinrenV6xx icon"}),p(l,{class:"desc"},{default:n((()=>[m("活动规则")])),_:1})])),_:1})):i("v-if",!0),ee.value>0&&null!=ee.value?(r(),o(s,{key:3,class:"time newcomer-time",style:x({"background-image":"url("+u(E)("addon/shop/newcomer/time_bg.png")+")"})},{default:n((()=>[p(l,{class:"text-[26rpx]"},{default:n((()=>[m("距结束还有：")])),_:1}),p(g,{class:"text-[#fff] min-w-[150rpx] text-[28rpx]",time:ee.value,format:"DD:HH:mm:ss",onChange:$},{default:n((()=>[p(s,{class:"flex"},{default:n((()=>[Z.value.days&&Z.value.days>0?(r(),o(s,{key:0,class:"text-[24rpx] flex items-center"},{default:n((()=>[p(l,{class:"min-w-[30rpx] text-right"},{default:n((()=>[m(f(Z.value.days),1)])),_:1}),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("天")])),_:1})])),_:1})):i("v-if",!0),p(s,{class:"text-[24rpx] flex items-center"},{default:n((()=>[Z.value.hours?(r(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:n((()=>[m(f(Z.value.hours>=10?Z.value.hours:"0"+Z.value.hours),1)])),_:1})):(r(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:n((()=>[m("00")])),_:1})),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("时")])),_:1})])),_:1}),p(s,{class:"text-[24rpx] flex items-center"},{default:n((()=>[Z.value.minutes?(r(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:n((()=>[m(f(Z.value.minutes>=10?Z.value.minutes:"0"+Z.value.minutes),1)])),_:1})):(r(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:n((()=>[m("00")])),_:1})),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("分")])),_:1})])),_:1}),p(s,{class:"text-[24rpx] flex items-center"},{default:n((()=>[Z.value.seconds?(r(),o(l,{key:0,class:"min-w-[30rpx] text-center"},{default:n((()=>[m(f(Z.value.seconds<10?"0"+Z.value.seconds:Z.value.seconds),1)])),_:1})):(r(),o(l,{key:1,class:"min-w-[30rpx] text-center"},{default:n((()=>[m("00")])),_:1})),p(l,{class:"text-[20rpx]"},{default:n((()=>[m("秒")])),_:1})])),_:1})])),_:1})])),_:1},8,["time"])])),_:1},8,["style"])):i("v-if",!0)])),_:1})),L.value.length?(r(),o(s,{key:1,class:"marketing-list p-[20rpx] relative -mt-[50rpx]"},{default:n((()=>[(r(!0),c(d,null,_(L.value,((e,t)=>(r(),o(s,{class:S(["bg-[#fff] flex rounded-[var(--rounded-mid)] p-[20rpx]",{"mb-[20rpx]":L.value.length-1!=t}]),key:t,onClick:t=>{I({url:"/addon/shop/pages/goods/detail",param:{sku_id:e.goodsSku.sku_id,type:"newcomer_discount"}})}},{default:n((()=>[p(s,{class:"w-[250rpx] h-[250rpx] flex items-center justify-center"},{default:n((()=>[e.goods_cover?(r(),o(a,{key:0,class:"w-[250rpx] h-[250rpx] rounded-[var(--rounded-mid)]",src:u(E)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:t=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),o(a,{key:1,class:"w-[250rpx] h-[250rpx] rounded-[var(--rounded-mid)]",src:u(E)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"]))])),_:2},1024),p(s,{class:"flex flex-col flex-1 ml-[20rpx] pt-[4rpx]"},{default:n((()=>[p(s,{class:"text-[28rpx] multi-hidden leading-[1.3]"},{default:n((()=>[m(f(e.goods_name),1)])),_:2},1024),p(s,{class:"w-[400rpx] flex items-center justify-center"},{default:n((()=>[p(a,{class:"w-[400rpx] h-[106rpx] mt-[auto] mb-[10rpx]",src:u(E)("addon/shop/newcomer/subsidy.png"),mode:"aspectFit"},null,8,["src"])])),_:1}),p(s,{class:"flex items-center justify-center btn-wrap"},{default:n((()=>[p(s,{class:"flex items-center text-[#FFF3E0] relative z-4",style:x({background:"linear-gradient(to right, #FF8A04 0%, #ff1b1b 84%)"})},{default:n((()=>[p(l,{class:"text-[22rpx]"},{default:n((()=>[m("新人价")])),_:1}),p(l,{class:"text-[20rpx] ml-[6rpx] mr-[2rpx]"},{default:n((()=>[m("￥")])),_:1}),p(l,{class:"text-[36rpx] truncate max-w-[160rpx]"},{default:n((()=>[m(f(parseFloat(e.goodsSku.newcomer_price).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"]),p(a,{class:"w-[26rpx] h-[54rpx]",src:u(E)("addon/shop/newcomer/btn_02.png"),mode:"heightFix"},null,8,["src"]),p(a,{class:"w-[84rpx] h-[54rpx] relative ml-[-5rpx] z-2",src:u(E)("addon/shop/newcomer/btn_03.png"),mode:"aspectFit"},null,8,["src"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})):!L.value.length&&J.value?(r(),o(O,{key:2,option:{tip:"暂无商品，请看看其他商品吧！",btnText:"去逛逛"},onEmptyclick:t[2]||(t[2]=e=>u(I)({url:"/addon/shop/pages/goods/list"}))})):i("v-if",!0)])),_:1},8,["onInit"])):i("v-if",!0),!K.value&&Object.keys(se.value).length&&"active"!=se.value.active_status?(r(),c(d,{key:1},[p(h,{data:u(Y),class:"top-header"},null,8,["data"]),p(O,{option:{tip:"活动未开启,请看看其他商品吧！",btnText:"去逛逛"},onEmptyclick:t[3]||(t[3]=e=>u(I)({url:"/addon/shop/pages/index"}))})],64)):i("v-if",!0),p(s,{onTouchmove:t[5]||(t[5]=v((()=>{}),["prevent","stop"]))},{default:n((()=>[p(D,{show:te.value,onClose:ae,mode:"center",round:"var(--rounded-big)"},{default:n((()=>[p(s,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:n((()=>[p(s,{class:"title"},{default:n((()=>[m("活动规则")])),_:1}),se.value.active_desc?(r(),o(y,{key:0,"scroll-y":!0,class:"px-[30rpx] box-border max-h-[260rpx]"},{default:n((()=>[(r(!0),c(d,null,_(se.value.active_desc.split("\n"),(e=>(r(),o(s,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:n((()=>[m(f(e),1)])),_:2},1024)))),256))])),_:1})):i("v-if",!0),p(s,{class:"btn-wrap !pt-[40rpx]"},{default:n((()=>[p(z,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:t[4]||(t[4]=e=>te.value=!1)},{default:n((()=>[m("我知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1}),p(A,{loading:K.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-02ca746e"]]);export{q as default};

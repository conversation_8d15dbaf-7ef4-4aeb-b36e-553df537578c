import{bK as e,bE as t,W as a,b8 as s,L as o,y as r,aw as l,o as n,c as i,w as d,k as c,b as u,A as p,g as m,R as f,a3 as _,S as h,B as g,f as y,v as b,aT as w,i as j,j as I,I as k,aH as L,aI as S,bb as v,ag as x,ay as F,T}from"./index-4b8dc7db.js";import{_ as C}from"./u-loading-icon.11ef83b8.js";import{_ as M}from"./u-image.18977c35.js";import{_ as A}from"./u-tag.aca70620.js";import{_ as U}from"./u-line-progress.a964ad88.js";import{_ as V}from"./u-avatar.db5bcfa1.js";import{_ as R}from"./u-icon.33002907.js";import{_ as P}from"./u-tabs.da58618d.js";import{_ as $}from"./u-empty.a1ed4d4b.js";import{_ as O}from"./u-button.7aa0e948.js";import{_ as D}from"./u-checkbox.598bfa18.js";import{_ as E}from"./u-popup.e2790691.js";import{_ as N}from"./pay.2b11dfa6.js";import{d as K,e as z,f as J,h as B,i as G,j as q}from"./project.df03875d.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.5763ee65.js";/* empty css                                                                     *//* empty css                                                                */import"./u-text.40d19739.js";/* empty css                                                               */import"./u-badge.e47151e6.js";import"./u-safe-bottom.987908cd.js";import"./pay.eee54be7.js";function Q(){const e=uni.getStorageSync("wapToken"),t=uni.getStorageSync("wap_member_info");return!(!e||!t)}function W(e,o="请先登录后再进行此操作"){Q()?e&&e():t({title:"登录提示",content:o,confirmText:"去登录",cancelText:"取消",success:e=>{if(e.confirm){const e=a(),t=e[e.length-1],o=t.route,r=t.options;let l="/"+o;if(r&&Object.keys(r).length>0){l+="?"+Object.keys(r).map((e=>`${e}=${r[e]}`)).join("&")}const n=l.split("?"),i=n[0],d={};n[1]&&n[1].split("&").forEach((e=>{const[t,a]=e.split("=");t&&a&&(d[t]=decodeURIComponent(a))})),uni.setStorageSync("loginBack",{url:i,param:d}),s({url:"/app/pages/auth/login"})}}})}function X(e,t){return!!function(e){return!(!e.code||![401,"MUST_LOGIN","LOGIN_EXPIRE","TOKEN_INVALID"].includes(e.code))||!(!e.message||!e.message.includes("登录"))}(e)&&(W((()=>{t&&t()}),"登录已过期，请重新登录"),!0)}const Y=H({components:{Pay:N},data:()=>({projectId:0,projectInfo:{},rewardList:[],supportList:[],updateList:[],memberInfo:{},loading:!0,rewardLoading:!1,supportLoading:!1,updateLoading:!1,showSupportModal:!1,currentTab:0,tabList:[{name:"项目详情"},{name:"奖励档位"},{name:"项目动态"},{name:"支持记录"}],supportForm:{reward_id:0,amount:0,message:"",is_anonymous:!1,contact_name:"",contact_mobile:""}}),computed:{imageList(){return this.projectInfo.images?Array.isArray(this.projectInfo.images)?this.projectInfo.images.filter((e=>e)):this.projectInfo.images.split(",").filter((e=>e)):[]}},onLoad(e){this.projectId=parseInt(e.id),this.loadProjectInfo(),this.loadRewardList(),this.loadUpdateList(),this.loadSupportList(),Q()&&this.loadMemberInfo()},onShow(){Q()&&!this.memberInfo.member_id&&this.loadMemberInfo()},methods:{handleSupportClick(){console.log("点击立即支持按钮"),function(){const t=uni.getStorageSync("wapToken"),a=uni.getStorageSync("wap_member_info"),s=uni.getStorageSync("wap_member_id"),o={hasToken:!!t,tokenLength:t?t.length:0,hasMemberInfo:!!a,hasMemberId:!!s,memberInfo:a||null,memberId:s||null,allStorageKeys:[]};try{const t=e();o.allStorageKeys=t.keys}catch(r){o.allStorageKeys=["获取失败"]}console.log("登录状态调试信息:",o)}(),console.log("当前登录状态:",Q()),W((()=>{console.log("登录验证通过，显示支持弹窗"),console.log("设置前 showSupportModal:",this.showSupportModal),this.showSupportModal=!0,console.log("设置后 showSupportModal:",this.showSupportModal),this.$forceUpdate(),setTimeout((()=>{console.log("延迟检查 showSupportModal:",this.showSupportModal)}),100)}),"请先登录后再支持项目")},async loadMemberInfo(){try{const e=uni.getStorageSync("wap_member_info");if(e)return this.memberInfo=e,console.log("从本地存储获取用户信息:",this.memberInfo),void this.fillContactInfo();const t=await K();this.memberInfo=t.data,console.log("从API获取用户信息:",this.memberInfo),this.fillContactInfo()}catch(e){if(console.error("获取用户信息失败:",e),X(e,(()=>this.loadMemberInfo())))return}},fillContactInfo(){this.memberInfo&&(this.supportForm.contact_name=this.memberInfo.nickname||this.memberInfo.username||"",this.supportForm.contact_mobile=this.memberInfo.mobile||"")},async loadProjectInfo(){try{const e=await z(this.projectId);this.projectInfo=e.data,o({title:this.projectInfo.project_name})}catch(e){console.error(e),r({title:"加载失败",icon:"none"})}finally{this.loading=!1}},async loadRewardList(){this.rewardLoading=!0;try{console.log("开始加载奖励档位，项目ID:",this.projectId);const e=await J(this.projectId);console.log("奖励档位API响应:",e),e&&e.data?(this.rewardList=Array.isArray(e.data)?e.data:[],console.log("奖励档位数据:",this.rewardList),console.log("奖励档位数量:",this.rewardList.length),console.log("第一个奖励档位:",this.rewardList[0]),console.log("rewardList 赋值后:",JSON.stringify(this.rewardList)),this.$forceUpdate(),setTimeout((()=>{this.$forceUpdate(),console.log("延迟强制更新后 rewardList:",this.rewardList.length)}),100)):(this.rewardList=[],console.warn("奖励档位API返回数据格式异常:",e))}catch(e){console.error("加载奖励档位失败:",e),this.rewardList=[],r({title:"加载奖励档位失败",icon:"none"})}finally{this.rewardLoading=!1}},async loadUpdateList(){this.updateLoading=!0;try{console.log("开始加载项目动态，项目ID:",this.projectId);const e=await B(this.projectId,{limit:20});console.log("项目动态API响应:",e),e&&e.data&&e.data.data?this.updateList=Array.isArray(e.data.data)?e.data.data:[]:(this.updateList=[],console.warn("项目动态API返回数据格式异常:",e))}catch(e){console.error("加载项目动态失败:",e),this.updateList=[]}finally{this.updateLoading=!1}},async loadSupportList(){this.supportLoading=!0;try{console.log("开始加载支持记录，项目ID:",this.projectId);const e=await G({project_id:this.projectId,limit:20});console.log("支持记录API响应:",e),e&&e.data&&e.data.data?this.supportList=Array.isArray(e.data.data)?e.data.data:[]:(this.supportList=[],console.warn("支持记录API返回数据格式异常:",e))}catch(e){console.error("加载支持记录失败:",e),this.supportList=[]}finally{this.supportLoading=!1}},handleTabChange(e){console.log("切换标签页:",e),console.log("当前奖励档位数量:",this.rewardList.length),console.log("当前支持记录数量:",this.supportList.length),this.currentTab=e,this.$nextTick((()=>{this.$forceUpdate()})),1===e&&0===this.rewardList.length?this.loadRewardList():2===e&&0===this.updateList.length?this.loadUpdateList():3===e&&0===this.supportList.length&&this.loadSupportList()},selectReward(e){this.supportForm.reward_id=e.reward_id,this.supportForm.amount=e.amount,this.showSupportModal=!0},selectRewardOption(e){this.supportForm.reward_id=e.reward_id,this.supportForm.amount=e.amount},async handleSupport(){if(!this.supportForm.amount||this.supportForm.amount<=0)return void r({title:"请输入正确的支持金额",icon:"none"});if(!this.supportForm.contact_mobile)return void r({title:"请输入联系人手机号",icon:"none"});if(/^1[3-9]\d{9}$/.test(this.supportForm.contact_mobile))if(this.memberInfo.member_id){this.supportLoading=!0;try{const e={project_id:this.projectId,reward_id:this.supportForm.reward_id,amount:this.supportForm.amount,message:this.supportForm.message,is_anonymous:this.supportForm.is_anonymous?1:0,contact_name:this.supportForm.contact_name||this.memberInfo.nickname||this.memberInfo.username||"用户",contact_mobile:this.supportForm.contact_mobile,contact_address:this.memberInfo.address||""},t=await q(e);this.showSupportModal=!1,console.log("准备调用支付组件:",{trade_type:t.data.trade_type,trade_id:t.data.trade_id,return_url:"/addon/niucrowd/pages/member/support-list"}),uni.setStorageSync("payReturn",encodeURIComponent("/addon/niucrowd/pages/member/supports")),console.log("手动设置payReturn:",uni.getStorageSync("payReturn")),this.$refs.payRef.open(t.data.trade_type,t.data.trade_id,"/addon/niucrowd/pages/member/supports"),this.loadProjectInfo(),this.loadUpdateList(),this.loadSupportList()}catch(e){if(console.error(e),X(e,(()=>this.handleSupport())))return;r({title:e.message||"支持失败",icon:"none"})}finally{this.supportLoading=!1}}else r({title:"请先登录",icon:"none"});else r({title:"请输入正确的手机号格式",icon:"none"})},getStatusType:e=>["warning","primary","success","error","info"][e]||"info",formatTime:e=>new Date(1e3*e).toLocaleString(),formatJoinTime(e){if(!e)return"未知";const t=new Date(1e3*e);return`${t.getFullYear()}年${t.getMonth()+1}月加入`},getUpdateImages(e){if(!e)return[];try{if("string"==typeof e){const t=JSON.parse(e);return Array.isArray(t)?t.filter((e=>e)):[]}return Array.isArray(e)?e.filter((e=>e)):[]}catch(t){return console.error("解析项目动态图片失败:",t),[]}},previewImage(e,t){l({current:e,urls:t})},viewPublisherProfile(){this.projectInfo.member&&this.projectInfo.member.member_id?s({url:`/addon/niucrowd/pages/member/profile?member_id=${this.projectInfo.member.member_id}`}).catch((()=>{r({title:"发布人主页暂未开放",icon:"none"})})):r({title:"发布人信息不存在",icon:"none"})}}},[["render",function(e,t,a,s,o,r){const l=j(I("u-loading-icon"),C),K=k,z=c,J=j(I("u-image"),M),B=L,G=S,q=j(I("u-tag"),A),H=j(I("u-line-progress"),U),Q=j(I("u-avatar"),V),W=j(I("u-icon"),R),X=j(I("u-tabs"),P),Y=v,Z=j(I("u-empty"),$),ee=j(I("u-button"),O),te=x,ae=F,se=j(I("u-checkbox"),D),oe=j(I("u-popup"),E),re=j(I("pay"),N);return n(),i(z,{class:"project-detail"},{default:d((()=>[o.loading?(n(),i(z,{key:0,class:"loading-wrap"},{default:d((()=>[u(l),u(K,{class:"loading-text"},{default:d((()=>[p("加载中...")])),_:1})])),_:1})):o.projectInfo.project_id?(n(),i(z,{key:1,class:"detail-content"},{default:d((()=>[m(" 项目头图 "),u(z,{class:"project-header"},{default:d((()=>[u(G,{class:"project-swiper","indicator-dots":"",autoplay:""},{default:d((()=>[o.projectInfo.cover_image?(n(),i(B,{key:0},{default:d((()=>[u(J,{src:o.projectInfo.cover_image,width:"100%",height:"400rpx",mode:"aspectFill"},null,8,["src"])])),_:1})):m("v-if",!0),(n(!0),f(h,null,_(r.imageList,((e,t)=>(n(),i(B,{key:t},{default:d((()=>[u(J,{src:e,width:"100%",height:"400rpx",mode:"aspectFill"},null,8,["src"])])),_:2},1024)))),128))])),_:1}),u(z,{class:"project-status"},{default:d((()=>[u(q,{text:o.projectInfo.status_name,type:r.getStatusType(o.projectInfo.status)},null,8,["text","type"])])),_:1})])),_:1}),m(" 项目信息 "),u(z,{class:"project-info"},{default:d((()=>[u(z,{class:"project-title"},{default:d((()=>[p(g(o.projectInfo.project_name),1)])),_:1}),u(z,{class:"project-desc"},{default:d((()=>[p(g(o.projectInfo.project_desc),1)])),_:1}),m(" 进度条 "),u(z,{class:"progress-section"},{default:d((()=>[u(z,{class:"progress-bar"},{default:d((()=>[u(H,{percentage:o.projectInfo.progress_percent,"show-percent":!1,height:"12","active-color":"#007aff"},null,8,["percentage"])])),_:1}),u(z,{class:"progress-text"},{default:d((()=>[p(g(o.projectInfo.progress_percent)+"%",1)])),_:1})])),_:1}),m(" 统计数据 "),u(z,{class:"stats-grid"},{default:d((()=>[u(z,{class:"stat-card"},{default:d((()=>[u(z,{class:"stat-value"},{default:d((()=>[p("¥"+g(o.projectInfo.current_amount),1)])),_:1}),u(z,{class:"stat-label"},{default:d((()=>[p("已筹金额")])),_:1})])),_:1}),u(z,{class:"stat-card"},{default:d((()=>[u(z,{class:"stat-value"},{default:d((()=>[p("¥"+g(o.projectInfo.target_amount),1)])),_:1}),u(z,{class:"stat-label"},{default:d((()=>[p("目标金额")])),_:1})])),_:1}),u(z,{class:"stat-card"},{default:d((()=>[u(z,{class:"stat-value"},{default:d((()=>[p(g(o.projectInfo.support_count),1)])),_:1}),u(z,{class:"stat-label"},{default:d((()=>[p("支持人数")])),_:1})])),_:1}),u(z,{class:"stat-card"},{default:d((()=>[u(z,{class:"stat-value"},{default:d((()=>[p(g(o.projectInfo.remain_days),1)])),_:1}),u(z,{class:"stat-label"},{default:d((()=>[p("剩余天数")])),_:1})])),_:1})])),_:1})])),_:1}),m(" 发布人信息 "),o.projectInfo.member?(n(),i(z,{key:0,class:"publisher-info"},{default:d((()=>[u(z,{class:"publisher-header"},{default:d((()=>[u(z,{class:"publisher-avatar"},{default:d((()=>[u(Q,{src:o.projectInfo.member.headimg||"/static/images/default-avatar.png",size:"60",shape:"circle"},null,8,["src"])])),_:1}),u(z,{class:"publisher-details"},{default:d((()=>[u(z,{class:"publisher-name"},{default:d((()=>[u(K,{class:"name"},{default:d((()=>[p(g(o.projectInfo.member.display_name),1)])),_:1}),o.projectInfo.member.is_verified?(n(),i(z,{key:0,class:"verified-badge"},{default:d((()=>[u(W,{name:"checkmark-circle-fill",color:"#52c41a",size:"16"}),u(K,{class:"verified-text"},{default:d((()=>[p("已认证")])),_:1})])),_:1})):(n(),i(z,{key:1,class:"unverified-badge"},{default:d((()=>[u(W,{name:"info-circle",color:"#faad14",size:"16"}),u(K,{class:"unverified-text"},{default:d((()=>[p(g(o.projectInfo.member.auth_status_text),1)])),_:1})])),_:1}))])),_:1}),u(z,{class:"publisher-meta"},{default:d((()=>[u(K,{class:"join-time"},{default:d((()=>[p("项目发布人")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})):m("v-if",!0),m(" 标签页 "),u(z,{class:"tabs-section"},{default:d((()=>[u(X,{list:o.tabList,modelValue:o.currentTab,"onUpdate:modelValue":t[0]||(t[0]=e=>o.currentTab=e),onChange:r.handleTabChange,scrollable:!1},null,8,["list","modelValue","onChange"]),u(z,{class:"tab-content"},{default:d((()=>[m(" 项目详情 "),y((n(),i(z,{class:"tab-panel",key:"detail-"+o.projectInfo.project_id},{default:d((()=>[u(z,{class:"detail-section"},{default:d((()=>[u(z,{class:"section-title"},{default:d((()=>[p("项目详情")])),_:1}),o.projectInfo.project_content?(n(),i(Y,{nodes:o.projectInfo.project_content,key:"content-"+o.projectInfo.project_id},null,8,["nodes"])):(n(),i(z,{key:1,class:"empty-content"},{default:d((()=>[p("暂无项目详情")])),_:1}))])),_:1}),o.projectInfo.reward_description?(n(),i(z,{key:0,class:"detail-section"},{default:d((()=>[u(z,{class:"section-title"},{default:d((()=>[p("回报说明")])),_:1}),(n(),i(Y,{nodes:o.projectInfo.reward_description,key:"reward-desc-"+o.projectInfo.project_id},null,8,["nodes"]))])),_:1})):m("v-if",!0)])),_:1})),[[b,0===o.currentTab.index||0===o.currentTab]]),m(" 奖励档位 "),y((n(),i(z,{class:"tab-panel",key:"reward-"+o.projectInfo.project_id},{default:d((()=>[o.rewardLoading?(n(),i(z,{key:0,class:"loading-wrap"},{default:d((()=>[u(l),u(K,{class:"loading-text"},{default:d((()=>[p("加载奖励档位中...")])),_:1})])),_:1})):0===o.rewardList.length?(n(),i(z,{key:1,class:"empty-wrap"},{default:d((()=>[u(Z,{text:"暂无奖励档位"})])),_:1})):(n(),i(z,{key:2,class:"reward-list"},{default:d((()=>[(n(!0),f(h,null,_(o.rewardList,(e=>(n(),i(z,{key:e.reward_id,class:"reward-card",onClick:t=>r.selectReward(e)},{default:d((()=>[u(z,{class:"reward-header"},{default:d((()=>[u(z,{class:"reward-amount"},{default:d((()=>[p("¥"+g(e.amount),1)])),_:2},1024),u(z,{class:"reward-stock"},{default:d((()=>[e.stock>0?(n(),i(K,{key:0},{default:d((()=>[p("剩余"+g(e.remain_stock||e.stock),1)])),_:2},1024)):(n(),i(K,{key:1},{default:d((()=>[p("无限制")])),_:1}))])),_:2},1024)])),_:2},1024),u(z,{class:"reward-name"},{default:d((()=>[p(g(e.reward_name),1)])),_:2},1024),u(z,{class:"reward-desc"},{default:d((()=>[p(g(e.reward_desc),1)])),_:2},1024),u(z,{class:"reward-delivery"},{default:d((()=>[p("预计发货："+g(e.delivery_time),1)])),_:2},1024),u(z,{class:"reward-support"},{default:d((()=>[p(g(e.support_count)+"人支持",1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1})),[[b,1===o.currentTab.index||1===o.currentTab]]),m(" 项目动态 "),y((n(),i(z,{class:"tab-panel",key:"update-"+o.projectInfo.project_id},{default:d((()=>[o.updateLoading?(n(),i(z,{key:0,class:"loading-wrap"},{default:d((()=>[u(l),u(K,{class:"loading-text"},{default:d((()=>[p("加载项目动态中...")])),_:1})])),_:1})):0===o.updateList.length?(n(),i(z,{key:1,class:"empty-wrap"},{default:d((()=>[u(Z,{text:"暂无项目动态"})])),_:1})):(n(),i(z,{key:2,class:"update-list"},{default:d((()=>[(n(!0),f(h,null,_(o.updateList,(e=>(n(),i(z,{key:e.update_id,class:"update-card"},{default:d((()=>[u(z,{class:"update-header"},{default:d((()=>[u(z,{class:"update-title"},{default:d((()=>[p(g(e.title),1)])),_:2},1024),u(z,{class:"update-time"},{default:d((()=>[p(g(r.formatTime(e.create_time)),1)])),_:2},1024)])),_:2},1024),u(z,{class:"update-content"},{default:d((()=>[e.content?(n(),i(Y,{nodes:e.content,key:"update-content-"+e.update_id},null,8,["nodes"])):m("v-if",!0)])),_:2},1024),e.images&&r.getUpdateImages(e.images).length>0?(n(),i(z,{key:0,class:"update-images"},{default:d((()=>[u(z,{class:"image-grid"},{default:d((()=>[(n(!0),f(h,null,_(r.getUpdateImages(e.images),((t,a)=>(n(),i(z,{key:a,class:"image-item",onClick:a=>r.previewImage(t,r.getUpdateImages(e.images))},{default:d((()=>[u(J,{src:t,width:"200rpx",height:"200rpx",mode:"aspectFill","border-radius":"8"},null,8,["src"])])),_:2},1032,["onClick"])))),128))])),_:2},1024)])),_:2},1024)):m("v-if",!0),e.member?(n(),i(z,{key:1,class:"update-author"},{default:d((()=>[u(K,{class:"author-label"},{default:d((()=>[p("发布者：")])),_:1}),u(K,{class:"author-name"},{default:d((()=>[p(g(e.member.nickname||e.member.username||"项目方"),1)])),_:2},1024)])),_:2},1024)):m("v-if",!0)])),_:2},1024)))),128))])),_:1}))])),_:1})),[[b,2===o.currentTab.index||2===o.currentTab]]),m(" 支持记录 "),y((n(),i(z,{class:"tab-panel",key:"support-"+o.projectInfo.project_id},{default:d((()=>[o.supportLoading?(n(),i(z,{key:0,class:"loading-wrap"},{default:d((()=>[u(l),u(K,{class:"loading-text"},{default:d((()=>[p("加载支持记录中...")])),_:1})])),_:1})):0===o.supportList.length?(n(),i(z,{key:1,class:"empty-wrap"},{default:d((()=>[u(Z,{text:"暂无支持记录"})])),_:1})):(n(),i(z,{key:2,class:"support-list"},{default:d((()=>[(n(!0),f(h,null,_(o.supportList,(e=>(n(),i(z,{key:e.support_id,class:"support-item"},{default:d((()=>[u(z,{class:"support-header"},{default:d((()=>[u(z,{class:"supporter-name"},{default:d((()=>{var t;return[p(g(e.is_anonymous?"匿名用户":null==(t=e.member)?void 0:t.nickname),1)]})),_:2},1024),u(z,{class:"support-time"},{default:d((()=>[p(g(r.formatTime(e.create_time)),1)])),_:2},1024)])),_:2},1024),u(z,{class:"support-amount"},{default:d((()=>[p("支持了 ¥"+g(e.amount),1)])),_:2},1024),e.message?(n(),i(z,{key:0,class:"support-message"},{default:d((()=>[p(g(e.message),1)])),_:2},1024)):m("v-if",!0)])),_:2},1024)))),128))])),_:1}))])),_:1})),[[b,3===o.currentTab.index||3===o.currentTab]])])),_:1})])),_:1})])),_:1})):m("v-if",!0),m(" 底部操作栏 "),o.projectInfo.project_id&&1===o.projectInfo.status?(n(),i(z,{key:2,class:"bottom-bar"},{default:d((()=>[u(z,{class:"action-buttons"},{default:d((()=>[u(ee,{type:"primary",size:"large",onClick:r.handleSupportClick,disabled:1!==o.projectInfo.status},{default:d((()=>[p(" 立即支持 ")])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})):m("v-if",!0),m(" 支持弹窗 "),u(oe,{show:o.showSupportModal,onClose:t[8]||(t[8]=e=>o.showSupportModal=!1),mode:"bottom","border-radius":"20","safe-area-inset-bottom":!0},{default:d((()=>[u(z,{class:"support-modal"},{default:d((()=>[u(z,{class:"modal-header"},{default:d((()=>[u(z,{class:"modal-title"},{default:d((()=>[p("选择支持档位")])),_:1}),u(W,{name:"close",onClick:t[1]||(t[1]=e=>o.showSupportModal=!1)})])),_:1}),u(z,{class:"modal-content"},{default:d((()=>[0===o.rewardList.length?(n(),i(z,{key:0,class:"custom-support"},{default:d((()=>[u(z,{class:"support-title"},{default:d((()=>[p("自定义支持金额")])),_:1}),u(te,{modelValue:o.supportForm.amount,"onUpdate:modelValue":t[2]||(t[2]=e=>o.supportForm.amount=e),type:"number",placeholder:"请输入支持金额",class:"custom-input"},null,8,["modelValue"])])),_:1})):(n(),i(z,{key:1,class:"reward-options"},{default:d((()=>[(n(!0),f(h,null,_(o.rewardList,(e=>(n(),i(z,{key:e.reward_id,class:T(["reward-option",{selected:o.supportForm.reward_id===e.reward_id}]),onClick:t=>r.selectRewardOption(e)},{default:d((()=>[u(z,{class:"option-header"},{default:d((()=>[u(z,{class:"option-amount"},{default:d((()=>[p("¥"+g(e.amount),1)])),_:2},1024),o.supportForm.reward_id===e.reward_id?(n(),i(W,{key:0,name:"checkmark-circle-fill",color:"#007aff"})):m("v-if",!0)])),_:2},1024),u(z,{class:"option-name"},{default:d((()=>[p(g(e.reward_name),1)])),_:2},1024),u(z,{class:"option-desc"},{default:d((()=>[p(g(e.reward_desc),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})),u(z,{class:"support-form"},{default:d((()=>[u(z,{class:"form-item"},{default:d((()=>[u(z,{class:"form-label"},{default:d((()=>[p("联系手机号 *")])),_:1}),u(te,{modelValue:o.supportForm.contact_mobile,"onUpdate:modelValue":t[3]||(t[3]=e=>o.supportForm.contact_mobile=e),type:"number",placeholder:"请输入联系手机号",maxlength:"11",class:"custom-input"},null,8,["modelValue"])])),_:1}),u(z,{class:"form-item"},{default:d((()=>[u(z,{class:"form-label"},{default:d((()=>[p("联系人姓名")])),_:1}),u(te,{modelValue:o.supportForm.contact_name,"onUpdate:modelValue":t[4]||(t[4]=e=>o.supportForm.contact_name=e),placeholder:"请输入联系人姓名",maxlength:"20",class:"custom-input"},null,8,["modelValue"])])),_:1}),u(z,{class:"form-item"},{default:d((()=>[u(z,{class:"form-label"},{default:d((()=>[p("留言给项目方（可选）")])),_:1}),u(ae,{modelValue:o.supportForm.message,"onUpdate:modelValue":t[5]||(t[5]=e=>o.supportForm.message=e),placeholder:"留言给项目方（可选）",maxlength:"200",class:"custom-textarea"},null,8,["modelValue"])])),_:1}),u(z,{class:"form-row"},{default:d((()=>[u(se,{modelValue:o.supportForm.is_anonymous,"onUpdate:modelValue":t[6]||(t[6]=e=>o.supportForm.is_anonymous=e)},{default:d((()=>[p("匿名支持")])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(z,{class:"modal-footer"},{default:d((()=>[w("uni-button",{class:"primary-btn-bg btn",type:"primary",onClick:t[7]||(t[7]=(...e)=>r.handleSupport&&r.handleSupport(...e)),loading:o.supportLoading,disabled:!o.supportForm.amount||o.supportForm.amount<=0}," 确认支付 ¥"+g(o.supportForm.amount||0),9,["loading","disabled"])])),_:1})])),_:1})])),_:1},8,["show"]),m(" 支付组件 "),u(re,{ref:"payRef"},null,512)])),_:1})}],["__scopeId","data-v-fc3775e3"]]);export{Y as default};

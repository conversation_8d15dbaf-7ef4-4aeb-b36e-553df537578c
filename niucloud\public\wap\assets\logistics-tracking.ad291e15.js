import{d as e,r as s,o as a,c as t,w as l,b as o,C as r,A as u,B as i,e as c,R as n,a3 as p,S as d,g as x,k as f,ap as v,I as m,i as _,j as b,F as h,T as y,aj as g}from"./index-4b8dc7db.js";import{_ as k,a as j}from"./u-steps.63afec2b.js";import{_ as w}from"./u-popup.e2790691.js";import{c as C}from"./order.b56aabaa.js";import{_ as I}from"./_plugin-vue_export-helper.1b428a4d.js";const T=I(e({__name:"logistics-tracking",setup(e,{expose:I}){const T=s(!1),A=s([]),B=s({}),V=async e=>{let s=await C(e);B.value=s.data},q=s(0),z=s(0),F=()=>{T.value=!1};return I({packageList:A,open:e=>{q.value=e.id,V(e),T.value=!0}}),(e,s)=>{const C=f,I=v,L=m,O=_(b("u-steps-item"),k),R=_(b("u-steps"),j),S=_(b("u-popup"),w);return a(),t(C,{onTouchmove:s[3]||(s[3]=r((()=>{}),["prevent","stop"]))},{default:l((()=>[o(S,{show:T.value,mode:"bottom",round:10,onClose:F,closeable:!0,safeAreaInsetBottom:!0,onTouchmove:s[2]||(s[2]=r((()=>{}),["prevent","stop"]))},{default:l((()=>[Object.keys(B.value).length?(a(),t(C,{key:0,class:"h-[70vh] px-[24rpx] bg-page pb-[20rpx]",onTouchmove:s[1]||(s[1]=r((()=>{}),["prevent","stop"]))},{default:l((()=>[o(C,{class:"font-500 text-center text-[32rpx] leading-[104rpx] box-border h-[104rpx]"},{default:l((()=>[u(i(c(h)("detailedInformation")),1)])),_:1}),o(I,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(z.value>3?z.value-2:0)},{default:l((()=>[A.value.length>1?(a(),t(C,{key:0,class:"flex py-[22rpx] whitespace-nowrap"},{default:l((()=>[(a(!0),n(d,null,p(A.value,((e,s)=>(a(),t(C,{id:"id"+s,class:y(["text-[26rpx] leading-[36rpx] mr-[30rpx] text-[#626779]",{"!text-primary class-select":e.id==q.value}]),key:s,onClick:a=>((e,s)=>{q.value=e.id,z.value=s;let a={id:e.id,mobile:e.mobile};V(a)})(e,s)},{default:l((()=>[u(i(e.name),1)])),_:2},1032,["id","class","onClick"])))),128))])),_:1})):x("v-if",!0)])),_:1},8,["scroll-into-view"]),o(C,{class:"text-[28rpx] mt-[20rpx]"},{default:l((()=>[o(C,{class:"flex justify-between mb-[20rpx]"},{default:l((()=>["none_express"==B.value.sub_delivery_type?(a(),t(L,{key:0,class:"mr-[20rpx]"},{default:l((()=>[u("无需物流")])),_:1})):(a(),n(d,{key:1},[o(L,{class:"mr-[20rpx]"},{default:l((()=>[u(i(B.value.company.company_name),1)])),_:1}),o(C,null,{default:l((()=>[o(L,{class:"mr-[14rpx]"},{default:l((()=>[u(i(B.value.express_number),1)])),_:1}),o(L,{onClick:s[0]||(s[0]=e=>c(g)(B.value.express_number))},{default:l((()=>[u(i(c(h)("copy")),1)])),_:1})])),_:1})],64))])),_:1})])),_:1}),"express"==B.value.sub_delivery_type?(a(),t(C,{key:0,class:"parcel",style:{height:"53vh"}},{default:l((()=>[0==B.value.traces.success?(a(),t(C,{key:0,class:"h-[56vh] flex flex-col items-center justify-center"},{default:l((()=>[o(L,{class:"nc-iconfont nc-icon-daishouhuoV6xx text-[180rpx] text-[#bfbfbf]"}),o(C,{class:"text-[28rpx] text-[#bfbfbf] leading-8"},{default:l((()=>[u("暂无物流信息～～")])),_:1})])),_:1})):(a(),t(I,{key:1,"scroll-y":!0,style:{height:"53vh",padding:"20rpx","box-sizing":"border-box"},class:"bg-white rounded-md"},{default:l((()=>[o(R,{current:0,dot:"",direction:"column",activeColor:"var(--primary-color)"},{default:l((()=>[(a(!0),n(d,null,p(B.value.traces.list,((e,s)=>(a(),t(O,{key:s+"id",title:e.remark,desc:e.datetime},null,8,["title","desc"])))),128))])),_:1})])),_:1}))])),_:1})):"none_express"==B.value.sub_delivery_type?(a(),t(C,{key:1,style:{height:"53vh"}},{default:l((()=>[o(C,{class:"h-[56vh] flex-col flex items-center justify-center"},{default:l((()=>[o(L,{class:"nc-iconfont nc-icon-daishouhuoV6xx text-[180rpx] text-[#bfbfbf]"}),o(C,{class:"text-[28rpx] text-[#bfbfbf] leading-8"},{default:l((()=>[u("无需物流～～")])),_:1})])),_:1})])),_:1})):x("v-if",!0)])),_:1})):x("v-if",!0)])),_:1},8,["show"])])),_:1})}}}),[["__scopeId","data-v-550c67e8"]]);export{T as l};

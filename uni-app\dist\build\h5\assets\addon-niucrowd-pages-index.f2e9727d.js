import{_ as o}from"./loading-page.vue_vue_type_script_setup_true_lang.c88f563e.js";import{d as t,r as s,o as e,c as r,w as i,b as p,e as a,f as m,v as u,g as n,n as _,i as j,j as l,k as d}from"./index-dd56d0cc.js";import{u as c}from"./useDiy.7a879ac9.js";import{d as g}from"./index.2c75d097.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.f15d7447.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-popup.457e1f1f.js";import"./u-safe-bottom.22d4d63b.js";import"./top-tabbar.c9ba9447.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.9e479d8a.js";import"./u-checkbox.e4ea7913.js";import"./u-checkbox-group.c46d3a73.js";import"./u-button.b6743e99.js";import"./u-input.ef44c0c4.js";import"./u-picker.1af38a2a.js";import"./u-upload.44346d61.js";import"./u-radio-group.3476fb8a.js";import"./diy_form.2c8e4c0e.js";import"./u-action-sheet.daa5fa92.js";import"./u-line.ddd38835.js";import"./u-avatar.ea828bd7.js";import"./u-text.1f240d34.js";import"./u-parse.ae2d35cb.js";import"./tabbar.0d5e534b.js";import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./category.367da76d.js";import"./common.eabc72c7.js";import"./project.e6204607.js";import"./index.2657d9a5.js";import"./u--image.cd475bba.js";import"./u-image.dfca355c.js";/* empty css                                                                */import"./goods.dbf7b09d.js";import"./useGoods.392f2eb1.js";import"./add-cart-popup.4746de5d.js";import"./u-number-box.41986fc4.js";import"./coupon.506f719c.js";import"./point.00412433.js";import"./rank.d3d05a88.js";import"./bind-mobile.9929c841.js";import"./u-form.b5669646.js";import"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import"./u-modal.0666cf44.js";import"./newcomer.c56b90d6.js";import"./order.22f5d222.js";const f=y(t({__name:"index",setup(t){const y=c({name:"DIY_NIUCROWD_INDEX"}),f=s(null);return s(null),y.onLoad(),y.onShow((o=>{var t;null==(t=f.value)||t.refresh()})),y.onHide(),y.onUnload(),y.onPageScroll(),(t,s)=>{const c=j(l("loading-page"),o),v=d;return e(),r(v,{style:_(t.themeColor())},{default:i((()=>[p(c,{loading:a(y).getLoading()},null,8,["loading"]),m(p(v,null,{default:i((()=>[n(" 自定义模板渲染 "),p(v,{class:"diy-template-wrap bg-index",style:_(a(y).pageStyle())},{default:i((()=>[p(g,{ref_key:"diyGroupRef",ref:f,data:a(y).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[u,!a(y).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-12175715"]]);export{f as default};

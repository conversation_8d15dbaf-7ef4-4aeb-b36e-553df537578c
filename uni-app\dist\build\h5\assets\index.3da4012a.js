import{d as e,r as t,l as o,a9 as l,o as r,c as s,w as a,g as i,T as d,n,R as u,S as p,a3 as x,e as c,b as g,$ as m,k as f,q as y,p as _,az as h,N as v,Q as b,an as k,E as w,aq as S,ar as R,i as C,j as B,ap as j,A as N,B as F,C as G,J as E,y as T,b2 as W,a as $,G as A,I as H}from"./index-4b8dc7db.js";import{_ as I}from"./u--image.892273b2.js";import{_ as D}from"./_plugin-vue_export-helper.1b428a4d.js";import{a as O}from"./goods.f34d2594.js";import{u as P}from"./useGoods.0898f296.js";import{u as V,a as M}from"./add-cart-popup.da2aa6c6.js";const L=D(e({__name:"x-skeleton",props:{type:{type:String,default:"",required:!0},loading:{type:Boolean,default:!0},animate:{type:Boolean,default:!0},animateTime:{type:Number,default:1.8},fadeOut:{type:Boolean,default:!0},fadeOutTime:{type:Number,default:.5},bgColor:{type:String,default:""},highlightBgColor:{type:String,default:""},config:{type:Object,default:()=>({})}},setup(e){const y=e,_=t(y.config||{}),h=t(y.loading),v=t(!1);t(0);const b=o((()=>{var e;return new Array(Number((null==(e=_.value)?void 0:e.gridRows)||[]))})),k=o((()=>{var e;return new Array(Number((null==(e=_.value)?void 0:e.gridColumns)||[]))})),w=o((()=>{var e;if(!(null==(e=_.value)?void 0:e.textShow))return[];/%$/.test(_.value.textHeight)&&console.error("x-skeleton: textHeight参数不支持百分比单位");const t=[];for(let o=0;o<_.value.textRows;o++){const{gridRows:e,textWidth:l,textHeight:r}=_.value;let s={},a=R(l)?l[o]||(o===e-1?"70%":"100%"):o===e-1?"70%":l,i=R(r)?r[o]||"30rpx":r;/%$/.test(a)?s.width=a:s.width=C(a),s.height=C(i),t.push(s)}return t})),S=o((()=>["animateTime","fadeOutTime","bgColor","highlightBgColor"].map((e=>e.indexOf("Time")>-1?`--${e}:${y[e]}s`:`--${e}:${y[e]}`)).join(";")));l((()=>y.loading),(e=>{e?h.value=!0:y.fadeOut?(v.value=!0,setTimeout((()=>{h.value=!1,v.value=!1}),1e3*y.fadeOutTime)):h.value=!1}),{immediate:!0}),l((()=>y.type),(e=>{var t;_.value="banner"===e?(t=y.config,{padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"40rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"center",headShow:!0,headWidth:"100%",headHeight:"300rpx",headBorderRadius:"20rpx",textShow:!1,textRows:3,textRowsGap:"20rpx",textWidth:"100%",textHeight:"30rpx",textBorderRadius:"6rpx",...t}):"info"===e?function(e){return{padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!0,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:["40rpx","24rpx","24rpx","24rpx"],textBorderRadius:"6rpx",...e}}(y.config):"text"===e?function(e){return{padding:"20rpx",gridRows:1,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!1,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:"30rpx",textBorderRadius:"6rpx",...e}}(y.config):"menu"===e?function(e){return{padding:"20rpx",gridRows:2,gridColumns:5,gridRowsGap:"40rpx",gridColumnsGap:"40rpx",itemDirection:"column",itemGap:"16rpx",itemAlign:"center",headShow:!0,headWidth:"100rpx",headHeight:"100rpx",headBorderRadius:"50%",textShow:!0,textRows:1,textRowsGap:"0rpx",textWidth:"100%",textHeight:"24rpx",textBorderRadius:"6rpx",...e}}(y.config):"list"===e?function(e){return{padding:"20rpx",gridRows:2,gridColumns:1,gridRowsGap:"50rpx",gridColumnsGap:"24rpx",itemDirection:"row",itemGap:"30rpx",itemAlign:"flex-start",headShow:!0,headWidth:"200rpx",headHeight:"200rpx",headBorderRadius:"16rpx",textShow:!0,textRows:4,textRowsGap:"30rpx",textWidth:["50%","100%","100%","80%"],textHeight:["38rpx","24rpx","24rpx","24rpx"],textBorderRadius:"6rpx",...e}}(y.config):"waterfall"===e?function(e){return{padding:"20rpx",gridRows:2,gridColumns:2,gridRowsGap:"40rpx",gridColumnsGap:"24rpx",itemDirection:"column",itemGap:"16rpx",itemAlign:"center",headShow:!0,headWidth:"100%",headHeight:"400rpx",headBorderRadius:"12rpx",textShow:!0,textRows:3,textRowsGap:"12rpx",textWidth:["40%","85%","60%"],textHeight:["30rpx","20rpx","20rpx"],textBorderRadius:"6rpx",...e}}(y.config):y.config||{}}),{immediate:!0});const R=e=>"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e),C=(e="auto",t="px")=>(e=String(e),/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)?`${e}${t}`:e);return(t,o)=>{const l=f;return r(),s(l,{class:"x-skeleton",style:n(c(S))},{default:a((()=>[i(" 骨架屏 "),h.value?(r(),s(l,{key:0,class:d(["x-skeleton__wrapper",[v.value&&"fade-out"]]),style:n({padding:_.value.padding})},{default:a((()=>[(r(!0),u(p,null,x(c(b),((t,o)=>(r(),s(l,{key:o,class:"x-skeleton__wrapper__rows",style:n({marginBottom:o<c(b).length-1?_.value.gridRowsGap:0})},{default:a((()=>[(r(!0),u(p,null,x(c(k),((t,o)=>(r(),s(l,{key:o,class:"x-skeleton__wrapper__columns",style:n({flexDirection:_.value.itemDirection,alignItems:_.value.itemAlign,marginRight:o<c(k).length-1?_.value.gridColumnsGap:0})},{default:a((()=>[_.value.headShow?(r(),s(l,{key:0,class:d(["x-skeleton__wrapper__head",[e.animate&&"animate"]]),style:n({width:_.value.headWidth,height:_.value.headHeight,borderRadius:_.value.headBorderRadius,marginRight:"row"==_.value.itemDirection&&_.value.textShow?_.value.itemGap:0,marginBottom:"column"==_.value.itemDirection&&_.value.textShow?_.value.itemGap:0})},null,8,["class","style"])):i("v-if",!0),_.value.textShow?(r(),s(l,{key:1,class:"x-skeleton__wrapper__text"},{default:a((()=>[(r(!0),u(p,null,x(c(w),((t,o)=>(r(),s(l,{key:o,class:d(["x-skeleton__wrapper__text__row",[e.animate&&"animate"]]),style:n({width:t.width,height:t.height,borderRadius:_.value.textBorderRadius,marginBottom:o<c(w).length-1?_.value.textRowsGap:0})},null,8,["class","style"])))),128))])),_:1})):i("v-if",!0)])),_:2},1032,["style"])))),128))])),_:2},1032,["style"])))),128))])),_:1},8,["class","style"])):(r(),u(p,{key:1},[i(" 插槽 "),g(l,null,{default:a((()=>[m(t.$slots,"default",{},void 0,!0)])),_:3})],2112))])),_:3},8,["style"])}}}),[["__scopeId","data-v-507980c7"]]),U=D(e({__name:"index",props:["component","index","value"],emits:["loadingFn"],setup(e,{emit:m}){const D=e,U=y(),q=V();q.getList();const z=_(),J=o((()=>q.cartList)),Q=o((()=>z.info)),K=P(),X=h(),Y=v({type:"",loading:"decorate"!=X.mode,config:{}}),Z=t([]),ee=o((()=>D.value?D.value:"decorate"==X.mode?X.value[D.index]:D.component)),te=o((()=>{var e="";return e+="position:relative;",ee.value.componentStartBgColor&&ee.value.componentEndBgColor?e+=`background:linear-gradient(${ee.value.componentGradientAngle},${ee.value.componentStartBgColor},${ee.value.componentEndBgColor});`:e+="background-color:"+(ee.value.componentStartBgColor||ee.value.componentEndBgColor)+";",ee.value.componentBgUrl&&(e+=`background-image:url('${w(ee.value.componentBgUrl)}');`,e+="background-size: cover;background-repeat: no-repeat;"),ee.value.topRounded&&(e+="border-top-left-radius:"+2*ee.value.topRounded+"rpx;"),ee.value.topRounded&&(e+="border-top-right-radius:"+2*ee.value.topRounded+"rpx;"),ee.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*ee.value.bottomRounded+"rpx;"),ee.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*ee.value.bottomRounded+"rpx;"),e})),oe=o((()=>{var e={val:"",style:""};return ee.value.imgElementRounded&&(e.val=2*ee.value.imgElementRounded+"rpx",e.style+="border-radius:"+2*ee.value.imgElementRounded+"rpx;"),e})),le=o((()=>{var e="";return ee.value.componentBgUrl&&(e+="position:absolute;top:0;width:100%;",e+=`background: rgba(0,0,0,${ee.value.componentBgAlpha/10});`,e+=`height:${he.value}px;`,ee.value.topRounded&&(e+="border-top-left-radius:"+2*ee.value.topRounded+"rpx;"),ee.value.topRounded&&(e+="border-top-right-radius:"+2*ee.value.topRounded+"rpx;"),ee.value.bottomRounded&&(e+="border-bottom-left-radius:"+2*ee.value.bottomRounded+"rpx;"),ee.value.bottomRounded&&(e+="border-bottom-right-radius:"+2*ee.value.bottomRounded+"rpx;")),e})),re=o((()=>{var e="";return ee.value.elementBgColor&&(e+="background-color:"+ee.value.elementBgColor+";"),ee.value.topElementRounded&&(e+="border-top-left-radius:"+2*ee.value.topElementRounded+"rpx;"),ee.value.topElementRounded&&(e+="border-top-right-radius:"+2*ee.value.topElementRounded+"rpx;"),ee.value.bottomElementRounded&&(e+="border-bottom-left-radius:"+2*ee.value.bottomElementRounded+"rpx;"),ee.value.bottomElementRounded&&(e+="border-bottom-right-radius:"+2*ee.value.bottomElementRounded+"rpx;"),"style-2"==ee.value.style&&(ee.value.margin&&ee.value.margin.both?e+="width: calc((100vw - "+4*ee.value.margin.both+"rpx - 20rpx) / 2);":e+="width: calc((100vw - 20rpx) / 2 );"),e})),se=o((()=>{var e="";return"button"==ee.value.btnStyle.style&&ee.value.btnStyle.aroundRadius&&(e+="border-radius:"+2*ee.value.btnStyle.aroundRadius+"rpx;"),ee.value.btnStyle.startBgColor&&ee.value.btnStyle.endBgColor?e+=`background:linear-gradient(${ee.value.btnStyle.startBgColor},${ee.value.btnStyle.endBgColor});`:e+="background-color:"+(ee.value.btnStyle.startBgColor||ee.value.btnStyle.endBgColor)+";",ee.value.btnStyle.textColor&&(e+="color:"+ee.value.btnStyle.textColor+";"),"button"==ee.value.btnStyle.style&&ee.value.btnStyle.fontWeight&&(e+="font-weight: bold;"),e})),ae=o((()=>{var e="";return ee.value.margin&&ee.value.margin.both?e+="calc((100vw - "+4*ee.value.margin.both+"rpx - 20rpx) / 2)":e+="calc((100vw - 20rpx) / 2 )",e})),ie=o((()=>{var e="";return e+="padding:0 20rpx;",ee.value.margin&&ee.value.margin.both?e+="width: calc(100vw - "+(4*ee.value.margin.both+40)+"rpx);":e+="box-sizing: border-box; width: 100vw;",e})),de=t(""),ne=()=>{de.value="margin-right:14rpx;"},ue=()=>{Z.value.forEach(((e,t)=>{e.isMaxBuy=!1;let o=-1;if(e.is_limit&&e.max_buy){let t=0;if(1==e.limit_type)t=e.max_buy;else{let o=e.max_buy-(e.has_buy||0);t=o>0?o:0}t>e.stock?o=e.stock:t<=e.stock&&(o=t)}0==o&&(e.isMaxBuy=!0)}))},pe=e=>{let t="0.00";return t=e.goodsSku.show_price,t},xe=(e,t)=>{if(ge.value)return!1;ge.value=!0;let o={goods_id:e.goodsSku.goods_id,sku_id:e.goodsSku.sku_id,sale_price:pe(e),stock:e.goodsSku.stock};e.id&&(o.num=e.num,o.id=e.id);let l=1;l=e.min_buy>0&&!e.num?e.min_buy:1,q.increase(o,l,(()=>{ge.value=!1,U.isAddCartRecommend=!0}))},ce=t(),ge=t(!1),me=(e,t)=>{if("decorate"==X.mode)return!1;if("virtual"==e.goods_type&&"verify"==e.virtual_receive_type)return be(e);if("cart"!==ee.value.btnStyle.cartEvent)return be(e);if(!Q.value)return E().setLoginBack({url:"/addon/shop/pages/index"}),!1;if(e.goodsSku.sku_spec_format)ce.value.open(e.goodsSku.sku_id);else{if(!e.goodsSku.stock||parseInt(e.goodsSku.num||0)>parseInt(e.goodsSku.stock))return void T({title:"商品库存不足",icon:"none"});if(e.min_buy&&e.min_buy>parseInt(e.stock))return void T({title:"商品库存小于起购数量",icon:"none"});xe(e)}},fe=(e,t,o)=>{if("decorate"==X.mode)return!1;if(parseInt(t.num)>=parseInt(t.stock))return void T({title:"商品库存不足",icon:"none"});let l=t.num;if(e.min_buy>0&&e.min_buy>t.num&&(l=e.min_buy),e.is_limit&&e.max_buy&&(1==e.limit_type?e.max_buy:(e.max_buy,e.has_buy)),e.is_limit&&l>=e.max_buy){let t=`该商品单次限购${e.max_buy}件`;return 1!=e.limit_type&&(t=`该商品每人限购${e.max_buy}件`),T({title:t,icon:"none"}),!1}let r=W(e);r.num=l,r.id=t.id,xe(r)},ye=(e,t)=>{if(ge.value||"decorate"==X.mode)return!1;ge.value=!0;let o=1;e.min_buy>0&&e.min_buy==t.num&&(o=e.min_buy),q.reduce({id:t.id,goods_id:t.goods_id,sku_id:t.sku_id,stock:t.stock,sale_price:t.sale_price,num:t.num},o,(()=>{ge.value=!1,U.isAddCartRecommend=!0}))},_e=R(),he=t(0);b((()=>{ve(),"decorate"==X.mode?l((()=>ee.value),((e,t)=>{e&&"GoodsList"==e.componentName&&k((()=>{S().in(_e).select(".diy-shop-goods-list").boundingClientRect((e=>{e&&(he.value=e.height)})).exec(),"style-3"==ee.value.style&&ne()}))})):l((()=>ee.value),((e,t)=>{ve()}),{deep:!0})}));const ve=()=>{if("decorate"==X.mode){let e={goods_cover_thumb_mid:"",goods_name:"商品名称",sale_num:"100",unit:"件",goodsSku:{show_price:100}};Z.value.push(e),Z.value.push(e),k((()=>{"style-3"==ee.value.style&&ne()}))}else"style-1"==ee.value.style?(Y.type="list",Y.type="list",Y.config={textRows:2}):"style-2"==ee.value.style?(Y.type="waterfall",Y.config={headHeight:"320rpx",gridRows:1,textRows:2,textWidth:["100%","80%"]}):"style-3"==ee.value.style&&(Y.type="waterfall",Y.config={gridRows:1,gridColumns:3,headHeight:"200rpx",textRows:2,textWidth:["100%","80%"]}),(()=>{let e={num:"all"==ee.value.source||"category"==ee.value.source?ee.value.num:"",goods_ids:"custom"==ee.value.source?ee.value.goods_ids:"",goods_category:"category"==ee.value.source?ee.value.goods_category:"",order:ee.value.sortWay};O(e).then((e=>{Z.value=e.data,Y.loading=!1,m("loadingFn",e.data),ee.value.componentBgUrl&&setTimeout((()=>{S().in(_e).select(".diy-shop-goods-list").boundingClientRect((e=>{e&&(he.value=e.height)})).exec()}),1e3),k((()=>{setTimeout((()=>{"style-3"==ee.value.style&&ne()}),500)})),ue()}))})()},be=e=>{$({url:"/addon/shop/pages/goods/detail",param:{goods_id:e.goods_id}})};return(e,t)=>{const o=f,l=A,m=C(B("u--image"),I),y=H,_=j,h=C(B("x-skeleton"),L);return r(),s(h,{type:Y.type,loading:Y.loading,config:Y.config},{default:a((()=>[g(o,{style:n(c(te)),class:"overflow-hidden"},{default:a((()=>[g(o,{style:n(c(le))},null,8,["style"]),g(o,{class:d({"diy-shop-goods-list relative flex flex-wrap justify-between":"style-2"!=c(ee).style,"biserial-goods-list":"style-2"==c(ee).style})},{default:a((()=>["style-1"==c(ee).style?(r(!0),u(p,{key:0},x(Z.value,((e,f)=>(r(),s(o,{class:d(["bg-white w-full flex p-[20rpx] overflow-hidden",{"mt-[20rpx]":f>0}]),style:n(c(re)),key:e.goods_id,onClick:t=>be(e)},{default:a((()=>[g(m,{radius:c(oe).val,width:"200rpx",height:"200rpx",src:c(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[g(l,{class:"w-[200rpx] h-[200rpx] overflow-hidden",style:n(c(oe).style),src:c(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"]),g(o,{class:"flex-1 flex flex-col ml-[20rpx] py-[6rpx] relative"},{default:a((()=>[c(ee).goodsNameStyle.control?(r(),s(o,{key:0,class:"text-[28rpx] leading-[40rpx] text-[#303133] multi-hidden mb-[10rpx]",style:n({color:c(ee).goodsNameStyle.color,fontWeight:c(ee).goodsNameStyle.fontWeight})},{default:a((()=>[e.goods_brand?(r(),s(o,{key:0,class:"brand-tag",style:n(c(K).baseTagStyle(e.goods_brand))},{default:a((()=>[N(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),N(" "+F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),e.sub_title?(r(),s(o,{key:1,class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden mb-[5rpx]"},{default:a((()=>[N(F(e.sub_title),1)])),_:2},1024)):i("v-if",!0),e.goods_label_name&&e.goods_label_name.length&&c(ee).labelStyle.control?(r(),s(o,{key:2,class:"flex flex-wrap mb-[10rpx]"},{default:a((()=>[(r(!0),u(p,null,x(e.goods_label_name,((e,t)=>(r(),u(p,null,["icon"==e.style_type&&e.icon?(r(),s(l,{key:0,class:"img-tag",src:c(w)(e.icon),mode:"heightFix",onError:t=>c(K).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),s(o,{key:1,class:"base-tag",style:n(c(K).baseTagStyle(e))},{default:a((()=>[N(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),g(o,{class:"mt-auto flex justify-between items-center"},{default:a((()=>[g(o,{class:"flex flex-col"},{default:a((()=>[c(ee).priceStyle.control?(r(),s(o,{key:0,class:"flex items-baseline leading-[1]"},{default:a((()=>[g(o,{class:"font-bold text-[var(--price-text-color)] price-font block truncate max-w-[350rpx]",style:n({color:c(ee).priceStyle.color})},{default:a((()=>[g(y,{class:"text-[24rpx] font-500 mr-[4rpx]"},{default:a((()=>[N("￥")])),_:1}),g(y,{class:"text-[40rpx] font-500"},{default:a((()=>[N(F(parseFloat(c(K).goodsPrice(e)).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"]),"member_price"==c(K).priceType(e)?(r(),s(l,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==c(K).priceType(e)?(r(),s(l,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==c(K).priceType(e)?(r(),s(l,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024)):i("v-if",!0),c(ee).saleStyle.control?(r(),s(y,{key:1,class:"mt-[8rpx] text-[22rpx] text-[var(--text-color-light9)]",style:n({color:c(ee).saleStyle.color})},{default:a((()=>[N(" 已售"+F(e.sale_num)+F(e.unit||"件"),1)])),_:2},1032,["style"])):i("v-if",!0)])),_:2},1024),c(ee).btnStyle.control&&!e.isMaxBuy||"decorate"==c(X).mode?(r(),s(o,{key:0,class:"absolute right-[16rpx] bottom-[16rpx]",onClick:t[0]||(t[0]=G((()=>{}),["stop"]))},{default:a((()=>[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&"cart"===c(ee).btnStyle.cartEvent?(r(),s(o,{key:0,class:"flex items-center"},{default:a((()=>[g(o,{class:"relative w-[40rpx] h-[40rpx]"},{default:a((()=>[g(y,{class:"!text-[40rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[12rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:G((t=>ye(e,c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),g(y,{class:"text-[#333] text-[24rpx] mx-[16rpx] w-[20rpx] text-center"},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),g(o,{class:"relative w-[40rpx] h-[40rpx]"},{default:a((()=>[g(y,{class:"!text-[40rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+f,onClick:G((t=>fe(e,c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type||"real"==e.goods_type||"decorate"==c(X).mode?(r(),u(p,{key:1},["button"==c(ee).btnStyle.style?(r(),s(o,{key:0,style:n(c(se)),class:"relative px-[18rpx] h-[48rpx] flex items-center justify-center bg-[red]",onClick:G((t=>me(e)),["stop"])},{default:a((()=>[g(y,{class:"text-[20rpx]"},{default:a((()=>[N(F(c(ee).btnStyle.text),1)])),_:1}),c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id].totalNum?(r(),s(o,{key:0,class:d(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",c(J)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):i("v-if",!0)])),_:2},1032,["style","onClick"])):(r(),s(o,{key:1,style:n(c(se)),class:"relative w-[46rpx] h-[46rpx] rounded-[50%] flex items-center justify-center",onClick:G((t=>me(e)),["stop"])},{default:a((()=>[g(y,{class:d(["nc-iconfont","text-[30rpx]",c(ee).btnStyle.style])},null,8,["class"]),c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id].totalNum?(r(),s(o,{key:0,class:d(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",c(J)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):i("v-if",!0)])),_:2},1032,["style","onClick"]))],64)):i("v-if",!0)])),_:2},1024)):i("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])))),128)):i("v-if",!0),"style-2"==c(ee).style?(r(),u(p,{key:1},[g(o,null,{default:a((()=>[(r(!0),u(p,null,x(Z.value,((e,f)=>(r(),u(p,null,[f%2==0?(r(),s(o,{key:0,class:d(["flex flex-col bg-[#fff] box-border overflow-hidden",{"mt-[24rpx]":f>1}]),style:n(c(re)),onClick:t=>be(e)},{default:a((()=>[g(m,{radius:c(oe).val,width:c(ae),height:c(ae),src:c(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[g(l,{style:n({width:c(ae),height:c(ae),"border-radius":c(oe).val}),src:c(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","width","height","src"]),g(o,{class:"relative min-h-[44rpx] px-[16rpx] flex-1 pt-[16rpx] pb-[20rpx] flex flex-col justify-between"},{default:a((()=>[c(ee).goodsNameStyle.control?(r(),s(o,{key:0,class:"text-[#303133] leading-[40rpx] text-[28rpx] multi-hidden",style:n({color:c(ee).goodsNameStyle.color,fontWeight:c(ee).goodsNameStyle.fontWeight})},{default:a((()=>[e.goods_brand?(r(),s(o,{key:0,class:"brand-tag",style:n(c(K).baseTagStyle(e.goods_brand))},{default:a((()=>[N(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),N(" "+F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),e.sub_title?(r(),s(o,{key:1,class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden my-[5rpx]"},{default:a((()=>[N(F(e.sub_title),1)])),_:2},1024)):i("v-if",!0),e.goods_label_name&&e.goods_label_name.length&&c(ee).labelStyle.control?(r(),s(o,{key:2,class:"flex flex-wrap"},{default:a((()=>[(r(!0),u(p,null,x(e.goods_label_name,((e,t)=>(r(),u(p,null,["icon"==e.style_type&&e.icon?(r(),s(l,{key:0,class:"img-tag",src:c(w)(e.icon),mode:"heightFix",onError:t=>c(K).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),s(o,{key:1,class:"base-tag",style:n(c(K).baseTagStyle(e))},{default:a((()=>[N(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),g(o,{class:"flex justify-between flex-wrap items-center mt-[20rpx]"},{default:a((()=>[g(o,{class:"flex flex-col"},{default:a((()=>[c(ee).priceStyle.control?(r(),s(o,{key:0,class:"flex items-baseline leading-[1]"},{default:a((()=>[g(o,{class:"text-[var(--price-text-color)] price-font block truncate max-w-[270rpx]",style:n({color:c(ee).priceStyle.color})},{default:a((()=>[g(y,{class:"text-[24rpx] font-400"},{default:a((()=>[N("￥")])),_:1}),g(y,{class:"text-[40rpx] font-500"},{default:a((()=>[N(F(parseFloat(c(K).goodsPrice(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),g(y,{class:"text-[24rpx] font-500"},{default:a((()=>[N("."+F(parseFloat(c(K).goodsPrice(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1032,["style"]),"member_price"==c(K).priceType(e)?(r(),s(l,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==c(K).priceType(e)?(r(),s(l,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==c(K).priceType(e)?(r(),s(l,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024)):i("v-if",!0),c(ee).saleStyle.control?(r(),s(y,{key:1,class:"text-[22rpx] mt-[8rpx] text-[var(--text-color-light9)]",style:n({color:c(ee).saleStyle.color})},{default:a((()=>[N(" 已售"+F(e.sale_num)+F(e.unit||"件"),1)])),_:2},1032,["style"])):i("v-if",!0)])),_:2},1024),c(ee).btnStyle.control&&!e.isMaxBuy||"decorate"==c(X).mode?(r(),s(o,{key:0,class:"absolute right-[16rpx] bottom-[16rpx]",onClick:t[1]||(t[1]=G((()=>{}),["stop"]))},{default:a((()=>[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&"cart"===c(ee).btnStyle.cartEvent?(r(),s(o,{key:0,class:"flex items-center"},{default:a((()=>[g(o,{class:"relative w-[40rpx] h-[40rpx]"},{default:a((()=>[g(y,{class:"!text-[40rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[12rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:G((t=>ye(e,c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),g(y,{class:"text-[#333] text-[24rpx] mx-[16rpx] w-[20rpx] text-center"},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),g(o,{class:"relative w-[40rpx] h-[40rpx]"},{default:a((()=>[g(y,{class:"!text-[40rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+f,onClick:G((t=>fe(e,c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type||"real"==e.goods_type||"decorate"==c(X).mode?(r(),u(p,{key:1},["button"==c(ee).btnStyle.style?(r(),s(o,{key:0,style:n(c(se)),class:"relative px-[18rpx] h-[48rpx] flex items-center justify-center bg-[red]",onClick:G((t=>me(e)),["stop"])},{default:a((()=>[g(y,{class:"text-[20rpx]"},{default:a((()=>[N(F(c(ee).btnStyle.text),1)])),_:1}),c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id].totalNum?(r(),s(o,{key:0,class:d(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",c(J)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):i("v-if",!0)])),_:2},1032,["style","onClick"])):(r(),s(o,{key:1,style:n(c(se)),class:"relative w-[46rpx] h-[46rpx] rounded-[50%] flex items-center justify-center",onClick:G((t=>me(e)),["stop"])},{default:a((()=>[g(y,{class:d(["nc-iconfont","text-[30rpx]",c(ee).btnStyle.style])},null,8,["class"]),c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id].totalNum?(r(),s(o,{key:0,class:d(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",c(J)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):i("v-if",!0)])),_:2},1032,["style","onClick"]))],64)):i("v-if",!0)])),_:2},1024)):i("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])):i("v-if",!0)],64)))),256))])),_:1}),g(o,null,{default:a((()=>[(r(!0),u(p,null,x(Z.value,((e,f)=>(r(),u(p,null,[f%2==1?(r(),s(o,{key:0,class:d(["flex flex-col bg-[#fff] box-border overflow-hidden",{"mt-[24rpx]":f>1}]),style:n(c(re)),onClick:t=>be(e)},{default:a((()=>[g(m,{width:c(ae),height:c(ae),radius:c(oe).val,src:c(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[g(l,{style:n({width:c(ae),height:c(ae),"border-radius":c(oe).val}),src:c(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["width","height","radius","src"]),g(o,{class:"relative min-h-[44rpx] px-[16rpx] flex-1 pt-[16rpx] pb-[20rpx] flex flex-col justify-between"},{default:a((()=>[c(ee).goodsNameStyle.control?(r(),s(o,{key:0,class:"text-[#303133] leading-[40rpx] text-[28rpx] multi-hidden",style:n({color:c(ee).goodsNameStyle.color,fontWeight:c(ee).goodsNameStyle.fontWeight})},{default:a((()=>[e.goods_brand?(r(),s(o,{key:0,class:"brand-tag",style:n(c(K).baseTagStyle(e.goods_brand))},{default:a((()=>[N(F(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),N(" "+F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),e.sub_title?(r(),s(o,{key:1,class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden my-[5rpx]"},{default:a((()=>[N(F(e.sub_title),1)])),_:2},1024)):i("v-if",!0),e.goods_label_name&&e.goods_label_name.length&&c(ee).labelStyle.control?(r(),s(o,{key:2,class:"flex flex-wrap"},{default:a((()=>[(r(!0),u(p,null,x(e.goods_label_name,((e,t)=>(r(),u(p,null,["icon"==e.style_type&&e.icon?(r(),s(l,{key:0,class:"img-tag",src:c(w)(e.icon),mode:"heightFix",onError:t=>c(K).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),s(o,{key:1,class:"base-tag",style:n(c(K).baseTagStyle(e))},{default:a((()=>[N(F(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),g(o,{class:"flex justify-between flex-wrap items-center mt-[20rpx]"},{default:a((()=>[g(o,{class:"flex flex-col"},{default:a((()=>[c(ee).priceStyle.control?(r(),s(o,{key:0,class:"flex items-baseline leading-[1]"},{default:a((()=>[g(o,{class:"text-[var(--price-text-color)] price-font block truncate max-w-[270rpx]",style:n({color:c(ee).priceStyle.color})},{default:a((()=>[g(y,{class:"text-[24rpx] font-400"},{default:a((()=>[N("￥")])),_:1}),g(y,{class:"text-[40rpx] font-500"},{default:a((()=>[N(F(parseFloat(c(K).goodsPrice(e)).toFixed(2).split(".")[0]),1)])),_:2},1024),g(y,{class:"text-[24rpx] font-500"},{default:a((()=>[N("."+F(parseFloat(c(K).goodsPrice(e)).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1032,["style"]),"member_price"==c(K).priceType(e)?(r(),s(l,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==c(K).priceType(e)?(r(),s(l,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==c(K).priceType(e)?(r(),s(l,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:c(w)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0)])),_:2},1024)):i("v-if",!0),c(ee).saleStyle.control?(r(),s(y,{key:1,class:"text-[22rpx] mt-[8rpx] text-[var(--text-color-light9)]",style:n({color:c(ee).saleStyle.color})},{default:a((()=>[N(" 已售"+F(e.sale_num)+F(e.unit||"件"),1)])),_:2},1032,["style"])):i("v-if",!0)])),_:2},1024),c(ee).btnStyle.control&&!e.isMaxBuy||"decorate"==c(X).mode?(r(),s(o,{key:0,class:"absolute right-[16rpx] bottom-[16rpx]",onClick:t[2]||(t[2]=G((()=>{}),["stop"]))},{default:a((()=>[("real"==e.goods_type||"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type)&&""===e.goodsSku.sku_spec_format&&c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id]&&"cart"===c(ee).btnStyle.cartEvent?(r(),s(o,{key:0,class:"flex items-center"},{default:a((()=>[g(o,{class:"relative w-[40rpx] h-[40rpx]"},{default:a((()=>[g(y,{class:"!text-[40rpx] text-color nc-iconfont nc-icon-jianshaoV6xx absolute flex items-center justify-center -left-[12rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",onClick:G((t=>ye(e,c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["onClick"])])),_:2},1024),g(y,{class:"text-[#333] text-[24rpx] mx-[16rpx] w-[20rpx] text-center"},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id].num),1)])),_:2},1024),g(o,{class:"relative w-[40rpx] h-[40rpx]"},{default:a((()=>[g(y,{class:"!text-[40rpx] text-color iconfont iconjiahao2fill absolute flex items-center justify-center -left-[14rpx] -bottom-[14rpx] -right-[14rpx] -top-[14rpx]",id:"itemCart"+f,onClick:G((t=>fe(e,c(J)["goods_"+e.goods_id]["sku_"+e.goodsSku.sku_id])),["stop"])},null,8,["id","onClick"])])),_:2},1024)])),_:2},1024)):"virtual"==e.goods_type&&"verify"!=e.virtual_receive_type||"real"==e.goods_type||"decorate"==c(X).mode?(r(),u(p,{key:1},["button"==c(ee).btnStyle.style?(r(),s(o,{key:0,style:n(c(se)),class:"relative px-[18rpx] h-[48rpx] flex items-center justify-center bg-[red]",onClick:G((t=>me(e)),["stop"])},{default:a((()=>[g(y,{class:"text-[20rpx]"},{default:a((()=>[N(F(c(ee).btnStyle.text),1)])),_:1}),c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id].totalNum?(r(),s(o,{key:0,class:d(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",c(J)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):i("v-if",!0)])),_:2},1032,["style","onClick"])):(r(),s(o,{key:1,style:n(c(se)),class:"relative w-[46rpx] h-[46rpx] rounded-[50%] flex items-center justify-center",onClick:G((t=>me(e)),["stop"])},{default:a((()=>[g(y,{class:d(["nc-iconfont","text-[30rpx]",c(ee).btnStyle.style])},null,8,["class"]),c(J)["goods_"+e.goods_id]&&c(J)["goods_"+e.goods_id].totalNum?(r(),s(o,{key:0,class:d(["absolute right-[-16rpx] top-[-16rpx] rounded-[30rpx] h-[30rpx] min-w-[30rpx] text-center leading-[26rpx] bg-[var(--primary-color)] text-[#fff] text-[20rpx] font-500 box-border box-border border-[2rpx] border-solid border-[#fff]",c(J)["goods_"+e.goods_id].totalNum>9?"px-[10rpx]":""])},{default:a((()=>[N(F(c(J)["goods_"+e.goods_id].totalNum),1)])),_:2},1032,["class"])):i("v-if",!0)])),_:2},1032,["style","onClick"]))],64)):i("v-if",!0)])),_:2},1024)):i("v-if",!0)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","style","onClick"])):i("v-if",!0)],64)))),256))])),_:1})],64)):i("v-if",!0),"style-3"==c(ee).style?(r(),u(p,{key:2},[Z.value.length?(r(),s(o,{key:0,style:n(c(ie))},{default:a((()=>[g(_,{id:"warpStyle3-"+c(ee).id,class:"whitespace-nowrap min-h-[290rpx]","scroll-x":!0},{default:a((()=>[(r(!0),u(p,null,x(Z.value,((e,t)=>(r(),s(o,{id:"item"+t+c(ee).id,class:d(["w-[214rpx] mb-[6rpx] inline-block bg-[#fff] box-border overflow-hidden",{"!mr-[0rpx]":t==Z.value.length-1}]),style:n(c(re)+de.value),key:e.goods_id,onClick:t=>be(e)},{default:a((()=>[g(m,{width:"214rpx",height:"160rpx",radius:c(oe).val,src:c(w)(e.goods_cover_thumb_mid||""),model:"aspectFill"},{error:a((()=>[g(l,{class:"w-[214rpx] h-[160rpx]",style:n(c(oe).style),src:c(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["style","src"])])),_:2},1032,["radius","src"]),g(o,{class:"relative min-h-[40rpx] px-[10rpx] pt-[16rpx] pb-[10rpx]"},{default:a((()=>[c(ee).goodsNameStyle.control?(r(),s(o,{key:0,class:"text-[26rpx] text-[#303133] truncate",style:n({color:c(ee).goodsNameStyle.color,fontWeight:c(ee).goodsNameStyle.fontWeight})},{default:a((()=>[N(F(e.goods_name),1)])),_:2},1032,["style"])):i("v-if",!0),c(ee).priceStyle.control?(r(),s(o,{key:1,class:"text-[var(--price-text-color)] pt-[16rpx] pb-[6rpx] font-bold price-font block truncate max-w-[160rpx] leading-[1] overflow-hidden",style:n({color:c(ee).priceStyle.color})},{default:a((()=>[g(y,{class:"text-[20rpx] font-400 mr-[2rpx]"},{default:a((()=>[N("￥")])),_:1}),g(y,{class:"text-[36rpx] font-500"},{default:a((()=>[N(F(parseFloat(c(K).goodsPrice(e)).toFixed(2)),1)])),_:2},1024)])),_:2},1032,["style"])):i("v-if",!0),c(ee).btnStyle.control?(r(),s(o,{key:2,class:"absolute right-[10rpx] bottom-[12rpx]"},{default:a((()=>["button"!=c(ee).btnStyle.style?(r(),s(o,{key:0,style:n(c(se)),class:"w-[40rpx] h-[40rpx] rounded-[50%] flex items-center justify-center"},{default:a((()=>[g(y,{class:d([[c(ee).btnStyle.style],"nc-iconfont text-[28rpx]"])},null,8,["class"])])),_:1},8,["style"])):i("v-if",!0)])),_:1})):i("v-if",!0)])),_:2},1024)])),_:2},1032,["id","class","style","onClick"])))),128))])),_:1},8,["id"])])),_:1},8,["style"])):i("v-if",!0)],64)):i("v-if",!0),g(M,{ref_key:"cartRef",ref:ce},null,512)])),_:1},8,["class"])])),_:1},8,["style"])])),_:1},8,["type","loading","config"])}}}),[["__scopeId","data-v-90d792ae"]]);export{L as _,U as d};

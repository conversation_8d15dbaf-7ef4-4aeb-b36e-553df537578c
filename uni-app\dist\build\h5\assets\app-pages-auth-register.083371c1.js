import{d as e,l,N as a,r,Q as o,p as t,m as s,s as i,x as n,y as d,a as u,o as p,c as m,w as c,b as x,A as g,B as f,e as b,R as _,g as h,S as v,C as y,n as k,D as w,F as C,Y as j,Z as V,J as F,W as P,X as S,k as T,i as R,j as z,G as A,I as U,H as q}from"./index-dd56d0cc.js";import{_ as L}from"./u-input.ef44c0c4.js";import{_ as B,a as O}from"./u-form.b5669646.js";import{_ as $}from"./u-icon.5895f8fc.js";import{u as D,_ as E}from"./sms-code.vue_vue_type_script_setup_true_lang.27501412.js";import{_ as I}from"./u-checkbox.e4ea7913.js";import{_ as N}from"./u-checkbox-group.c46d3a73.js";import{_ as Z}from"./uni-popup.af571cf3.js";import{t as G}from"./topTabbar.986d54c4.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-line.ddd38835.js";/* empty css                                                               */import"./u-modal.0666cf44.js";import"./u-loading-icon.f15d7447.js";import"./u-popup.457e1f1f.js";import"./u-transition.ab3d3894.js";/* empty css                                                                     */import"./u-safe-bottom.22d4d63b.js";const J=H(e({__name:"register",setup(e){let H={};G().setTopTabbarParam({title:"",topStatusBar:{bgColor:"#fff",textColor:"#333"}}),l((()=>Object.keys(H).length?w(Number(H.height))+w(H.top)+w(8)+"rpx":"auto"));const J=a({username:"",password:"",confirm_password:"",mobile:"",mobile_code:"",mobile_key:"",captcha_key:"",captcha_code:""}),Q=r(),W=()=>{Q.value.close()},X=()=>{pe.value=!0,Q.value.close(),ce()},Y=r(!0);o((()=>{setTimeout((()=>{Y.value=!1}),800)}));const K=r(!0),M=r(!0),ee=()=>{K.value=!K.value},le=()=>{M.value=!M.value},ae=t(),re=s(),oe=r("");i((async e=>{await re.getLoginConfig(),n()||re.login.is_username||re.login.is_mobile||re.login.is_bind_mobile||(d({title:"商家未开启普通账号注册",icon:"none"}),setTimeout((()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})}),100)),uni.getStorageSync("openid")&&Object.assign(J,{wx_openid:uni.getStorageSync("openid")}),uni.getStorageSync("pid")&&Object.assign(J,{pid:uni.getStorageSync("pid")}),re.login.is_username?ie.value="username":(re.login.is_mobile||re.login.is_bind_mobile)&&(ie.value="mobile"),oe.value=e.type}));const te=D(J);te.refresh();const se=r(!1),ie=r(""),ne=()=>{pe.value=!pe.value},de=l((()=>{const e=[];return re.login.is_username&&e.push({type:"username",title:C("usernameRegister")}),re.login.is_mobile&&!re.login.is_bind_mobile&&e.push({type:"mobile",title:C("mobileRegister")}),e})),ue=l((()=>({username:[{type:"string",required:"username"==ie.value,message:C("usernamePlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>!uni.$u.test.number(l),message:C("usernameTips"),trigger:["change","blur"]}],password:{type:"string",required:"username"==ie.value,message:C("passwordPlaceholder"),trigger:["blur","change"]},confirm_password:[{type:"string",required:"username"==ie.value,message:C("confirmPasswordPlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>l==J.password,message:C("confirmPasswordError"),trigger:["change","blur"]}],mobile:[{type:"string",required:"mobile"==ie.value||re.login.is_bind_mobile,message:C("mobilePlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>"mobile"!=ie.value&&!re.login.is_bind_mobile||uni.$u.test.mobile(l),message:C("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:"mobile"==ie.value||re.login.is_bind_mobile,message:C("codePlaceholder"),trigger:["blur","change"]},captcha_code:{type:"string",required:"username"==ie.value,message:C("captchaPlaceholder"),trigger:["blur","change"]}}))),pe=r(!1),me=r(null),ce=()=>{me.value.validate().then((()=>{if(re.login.agreement_show&&!pe.value)return Q.value.open(),!1;if(se.value)return;se.value=!0;("username"==ie.value?j:V)(J).then((e=>{ae.setToken(e.data.token),F().handleLoginBack()})).catch((()=>{se.value=!1,te.refresh()}))}))},xe=()=>{const e=P();if(e.length>1){"app/pages/auth/login"==e[e.length-2].route?S({delta:1}):u({url:"/app/pages/auth/login",mode:"redirectTo"})}else u({url:"/app/pages/auth/login",mode:"redirectTo"})};return(e,l)=>{const a=T,r=R(z("u-input"),L),o=R(z("u-form-item"),B),t=R(z("u-icon"),$),s=R(z("sms-code"),E),i=A,n=R(z("u-form"),O),d=R(z("u-checkbox"),I),w=R(z("u-checkbox-group"),N),j=U,V=q,F=R(z("uni-popup"),Z);return ie.value?(p(),m(a,{key:0,class:"w-screen h-screen flex flex-col",style:k(e.themeColor())},{default:c((()=>[x(a,{class:"mx-[60rpx]"},{default:c((()=>[x(a,{class:"pt-[140rpx] text-[44rpx] font-500 text-[#333]"},{default:c((()=>[g(f("username"==ie.value?b(C)("usernameRegister"):b(C)("mobileRegister")),1)])),_:1}),x(a,{class:"text-[26rpx] leading-[39rpx] text-[var(--text-color-light6)] mt-[16rpx] mb-[80rpx]"},{default:c((()=>[g(f("username"==ie.value?b(C)("usernameRegisterTip"):b(C)("mobileRegisterTip")),1)])),_:1}),x(n,{labelPosition:"left",model:J,errorType:"toast",rules:b(ue),ref_key:"formRef",ref:me},{default:c((()=>["username"==ie.value?(p(),_(v,{key:0},[x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:c((()=>[x(o,{label:"",prop:"username","border-bottom":!1},{default:c((()=>[x(r,{modelValue:J.username,"onUpdate:modelValue":l[0]||(l[0]=e=>J.username=e),border:"none",maxlength:"40",placeholder:b(C)("usernamePlaceholder"),class:"!bg-transparent",disabled:Y.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"password","border-bottom":!1},{default:c((()=>[x(r,{modelValue:J.password,"onUpdate:modelValue":l[1]||(l[1]=e=>J.password=e),border:"none",password:K.value,maxlength:"40",placeholder:b(C)("passwordPlaceholder"),class:"!bg-transparent",disabled:Y.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[J.password?(p(),m(a,{key:0,onClick:ee},{default:c((()=>[x(t,{name:K.value?"eye-off":"eye-fill",color:"#b9b9b9",size:"20"},null,8,["name"])])),_:1})):h("v-if",!0)])),_:1},8,["modelValue","password","placeholder","disabled"])])),_:1})])),_:1}),x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"confirm_password","border-bottom":!1},{default:c((()=>[x(r,{modelValue:J.confirm_password,"onUpdate:modelValue":l[2]||(l[2]=e=>J.confirm_password=e),border:"none",password:M.value,maxlength:"40",placeholder:b(C)("confirmPasswordPlaceholder"),class:"!bg-transparent",disabled:Y.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[J.confirm_password?(p(),m(a,{key:0,onClick:le},{default:c((()=>[x(t,{name:M.value?"eye-off":"eye-fill",color:"#b9b9b9",size:"20"},null,8,["name"])])),_:1})):h("v-if",!0)])),_:1},8,["modelValue","password","placeholder","disabled"])])),_:1})])),_:1})],64)):h("v-if",!0),"mobile"==ie.value||b(re).login.is_bind_mobile?(p(),_(v,{key:1},[x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"mobile","border-bottom":!1},{default:c((()=>[x(r,{modelValue:J.mobile,"onUpdate:modelValue":l[3]||(l[3]=e=>J.mobile=e),border:"none",maxlength:"11",placeholder:b(C)("mobilePlaceholder"),class:"!bg-transparent",disabled:Y.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),x(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"mobile_code","border-bottom":!1},{default:c((()=>[x(r,{modelValue:J.mobile_code,"onUpdate:modelValue":l[6]||(l[6]=e=>J.mobile_code=e),border:"none",maxlength:"4",placeholder:b(C)("codePlaceholder"),class:"!bg-transparent",disabled:Y.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[b(re).login.agreement_show?(p(),m(s,{key:0,mobile:J.mobile,type:"login",modelValue:J.mobile_key,"onUpdate:modelValue":l[4]||(l[4]=e=>J.mobile_key=e),isAgree:pe.value},null,8,["mobile","modelValue","isAgree"])):(p(),m(s,{key:1,mobile:J.mobile,type:"login",modelValue:J.mobile_key,"onUpdate:modelValue":l[5]||(l[5]=e=>J.mobile_key=e)},null,8,["mobile","modelValue"]))])),_:1},8,["modelValue","placeholder","disabled"])])),_:1})])),_:1})],64)):h("v-if",!0),"username"==ie.value?(p(),m(a,{key:2,class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:c((()=>[x(o,{label:"",prop:"captcha_code","border-bottom":!1},{default:c((()=>[x(r,{modelValue:J.captcha_code,"onUpdate:modelValue":l[8]||(l[8]=e=>J.captcha_code=e),border:"none",placeholder:b(C)("captchaPlaceholder"),class:"!bg-transparent",disabled:Y.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:c((()=>[x(i,{src:b(te).image.value,class:"h-[48rpx] w-[60rpx] ml-[20rpx]",mode:"heightFix",onClick:l[7]||(l[7]=e=>b(te).refresh())},null,8,["src"])])),_:1},8,["modelValue","placeholder","disabled"])])),_:1})])),_:1})):h("v-if",!0)])),_:1},8,["model","rules"]),x(a,{class:"mt-[160rpx]"},{default:c((()=>[b(re).login.agreement_show?(p(),m(a,{key:0,class:"flex items-center mb-[20rpx] py-[14rpx]",onClick:y(ne,["stop"])},{default:c((()=>[x(w,{onChange:ne},{default:c((()=>[x(d,{activeColor:"var(--primary-color)",checked:pe.value,shape:"circle",size:"30rpx"},null,8,["checked"])])),_:1}),x(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap leading-[30rpx]"},{default:c((()=>[x(j,null,{default:c((()=>[g(f(b(C)("agreeTips")),1)])),_:1}),x(j,{onClick:l[9]||(l[9]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(C)("privacyAgreement"))+"》",1)])),_:1}),x(j,null,{default:c((()=>[g(f(b(C)("and")),1)])),_:1}),x(j,{onClick:l[10]||(l[10]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(C)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):h("v-if",!0),x(V,{class:"w-full h-[80rpx] !bg-[var(--primary-color)] text-[26rpx] rounded-[40rpx] leading-[80rpx] font-500 !text-[#fff]",onClick:ce},{default:c((()=>[g(f(b(C)("register")),1)])),_:1}),x(a,{class:"flex items-center justify-between mt-[30rpx]"},{default:c((()=>[b(de).length>1?(p(),m(a,{key:0,class:"text-[26rpx] text-[var(--text-color-light6)] leading-[34rpx]",onClick:l[11]||(l[11]=e=>ie.value="username"==ie.value?"mobile":"username")},{default:c((()=>[g(f("username"==ie.value?b(C)("mobileRegister"):b(C)("usernameRegister")),1)])),_:1})):h("v-if",!0),x(a,{class:"text-[26rpx] text-[#333] leading-[34rpx]",onClick:xe},{default:c((()=>[x(j,null,{default:c((()=>[g(f(b(C)("haveAccount"))+",",1)])),_:1}),x(j,{class:"text-primary"},{default:c((()=>[g(f(b(C)("toLogin")),1)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),x(F,{ref_key:"popupRef",ref:Q,type:"dialog"},{default:c((()=>[x(a,{class:"bg-[#fff] flex flex-col justify-between w-[600rpx] min-h-[280rpx] rounded-[var(--rounded-big)] box-border px-[35rpx] pt-[35rpx] pb-[8rpx] relative"},{default:c((()=>[x(a,{class:"flex justify-center"},{default:c((()=>[x(j,{class:"text-[33rpx] font-700"},{default:c((()=>[g(" 用户协议及隐私保护")])),_:1})])),_:1}),x(a,{class:"flex items-center mb-[20rpx] mt-[20rpx] py-[20rpx]",onClick:y(ne,["stop"])},{default:c((()=>[x(a,{class:"text-[26rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:c((()=>[x(j,null,{default:c((()=>[g(f(b(C)("agreeTips")),1)])),_:1}),x(j,{onClick:l[12]||(l[12]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(C)("privacyAgreement"))+"》",1)])),_:1}),x(j,null,{default:c((()=>[g(f(b(C)("and")),1)])),_:1}),x(j,{onClick:l[13]||(l[13]=y((e=>b(u)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:c((()=>[g("《"+f(b(C)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"]),x(a,null,{default:c((()=>[x(a,{class:"w-[100%] flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] font-500 rounded-[50rpx]",onClick:X},{default:c((()=>[g("同意并注册")])),_:1}),x(a,{class:"w-[100%] flex justify-center h-[70rpx] leading-[70rpx] text-[#999] text-[24rpx] border-[0] font-500 rounded-[50rpx]",onClick:W},{default:c((()=>[g("不同意")])),_:1})])),_:1})])),_:1})])),_:1},512)])),_:1},8,["style"])):h("v-if",!0)}}}),[["__scopeId","data-v-6ffb37e8"]]);export{J as default};

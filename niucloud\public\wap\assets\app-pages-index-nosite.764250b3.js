import{d as s,h as a,q as e,a9 as t,o,c as l,w as c,b as n,e as r,A as i,B as d,n as u,G as p,k as m,E as g,F as f,a as h}from"./index-dd56d0cc.js";const _=s({__name:"nosite",setup(s){a();const _=e();return t((()=>_.site),((s,a)=>{s&&1==s.status&&h({url:"/app/pages/index/index",mode:"reLaunch"})})),(s,a)=>{const e=p,t=m;return o(),l(t,{class:"min-h-[100vh] bg-[var(--page-bg-color)] overflow-hidden",style:u(s.themeColor())},{default:c((()=>[n(t,{class:"empty-page"},{default:c((()=>[n(e,{class:"img",src:r(g)("static/resource/images/site/close.png"),model:"aspectFit"},null,8,["src"]),n(t,{class:"desc"},{default:c((()=>[i(d(r(f)("noSite")),1)])),_:1})])),_:1})])),_:1},8,["style"])}}});export{_ as default};

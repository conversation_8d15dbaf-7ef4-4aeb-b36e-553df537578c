import{a4 as e,a5 as t,a6 as o,o as a,c as i,w as l,$ as c,T as r,k as s}from"./index-4b8dc7db.js";import{_ as n}from"./_plugin-vue_export-helper.1b428a4d.js";const p=n({name:"u-checkbox-group",mixins:[t,o,{props:{name:{type:String,default:()=>e.checkboxGroup.name},modelValue:{type:Array,default:()=>e.checkboxGroup.value},shape:{type:String,default:()=>e.checkboxGroup.shape},disabled:{type:<PERSON><PERSON>an,default:()=>e.checkboxGroup.disabled},activeColor:{type:String,default:()=>e.checkboxGroup.activeColor},inactiveColor:{type:String,default:()=>e.checkboxGroup.inactiveColor},size:{type:[String,Number],default:()=>e.checkboxGroup.size},placement:{type:String,default:()=>e.checkboxGroup.placement},labelSize:{type:[String,Number],default:()=>e.checkboxGroup.labelSize},labelColor:{type:[String],default:()=>e.checkboxGroup.labelColor},labelDisabled:{type:Boolean,default:()=>e.checkboxGroup.labelDisabled},iconColor:{type:String,default:()=>e.checkboxGroup.iconColor},iconSize:{type:[String,Number],default:()=>e.checkboxGroup.iconSize},iconPlacement:{type:String,default:()=>e.checkboxGroup.iconPlacement},borderBottom:{type:Boolean,default:()=>e.checkboxGroup.borderBottom}}}],computed:{parentData(){return[this.modelValue,this.disabled,this.inactiveColor,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.borderBottom,this.placement]},bemClass(){return this.bem("checkbox-group",["placement"])}},watch:{parentData:{handler(){this.children.length&&this.children.map((e=>{"function"==typeof e.init&&e.init()}))},deep:!0}},data:()=>({}),created(){this.children=[]},emits:["update:modelValue","change"],methods:{unCheckedOther(e){const t=[];this.children.map((e=>{e.isChecked&&t.push(e.name)})),this.$emit("change",t),this.$emit("update:modelValue",t)}}},[["render",function(e,t,o,n,p,u){const h=s;return a(),i(h,{class:r(["u-checkbox-group",u.bemClass])},{default:l((()=>[c(e.$slots,"default",{},void 0,!0)])),_:3},8,["class"])}],["__scopeId","data-v-4665ee80"]]);export{p as _};

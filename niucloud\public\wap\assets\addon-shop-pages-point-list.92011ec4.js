import{d as e,r as t,Q as a,F as l,o as r,c as s,w as o,b as c,C as n,g as i,T as u,A as d,R as x,a3 as p,S as m,e as f,n as _,ah as v,ai as g,I as h,ag as y,k as b,i as j,j as k,E as w,B as C,a as V,G as F}from"./index-dd56d0cc.js";import{_ as E}from"./tabbar.0d5e534b.js";import{b as I}from"./point.00412433.js";import{M as S}from"./mescroll-body.520ba8e3.js";import{M}from"./mescroll-empty.8630a00e.js";import{u as T}from"./useMescroll.26ccf5de.js";import{u as U}from"./useGoods.392f2eb1.js";import{_ as G}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.5895f8fc.js";/* empty css                                                               */import"./u-badge.206da3ef.js";import"./u-tabbar.3565fd74.js";import"./u-safe-bottom.22d4d63b.js";import"./mescroll-i18n.92d484d5.js";const N=G(e({__name:"list",setup(e){const G=U(),{mescrollInit:N,downCallback:R,getMescroll:q}=T(g,v),z=t([]),A=t(""),B=t(""),H=t(null),J=t(!1),Q=t(""),W=t(""),D=t(""),K=t("total_order_num"),L=e=>{J.value=!1;let t={goods_category:B.value,page:e.num,limit:e.size,names:Q.value,coupon_id:A.value,order:"total_order_num"===K.value?"":K.value,sort:"price"==K.value?W.value:D.value};I(t).then((t=>{let a=t.data.data;1===Number(e.num)&&(z.value=[]),z.value=z.value.concat(a),e.endSuccess(a.length),J.value=!0})).catch((()=>{J.value=!0,e.endErr()}))},O=e=>{K.value=e,"total_order_num"==e&&(D.value="",W.value=""),"price"==e&&(D.value="",W.value?W.value="asc"==W.value?"desc":"asc":W.value="asc"),"total_exchange_num"==e&&(W.value="",D.value?D.value="asc"==D.value?"desc":"asc":D.value="asc"),z.value=[],q().resetUpScroll()};return a((()=>{setTimeout((()=>{q().optUp.textNoMore=l("end")}),500)})),(e,t)=>{const a=h,l=y,v=b,g=F,I=j(k("tabbar"),E);return r(),s(v,{class:"bg-[var(--page-bg-color)] min-h-[100vh]",style:_(e.themeColor())},{default:o((()=>[c(v,{class:"fixed left-0 right-0 top-0 product-warp bg-[#fff]"},{default:o((()=>[c(v,{class:"py-[14rpx] flex items-center justify-between px-[20rpx]"},{default:o((()=>[c(v,{class:"flex-1 search-input"},{default:o((()=>[c(a,{onClick:t[0]||(t[0]=n((e=>O("all")),["stop"])),class:"nc-iconfont nc-icon-sousuo-duanV6xx1 btn"}),c(l,{class:"input",maxlength:"50",type:"text",modelValue:Q.value,"onUpdate:modelValue":t[1]||(t[1]=e=>Q.value=e),placeholder:"请搜索您想要的商品",placeholderClass:"text-[var(--text-color-light9)] text-[24rpx]","confirm-type":"search",onConfirm:t[2]||(t[2]=e=>O("all"))},null,8,["modelValue"]),Q.value?(r(),s(a,{key:0,class:"nc-iconfont nc-icon-cuohaoV6xx1 clear",onClick:t[3]||(t[3]=e=>Q.value="")})):i("v-if",!0)])),_:1})])),_:1}),c(v,{class:"h-[88rpx] px-[30rpx]"},{default:o((()=>[c(v,{class:"flex items-center justify-between text-[26rpx] text-[var(--text-color-light6)] h-[88rpx]"},{default:o((()=>[c(a,{class:u({"!text-[var(--primary-color)] font-500":"total_order_num"==K.value}),onClick:t[4]||(t[4]=e=>O("total_order_num"))},{default:o((()=>[d("综合排序")])),_:1},8,["class"]),c(v,{class:"flex items-center",onClick:t[5]||(t[5]=e=>O("total_exchange_num"))},{default:o((()=>[c(a,{class:u(["mr-[4rpx]",{"!text-[var(--primary-color)] font-500":"total_exchange_num"==K.value}])},{default:o((()=>[d("销量")])),_:1},8,["class"]),"asc"==D.value?(r(),s(a,{key:0,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangshangV6xx1",{"!text-[var(--primary-color)]":"total_exchange_num"==K.value}])},null,8,["class"])):(r(),s(a,{key:1,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangxiaV6xx1",{"!text-[var(--primary-color)]":"total_exchange_num"==K.value}])},null,8,["class"]))])),_:1}),c(v,{class:"flex items-center",onClick:t[6]||(t[6]=e=>O("price"))},{default:o((()=>[c(a,{class:u(["mr-[4rpx]",{"!text-[var(--primary-color)] font-500":"price"==K.value}])},{default:o((()=>[d("价格")])),_:1},8,["class"]),"asc"==W.value?(r(),s(a,{key:0,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangshangV6xx1",{"!text-[var(--primary-color)]":"price"==K.value}])},null,8,["class"])):(r(),s(a,{key:1,class:u(["text-[16rpx] text-[var(--text-color-light6)] nc-iconfont nc-icon-a-xiangxiaV6xx1",{"!text-[var(--primary-color)]":"price"==K.value}])},null,8,["class"]))])),_:1})])),_:1})])),_:1})])),_:1}),c(S,{ref_key:"mescrollRef",ref:H,top:"176rpx",bottom:"60px",onInit:f(N),down:{use:!1},onUp:L},{default:o((()=>[z.value.length?(r(),s(v,{key:0,class:"sidebar-margin flex justify-between flex-wrap"},{default:o((()=>[(r(!0),x(m,null,p(z.value,((e,t)=>(r(),s(v,{class:"goods-item-style-two flex flex-col bg-[#fff] box-border rounded-[var(--rounded-mid)] overflow-hidden mt-[var(--top-m)]",onClick:t=>{return a=e.id,void V({url:"/addon/shop/pages/point/detail",param:{id:a},mode:"navigateTo"});var a}},{default:o((()=>[i(' <u--image width="100%" height="350rpx" :src="img(item.goods_cover_thumb_mid ? item.goods_cover_thumb_mid : \'\')" model="aspectFill">\r\n                          <template #error>\r\n                            <image class="w-[100%] h-[350rpx]" :src="img(\'static/resource/images/diy/shop_default.jpg\')" mode="aspectFill" />\r\n                          </template>\r\n                        </u--image> '),e.goods_cover_thumb_mid?(r(),s(g,{key:0,class:"w-[100%] h-[350rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(w)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:t=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(r(),s(g,{key:1,class:"w-[100%] h-[350rpx] rounded-tl-[var(--rounded-mid)] rounded-tr-[var(--rounded-mid)]",src:f(w)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])),c(v,{class:"px-[16rpx] flex-1 pt-[10rpx] pb-[20rpx] flex flex-col justify-between"},{default:o((()=>[i('   <view class="text-[#333] leading-[40rpx] text-[28rpx] multi-hidden">{{ item.names }}</view> '),c(v,{class:"text-[28rpx] text-[#333] leading-[40rpx] multi-hidden mb-[10rpx]"},{default:o((()=>[e.goods_brand?(r(),s(v,{key:0,class:"brand-tag",style:_(f(G).baseTagStyle(e.goods_brand))},{default:o((()=>[d(C(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),d(" "+C(e.names),1)])),_:2},1024),c(v,{class:"text-[24rpx] text-[#999] leading-[30rpx] using-hidden mb-[8rrpx]"},{default:o((()=>[d(C(e.sub_title),1)])),_:2},1024),e.goods_label&&e.goods_label.length?(r(),s(v,{key:0,class:"flex flex-wrap"},{default:o((()=>[(r(!0),x(m,null,p(e.goods_label,((e,t)=>(r(),x(m,null,["icon"==e.style_type&&e.icon?(r(),s(g,{key:0,class:"img-tag",src:f(w)(e.icon),mode:"heightFix",onError:t=>f(G).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(r(),s(v,{key:1,class:"base-tag",style:_(f(G).baseTagStyle(e))},{default:o((()=>[d(C(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),c(v,{class:"text-[24rpx] font-400 leading-[34rpx] mt-[10rpx] text-[var(--text-color-light9)]"},{default:o((()=>[d("已兑"+C(e.total_exchange_num)+"人",1)])),_:2},1024),c(v,{class:"flex justify-between flex-wrap items-center mt-[16rpx]"},{default:o((()=>[c(v,{class:"flex flex-col"},{default:o((()=>[c(v,{class:"text-[var(--price-text-color)] price-font ml-[2rpx] flex items-center"},{default:o((()=>[c(a,{class:"text-[32rpx]"},{default:o((()=>[d(C(e.point),1)])),_:2},1024),c(a,{class:"text-[26rpx] ml-[4rpx]"},{default:o((()=>[d("积分")])),_:1})])),_:2},1024),e.price&&e.price>0?(r(),s(v,{key:0,class:"flex items-center price-font mt-[6rpx]"},{default:o((()=>[c(a,{class:"text-[var(--price-text-color)] font-400 text-[32rpx]"},{default:o((()=>[d("+"+C(parseFloat(e.price).toFixed(2)),1)])),_:2},1024),c(a,{class:"text-[var(--price-text-color)] font-400 ml-[4rpx] text-[20rpx]"},{default:o((()=>[d("元")])),_:1})])),_:2},1024)):i("v-if",!0)])),_:2},1024),c(v,{class:"w-[120rpx] h-[54rpx] text-[22rpx] flex-center !text-[#fff] m-0 rounded-full primary-btn-bg remove-border text-center",shape:"circle"},{default:o((()=>[d("去兑换")])),_:1})])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),256))])),_:1})):i("v-if",!0),!z.value.length&&J.value?(r(),s(M,{key:1,option:{tip:"暂无商品"}})):i("v-if",!0)])),_:1},8,["onInit"]),c(I)])),_:1},8,["style"])}}}),[["__scopeId","data-v-e622e72e"]]);export{N as default};
